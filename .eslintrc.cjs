/* eslint-env node */
require("@rushstack/eslint-patch/modern-module-resolution")

module.exports = {
  root: true,
  extends: [
    "./.eslintrc-auto-import.json",
    "plugin:vue/vue3-essential",
    "eslint:recommended",
    "@vue/eslint-config-typescript",
    "@vue/eslint-config-prettier",
  ],
  rules: {
    "@typescript-eslint/no-unused-vars": 1,
    "vue/multi-word-component-names": 0,
    "no-undef": 0,
    "vue/no-mutating-props": 0,
    "no-prototype-builtins": 0,
    "no-async-promise-executor": 0,
    "vue/valid-v-slot": 0,
    "vue/no-use-v-if-with-v-for": 0,
  },
  parserOptions: {
    ecmaVersion: "latest",
  },
}
