{
  "log增强ㅤ🌕": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "log", // 片段缩写
    "body": [
      "console.log('⚡ $1 ==> ', $1)"
    ],
    "description": "log增强ㅤ🌕"
  },
  "vue3基础片段ㅤ🌕": {
    "scope": "vue",
    "prefix": "v3",
    "body": [
      "<template>",
      "  <div>",
      "    $1",
      "  </div>",
      "</template>",
      "",
      "<script setup lang=\"ts\"> ",
      "",
      "</script>",
      "",
      "<style lang=\"scss\" scoped>",
      "",
      "</style>"
    ],
    "description": "vue3基础片段ㅤ🌕"
  },
  "g-chart模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-chart",
    "body": [
      "  <g-chart class=\"w-350px h-250px\" :option=\"chartOption\" />"
    ],
    "description": "g-chart模板ㅤ🌕"
  },
  "g-chart数据ㅤ🌕": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "g-chart",
    "body": [
      "const chartOption = reactive({",
      "  xAxis: {",
      "    type: 'category',",
      "    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],",
      "  },",
      "  yAxis: {",
      "    type: 'value',",
      "  },",
      "  series: [",
      "    {",
      "      data: [120, 200, 150, 80, 70, 110, 130],",
      "      type: 'bar',",
      "      showBackground: true,",
      "      backgroundStyle: {",
      "        color: 'rgba(180, 180, 180, 0.2)',",
      "      },",
      "    },",
      "  ],",
      "})"
    ],
    "description": "g-chart数据ㅤ🌕"
  },
  "g-icon模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-icon",
    "body": [
      "<g-icon name=\"icon-sousuo\" size=\"\" color=''/>"
    ],
    "description": "g-icon模板ㅤ🌕"
  },
  "g-table模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-table",
    "body": [
      "<g-table :tableOptions=\"tableOptions\" @changePage=\"getCourseListApi\">",
      "    <!-- <template #name=\"{ row, index }\"> </template> -->",
      "  </g-table>"
    ],
    "description": "g-table模板ㅤ🌕"
  },
  "g-table数据ㅤ🌕": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "g-table",
    "body": [
      "const tableOptions = reactive({",
      "  ref: null as any,",
      "  key: '',",
      "  loading: true,",
      "  pageOptions: {",
      "    page: 1,",
      "    page_size: 10,",
      "    total: 20,",
      "  },",
      "  column: [",
      "    {",
      "      type: \"selection\",",
      "    },",
      "    {",
      "      type: \"index\",",
      "      label: \"序号\",",
      "    },",
      "    { prop: 'a', label: '表头a'},",
      "    { prop: 'b', label: '表头b' }, ",
      "    { prop: 'c', label: '表头c' },",
      "  ],",
      "  data: [{ a: 1, b: 2, c: 3 }],})"
    ],
    "description": "g-table数据ㅤ🌕"
  },
  "$refㅤ🌕": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "ref",
    "body": [
      "let $1= \\$ref($2)"
    ],
    "description": "$refㅤ🌕"
  },
  "$computedㅤ🌕": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "computed",
    "body": [
      "const $1 = \\$computed(() => {",
      "  return $2",
      "})"
    ],
    "description": "$computedㅤ🌕"
  },
  "g-form-filter模板 ㅤ🌕": {
    "scope": "html",
    "prefix": "g-form-filter",
    "body": [
      "<g-form",
      "      @search=\"search\"",
      "      @reset=\"reset\"",
      "      :formOptions=\"filterFormOptions\"",
      "      :tableOptions=\"tableOptions\"",
      "    >",
      "    </g-form>"
    ],
    "description": "g-form 表格筛选类型的玩法ㅤ🌕"
  },
  "g-form-filter数据ㅤ🌕": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "g-form-filter",
    "body": [
      "const filterFormOptions = reactive({",
      "  ref: null as any,",
      "  filter: true,",
      "  labelWidth: \"84px\",",
      "  items: {",
      "    直播时间: {",
      "      type: \"daterange\",",
      "      label: \"直播时间\",",
      "      showLabel: true,",
      "      width: \"300px\",",
      "    },",
      "    直播状态: {",
      "      type: \"select\",",
      "      label: \"直播状态\",",
      "      // showLabel: false,",
      "      options: [",
      "        { label: \"直播中\", value: 1 },",
      "        { label: \"未开播\", value: 2 },",
      "        { label: \"已结束\", value: 3 },",
      "      ],",
      "      width: \"150px\",",
      "    },",
      "    描述: {",
      "      type: \"text\",",
      "      label: \"描述\",",
      "      // showLabel: false,",
      "      width: \"200px\",",
      "    },",
      "  },",
      "  // 列表接口使用到的筛选值,及时搜索时使用",
      "  data: {",
      "    直播时间: null,",
      "    直播状态: null,",
      "    描述: null,",
      "  },",
      "  // 列表接口使用到的筛选值,非及时搜索时使用",
      "  filterData: {},",
      "})",
      "function search(form) {}",
      "function reset(form) {}"
    ],
    "description": "g-form-filter数据ㅤ🌕"
  },
  "g-form模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-form",
    "body": [
      "<g-form :formOptions=\"formOptions\">",
      "      <!-- 上传,upload-key需要正确设置 -->",
      "      <template #b>",
      "        <g-upload",
      "          upload-key=\"haed\"",
      "          v-model:fileList=\"formOptions.data.haed\"",
      "          type=\"drag\"",
      "          :max=\"1\"",
      "          accept=\".doc,.docx\"",
      "        ></g-upload>",
      "      </template>",
      "    </g-form>"
    ],
    "description": "g-form模板ㅤ🌕"
  },
  "g-form数据ㅤ🌕": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "g-form",
    "body": [
      "const formOptions = reactive({",
      "  ref: null as any,",
      "  loading: false,",
      "  items: {",
      "    a: {",
      "      type: \"text\",",
      "      label: \"讲师姓名\",",
      "      placeholder: \"请输入讲师姓名\",",
      "      width: \"200px\",",
      "      rule: true,",
      "      span: 12,",
      "    },",
      "    b: {",
      "      type: \"upload\",",
      "      label: \"讲师照片\",",
      "      width: \"220px\",",
      "      slot: true,",
      "      rule: true,",
      "      span: 12,",
      "    },",
      "    c: {",
      "      type: \"textarea\",",
      "      label: \"讲师介绍\",",
      "      maxlength: 300,",
      "      width: \"400px\",",
      "      rule: true,",
      "    },",
      "    d: {",
      "      type: \"text\",",
      "      label: \"年龄\",",
      "      width: \"150px\",",
      "      rule: {",
      "        required: true,",
      "        validator(rule, value) {",
      "          if (!value) {",
      "            return new Error(\"需要年龄\");",
      "          } else if (value < 10){ ",
      "            return new Error(\"年龄应该大于10\");",
      "          }",
      "          return true;",
      "        },",
      "      },",
      "    },",
      "  },",
      "  data: {",
      "    a: null,",
      "    b: [],",
      "    c: null,",
      "    d: null,",
      "  },",
      "});"
    ],
    "description": "g-form数据ㅤ🌕"
  },
  "g-form方法ㅤ🌕": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "g-form",
    "body": [
      "function submitForm() {",
      "  formOptions.ref.validate((errors) => {",
      "    if (errors) return false;",
      "    const form = \\$g._.cloneDeep(formOptions.data);",
      "    // 提交表单",
      "  });",
      "}",
      "",
      "function resetFormData() {",
      "  formOptions.ref.resetFormData();",
      "}"
    ],
    "description": "g-form方法ㅤ🌕"
  },
  "g-upload模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-upload",
    "body": [
      "<g-upload",
      "        v-model:fileList=\"list\"",
      "        type=\"image-card\"",
      "        :max=\"2\"",
      "        accept=\".png\"",
      "      ></g-upload>"
    ],
    "description": "g-upload模板ㅤ🌕"
  },
  "g-confirm确认提示框ㅤ🌕": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "g-confirm",
    "body": [
      "  \\$g.confirm({",
      "    title: \"温馨提示\",",
      "    content: \"是否确认删除？\",",
      "    positiveText: \"确定\",",
      "    negativeText: \"取消\",",
      "  }).then(() => {",
      "    deleteBanner({ banner_id: item.banner_id }).then((res) => {",
      "      \\$g.msg(\"删除成功\");",
      "    });",
      "  });"
    ],
    "description": "g-confirm确认提示框ㅤ🌕"
  },
  "g-dialogㅤ🌕": {
    "prefix": "g-dialog",
    "body": [
      "<g-dialog",
      "      title=\"标题\"",
      "      :formOptions=\"formOptions\"",
      "      v-model:show=\"showDialog\"",
      "      @confirm=\"confirm\"",
      "    >",
      "      <g-form :formOptions=\"formOptions\">",
      "        <!-- 讲师照片 -->",
      "        <template #head>",
      "          <g-upload",
      "            v-model:fileList=\"formOptions.data.head\"",
      "            type=\"image-card\"",
      "            :max=\"1\"",
      "          ></g-upload>",
      "        </template>",
      "      </g-form>",
      "    </g-dialog>"
    ],
    "description": "g-dialogㅤ🌕"
  },
  "g-img模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-img",
    "body": [
      " <g-img",
      "    width=\"100\"",
      "    src=\"\"",
      "  />"
    ],
    "description": "g-img模板ㅤ🌕"
  },
  "g-empty模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-empty",
    "body": [
      "<g-empty v-if=\"!liveList.length\"></g-empty>"
    ],
    "description": "g-empty模板ㅤ🌕"
  },
  "g-page模板ㅤ🌕": {
    "scope": "html",
    "prefix": "g-page",
    "body": [
      "<g-page :pageOptions=\"pageOptions\" @change=\"\"></g-page>"
    ],
    "description": "g-page模板ㅤ🌕"
  },
  "g-page数据ㅤ🌕": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "g-page",
    "body": [
      "const pageOptions = reactive({",
      "  page: 1,",
      "  page_size: 10,",
      "  total: 0,",
      "});"
    ],
    "description": "g-page数据ㅤ🌕"
  },
  "g-loading模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-loading",
    "body": [
      "<g-loading class=\"h-200px\" v-show=\"showLoading\"></g-loading>",
      ""
    ],
    "description": "g-loading模板ㅤ🌟🌈⚡"
  },
  "g-video模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-video",
    "body": [
      "<g-video :url=\"url\" class=\"w-full h-full\"/>"
    ],
    "description": "g-video模板ㅤ🌟🌈⚡"
  },
  "g-lottie模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-lottie ",
    "body": [
      "<g-lottie :options=\"lottieOptions\" @animCreated=\"animCreated\"> </g-lottie>"
    ],
    "description": "g-lottie模板ㅤ🌟🌈⚡"
  },
  "g-lottie数据ㅤ🌟🌈⚡": {
    "scope": "javascript,typescript",
    "prefix": "g-lottie ",
    "body": [
      "const lottieOptions = {",
      "  path: 'https://frontend-cdn.qimingdaren.com/cloud-school/exam/feiji.json',",
      "  loop: true,",
      "  renderer: 'svg',",
      "  autoplay: true,",
      "}",
      "",
      "function animCreated(anim) {",
      "  anim.setSpeed(1.4)",
      "}"
    ],
    "description": "g-lottie数据ㅤ🌟🌈⚡"
  },
  "g-mathjax模板ㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-mathjax ",
    "body": [
      " <g-mathjax :text=\"title\" />"
    ],
    "description": "g-mathjax模板ㅤ🌟🌈⚡"
  },
  "g-treeㅤ🌟🌈⚡": {
    "prefix": "g-tree",
    "body": [
      "  <g-tree",
      "        ref=\"Tree2Ref\"",
      "        treeName=\"RightTree\"",
      "        class=\"p-10px\"",
      "        :treeData=\"treeData2\"",
      "        check-strictly",
      "        style=\"height: calc(100vh - 350px); overflow: auto\"",
      "        :highlight-check=\"false\"",
      "        @node-click=\"nodeClick\"",
      "        render-after-expand",
      "      >",
      "        <template #body=\"{ data }\">",
      "          <div>",
      "            <span>{{ data.name }} </span>",
      "          </div>",
      "        </template>",
      "      </g-tree>"
    ],
    "description": "g-treeㅤ🌟🌈⚡"
  },
  "g-editorㅤ🌟🌈⚡": {
    "scope": "html",
    "prefix": "g-editor",
    "body": [
      "<g-editor",
      "  v-model=\"description\"",
      "  :oss=\"{",
      "   application: 'image'",
      "  }\"",
      "  :initProps=\"{",
      "    menubar: false,",
      "    statusbar: false,",
      "   }\"",
      ">",
      "</g-editor>"
    ],
    "description": "富文本编辑器ㅤ🌟🌈⚡"
  }
}