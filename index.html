<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content="2c5ade56282fc2e55fb219ab11b369f9" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- iconfont前端共用资源地址 -->
    <script
      src="//at.alicdn.com/t/c/font_3818377_t53ywbsetxn.js"
      async
    ></script>
    <!-- iconfont项目资源地址 -->
    <script src="//at.alicdn.com/t/c/font_3763809_enmom8wlfk.js" async></script>
    <!-- oss云点播 -->
    <script
      src="https://frontend-cdn.qimingdaren.com/cdn/aliyun-upload-sdk-1.5.4/lib/aliyun-oss-sdk-6.17.1.min.js"
      async
    ></script>
    <script
      src="https://frontend-cdn.qimingdaren.com/cdn/aliyun-upload-sdk-1.5.4/aliyun-upload-sdk-1.5.4.min.js"
      async
    ></script>
    <script>
      window.MathJax = {
        tex: {
          inlineMath: [
            ["$", "$"],
            ["\\(", "\\)"],
          ], // ⾏内公式选择符
          displayMath: [
            ["$$", "$$"],
            ["\\[", "\\]"],
          ], // 段内公式选择符
          autoload: {
            upgreek: ["uppi", "Uppi"],
          },
          processEscapes: true,
        },
        options: {
          enableMenu: false,
          ignoreHtmlClass: "tex2jax_ignore",
          processHtmlClass: "tex2jax_process",
        },
      }
    </script>
    <script src="https://frontend-cdn.qimingdaren.com/yunying/js/hls.js"></script>
    <script
      src="https://frontend-store.oss-cn-chengdu.aliyuncs.com/cdn/mathjax/tex-chtml.js"
      id="Ma thJax-script"
      async
    ></script>
    <link href="/static/css/loading.css" rel="stylesheet" />
    <link
      href="https://frontend-cdn.qimingdaren.com/ct/remixicon/remixicon.css"
      rel="stylesheet"
    />
    <!-- vMdEditor所需资源start -->
    <link
      href="//frontend-cdn.qimingdaren.com/cdn/jquery/katex-v3/katex.min.css"
      rel="stylesheet"
    />
    <!-- vMdEditor所需资源end -->
    <script
      src="https://frontend-cdn.qimingdaren.com/zxs/tinymce/tinymce.min.js"
      referrerpolicy="origin"
      async
    ></script>
    <title>金字塔后台管理</title>
  </head>

  <body>
    <div id="app">
      <div class="app-loading" id="appLoading">
        <div class="app-loading-wrap">
          <img src="/static/img/logo.png" class="app-loading-logo" alt="Logo" />
          <div class="app-loading-dots">
            <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
          </div>
          <div class="app-loading-title">金字塔后台管理</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
    <!-- ueditor -->
    <script src="https://frontend-store.oss-cn-chengdu.aliyuncs.com/cdn/jquery/jquery.min.js"></script>
    <script
      type="text/javascript"
      charset="utf-8"
      src="./ueditor/ueditor.config.js"
    ></script>
    <script
      type="text/javascript"
      charset="utf-8"
      src="./ueditor/ueditor.all.js"
    ></script>
    <script
      type="text/javascript"
      charset="utf-8"
      src="./ueditor/lang/zh-cn/zh-cn.js"
    ></script>
    <script src="./ueditor/kityformula-plugin/addKityFormulaDialog.js"></script>
    <script src="./ueditor/kityformula-plugin/getKfContent.js"></script>

    <!-- vMdEditor所需资源start -->
    <script src="//frontend-cdn.qimingdaren.com/cdn/jquery/katex-v3/katex.min.js"></script>
    <script src="//frontend-cdn.qimingdaren.com/cdn/jquery/mermaid.min-v2.js"></script>
    <!-- vMdEditor所需资源end -->
    <!-- 解析apk包信息 -->
    <script src="https://qm-cloud.oss-cn-chengdu.aliyuncs.com/public/js/app-info-parser.min.js"></script>
  </body>
</html>
