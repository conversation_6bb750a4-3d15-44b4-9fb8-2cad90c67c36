{"name": "vite-admin-template", "version": "0.0.0", "private": true, "scripts": {"dev": "env-cmd -e development vite", "build:test": "run-p  build-only:t", "build": "run-p type-check build-only:p", "preview": "vite preview", "build-only:p": "env-cmd -e production node --max-old-space-size=4096 node_modules/vite/bin/vite.js build", "build-only:t": "env-cmd -e test node --max-old-space-size=4096 node_modules/vite/bin/vite.js build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "git:push": "git add -A && git-pro commit && git pull && git push", "git:push2": "git add -A && git cz && git pull && git push", "git:tag": "run-p type-check && git-pro tag", "git:update-branch": "git remote update origin --prune && git checkout master && git branch | grep -v 'master' | xargs git branch -D", "cle": "rm -rf node_modules", "up": "taze major -I", "postinstall": "patch-package", "git:merge-test": "git-pro merge-test", "predev": "yarn"}, "dependencies": {"@ballcat/vue-cropper": "^1.0.5", "@cjh0/git-pro": "^7.0.0", "@l9m/v-md-editor": "^3.2.12", "@microsoft/fetch-event-source": "^2.0.1", "@sentry/vite-plugin": "^2.20.1", "@sentry/vue": "^8.14.0", "@tinymce/tinymce-vue": "^5.0.0", "@vueuse/core": "^9.6.0", "axios": "^1.3.4", "bignumber.js": "^9.1.0", "chroma-js": "^2.4.2", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "echarts": "^5.4.3", "element-plus": "^2.6.3", "element-tree-line": "^0.2.1", "eventemitter3": "^5.0.0", "file-saver": "^2.0.5", "js-cookie": "^3.0.1", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "lottie-web": "^5.12.2", "namedavatar": "^1.2.0", "nprogress": "^0.2.0", "pinia": "^2.0.33", "pinia-plugin-persistedstate": "^3.0.1", "plotly.js": "^2.25.0", "simple-mind-map": "^0.11.1", "timeago.js": "^4.0.2", "typed.js": "^2.1.0", "vue": "3.3.4", "vue-router": "^4.1.6", "vue-ueditor-wrap": "3.x", "vue3-json-editor": "^1.1.5", "vuedraggable": "^4.1.0", "xgplayer": "^2.32.2", "xgplayer-flv.js": "2.3.0", "xgplayer-hls": "2.5.2", "xgplayer-hls.js": "2.3.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@rollup/plugin-inject": "^5.0.3", "@rushstack/eslint-patch": "^1.1.4", "@types/node": "16.11.56", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.13", "cc-vite-progress": "1.1.3", "code-inspector-plugin": "^0.9.2", "commitlint-config-gitmoji": "^2.2.6", "env-cmd": "^10.1.0", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "naive-ui": "2.34.4", "npm-run-all": "^4.1.5", "patch-package": "^8.0.0", "postcss": "^8.4.19", "postinstall-postinstall": "^2.1.0", "prettier": "^2.7.1", "rollup-plugin-external-globals": "^0.7.1", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.56.1", "tailwindcss": "^3.2.7", "taze": "^0.9.1", "typescript": "~4.7.4", "unplugin-auto-import": "^0.12.1", "unplugin-vue-components": "^0.22.12", "vite": "^4.4.8", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-tsc": "1.6.4"}, "volta": {"node": "18.20.1", "yarn": "1.22.22"}}