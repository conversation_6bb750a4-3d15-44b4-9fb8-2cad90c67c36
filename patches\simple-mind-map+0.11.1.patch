diff --git a/node_modules/simple-mind-map/src/plugins/RichText.js b/node_modules/simple-mind-map/src/plugins/RichText.js
index e32c96c..c334dd5 100644
--- a/node_modules/simple-mind-map/src/plugins/RichText.js
+++ b/node_modules/simple-mind-map/src/plugins/RichText.js
@@ -838,9 +838,38 @@ class RichText {
       }
     }
     walk(data)
+      // 调用回调以处理公式渲染
+      this.processFormulas(data)
     return data
   }
 
+    // 添加一个新方法来处理公式
+    processFormulas(data) {
+      if (
+        this.mindMap.opt.beforeHideRichTextEdit &&
+        typeof this.mindMap.opt.beforeHideRichTextEdit === 'function'
+      ) {
+        const traverse = node => {
+          if (node.data && node.data.text) {
+            const mockRichText = {
+              quill: new Quill(document.createElement('div'))
+            }
+  
+            mockRichText.quill.clipboard.dangerouslyPasteHTML(node.data.text)
+  
+            this.mindMap.opt.beforeHideRichTextEdit(mockRichText)
+  
+            // // 更新节点的文本内容
+            node.data.text = mockRichText.quill.root.innerHTML
+          }
+          if (node.children && node.children.length > 0) {
+            node.children.forEach(child => traverse(child))
+          }
+        }
+        traverse(data)
+      }
+    }
+
   // 插件被移除前做的事情
   beforePluginRemove() {
     this.transformAllNodesToNormalNode()
