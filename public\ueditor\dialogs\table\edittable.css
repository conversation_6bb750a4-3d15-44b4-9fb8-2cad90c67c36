body{
    width: 540px;
    overflow: hidden;
}
.wrapper {
    width: 520px;
    height: 315px;
    margin: 10px auto 0;
    overflow: hidden;
    font-size: 12px;
}

.clear {
    clear: both;
}

.wrapper .left {
    float: left;
    margin-left: 10px;;
}

.wrapper .right {
    float: right;
    padding-left: 15px;
    border-left: 2px dotted #EDEDED;
}

.section {
    width: 240px;
    margin-bottom: 15px;
    overflow: hidden;
}

.section h3 {
    padding: 5px 0;
    margin-bottom: 10px;
    font-size: 12px;
    font-weight: bold;
    border-bottom: 1px solid #EDEDED;
}

.section ul {
    overflow: hidden;
    clear: both;
    list-style: none;

}

.section li {
    float: left;
    width: 120px;;
}

.section .tone {
    width: 80px;;
}

.section .preview {
    width: 220px;
}

.section .preview table {
    color: #666;
    text-align: center;
    vertical-align: middle;
}

.section .preview caption {
    font-weight: bold;
}

.section .preview td {
    height: 22px;
    border-style: solid;
    border-width: 1px;
}

.section .preview th {
    height: 22px;
    background-color: #F7F7F7;
    border-color: #DDD;
    border-style: solid;
    border-width: 2px 1px 1px 1px;
}

/*reset
*/
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, font, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td {
    padding: 0;
    margin: 0;
    font-size: 100%;
    outline: 0;
}

body {
    line-height: 1;
}

ol, ul {
    list-style: none;
}

blockquote, q {
    quotes: none;
}

ins {
    text-decoration: none;
}

del {
    text-decoration: line-through;
}

table {
    border-spacing: 0;
    border-collapse: collapse;
}

/*module
*/
body {
    font: 12px/1.5 sans-serif, "宋体", "Arial Narrow", HELVETICA;
    color: #646464;
    background-color: #fff;
}

/*tab*/
.tabhead {
    position: relative;
    z-index: 10;
}

.tabhead span {
    display: inline-block;
    height: 30px;
    padding: 0 5px;
    *margin-right: 5px;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
    background: url("images/dialog-title-bg.png") repeat-x;
    border: 1px solid #ccc;
}

.tabhead span.focus {
    height: 31px;
    background: #fff;
    border-bottom: none;
}

.tabbody {
    position: relative;
    top: -1px;
    margin: 0 auto;
    border: 1px solid #ccc;
}


a.button:hover {
    background-position: 0 -30px;
}