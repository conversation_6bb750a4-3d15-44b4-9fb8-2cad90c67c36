<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="renderer" content="webkit" />
    <link rel="stylesheet" href="kityformula/assets/styles/base.css" />
    <link rel="stylesheet" href="kityformula/assets/styles/ui.css" />
    <link rel="stylesheet" href="kityformula/assets/styles/scrollbar.css" />
    <style>
      html,
      body {
        padding: 0;
        margin: 0;
      }
      .kf-editor {
        width: 780px;
        height: 320px;
      }
      #loading {
        position: absolute;
        top: 42%;
        left: 50%;
        width: 340px;
        height: 32px;
        margin-left: -170px;
        font-family: arial, 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
        line-height: 32px;
      }
      #loading img {
        position: absolute;
      }
      #loading p {
        position: absolute;
        top: 0px;
        left: 40px;
        display: block;
        margin: 0;
      }
      .edui-dialog-body {
        width: 1200px;
      }
      .nz-btn {
        width: 120px;
        margin-right: 40px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
        border: 1px solid #00ce9b;
        border-radius: 4px;
      }
      .wz-btn {
        width: 120px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
        border: 1px solid #4cacfc;
        border-radius: 4px;
      }
      .fuzha-box {
        padding: 10px;
        padding-top: 0;
      }
      .fuzha-box .g-mathjax {
        min-height: 112px;
        overflow: auto;
      }
      .info {
        padding: 6px;
        margin-bottom: 10px;
        background: #d9edf7;
        border-radius: 4px;
      }
      .textarea:focus {
        border: 1px solid #4cacfc;
      }
    </style>
    <title></title>
  </head>
  <body>
    <div id="vue-mathjax">
      <div style="display: flex; justify-content: center; margin-top: 10px; margin-bottom: 10px">
        <div
          @click="status=1"
          class="nz-btn"
          :style="status==1 ? 'background:#00ce9b;color:#fff' : ''"
        >
          简单公式
        </div>
        <div
          @click="status=2"
          class="wz-btn"
          :style="status==2 ? 'background:#4cacfc;color:#fff' : ''"
        >
          复杂公式
        </div>
      </div>

      <div v-show="status==1" id="kfEditorContainer" class="kf-editor">
        <div id="tips" class="tips">
          sorry! Beta版本仅支持IE9及以上版本的浏览器，正式版本将会支持低版本浏览器，谢谢您的关注！
        </div>
      </div>
      <div class="fuzha-box" v-show="status==2">
        <div class="info">
          <div>
            步骤：1、在公式编辑器网站中编辑自己想要的公式；
            <a href="https://www.latexlive.com/##" target="_blank" rel="noopener noreferrer">
              点击前往
            </a>
          </div>
          　　　2、公式编辑完成后“复制公式（代码）”将公式复制到输入框；（复制后会自动给公式前后分别加\\(和\\))
          <br />
          　　　3、点击确定按钮，将复制的公式粘贴到试题编辑器。（查看效果请点击富文本预览）
        </div>
        <textarea
          v-model="value"
          @input="textFormatting"
          class="textarea"
          style="
            width: 744px;
            min-height: 100px;
            padding: 10px;
            resize: none;
            background-color: rgba(241, 241, 241, 0.98);
            border: 0;
            border-radius: 5px;
          "
          placeholder="请粘贴公式代码"
        ></textarea>
        <div style="width: 744px; min-height: 42px; overflow: auto">
          <mathjax
            :text="processingData"
            style="padding-top: 10px; font-size: 18px; text-align: center"
          ></mathjax>
        </div>
      </div>
    </div>

    <!--页面中一定要引入internal.js为了能直接使用当前打开dialog的实例变量-->
    <!--internal.js默认是放到dialogs目录下的-->
    <script type="text/javascript" src="../dialogs/internal.js"></script>
    <script src="https://frontend-cdn.qimingdaren.com/three/js/vue.min.js"></script>
    <script src="https://frontend-store.oss-cn-chengdu.aliyuncs.com/cdn/lodash.min.js"></script>
    <script src="kityformula/js/jquery-1.11.0.min.js"></script>
    <script src="kityformula/js/kitygraph.all.js"></script>
    <script src="kityformula/js/kity-formula-render.all.js"></script>
    <script src="kityformula/js/kity-formula-parser.all.min.js"></script>
    <script src="kityformula/js/kityformula-editor.all.min.js"></script>
    <script>
      var status = 1 // 1-普通公式 2-复杂公式
      var myLatex = ''
      Vue.component('mathjax', {
        template: '<div :ref="id" class="g-mathjax" all:upset v-html="text"></div>',
        props: {
          text: {
            type: String,
          },
        },
        data() {
          return {
            id: '',
          }
        },
        mounted() {
          this.id = this.randomStr(4)
          this.$nextTick(() => {
            if (!window.isMathjaxConfig) this.initMathJax()
          })
        },
        updated() {
          
          window.MathJax && _.get( window.MathJax,'startup.defaultReady') && window.MathJax.startup.defaultReady()
        },
        methods: {
          injectMathJax() {
            if (!window.MathJax) {
              const script = document.createElement('script')
              script.src =
                'https://frontend-store.oss-cn-chengdu.aliyuncs.com/cdn/mathjax/tex-chtml.js'
              script.async = true
              document.head.appendChild(script)
            }
          },
          initMathJax() {
            this.injectMathJax()
            window.MathJax = {
              tex: {
                inlineMath: [
                  ['$', '$'],
                  ['\\(', '\\)'],
                ],
                displayMath: [
                  ['$$', '$$'],
                  ['\\[', '\\]'],
                ],
              },
              options: {
                skipHtmlTags: ['noscript', 'style', 'textarea', 'pre', 'code'],
                ignoreHtmlClass: 'tex2jax_ignore',
              },
              svg: {
                fontCache: 'global',
              },
            }
           _.get(window.MathJax,'typesetPromise') && window.MathJax.typesetPromise(this.$refs[this.id])
            window.isMathjaxConfig = true
          },
          randomStr(num) {
            const str = 'aporhjbmvncjrovmbxvzzoeclolmqlpvsdffgfgf'
            let data = 'katex-'
            for (let i = 0; i < num; i++) {
              const aa = Math.floor(Math.random() * str.length)
              data += str.substring(aa, aa + 1)
            }
            return data
          },
        },
      })
      var vm = new Vue({
        el: '#vue-mathjax',
        data() {
          return {
            status: 1,
            value: '',
          }
        },
        computed: {
          processingData() {
            return `\\(${this.value}\\)`
          },
        },
        watch: {
          status(newValue, oldValue) {
            status = newValue
          },
          processingData(n, o) {
            myLatex = n
          },
        },
        created() {},
        methods: {
          textFormatting(e) {
            let val = e.target.value
            val = val.replace(/[\r\n]/g, '')
            // val = val.replace(/\s/g, '')
            this.value = val
          },
        },
      })

      jQuery(function ($) {
        if (document.body.addEventListener) {
          $('#tips').html(
            '<div id="loading"><img src="kityformula/loading.gif" alt="loading" /><p>正在加载，请耐心等待...</p></div>'
          )

          var factory = kf.EditorFactory.create($('#kfEditorContainer')[0], {
            render: {
              fontsize: 26,
            },
            resource: {
              path: './kityformula/resource/',
            },
          })

          factory.ready(function (KFEditor) {
            $('#tips').remove()

            // this指向KFEditor
            var rng = editor.selection.getRange(),
              img = rng.getClosedNode(),
              imgLatex = img && $(img).attr('data-latex')

            this.execCommand('render', imgLatex || '\\placeholder')
            this.execCommand('focus')

            window.kfe = this
          })

          dialog.onok = function () {
            kfe.execCommand('get.image.data', function (data) {
              if (status == 1) {
                var latex = kfe.execCommand('get.source')
                editor.execCommand('inserthtml', `\\(${latex}\\)`)
              } else {
                editor.execCommand('inserthtml', myLatex)
              }
              // editor.execCommand('inserthtml', '<img class="kfformula" src="'+ data.img +'" data-latex="' + latex + '" />');
              //  editor.execCommand('inserthtml', '<img class="formula" style="vertical-align: middle;"  src="https://latex.codecogs.com/gif.latex?' +
              //     latex +
              //     '" data-latex="' +
              //     latex +
              //     '" />');
              dialog.close()
            })

            return false
          }
        } else {
          $('#tips').css('color', 'black')
          $('#tips').css('padding', '10px')
        }
      })
    </script>
  </body>
</html>
