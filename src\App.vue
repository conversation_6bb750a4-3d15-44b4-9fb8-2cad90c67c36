<template>
  <n-config-provider
    abstract
    :locale="getNaiveLocale"
    :date-locale="getNaiveDateLocale"
    :theme-overrides="getNaiveThemeOverrides"
    inline-theme-disabled
  >
    <n-loading-bar-provider>
      <n-dialog-provider>
        <n-message-provider placement="top" :max="3">
          <n-notification-provider>
            <router-view />
          </n-notification-provider>
        </n-message-provider>
      </n-dialog-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { useNaive } from "@/hooks/useNaive"
const { getNaiveLocale, getNaiveDateLocale, getNaiveThemeOverrides } =
  useNaive()
</script>
