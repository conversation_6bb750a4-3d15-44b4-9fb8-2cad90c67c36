import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config

//获取活动列表
export function getActivityList(data) {
  return request.get(baseURL + "/tutoring/admin/activity/list", data)
}

//学段select
export function getStageSelect(data?) {
  return request.get(baseURL + "/tutoring/common/stages", data)
}

//年级select
export function getGradeSelect(data) {
  return request.get(baseURL + "/tutoring/common/grades", data)
}

//学科select
export function getSubjectSelect(data) {
  return request.get(baseURL + "/tutoring/common/courses", data)
}

//新增活动
export function addActivity(data) {
  return request.post(baseURL + "/tutoring/admin/activity/create", data)
}

//启用禁用
export function updateActivityStatus(data) {
  return request.put(baseURL + "/tutoring/admin/activity/toggle", data)
}

//活动科目列表
export function getActivitySubjectList(data) {
  return request.get(baseURL + "/tutoring/admin/activity/themeSubject", data)
}

//板块列表
export function getBlockList(data) {
  return request.get(baseURL + "/tutoring/admin/activity/themeList", data)
}

//新增板块
export function addBlock(data) {
  return request.post(baseURL + "/tutoring/admin/activity/insertTheme", data)
}

//编辑板块
export function editBlock(data) {
  return request.put(baseURL + "/tutoring/admin/activity/updateTheme", data)
}

//删除板块
export function deleteBlock(data) {
  return request.delete(baseURL + "/tutoring/admin/activity/deleteTheme", data)
}

//活动详情
export function getActivityDetail(data) {
  return request.get(baseURL + "/tutoring/admin/activity/detail", data)
}
/* 板块简介 */
export function getActivityDescribe(data) {
  return request.get(baseURL + "/tutoring/admin/activity/detailTheme", data)
}
/* 编辑板块简介 */
export function editActivityDescribe(data) {
  return request.put(baseURL + "/tutoring/admin/activity/updateTheme", data)
}
/* 模块列表 */
export function getActivityModuleList(data) {
  return request.get(
    baseURL + "/tutoring/admin/activity/theme/module/list",
    data,
  )
}
/* 书籍章节树 */
export function getBookChapterTree(data) {
  return request.get(
    baseURL + "/tutoring/admin/activity/theme/module/bookResourceTree",
    data,
  )
}
/* 新增模块 */
export function addActivityModule(data) {
  return request.post(
    baseURL + "/tutoring/admin/activity/theme/module/add",
    data,
  )
}
/* 删除模块 */
export function deleteActivityModule(data) {
  return request.delete(
    baseURL + "/tutoring/admin/activity/theme/module/delete",
    data,
  )
}
/* 模块详情 */
export function getActivityModuleDetail(data) {
  return request.get(
    baseURL + "/tutoring/admin/activity/theme/module/detail",
    data,
  )
}
/* 编辑模块 */
export function editActivityModule(data) {
  return request.put(
    baseURL + "/tutoring/admin/activity/theme/module/edit",
    data,
  )
}
/* 活动资源更新 */
export function updateActivityResource(data) {
  return request.post(baseURL + "/tutoring/admin/activity/updateResource", data)
}
/* 活动科目编辑介绍 */
export function editActivitySubjectDescribe(data) {
  return request.put(
    baseURL + "/tutoring/admin/activity/subject/introduce",
    data,
  )
}
/* 获取地区列表 */
export function getAreaApi() {
  return request.get(baseURL + "/tutoring/common/area")
}
/* 更新地区 */
export function editAreaApi(data) {
  return request.put(baseURL + "/tutoring/admin/activity/update", data)
}

/* 学期列表 */
export function getSemesterList(data?) {
  return request.get(baseURL + "/tutoring/admin/activity/termList", data)
}
