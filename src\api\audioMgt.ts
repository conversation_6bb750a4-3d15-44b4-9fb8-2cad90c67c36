import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config
/* 分页列表搜索 */
export function getAudioList(data) {
  return request.get(baseURL + "/tutoring/admin/audio/list", data)
}
/* 查看详情 */
export function getAudioDetail(data) {
  return request.get(baseURL + "/tutoring/admin/audio/details", data)
}
/* 一键清除缓存 */
export function clearCacheAll(data) {
  return request.put(baseURL + "/tutoring/admin/audio/batchRemoveCache", data)
}
/* 清除缓存 */
export function clearCacheOne(data) {
  return request.put(baseURL + "/tutoring/admin/audio/removeCache", data)
}
/* 题目音频流 */
export function getAudioStream(data) {
  return request.post(
    baseURL + "/tutoring/admin/audio/question/tts/stream",
    data,
  )
}
/* 测试音频流 */
export function getTestAudioStream(data) {
  return request.post(baseURL + "/tutoring/admin/audio/tts/test", data)
}
/* 获取当前基础URL */
export function getBaseUrl(data?) {
  return request.get(baseURL + "/tutoring/admin/audio/tts/baseUrl", data)
}
/* 修改tts当前基础URL */
export function updateBaseUrl(data) {
  return request.put(baseURL + "/tutoring/admin/audio/tts/baseUrl", data)
}
/* 测试基础URL */
export function testBaseUrl(data) {
  return request.put(baseURL + "/tutoring/admin/audio/tts/baseUrl/test", data)
}
