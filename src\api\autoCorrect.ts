import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config

/* 运行状态select */
export function getStatusSelect() {
  return request.get(baseURL + "/tutoring/admin/questionAutoCorrect/status")
}
/* 学校列表 */
export function getSchoolList(data?) {
  return request.get(baseURL + "/tutoring/common/school", data)
}
/* 用户意见select */
export function getUserOpinionSelect() {
  return request.get(baseURL + "/tutoring/admin/questionAutoCorrect/feedback")
}
/* 列表/搜索 */
export function getListApi(data) {
  return request.get(baseURL + "/tutoring/admin/questionAutoCorrect/page", data)
}
/* 查看详情 */
export function getDetailApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/questionAutoCorrect/details",
    data,
  )
}
/* 自动批改 测试 */
export function testApi(data) {
  return request.post(
    baseURL + "/tutoring/admin/questionAutoCorrect/test",
    data,
  )
}

/* 题目自动批改 */
export function autoCorrect(data) {
  return request.post(baseURL + "/tutoring/admin/questionAutoCorrect/do", data)
}

/* 题目自动批改信息 */
export function autoCorrectInfo(data) {
  return request.get(baseURL + "/tutoring/admin/questionAutoCorrect/info", data)
}
/* 获取当前基础URL */
export function getBaseUrl() {
  return request.get(baseURL + "/tutoring/admin/questionAutoCorrect/baseUrl")
}
/* 修改tts当前基础URL */
export function updateBaseUrl(data) {
  return request.put(
    baseURL + "/tutoring/admin/questionAutoCorrect/baseUrl",
    data,
  )
}
/* 测试基础URL */
export function testBaseUrl(data) {
  return request.put(
    baseURL + "/tutoring/admin/questionAutoCorrect/baseUrl/test",
    data,
  )
}
/* 查看详情 */
export function getCorrectDetailApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/questionAutoCorrect/details",
    data,
  )
}
/* 自动批改 测试 */
export function testCorrectApi(data) {
  return request.post(
    baseURL + "/tutoring/admin/questionAutoCorrect/test",
    data,
  )
}
