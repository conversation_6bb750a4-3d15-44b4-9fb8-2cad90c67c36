import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config

/* ------------------类型维护----------------------- */
/* 类型列表 */
export function getTypeList(data) {
  return request.get(baseURL + "/tutoring/admin/bookType/list", data)
}
/* 新增类型 */
export function addType(data) {
  return request.post(baseURL + "/tutoring/admin/bookType", data)
}
/* 更新类型 */
export function editType(data) {
  return request.put(baseURL + "/tutoring/admin/bookType", data)
}
/* ------------------书籍管理----------------------- */
/* 类型多级下拉框 */
export function getTypeSelect(data) {
  return request.get(
    baseURL + "/tutoring/admin/bookType/select/multiLevel",
    data,
  )
}
/* 状态 select */
export function getStatusSelect() {
  return request.get(baseURL + "/tutoring/admin/book/state")
}
/* 书籍列表 */
export function getListApi(data) {
  return request.get(baseURL + "/tutoring/admin/book/page", data)
}
/* 新增资源 */
export function addBookApi(data) {
  return request.post(baseURL + "/tutoring/admin/book", data)
}
/* 编辑资源 */
export function editBookApi(data) {
  return request.put(baseURL + "/tutoring/admin/book", data)
}
/* 删除书籍 */
export function deleteBookApi(data) {
  return request.delete(baseURL + "/tutoring/admin/book", data)
}
/* ------------------试卷编辑----------------------- */
/* 获取题目类型列表 */
export function getQuestionTypeList(data) {
  return request.get(baseURL + "/tutoring/common/question/type/list", data)
}
/* 获取题目类型列表Tree */
export function getQuestionTypeTree(data) {
  return request.get(baseURL + "/tutoring/common/question/type/tree", data)
}
/* 小题题型 */
export function getSubQuestionTypeList() {
  return request.get(baseURL + "/tutoring/common/subQuestionType")
}
/* 保存试题 */
export function saveQuestion(data) {
  return request.post(baseURL + "/tutoring/admin/question", data)
}
/* 试卷详情 */
export function getQuestionDetail(data) {
  return request.get(baseURL + "/tutoring/admin/question/detail", data)
}
/* 编辑试题 */
export function editQuestion(data) {
  return request.put(baseURL + "/tutoring/admin/question", data)
}

/* ------------------流程编辑器----------------------- */
/** 流程编辑器-生成流程节点ID */
export function genUniquenessId() {
  return request.get(baseURL + "/tutoring/admin/flow/id")
}

/** 流程编辑器-获取流程树 */
export function getProcessTree(data) {
  return request.get(baseURL + "/tutoring/admin/flow/tree", data)
}

/** 流程编辑器-保存流程树 */
export function saveProcessTree(data) {
  return request.post(baseURL + "/tutoring/admin/flow/save", data)
}
/* 保存试题描述  */
export function saveQuestionDesc(data) {
  return request.post(
    baseURL + "/tutoring/admin/question/questionDescribe",
    data,
  )
}
/* 试题描述-分页列表 */
export function getQuestionDescList(data) {
  return request.get(
    baseURL + "/tutoring/admin/question/questionDescribe/page",
    data,
  )
}
/* 试题描述-删除 */
export function deleteQuestionDesc(data) {
  return request.delete(
    baseURL + "/tutoring/admin/question/questionDescribe",
    data,
  )
}
/* 试题描述-更新 */
export function updateQuestionDesc(data) {
  return request.put(
    baseURL + "/tutoring/admin/question/questionDescribe",
    data,
  )
}
/* 试题描述-查看详情 */
export function getQuestionDescDetail(data) {
  return request.get(
    baseURL + "/tutoring/admin/question/questionDescribe/detail",
    data,
  )
}

/* ai解析步骤 */
export function getAiExplainContent(params) {
  return request.get(baseURL + "/tutoring/ai/aiExplain", params)
}

/* 保存ai解析 */
export function updateAiExplain(params) {
  return request.post(baseURL + "/tutoring/ai/aiExplain", params)
}

// fastGpt获取iframe配置
export function getIframeConfig() {
  return request.get(baseURL + "/tutoring/ai/iframe/config")
}

// 审核题目
export function auditQuestion(data) {
  return request.put(baseURL + "/tutoring/admin/question/audit", data)
}

/* ------------------文章----------------------- */
/* 文章列表 */
export function getArticleList(data) {
  return request.get(baseURL + "/tutoring/admin/article/page", data)
}
/* 新增文章 */
export function addArticle(data) {
  return request.post(baseURL + "/tutoring/admin/article", data)
}
/* 更新文章 */
export function editArticle(data) {
  return request.put(baseURL + "/tutoring/admin/article", data)
}

/* Markdown转思维导图 */
export function exchangeArticle(data) {
  return request.post(
    baseURL + "/tutoring/admin/book/markdownToMindArticle",
    data,
  )
}
/* 删除文章 */
export function deleteArticle(data) {
  return request.delete(baseURL + "/tutoring/admin/article", data)
}
/* 移动文章 */
export function moveArticle(data) {
  return request.put(baseURL + "/tutoring/admin/article/move", data)
}
/* 排序调整-查看 */
export function getArticleSort(data) {
  return request.get(baseURL + "/tutoring/admin/article/sort", data)
}
/* 排序调整-保存 */
export function saveArticleSort(data) {
  return request.post(baseURL + "/tutoring/admin/article/sort", data)
}
/* 统计书籍/目录试题、文章数量 */
export function getStatistics(data) {
  return request.get(baseURL + "/tutoring/admin/book/static", data)
}

/* 同步试卷 */
export function getSyncAdmin(data) {
  return request.post(baseURL + "/tutoring/admin/sync", data)
}

/** 导入试题时选择/取消选择 */
export function bindQuestion(data) {
  return request.post(baseURL + "/tutoring/admin/book/bind/question", data)
}

/** 查询试卷已导入的试题 */
export function getBindQuestions(data) {
  return request.get(baseURL + "/tutoring/admin/book/other/bind/question", data)
}

/** 导入文章时选择/取消选择 */
export function bindArticle(data) {
  return request.post(baseURL + "/tutoring/admin/article/bind/article", data)
}

/** 查询试卷已导入的文章 */
export function getBindArticles(data) {
  return request.get(
    baseURL + "/tutoring/admin/article/other/bind/article",
    data,
  )
}

/**导入视频时选择/取消选择 */
export function bindVideo(data) {
  return request.post(baseURL + "/tutoring/admin/book/bind/video", data)
}

/** 查询试卷已导入的视频 */
export function getBindVideos(data) {
  return request.get(baseURL + "/tutoring/admin/book/other/bind/video", data)
}

/** 查询资源id列表数据 */
export function getResourceIds(data) {
  return request.get(baseURL + "/tutoring/admin/book/resource/idList", data)
}

/** 书籍来源 */
export function getBookSource() {
  return request.get(baseURL + "/tutoring/common/source")
}
/* 学科素养 */
export function getSubjectLiteracyList(data) {
  return request.get(baseURL + "/tutoring/admin/sysCourseLiteracy/list", data)
}
/* 关键能力 */
export function getSubjectAbilityList(data) {
  return request.get(baseURL + "/tutoring/admin/sysCourseAbility/list", data)
}
/* 修改试题学科素养 */
export function updateQuestionSubjectLiteracy(data) {
  return request.post(
    baseURL + "/tutoring/admin/question/saveQuestionCourseLiteracy",
    data,
  )
}
/* 修改试题关键能力 */
export function updateQuestionSubjectAbility(data) {
  return request.post(
    baseURL + "/tutoring/admin/question/saveQuestionCourseAbility",
    data,
  )
}
/* 修改试题知识能力 */
export function updateQuestionKnowledgeAbility(data) {
  return request.post(
    baseURL + "/tutoring/admin/question/saveQuestionCourseKnowAbility",
    data,
  )
}
/* 已识别附件 select */
export function getQuestionAttachList(data) {
  return request.get(
    baseURL + "/tutoring/admin/bookAttachOcr/bookAttachSelect/recognized",
    data,
  )
}

/** 搜索试题列表 */
export function getEsQuestion(data) {
  return request.post(baseURL + "/tutoring/admin/question/es/detail", data)
}
/* 原始识别结果 */
export function getQuestionAttachOcr(data) {
  return request.get(baseURL + "/tutoring/admin/bookAttachOcr/ocrResult", data)
}
/* AI识别结果 */
export function getQuestionAttachOcrAi(data) {
  return request.get(baseURL + "/tutoring/admin/bookAttachOcr/aiResult", data)
}
/* es-试题详情-图片搜索 */
export function getEsQuestionImage(data) {
  return request.post(
    baseURL + "/tutoring/admin/question/es/detail/img",
    data,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    },
  )
}
// 图片识别成str
export function getImageToStr(data) {
  return request.post(baseURL + "/tutoring/admin/question/imgToStr", data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  })
}
/* 单题识别 */
export function getQuestionAttachOcrAiRecognition(data) {
  return request.post(
    baseURL + "/tutoring/admin/bookAttachOcr/singleQuestion",
    data,
  )
}
/* 单题ai转json */
export function getQuestionAttachOcrAiRecognitionJson(data) {
  return request.post(
    baseURL + "/tutoring/admin/bookAttachOcr/singleAiResult",
    data,
  )
}

/** 获取年份 */
export function getYearList() {
  return request.get(baseURL + "/tutoring/common/yearSelect")
}

/** 试题移动 */
export function moveQuestion(data) {
  return request.put(baseURL + "/tutoring/admin/book/questionMove", data)
}
/* 学科网搜题 */
export function searchQuestion(data) {
  return request.post(baseURL + "/tutoring/admin/question/xkw/search", data)
}
/* 图片URL识别为文字 */
export function imageToText(data) {
  return request.post(baseURL + "/tutoring/admin/question/imgToStr/url", data)
}

/* 思维导图节点uid */
export function getMindMapUid(data) {
  return request.get(baseURL + "/tutoring/admin/article/mindUid", data)
}

/* 思维导图转换 */
export function mindMapTransformation(data) {
  return request.get(
    baseURL + "/tutoring/admin/book/bookCatalogArticle/fixMind",
    data,
  )
}

/* 机构学校select */
export function getSchoolList(data?) {
  return request.get(baseURL + "/tutoring/common/companySchoolSelect", data)
}
/* 拆分题目阅读语音 */
export function splitReading(data) {
  return request.post(baseURL + "/tutoring/admin/question/read/mp3List", data, {
    timeout: 5 * 60 * 1000,
  })
}
/* 获取阅读语音或上传 */
export function getReading(data) {
  return request.post(baseURL + "/tutoring/admin/question/read/mp3", data)
}
/* ==================AI解题步骤================== */
/* 解题思路 */
export function getAiAnalysis(data) {
  return request.get(
    baseURL + "/tutoring/admin/question/parse/split/step",
    data,
  )
}
/* 详细解析 */
export function getAiAnalysisDetail(data) {
  return request.get(
    baseURL + "/tutoring/admin/question/parse/split/detail",
    data,
  )
}
/* 获取画板 */
export function getAiAnalysisBoard(data) {
  return request.get(baseURL + "/tutoring/admin/question/parse/artBoard", data)
}
/* 修改或新增画板 */
export function saveAiAnalysisBoard(data) {
  return request.post(baseURL + "/tutoring/admin/question/parse/artBoard", data)
}
/* 存入指令 */
export function saveAiAnalysisCommand(data) {
  return request.post(
    baseURL + "/tutoring/admin/question/parse/artBoard/order",
    data,
  )
}
/* 获取指令列表 */
export function getAiAnalysisCommandList(data) {
  return request.get(
    baseURL + "/tutoring/admin/question/parse/artBoard/order",
    data,
  )
}
/* 修改步骤绑定关系 */
export function updateAiAnalysisStepBind(data) {
  return request.put(
    baseURL + "/tutoring/admin/question/parse/split/step",
    data,
  )
}

/* ------------------自动批改----------------------- */
/* 上传图片 */
export function uploadImg(data) {
  return request.post(baseURL + "/tutoring/admin/question/correct", data)
}

/* 手写识别 */
export function handwritingRecognition(data) {
  return request.post(
    baseURL + "/tutoring/admin/question/correct/ocr/json",
    data,
  )
}

/* 自动批改 拿批改json */
export function getCorrectJson(data) {
  return request.post(baseURL + "/tutoring/admin/question/correct/json", data)
}

/* 合成图片 */
export function synthesisImg(data) {
  return request.post(baseURL + "/tutoring/admin/question/correct/file", data)
}

/* 获取题目批改数据 */
export function getCorrectData(data) {
  return request.get(baseURL + "/tutoring/admin/question/correct", data)
}

/* 获取题目批改数据 */
export function getNewTypeSelect(data) {
  return request.get(baseURL + "/tutoring/admin/bookType/select", data)
}
/* -----------------------绑定章节----------------------- */
/* 已选择的教材版本列表 */
export function getBookVersionList(data) {
  return request.get(
    baseURL + "/tutoring/admin/book/selectSysTextbookCatalog",
    data,
  )
}
/* 保存修改关联总树章节 */
export function saveBookCatalog(data) {
  return request.put(
    baseURL + "/tutoring/admin/book/saveSysTextbookCatalog",
    data,
  )
}
/* 版本和教材 */
export function getVersionAndTextbook(data) {
  return request.get(baseURL + "/tutoring/common/textbookVersion", data)
}
/* 教材详情 */
export function getTextbookDetail(data) {
  return request.get(baseURL + "/tutoring/common/textbookInfo", data)
}
/* 书籍/试卷-视频列表*/
export function getVideoListApi(data) {
  return request.get(baseURL + "/tutoring/admin/book/video/page", data)
}
/* 书籍/试卷-视频列表上传视频*/
export function uploadVideoApi(data) {
  return request.post(baseURL + "/tutoring/admin/book/video/save", data)
}
/* 书籍/试卷-视频列表排序列表*/
export function getVideoSort(data) {
  return request.get(baseURL + "/tutoring/admin/book/video/sort", data)
}
/* 书籍/试卷-视频列表排序保存*/
export function saveVideoSort(data) {
  return request.post(baseURL + "/tutoring/admin/book/video/sort", data)
}
/* 书籍/试卷-视频列表-删除 */
export function deleteVidieoApi(data) {
  return request.delete(baseURL + "/tutoring/admin/book/video/remove", data)
}
/* 考试题目答题学生列表 */
export function getAnswerStudentList(data) {
  return request.get(baseURL + "/tutoring/admin/question/examAnswerList", data)
}
/* 题目自动批改 */
export function autoCorrect(data) {
  return request.post(
    baseURL + "/tutoring/admin/questionAutoCorrect/do",
    data,
    { returnAll: true },
  )
}
/* 答题卡区域图片处理 */
export function handleAnswerCard(data) {
  return request.post(
    baseURL + "/tutoring/admin/question/answerCardAreaDownload",
    data,
  )
}

/*复制试卷或书籍 */
export function copyPaperOrBook(data) {
  return request.post(baseURL + "/tutoring/admin/book/copy", data)
}
/* 书籍/试卷-预跑 */
export function runApi(data) {
  return request.get(baseURL + "/tutoring/admin/book/ai/prepare", data)
}
/* 北京四中+学科网+系统+洋葱教材版本列表 */
export function getVersionListApi(data) {
  return request.get(baseURL + "/admin/videoResource/videoTextbookList", data)
}
/* 北京四中+学科网+系统教材章节列表 */
export function getChapterListApi(data) {
  return request.get(baseURL + "/admin/videoResource/textBookCatalogList", data)
}
/* 洋葱教材章节列表 */
export function getOnionChapterListApi(data) {
  return request.get(baseURL + "/admin/videoResource/onionChapterList", data)
}
/* 北京四中+学科网+强基教材章节视频列表 */
export function getChapterVideoListApi(data) {
  return request.get(
    baseURL + "/admin/videoResource/textBookCatalogVideoList",
    data,
  )
}
/* 洋葱章节视频列表 */
export function getOnionChapterVideoListApi(data) {
  return request.get(baseURL + "/admin/videoResource/onionVideoList", data)
}
/* 视频播放地址获取 */
export function getVideoPlayUrlApi(data) {
  return request.get(baseURL + "/admin/videoResource/videoPlayUrl", data)
}
/* 知识点视频列表 */
export function getKnowledgeVideoListApi(data) {
  return request.get(
    baseURL + "/admin/videoResource/knowledgePointVideoList",
    data,
  )
}
/* 科目知识点列表 */
export function getKnowledgeListApi(data) {
  return request.get(baseURL + "/admin/videoResource/knowledgePointList", data)
}
/* 章节资源树 */
export function getChapterResourceTreeApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/book/catalog/resource/tree",
    data,
  )
}
/* 资源排序 */
export function resourceSortApi(data) {
  return request.put(
    baseURL + "/tutoring/admin/book/catalog/resource/update",
    data,
  )
}
/* 关联视频 */
export function bindResourceApi(data) {
  return request.post(baseURL + "/tutoring/admin/book/linked/video", data)
}
/* 取消关联视频 */
export function unBindResourceApi(data) {
  return request.post(baseURL + "/tutoring/admin/book/linked/cancelVideo", data)
}
/* 获取已关联视频集合 */
export function getBindResourceApi(data) {
  return request.get(baseURL + "/tutoring/admin/book/linked/videoIdList", data)
}
/** 获取视频Url */
export function getVideoUrlApi(data) {
  return request.get(baseURL + "/admin/videoResource/videoPlayUrl", data)
}

//移动视频
export function moveVideoApi(data) {
  return request.post(baseURL + "/tutoring/admin/book/move/video", data)
}
//学科网搜题
export function getXKWSearch(data) {
  return request.post(baseURL + "/tutoring/admin/question/xkw/searchV2", data)
}

/* 题目难度列表 */
export function getQuestionDifficultyListApi() {
  return request.get(baseURL + "/tutoring/common/question/difficulty/list")
}
/* 更新题目难度 */
export function updateQuestionDifficulty(data) {
  return request.put(
    baseURL + "/tutoring/admin/question/updateQuestionDiff",
    data,
  )
}
/* 更新小题知识点 */
export function updateSubQuestionKnowledge(data) {
  return request.put(
    baseURL + "/tutoring/admin/question/updateSubQuestionKnowledge",
    data,
  )
}
/* 文件分页列表 */
export function getFileListApi(data) {
  return request.get(baseURL + "/tutoring/admin/book/file/page", data)
}
/* 保存文件 */
export function saveFileApi(data) {
  return request.post(baseURL + "/tutoring/admin/book/file/save", data)
}
/* 删除文件 */
export function deleteFileApi(data) {
  return request.delete(baseURL + "/tutoring/admin/book/file/remove", data)
}

/* 文件分页列表 */
export function getByBookName(data) {
  return request.get(baseURL + "/tutoring/admin/book/getByBookName", data)
}

/*获取页码*/
export function getPage(data) {
  return request.get(
    baseURL + "/tutoring/admin/question/getQuestionLocationPage",
    data,
  )
}

/*心跳接口*/
export function heartbeat(data) {
  return request.get(baseURL + "/tutoring/admin/book/heartbeat", data, {
    replace: true,
  })
}

/*解锁*/
export function deleteLock(data) {
  return request.get(baseURL + "/tutoring/admin/book/unlock", data, {
    replace: true,
  })
}

/*上锁*/
export function setLock(data) {
  return request.get(baseURL + "/tutoring/admin/book/getEditLock", data, {
    replace: true,
  })
}

/*检测资源是否有锁*/
export function hasLock(data) {
  return request.get(baseURL + "/tutoring/admin/book/hasLock", data, {
    replace: true,
  })
}
/*自动公式识别（进入试题编辑页面调用）*/
export function autoCorrectFormula(data) {
  return request.post(
    baseURL + "/tutoring/admin/question/formula/autoCcr",
    data,
    {
      replace: true,
    },
  )
}
/*手动公式识别（试题编辑页面调用题干、解析等富文本按钮调用）*/
export function manualCorrectFormula(data) {
  return request.post(
    baseURL + "/tutoring/admin/question/formula/manualCcr",
    data,
  )
}

/*释放用户的所有锁*/
export function deleteAllLock() {
  return request.get(
    baseURL + "/tutoring/admin/book/unlockAll",
    {},
    {
      replace: true,
    },
  )
}
/*公式列表*/
export function getFormulaList(data) {
  return request.post(baseURL + "/tutoring/admin/question/formula/list", data)
}
/*替换公式图片为公式*/
export function replaceImagToFormula(data) {
  return request.post(
    baseURL + "/tutoring/admin/question/formula/replaceImagToFormula",
    data,
  )
}

/* 获取AI预处理资源列表 */
export function getAiProcessList(data) {
  return request.get(baseURL + "/tutoring/admin/book/aiPrepareAttachPage", data)
}

/* AI预处理 */
export function aiProcess(data) {
  return request.post(
    baseURL + "/tutoring/admin/quickQuestion/addQuickQuestion",
    data,
  )
}

/* ------------快速录题------------ */
/* 书籍附件列表 */
export function getBookFileList(data) {
  return request.get(
    baseURL + "/tutoring/admin/quickQuestion/bookAttachList",
    data,
  )
}

/* 目录树 */
export function getBookCatalogTree(data) {
  return request.get(baseURL + "/tutoring/admin/book/bookCatalogTree", data)
}

/* 资源页码列表,带切片数量 */
export function getResourcePageList(data) {
  return request.get(
    baseURL + "/tutoring/admin/quickQuestion/quickQuestionPageList",
    data,
  )
}

/* 页码切片题目列表 */
export function getQuestionImageDetails(data) {
  return request.get(
    baseURL + "/tutoring/admin/quickQuestion/quickQuestionImageDetails",
    data,
  )
}

/* 从学科网搜题 */
export function getQuickXkwSearch(data) {
  return request.get(baseURL + "/tutoring/admin/quickQuestion/xkwSearch", data)
}

/* 本地搜题获取ocr结果 */
export function getQuickLocalSearchOcrResult(data) {
  return request.get(
    baseURL + "/tutoring/admin/quickQuestion/localSearchOcrResult",
    data,
  )
}

/* 编辑切图 */
export function editQuickQuestionImage(data) {
  return request.put(
    baseURL + "/tutoring/admin/quickQuestion/updateQuestionSubBox",
    data,
  )
}

/* 删除切片 */
export function deleteQuickQuestionImage(data) {
  return request.delete(
    baseURL + "/tutoring/admin/quickQuestion/deleteQuestionSubBox",
    data,
  )
}

/* 新增切片 */
export function addQuickQuestionImage(data) {
  return request.post(
    baseURL + "/tutoring/admin/quickQuestion/addQuestionSubBox",
    data,
  )
}

/* 新增试题区域 */
export function addQuickQuestionArea(data) {
  return request.post(
    baseURL + "/tutoring/admin/quickQuestion/addQuestionBox",
    data,
  )
}

/* 快速切题-删除题目区域 */
export function deleteQuickQuestionArea(data) {
  return request.delete(
    baseURL + "/tutoring/admin/quickQuestion/deleteQuestionBox",
    data,
  )
}

/* 快速切题-题目切片排序 */
export function updateQuestionBoxSort(data) {
  return request.put(
    baseURL + "/tutoring/admin/quickQuestion/quickQuestionImageBoxSort",
    data,
  )
}

/* 上传html文件 */
export function uploadHtmlFile(data) {
  return request.post(baseURL + "/tutoring/oss/uploadHtml", data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
    timeout: 3 * 60 * 1000,
  })
}

/* 获取目录下所有题目id */
export function getQuestionIdList(data) {
  return request.get(baseURL + "/tutoring/admin/question/idList", data, {
    replace: true,
  })
}

/* 获取视频下载链接 */
export function getDownloadVideo(data) {
  return request.get(baseURL + "/tutoring/admin/book/download/video", data)
}

/* 获取类型树 */
export function getBookTypeTree(data) {
  return request.get(baseURL + "/tutoring/admin/bookType/select/tree", data)
}

/* 批量删除题目 */
export function deleteQuestionList(data) {
  return request.post(baseURL + "/tutoring/admin/book/unbind/question", data)
}
