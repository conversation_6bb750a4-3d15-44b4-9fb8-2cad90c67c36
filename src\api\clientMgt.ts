import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config

/* ------------------固件管理开始----------------------- */
/* 获取客户端版本分页列表 */
export function getClientVersionList(data) {
  return request.get(baseURL + "/tutoring/admin/clientVersion/page", data)
}
/* 新增客户端版本 */
export function addClientVersion(data) {
  return request.post(baseURL + "/tutoring/admin/clientVersion", data)
}
/* 删除客户端版本 */
export function deleteClientVersion(data) {
  return request.delete(baseURL + "/tutoring/admin/clientVersion/delete", data)
}
/* ------------------固件管理结束----------------------- */

/* ------------------协议管理开始----------------------- */
/* 获取协议分页列表 */
export function getProtocolList(data) {
  return request.get(baseURL + "/tutoring/admin/clientProtocol/list", data)
}

/* 获取协议类型 */
export function getProtocolType() {
  return request.get(baseURL + "/tutoring/admin/clientProtocol/type")
}

/* 修改协议启用状态 */
export function updateProtocolState(data) {
  return request.post(
    baseURL + "/tutoring/admin/clientProtocol/stateChange",
    data,
  )
}

/* 新增协议 */
export function addProtocol(data) {
  return request.post(baseURL + "/tutoring/admin/clientProtocol/add", data)
}

/* 编辑协议 */
export function editProtocol(data) {
  return request.put(baseURL + "/tutoring/admin/clientProtocol/edit", data)
}

/* 删除协议 */
export function deleteProtocol(data) {
  return request.delete(baseURL + "/tutoring/admin/clientProtocol/delete", data)
}

/* 协议详情 */
export function getProtocolDetail(data) {
  return request.get(baseURL + "/tutoring/admin/clientProtocol/detail", data)
}
/* 获取应用列表 */
export function getApplyList(data?) {
  return request.get(baseURL + "/tutoring/admin/clientProtocol/applyInfo", data)
}
/* ------------------协议管理结束----------------------- */

/* ------------------举报开始----------------------- */
/* 获取举报列表 */
export function getReportList(data) {
  return request.get(baseURL + "/tutoring/admin/ai/report/page", data)
}
/* 获取举报详情 */
export function getReportDetail(data) {
  return request.get(baseURL + "/tutoring/admin/ai/report/detail", data)
}
/* 获取举报的聊天记录 */
export function handleReport(data) {
  return request.get(baseURL + "/tutoring/admin/ai/report/chatRecord", data)
}
/* ------------------举报结束----------------------- */

/* 分页查询应用安装列表*/
export function getApplicationInstallPageList(data?) {
  return request.get(
    baseURL +
      "/tutoring/admin/applicationInstall/getApplicationInstallPageList",
    data,
  )
}
/* 删除单个应用安装信息*/
export function deleteApplication(data?) {
  return request.delete(
    baseURL + "/tutoring/admin/applicationInstall/delOne",
    data,
  )
}
/* 新增第三方应用安装*/
export function addApplication(data?) {
  return request.post(
    baseURL + "/tutoring/admin/applicationInstall/saveOne",
    data,
  )
}
/* 编辑第三方应用安装*/
export function editApplication(data?) {
  return request.post(
    baseURL + "/tutoring/admin/applicationInstall/editOne",
    data,
  )
}
/* 查询分发设备列表*/
export function getDeviceTypeList(data?) {
  return request.get(
    baseURL + "/tutoring/admin/applicationInstall/getDistributeDeviceTypeList",
    data,
  )
}
/* 获取全部学校列表*/
export function getSchoolList(data?) {
  return request.get(baseURL + "/tutoring/admin/school/getAllSchoolList", data)
}
/* 查询指定学校下的年级列表*/
export function getSysGradeList(data?) {
  return request.get(
    baseURL + "/tutoring/admin/school/getSysGradeListBySchoolId",
    data,
  )
}
/* 查询指定年级下的班级列表*/
export function getClassList(data?) {
  return request.get(
    baseURL + "/tutoring/admin/school/getSchoolClassListByGradeId",
    data,
  )
}
/* 查询指定班级下的学生列表*/
export function getStudentList(data?) {
  return request.get(
    baseURL + "/tutoring/admin/school/getSchoolStudentListByClassId",
    data,
  )
}
/* 查询指定学校/年级/班级下面的学生列表*/
export function getSchoolStudentDetailVOList(data?) {
  return request.post(
    baseURL + "/tutoring/admin/school/getSchoolStudentDetailVOList",
    data,
  )
}
/* 查询应用分发详情*/
export function getApplicationDetail(data?) {
  return request.get(
    baseURL +
      "/tutoring/admin/applicationInstall/getApplicationInstallDetailById",
    data,
  )
}
/* 查询学校下面应用安装情况*/
export function getAppInstallDetail(data?) {
  return request.get(
    baseURL +
      "/tutoring/admin/applicationInstall/getApplicationInstallDistributeObjSchoolResponseVOList",
    data,
  )
}
/* 开启/关闭学校安装应用的分发状态*/
export function updateSchoolStatus(data?) {
  return request.post(
    baseURL + "/tutoring/admin/applicationInstall/modifyForbiddenStatus",
    data,
  )
}
/* 查询回显分发对象信息*/
export function getApplicationInstallDistributeObjList(data?) {
  return request.get(
    baseURL +
      "/tutoring/admin/applicationInstall/getApplicationInstallDistributeObjList",
    data,
  )
}
