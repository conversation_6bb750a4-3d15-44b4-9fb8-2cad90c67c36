import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config

/* 获取oss签名 */
export function getOssSignature(data) {
  return request.put(baseURL + "/tutoring/oss/policy", data)
}

/* 获取自定义路径oss签名 */
export function getOssCustomPolicySignature(data) {
  return request.put(baseURL + "/tutoring/oss/customPolicy", data, {})
}

export function getGradeListApi(data?) {
  return request.get(baseURL + "/tutoring/common/grades", data)
}
export function fetchExamXKWBind(data) {
  return request.get(baseURL + "/tutoring/api/xkwThird/auth", null, data)
}

export function fetchExamXKWOAuth(data, header) {
  return request.get(baseURL + "/tutoring/api/xkwThird/auth/url", data, header)
}
export function getAuthList() {
  return request.get(baseURL + "/tutoring/admin/accountManage/xkwAuth")
}
export function unbindApi(data) {
  return request.get(baseURL + "/tutoring/admin/accountManage/authUnbind", data)
}

export function bindExamXKWApi(data, header) {
  return request.post(
    baseURL + "/tutoring/api/xkwThird/auth/bind",
    data,
    header,
  )
}
