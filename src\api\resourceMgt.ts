import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config

/* 学段下拉 */
export function getSegmentSelect() {
  return request.get(baseURL + "/operate/resource-manage/stageSelect")
}
/* 学期 */
export function getTermSelect(data?) {
  return request.get(baseURL + "/tutoring/common/term", data)
}
/* 学科下拉 */
export function getSubjectSelect(data?) {
  return request.get(baseURL + "/operate/resource-manage/subjectSelect", data)
}

/* 版本下拉 */
export function getVersionSelect(data?) {
  return request.get(baseURL + "/operate/resource-manage/versionSelect", data)
}

/* 教材下拉 */
export function getTextbookSelect(data?) {
  return request.get(baseURL + "/operate/resource-manage/textbookSelect", data)
}

/* ---------------章节----------------- */
/* 知识点树 */
export function getKnowledgeTree(data?) {
  return request.get(baseURL + "/tutoring/common/courseKnowledge", data)
}

/* 获取知识点试题数量 */
export function getExclusiveQuestionCount(data?) {
  return request.get(
    baseURL + "/tutoring/common/courseKnowledge/exclusiveQuestionCount",
    data,
  )
}

/* 获取老教材章节树 */
export function getOldChapterTree(data?) {
  return request.get(baseURL + "/operate/resource-manage/oldChapterTree", data)
}

/* 保存新老章节关系 */
export function saveChapterRelation(data?) {
  return request.post(baseURL + "/operate/resource-manage/chapterMapping", data)
}

/* -------------知识点-------------- */

/* 获取学科章节树-章节绑定 */
export function getSubjectChapterTree(data?) {
  return request.get(baseURL + "/operate/resource-manage/xkwChapterTree", data)
}

/* 获取学科章节树-知识点 */
export function getSubjectKnowledgeTree(data?) {
  return request.get(
    baseURL + "/operate/resource-manage/xkwChapterTree/points",
    data,
  )
}

/* 保存章节知识点关系 */
export function saveChapterKnowledgeRelation(data?) {
  return request.put(
    baseURL + "/tutoring/admin/textbookCatalog/catalogMap",
    data,
  )
}
/* 知识点 章节相关*/
export function getKnowledgeChapterRelation(data?) {
  return request.get(baseURL + "/tutoring/admin/resource/catalog/detail", data)
}
/*知识点文件类型列表查询 */
export function getlessonTypeList(data?) {
  return request.get(
    baseURL + "/operate/resource-manage/knowledgeFileType/lessonTypeList",
    data,
  )
}
/* 知识点文件 来源列表查询 */
export function getSourceFromList(data?) {
  return request.get(
    baseURL + "/operate/resource-manage/knowledgeFileType/sourceFromList",
    data,
  )
}
/* 知识点文件层级列表查询 */
export function getLevelList(data?) {
  return request.get(
    baseURL + "/operate/resource-manage/knowledgeFileType/levelList",
    data,
  )
}
/* 知识点文件类型资源查询列表 */
export function getResourceList(data?) {
  return request.get(
    baseURL + "/operate/resource-manage/knowledgeFileType/resourceList",
    data,
  )
}
/* 删除知识点资源*/
export function deleteSource(data?) {
  return request.delete(
    baseURL + "/operate/resource-manage/knowledge/deleteSource",
    data,
  )
}
/* 绑定知识点资源*/
export function bindSource(data?) {
  return request.put(
    baseURL + "/operate/resource-manage/knowledge/updateSource",
    data,
  )
}
/*知识点试题列表查询 */
export function getKnowledgeQuestionList(data?) {
  return request.post(
    baseURL + "/operate/resource-manage/knowledge/question/list",
    data,
  )
}
/*知识点试题类型列表 */
export function getKnowledgeQuestionTypeList(data?) {
  return request.get(
    baseURL + "/operate/resource-manage/knowledge/question/jkType",
    data,
  )
}
/* 章节知识点资源统计*/
export function getChapterKnowledgeCount(data?) {
  return request.get(
    baseURL + "/operate/resource-manage/knowledge/chapter/resourceStatistics",
    data,
  )
}
/* oss*/
export function getOssSign(data?) {
  return request.get(baseURL + "/operate/oss/sign", data)
}
/* 上传知识点资源*/
export function addSource(data?) {
  return request.post(
    baseURL + "/operate/resource-manage/knowledge/addSource",
    data,
  )
}
/* 绑定知识点资源*/
export function updateSource(data?) {
  return request.put(
    baseURL + "/operate/resource-manage/knowledge/updateSource",
    data,
  )
}
/* 获取知识点所有资源ID list*/
export function getSourceIdList(data?) {
  return request.get(
    baseURL + "/operate/resource-manage/knowledge/sourceIdList",
    data,
  )
}

//新学段筛选
export function getNewStageListApi() {
  return request.get(baseURL + "/tutoring/common/stages")
}

//新学科筛选
export function getNewSubjectListApi(data) {
  return request.get(baseURL + "/tutoring/common/courses", data)
}

//新版本筛选
export function getNewVersionListApi(data) {
  return request.get(baseURL + "/tutoring/common/textVersion", data)
}
//教材筛选列表
export function getBookSelectApi(data) {
  return request.get(baseURL + "/tutoring/common/textbook", data)
}
//教材列表
export function getBookList(data) {
  return request.get(baseURL + "/tutoring/admin/textbook/page", data)
}

//章节树列表
export function getChapterTreeList(data) {
  return request.get(baseURL + "/tutoring/common/bookCatalog", data)
}

//添加章节
export function addChapter(data) {
  return request.post(baseURL + "/tutoring/admin/textbookCatalog", data)
}

//修改章节名称
export function updateChapter(data) {
  return request.put(
    baseURL + "/tutoring/admin/textbookCatalog/catalogName",
    data,
  )
}

//禁用/启用章节
export function disableChapter(data) {
  return request.put(
    baseURL + "/tutoring/admin/textbookCatalog/enableOrDisable",
    data,
  )
}

//启用章节
export function enableChapter(data) {
  return request.post(baseURL + "/operate/resource/chapter/enable", data)
}

//章节排序
export function sortChapter(data) {
  return request.post(baseURL + "/tutoring/admin/textbookCatalog/sort", data)
}
//上传封面
export function uploadCover(data) {
  return request.put(baseURL + "/tutoring/admin/textbook/cover", data)
}
//版本教材管理-启用/禁用教材
export function stopBookApi(data) {
  return request.put(baseURL + "/tutoring/admin/textbook/enableOrDisable", data)
}

//版本教材管理-新增教材
export function addBookApi(data) {
  return request.post(baseURL + "/tutoring/admin/textbook", data)
}

//版本教材管理-编辑教材
export function editBookApi(data) {
  return request.put(baseURL + "/tutoring/admin/textbook", data)
}

//版本教材管理-单词导入
export function wordCampImportApi(data) {
  return request.post(baseURL + "/operate/wordCamp/import", data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  })
}

//教材版本管理-列表
export function getListApi(data) {
  return request.get(baseURL + "/tutoring/admin/textbookVersion/page", data)
}

//教材版本管理-启用/禁用版本
export function stopVersionApi(data) {
  return request.put(
    baseURL + "/tutoring/admin/textbookVersion/enableOrDisable",
    data,
  )
}
//教材版本管理-新增版本
export function addVersionApi(data) {
  return request.post(baseURL + "/tutoring/admin/textbookVersion", data)
}

//教材版本管理-编辑版本
export function editVersionApi(data) {
  return request.put(baseURL + "/tutoring/admin/textbookVersion", data)
}

/* 获取老教材版本 list*/
export function getOldEditionGrade(data?) {
  return request.get(
    baseURL + "/operate/resource/edition/oldEditionGrade",
    data,
  )
}
/* 获取新教材版本 list*/
export function getNewEditionGrade(data?) {
  return request.get(
    baseURL + "/operate/resource/textbook/commonTextbookVersions/tree",
    data,
  )
}
/* 新老教材绑定 */
export function bindOldEditionToNew(data?) {
  return request.post(
    baseURL + "/operate/resource/edition/saveTextBookMapping",
    data,
  )
}

/* 获取教材版本主树 */
export function getMainTree(data) {
  return request.get(baseURL + "/operate/resource/edition/mainTree", data)
}

/* 获取关联主树的id */
export function getMainTreeId(data) {
  return request.get(
    baseURL + "/operate/resource/chapter/mainChapterIdList",
    data,
  )
}
//获取学校列表
export function getProcessSchoolList(data?) {
  return request.get(baseURL + "/operate/resource/examProcess/schoolList", data)
}

//获取考试类型列表
export function getExamTypeList(data?) {
  return request.get(
    baseURL + "/operate/resource/examProcess/examTypeList",
    data,
  )
}

/* 关联主树章节 */
export function setMainTreeId(data) {
  return request.post(
    baseURL + "/operate/resource/chapter/relevanceMainChapter",
    data,
  )
}
//获取学科列表
export function getProcessSubjectList(data?) {
  return request.get(
    baseURL + "/operate/resource/examProcess/subjectList",
    data,
  )
}

/* 获取章节资源统计 */
export function resourceStatistics(data) {
  return request.get(
    baseURL + "/operate/resource-manage/knowledge/chapter/resourceStatistics",
    data,
  )
}
//教材资源统计
export function getBookResourceCount(data) {
  return request.get(baseURL + "/operate/resource/textbook/statistic", data)
}
//主树章节关联章节列表
export function getChapterList(data) {
  return request.get(
    baseURL + "/operate/resource/chapter/mainTreeChapterMappingList",
    data,
  )
}
//章节文件类型资源查询列表
export function getChapterResourceList(data) {
  return request.get(
    baseURL + "/operate/resource-manage/chapter/resourceList",
    data,
  )
}
//上传章节资源
export function uploadChapterResource(data) {
  return request.post(
    baseURL + "/operate/resource-manage/chapter/addSource",
    data,
  )
}

//教材资源统计列表
export function getBookResourceCountList(data) {
  return request.get(
    baseURL + "/operate/resource/textbook/booksList/statistic",
    data,
  )
}
//获取章节所有资源ID list
export function getChapterResourceIdList(data) {
  return request.get(
    baseURL + "/operate/resource-manage/chapter/sourceIdList",
    data,
  )
}
//绑定章节资源
export function bindChapterResource(data) {
  return request.put(
    baseURL + "/operate/resource-manage/chapter/updateSource",
    data,
  )
}
//删除章节资源
export function deleteChapterResource(data) {
  return request.delete(
    baseURL + "/operate/resource-manage/chapter/deleteSource",
    data,
  )
}
//章节试题列表查询
export function getChapterQuestionList(data) {
  return request.post(
    baseURL + "/operate/resource-manage/chapter/question/list",
    data,
  )
}
//获取章节所有资源ID list
export function getChapterQuestionId(data) {
  return request.get(
    baseURL + "/operate/resource-manage/chapter/sourceIdList",
    data,
  )
}

//教材版本管理-设置主树
export function setMainTreeApi(data) {
  return request.put(baseURL + "/operate/resource/edition/mainTree", data)
}
//获取状态列表
export function getProcessStatusList(data?) {
  return request.get(baseURL + "/operate/resource/examProcess/statusList", data)
}

//获取录题列表
export function getExamProcessList(data?) {
  return request.get(
    baseURL + "/operate/resource/examProcess/examProcessList",
    data,
  )
}
//章节 文件 试题 统计
export function getChapterQuestionCount(data) {
  return request.get(
    baseURL + "/operate/resource-manage/chapter/resource/statistic",
    data,
  )
}
//题库试题 common类型查询
export function getQuestionList(data) {
  return request.get(
    baseURL + "/operate/resource-manage/bank-question/common-type",
    data,
  )
}
//知识点题库试题列表查询
export function getChapterQuestionBankList(data) {
  return request.post(
    baseURL + "/operate/resource-manage/knowledge/bank-question/list",
    data,
  )
}
//章节题库试题难度列表
export function getChapterQuestionBankLevelList() {
  return request.get(
    baseURL + "/operate/resource-manage/chapter/bank-question/diff/list",
  )
}
//章节题库试题列表查询
export function getChapterQuestionBankLevel(data) {
  return request.post(
    baseURL + "/operate/resource-manage/chapter/bank-question/list",
    data,
  )
}
//资源进度表
export function getResourceProgressTable(data) {
  return request.get(baseURL + "/operate/resource/textbook/unitProgress", data)
}
//详细资源接口
export function getDetailResource(data) {
  return request.get(
    baseURL + "/operate/resource/textbook/unitProgress/detail",
    data,
  )
}

//新学科筛选
export function getSubjectList(data) {
  return request.get(
    baseURL + "/operate/resource/course/courseList/withMain",
    data,
  )
}
// 获取系统教材以及版本树形结构
export function getVersionBookTreeList(data) {
  return request.get(
    baseURL + "/operate/resource/edition/versionBookTreeList",
    data,
  )
}
//系统教材以及版本树形结构
export function getVersionBookTreeListApi(data) {
  return request.get(
    baseURL + "/operate/resource/edition/versionBookTreeList",
    data,
  )
}

// 四中教材版本树形结构
export function getFourthVersionBookTreeList(data) {
  return request.get(
    baseURL + "/operate/fourth/fourthVersionBookTreeList",
    data,
  )
}

// 绑定教材关系
export function getFourthBind(data) {
  return request.post(baseURL + "/operate/fourth/bind", data)
}

// 教材关系解绑
export function deleteFourthBind(data) {
  return request.delete(baseURL + "/operate/fourth/delete", data)
}
//系统章节列表
export function getSysChapterList(data) {
  return request.get(baseURL + "/operate/fourth/sysChapterList", data)
}
//四中教材章节列表
export function getChapterListOf4Midschool(data) {
  return request.get(baseURL + "/operate/fourth/chapterList", data)
}
//系统章节与四中章节绑定
export function chapterBind(data) {
  return request.post(baseURL + "/operate/fourth/chapterBind", data)
}

//教材版本类型列表
export function getVersionsType() {
  return request.get(baseURL + "/operate/resource/course/getVersionsTypeList")
}
// 教材章节资源树-保存描述
export function getSaveDescribe(data) {
  return request.put(
    baseURL + "/tutoring/admin/textbookCatalog/description",
    data,
  )
}
// 资源总树-列表
export function getResourceListApi(data) {
  return request.get(baseURL + "/tutoring/admin/resource/list", data)
}

// 目录列表
export function getDirectoryList(data) {
  return request.get(baseURL + "/tutoring/admin/book/bookCatalogTree", data)
}

// 新增目录节点
export function addBookCatalog(data) {
  return request.post(baseURL + "/tutoring/admin/book/addBookCatalog", data)
}
// 重命名目录节点
export function updateBookCatalog(data) {
  return request.put(baseURL + "/tutoring/admin/book/editBookCatalog", data)
}
// 删除目录节点
export function delBookCatalog(data) {
  return request.delete(
    baseURL + "/tutoring/admin/book/deleteBookCatalog",
    data,
  )
}

// 试题分页列表
export function getDirQuestionList(data) {
  return request.get(baseURL + "/tutoring/admin/question/page", data)
}

// 发布、取消发布
export function togglePublish(data) {
  return request.put(baseURL + "/tutoring/admin/book/togglePublish", data)
}

// 试题排序值更新
export function setOrdinal(data) {
  return request.post(baseURL + "/tutoring/admin/question/ordinal", data)
}

// 上传试题讲解/文档
export function uploadQuesFile(data) {
  return request.post(baseURL + "/tutoring/admin/question/questionFile", data)
}
/* 删除试题 */
export function deleteQuestion(data) {
  return request.delete(baseURL + "/tutoring/admin/question", data)
}
/* 删除试题讲解/文档 */
export function deleteQuesFile(data) {
  return request.delete(baseURL + "/tutoring/admin/question/questionFile", data)
}
/* 排序查询 */
export function getQuestionOrdinal(data) {
  return request.get(baseURL + "/tutoring/admin/question/ordinal", data)
}
export function getChapterListApi(data) {
  return request.get(baseURL + "/tutoring/admin/resource/catalog/list", data)
}
export function getResourcesApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/resource/file/catalogType",
    data,
  )
}
export function getKnowResourcesApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/resource/file/knowledgeType",
    data,
  )
}

//获取题目难度select
export function getQuestionDifficultySelect() {
  return request.get(baseURL + "/tutoring/common/question/difficulty/list")
}

//获取题目分页列表
export function getQuestionData(data) {
  return request.get(baseURL + "/tutoring/admin/resource/question/list", data)
}
export function getSourceListApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/resource/file/catalogResource",
    data,
  )
}

export function getKnowSourceListApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/resource/file/knowledgeResource",
    data,
  )
}

//删除试题
export function deleteQuestionApi(data) {
  return request.delete(
    baseURL + "/tutoring/admin/resource/question/remove",
    data,
  )
}
export function getTypeListApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/resource/file/catalogType",
    data,
  )
}

export function getKnowTypeListApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/resource/file/knowledgeType",
    data,
  )
}
export function addSourceApi(data) {
  return request.post(
    baseURL + "/tutoring/admin/resource/file/catalogResource/save",
    data,
  )
}
export function addKnowSourceApi(data) {
  return request.post(
    baseURL + "/tutoring/admin/resource/file/knowledgeResource/save",
    data,
  )
}
export function deleteSourceApi(data) {
  return request.delete(
    baseURL + "/tutoring/admin/resource/file/catalogResource/remove",
    data,
  )
}
export function deleteKnowSourceApi(data) {
  return request.delete(
    baseURL + "/tutoring/admin/resource/file/knowledgeResource/remove",
    data,
  )
}

export function getTotalApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/resource/catalog/statistic",
    data,
  )
}

export function getKnowTotalApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/resource/knowledge/statistic",
    data,
  )
}

export function fetchExamXKWBind() {
  return request.get(baseURL + "/tutoring/api/xkw/auth")
}

export function fetchExamXKWOAuth(data) {
  return request.get(baseURL + "/tutoring/api/xkw/auth/url", data)
}
export function bindExamXKWApi(data) {
  return request.post(baseURL + "/tutoring/api/xkw/auth/bind", data)
}
export function getPaperQuestion(data) {
  return request.post(baseURL + "/tutoring/api/xkw/composition/book", data, {
    replace: true,
  })
}

//资源总树-学科网试卷试题导入
export function importPaperQuestion(data) {
  return request.post(
    baseURL + "/tutoring/admin/resource/xkw/paper/import",
    data,
  )
}
/* ========================= 知识点 ============================ */
/* 是否适用于精简版 */
export function getIsSimple() {
  return request.get(baseURL + "/tutoring/common/forLite")
}
/* 节点类型 */
export function getNodeType() {
  return request.get(baseURL + "/tutoring/common/sysKnowledgePointType")
}
/* 一级知识点标签 */
export function getFirstKnowledge() {
  return request.get(baseURL + "/tutoring/common/sysKnowledgePointTag")
}
/* 新增知识点 */
export function addKnowledge(data) {
  return request.post(baseURL + "/tutoring/admin/sysKnowledgePoint/add", data)
}
/* 编辑知识点 */
export function editKnowledge(data) {
  return request.put(baseURL + "/tutoring/admin/sysKnowledgePoint/edit", data)
}
/* 删除知识点 */
export function delKnowledge(data) {
  return request.delete(
    baseURL + "/tutoring/admin/sysKnowledgePoint/delete",
    data,
  )
}

/* ====================== 公式矫正 ============================ */
/* 获取公式列表 */
export function getFormulaList(data) {
  return request.get(baseURL + "/tutoring/admin/question/formula/list/v2", data)
}
/* 公式识别 */
export function getFormulaOcr(data) {
  return request.post(baseURL + "/tutoring/admin/question/formula/ocr", data)
}
/* 编辑保存公式 */
export function getEditFormula(data) {
  return request.put(baseURL + "/tutoring/admin/question/formula/edit/v2", data)
}

/* ================== 能力/学科素养 ====================== */
/* 学科素养列表 */
export function getLiteracyList(data) {
  return request.get(baseURL + "/tutoring/admin/sysCourseLiteracy/list", data)
}

/* 关键能力列表 */
export function getAbilityList(data) {
  return request.get(baseURL + "/tutoring/admin/sysCourseAbility/list", data)
}

/* 知识能力列表 */
export function getKnowledgeAbilityList(data) {
  return request.get(
    baseURL + "/tutoring/admin/sysCourseKnowAbility/list",
    data,
  )
}

/* 学科素养新增 */
export function getLiteracyAdd(data) {
  return request.post(baseURL + "/tutoring/admin/sysCourseLiteracy/add", data)
}

/* 关键能力新增 */
export function getAbilityAdd(data) {
  return request.post(baseURL + "/tutoring/admin/sysCourseAbility/add", data)
}

/* 知识能力新增 */
export function getKnowledgeAbilityAdd(data) {
  return request.post(
    baseURL + "/tutoring/admin/sysCourseKnowAbility/add",
    data,
  )
}

/* 学科素养编辑 */
export function getLiteracyEdit(data) {
  return request.put(baseURL + "/tutoring/admin/sysCourseLiteracy/edit", data)
}

/* 关键能力编辑 */
export function getAbilityEdit(data) {
  return request.put(baseURL + "/tutoring/admin/sysCourseAbility/edit", data)
}

/* 知识能力编辑 */
export function getKnowledgeAbilityEdit(data) {
  return request.put(
    baseURL + "/tutoring/admin/sysCourseKnowAbility/edit",
    data,
  )
}

/* 学科素养删除 */
export function getLiteracyDelete(data) {
  return request.delete(
    baseURL + "/tutoring/admin/sysCourseLiteracy/delete",
    data,
  )
}

/* 关键能力删除 */
export function getAbilityDelete(data) {
  return request.delete(
    baseURL + "/tutoring/admin/sysCourseAbility/delete",
    data,
  )
}

/* 知识能力删除 */
export function getKnowledgeAbilityDelete(data) {
  return request.delete(
    baseURL + "/tutoring/admin/sysCourseKnowAbility/delete",
    data,
  )
}

/* =================== OCR识别资源 ==================== */
/* ocr书籍资源列表 */
export function getBookAttachList(data) {
  return request.get(baseURL + "/tutoring/admin/book/attach/page", data)
}

/* ocr任务状态下拉选择列表 */
export function getOcrTaskStateSelect() {
  return request.get(baseURL + "/tutoring/common/ocrTaskStateSelect")
}

/* 投递OCR识别任务 */
export function getAddOcrTask(data) {
  return request.post(baseURL + "/tutoring/admin/bookAttachOcr/add", data)
}

/* 重新添加识别任务 */
export function getAgainOcrTask(data) {
  return request.post(
    baseURL + "/tutoring/admin/bookAttachOcr/reRecognize",
    data,
  )
}

/* 章节(目录)排序 */
export function catalogSort(data) {
  return request.put(baseURL + "/tutoring/admin/book/catalogSort", data)
}
/* 统计书籍试题总分 */
export function getBookQuestionScore(data) {
  return request.get(
    baseURL + "/tutoring/admin/question/countBookQuestionScore",
    data,
  )
}

/* 创建计划 */
export function createNewProject(data) {
  return request.post(baseURL + "/tutoring/admin/studentPlan/create", data)
}
/* 章节关联书籍列表 */
export function getRelatedBookList(data) {
  return request.get(
    baseURL + "/tutoring/admin/resource/catalog/relatedBookList",
    data,
  )
}

/* 章节关联书籍列表 */
export function getNodeList(data) {
  return request.get(
    baseURL + "/tutoring/admin/resource/catalog/nodeList",
    data,
  )
}
/* 来源select */
export function getSourceIdsApi() {
  return request.get(baseURL + "/tutoring/common/source")
}

/* 版本和教材 */
export function getVersionAndTextbook(data?) {
  return request.get(baseURL + "/tutoring/common/textbookVersion", data)
}

/** 获取区域列表*/
export function getAreaList(data?) {
  return request.get(baseURL + "/tutoring/common/area", data)
}

//保存评价
export function saveEvaluation(data) {
  return request.post(baseURL + "/tutoring/admin/textbookCatalog/save", data)
}

//知识点等级select
export function getKnowledgeLevelSelect() {
  return request.get(baseURL + "/tutoring/admin/sysKnowledgePoint/level")
}

//知识点等级切换
export function saveKnowledgeLevelChange(data) {
  return request.post(
    baseURL + "/tutoring/admin/sysKnowledgePoint/changeLevel",
    data,
  )
}
//同步知识点到其他小题
export function syncKnowledgeToOtherSubQuestion(data) {
  return request.put(
    baseURL + "/tutoring/admin/question/syncKnowledgeToOtherSubQuestion",
    data,
  )
}
