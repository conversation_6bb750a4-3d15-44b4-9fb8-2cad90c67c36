import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config

//获取学校分页列表
export function getSchool(data) {
  return request.get(baseURL + "/tutoring/admin/school/list", data)
}

//集团select
export function getCompany() {
  return request.get(baseURL + "/tutoring/admin/company/select")
}

//人脸识别开关
export function setFaceSwitch(data) {
  return request.post(baseURL + "/tutoring/admin/school/saveOpenFace", data)
}

//智习室开关

export function setService(data) {
  return request.post(baseURL + "/tutoring/admin/school/saveOpenService", data)
}

//数据同步
export function dataSync(data) {
  return request.post(baseURL + "/tutoring/admin/school/syncPadData", data)
}
/* 分页列表搜索 */
export function getLearningStatistics(data) {
  return request.get(baseURL + "/tutoring/admin/hxnStudyStat/page", data)
}
/* 创建学校机构列表 */
export function getInstitutionsApi() {
  return request.get(baseURL + "/tutoring/admin/company/selectOnCreateSchool")
}
/* 创建学校 */
export function addSchoolApi(data) {
  return request.post(baseURL + "/tutoring/admin/school/create", data)
}
/* 编辑学校 */
export function editSchoolApi(data) {
  return request.post(baseURL + "/tutoring/admin/school/update", data)
}
/* 编辑回显接口 */
export function getSchoolDetailApi(data) {
  return request.get(baseURL + "/tutoring/admin/school/detail", data)
}
// 学校年级选科详情
export function getSchoolSubjectConfig(data) {
  return request.get(baseURL + "/tutoring/admin/school/gradeSubject", data)
}

// 学校年级选科保存
export function saveSchoolSubjectConfig(data) {
  return request.post(
    baseURL + "/tutoring/admin/school/gradeSubject/save",
    data,
  )
}
