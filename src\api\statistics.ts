import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config

/* 错误类型 select */
export function getErrorTypeList() {
  return request.get(
    baseURL + "/tutoring/admin/studentErrorCorrection/errorType",
  )
}

/* 学校select */
export function getSchoolSelectList() {
  return request.get(baseURL + "/tutoring/admin/accountManage/schoolList")
}

/* 列表/搜索 */
export function getQuestionCorrectionList(data) {
  return request.get(
    baseURL + "/tutoring/admin/studentErrorCorrection/page",
    data,
  )
}

//题目状态下拉框
export function getQuestionStatus(data?) {
  return request.get(
    baseURL + "/tutoring/admin/studentErrorCorrection/stateList",
    data,
  )
}

//修正详情-获取试题详情
export function getQuestionDetail(data) {
  return request.get(
    baseURL + "/tutoring/admin/studentErrorCorrection/questionDetail",
    data,
  )
}

//修正详情-纠错保存
export function saveQuestionCorrection(data) {
  return request.put(
    baseURL + "/tutoring/admin/studentErrorCorrection/editQuestion",
    data,
  )
}
