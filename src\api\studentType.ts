import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config

/*新建学生账号*/
export function addApi(data) {
  return request.post(
    baseURL + "/tutoring/admin/accountManage/addAccount",
    data,
  )
}

/*修改学生账号*/
export function editApi(data) {
  return request.put(
    baseURL + "/tutoring/admin/accountManage/editAccount",
    data,
  )
}

/*账号详情*/
export function getDetailApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/accountManage/accountDetails",
    data,
  )
}

/*学生账号列表*/
export function getListApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/accountManage/listStudentAccount",
    data,
  )
}
/*更改自由对话配置*/
export function editState(data) {
  return request.put(
    baseURL + "/tutoring/admin/accountManage/student/edit/dialogState",
    data,
  )
}
/* 学校列表 */
export function getSchoolList() {
  return request.get(baseURL + "/tutoring/common/school")
}

/* 学校列表II */
export function getShoolListAdmin() {
  return request.get(baseURL + "/tutoring/admin/accountManage/schoolList")
}

/* 修改/删除学生人脸 */
export function editStudentPic(data) {
  return request.post(
    baseURL + "/tutoring/admin/accountManage/editStudentFace",
    data,
  )
}

/* 修改学生信息 */
export function editStudentInfo(data) {
  return request.post(
    baseURL + "/tutoring/admin/accountManage/editStudentInfo",
    data,
  )
}

/* 查询学科列表 */
export function getSubjectInfoList(data) {
  return request.get(
    baseURL + "/tutoring/admin/accountManage/student/subjectList",
    data,
  )
}

/* 绑定学科 */
export function bindSubjects(data) {
  return request.post(
    baseURL + "/tutoring/admin/accountManage/student/bindSubject",
    data,
  )
}
//开通活动-学校列表
export function getSchoolsApi() {
  return request.get(baseURL + "/tutoring/admin/accountManage/schoolList")
}
//开通活动-年级列表
export function getGradesApi() {
  return request.get(baseURL + "/tutoring/common/grades")
}
//开通活动-班级列表
export function getClassesApi(data) {
  return request.get(baseURL + "/tutoring/admin/school/classList", data)
}
//开通活动-学生列表
export function getStudentsApi(data) {
  return request.post(baseURL + "/tutoring/admin/school/studentList", data)
}
//开通活动-活动列表
export function getActivitysApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/accountManage/activity/list",
    data,
  )
}
//开通活动-保存
export function saveApi(data) {
  return request.post(baseURL + "/tutoring/admin/activity/student/open", data)
}
//已开通活动-列表
export function getActivityListApi(data) {
  return request.get(
    baseURL + "/tutoring/admin/accountManage/listStudentAccount/activityPage",
    data,
  )
}

//获取一键登录登录code
export function getLoginCode(data) {
  return request.get(baseURL + "/tutoring/admin/accountManage/loginCode", data)
}
//学生创建学校列表
export function getStudentCreateSchoolList(data?: any) {
  return request.get(
    baseURL + "/tutoring/admin/accountManage/studentCreateSchoolList",
    data,
  )
}

// 重置密码
export function resetPwd(data) {
  return request.put(
    baseURL + "/tutoring/admin/accountManage/resetPassword",
    data,
  )
}

// 恢复注销账号
export function recoverAccount(data) {
  return request.put(
    baseURL + "/tutoring/admin/accountManage/recoverAccount",
    data,
  )
}
