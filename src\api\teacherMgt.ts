import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config

//获取登录验证码分页列表-全部
export function getSchoolLoginCheckCodeList(data) {
  return request.get(
    baseURL + "/tutoring/admin/loginCheckCode/getSchoolLoginCheckCodeList",
    data,
  )
}
//获取登录验证码修改记录列表
export function getSchoolLoginCheckCodeHistoryList(data) {
  return request.get(
    baseURL +
      "/tutoring/admin/loginCheckCode/getSchoolLoginCheckCodeHistoryList",
    data,
  )
}

// 查询学习任务管理列表
export function getLearnTaskMgtList(data) {
  return request.get(
    baseURL +
      "/tutoring/admin/schoolLearningTaskCheck/getSchoolLearningTaskCheckList",
    data,
  )
}

// 学习任务开启/关闭检验
export function changeLearnTaskStatus(data) {
  return request.post(
    baseURL + "/tutoring/admin/schoolLearningTaskCheck/taskCheckUpdate",
    data,
  )
}

//学习任务管理修改列表
export function learnTaskHistoryList(data) {
  return request.get(
    baseURL +
      "/tutoring/admin/schoolLearningTaskCheck/getSchoolLearningTaskCheckHistoryList",
    data,
  )
}

// 获取天立学校列表
export function getTianliSchoolList() {
  return request.get(baseURL + "/tutoring/admin/school/getTlSchoolList")
}
// 提示词列表
export function getPromptList(data?) {
  return request.get(baseURL + "/tutoring/admin/ai/model/prompt/list", data, {
    replace: true,
  })
}
// 新建提示词
export function addPrompt(data?) {
  return request.post(baseURL + "/tutoring/admin/ai/model/prompt/create", data)
}
// 获取场景模式选择列表
export function getScenarioList(data?) {
  return request.get(
    baseURL + "/tutoring/admin/ai/model/prompt/scenario/type/list",
    data,
  )
}
// 模型选择列表
export function getModelList(data?) {
  return request.get(
    baseURL + "/tutoring/admin/ai/model/prompt/model/select/list",
    data,
  )
}
// 删除提示词
export function deletePrompt(data?) {
  return request.delete(
    baseURL + "/tutoring/admin/ai/model/prompt/delete",
    data,
  )
}
// 删除提示词
export function changeState(data?) {
  return request.post(
    baseURL + "/tutoring/admin/ai/model/prompt/switch/state",
    data,
  )
}
// 提示词详情
export function getPromptDetail(data?) {
  return request.get(baseURL + "/tutoring/admin/ai/model/prompt/detail", data)
}
// 更新提示词
export function updatePromptDetail(data?) {
  return request.post(baseURL + "/tutoring/admin/ai/model/prompt/update", data)
}
// 屏蔽试卷分页列表
export function getShieldPaperList(data?) {
  return request.get(baseURL + "/tutoring/admin/shield/exam/paged", data)
}
// 屏蔽试卷
export function shieldExam(data?) {
  return request.post(baseURL + "/tutoring/admin/shield/exam/shield", data)
}
// 考试搜索
export function getExamSearch(data?) {
  return request.post(baseURL + "/tutoring/admin/shield/exam/query", data)
}
