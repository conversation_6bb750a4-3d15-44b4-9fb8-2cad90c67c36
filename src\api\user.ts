import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL, v3BaseURL } = config

/* 获取用户信息 */
export function getUserInfo(data) {
  return request.post(baseURL + "/tutoring/login/admin", data)
}

/* 登出 */
export function logout() {
  return request.post(baseURL + "/operate/login/out")
}

/* 获取登录用户前端路由*/

export function getFrontRoute() {
  return request.get(baseURL + "/tutoring/admin/privilege/front-route")
}

//获取用户详细信息
export function getUserDetail() {
  return request.get(baseURL + "/tutoring/user/info")
}

// 兑换用户密钥
export function getEncrypt(headers) {
  return request.post(
    v3BaseURL + "/v3/teacher/jzt/login/encrypt",
    {},
    {
      headers,
    },
  )
}

// 密钥登录获取用户信息
export function getEncryptLogin(data) {
  return request.post(baseURL + "/tutoring/login/zhixishi/teacher", data)
}
