import config from "@/config/index"
import request from "@/utils/request/index"
const { baseURL } = config

/*检查账号是否存在  */
export function checkAccount(data) {
  return request.get(baseURL + "/tutoring/admin/accountManage/checkExist", data)
}
/* 新建后台账号 */
export function createAccount(data) {
  return request.post(
    baseURL + "/tutoring/admin/accountManage/addAdminAccount",
    data,
  )
}
/* 后台账号详情 */
export function getAccountDetail(data) {
  return request.get(
    baseURL + "/tutoring/admin/accountManage/adminAccountDetails",
    data,
  )
}
/* 修改后台账号 */
export function updateAccount(data) {
  return request.put(
    baseURL + "/tutoring/admin/accountManage/editAdminAccount",
    data,
  )
}
/* 后台账号列表 */
export function getAccountList(data) {
  return request.get(
    baseURL + "/tutoring/admin/accountManage/listAdminAccount",
    data,
  )
}
/* 后台账号登录日志列表 */
export function getAccountLogList(data) {
  return request.get(
    baseURL + "/tutoring/admin/accountManage/adminAccountLoginLogList",
    data,
  )
}

//获取角色列表
export function getRole(data) {
  return request.get(baseURL + "/tutoring/admin/privilege/role/list", data)
}

//添加角色
export function addRole(data) {
  return request.post(baseURL + "/tutoring/admin/privilege/role", data)
}

//删除角色
export function delRole(data) {
  return request.delete(baseURL + "/tutoring/admin/privilege/role", data)
}

//获取关联账号列表
export function getRoleAccount(data) {
  return request.get(baseURL + "/tutoring/admin/privilege/role/user-page", data)
}

//解绑账号
export function userUntie(data) {
  return request.put(baseURL + "/tutoring/admin/privilege/role/unbind", data)
}

//获取权限树
export function getAuthTree(data) {
  return request.get(baseURL + "/tutoring/admin/privilege/role/flag/tree", data)
}

//保存权限树
export function saveAuthTree(data) {
  return request.post(baseURL + "/tutoring/admin/privilege/role/flag", data)
}
