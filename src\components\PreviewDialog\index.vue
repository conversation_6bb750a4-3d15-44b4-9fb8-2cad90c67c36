<template>
  <div>
    <g-dialog
      class="w-[50%]"
      title="预览"
      v-model:show="showDialog"
      :show-footer="false"
      @confirm="confirm"
    >
      <n-scrollbar class="max-h-[750px]">
        <div class="mr-[20px]">
          <g-mathjax :text="content"></g-mathjax>
        </div>
      </n-scrollbar>
    </g-dialog>
  </div>
</template>

<script>
export default {
  props: {
    content: {
      type: String,
      default: "",
    },
    show: {
      type: [Boolean, undefined],
      default: false,
    },
  },
  data() {
    return {
      showDialog: false,
    }
  },
  watch: {
    show(val) {
      this.showDialog = val
    },
    showDialog(val) {
      this.$emit("update:show", val)
      if (val && this.content) {
        this.$g.tool.renderMathjax()
      }
    },
  },

  methods: {
    confirm() {},
  },
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-dialog__wrapper {
    z-index: 2030 !important;
  }
}
</style>
