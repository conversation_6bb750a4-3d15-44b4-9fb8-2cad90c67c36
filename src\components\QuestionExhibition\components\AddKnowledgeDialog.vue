<template>
  <g-dialog
    title="选择知识点"
    v-model:show="showDialog"
    :on-after-enter="initData"
    @confirm="confirm"
    width="1200"
    :show-close-button="false"
  >
    <div>
      <div
        class="max-h-[300px] overflow-auto mb-[10px] rounded-[5px] p-[5px]"
        :class="{
          border:
            question?.questionTitle || mainQuestion?.subQuestions.length > 0,
        }"
      >
        <g-mathjax
          v-if="question?.questionTitle"
          :text="question?.questionTitle"
        />
        <div v-for="(item, index) in mainQuestion?.subQuestions" :key="index">
          <div class="flex gap-[5px]">
            <div class="text-[#0f99eb] w-[40px]">小题{{ index + 1 }}</div>
            <g-mathjax
              v-if="item?.subQuestionTitle"
              :text="item?.subQuestionTitle"
            />
          </div>
          <div v-if="item?.options?.length > 0">
            <div
              v-for="(items, index) in item?.options"
              :key="index"
              class="flex items-center gap-[5px]"
            >
              <div>{{ items.name }}</div>
              <g-mathjax :text="items.title" />
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-between items-center">
        <div v-loading="loading">
          <el-input
            v-model="searchValue"
            style="width: 240px"
            placeholder="请输入关键词检索"
            @input="treeSearch()"
          />
          <g-tree
            treeName="RightTree"
            ref="Tree2Ref"
            class="pr-10px border-0 mt-10px !w-[400px] h-[400px] overflow-auto"
            :treeData="treeData"
            check-strictly
            :highlight-check="false"
            @node-click="nodeClick"
            render-after-expand
            :defaultProps="{
              label: 'sysKnowledgePointName',
            }"
          >
            <template #body="{ data }">
              <div>
                <span>{{ data.sysKnowledgePointName }} </span>
              </div>
            </template>
          </g-tree>
        </div>
        <div
          class="flex-1 ml-10px pl-20px h-[400px] overflow-auto"
          style="border-left: 1px solid #ccc"
          v-if="$g.tool.isTrue(subQuestion)"
        >
          <div
            v-for="(item, index) in subQuestion.knowledgePoints"
            :key="item.sysKnowledgePointId"
            class="mt-10px"
          >
            <n-tag
              class="cursor-pointer"
              type="primary"
              closable
              size="large"
              round
              @close="deleteKnowledge(index)"
              >{{ item.sysKnowledgePointName }}</n-tag
            >
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <slot name="footer">
        <div class="flex justify-end gap-[10px]">
          <n-button @click="close">取消</n-button>
          <n-button type="primary" @click="syncOtherQuestion"
            >同步到其他小题</n-button
          >
          <n-button type="primary" @click="confirm">确定</n-button>
        </div>
      </slot>
    </template>
  </g-dialog>
</template>

<script setup lang="ts">
import {
  getKnowledgeTree,
  syncKnowledgeToOtherSubQuestion,
} from "@/api/resourceMgt"
import { updateSubQuestionKnowledge } from "@/api/bookMgt"
import type { PropType } from "vue"
const props = defineProps({
  currentSubQuestion: {
    type: Object as PropType<any>,
    default: () => {},
  },
  subQuestions: {
    type: Array as PropType<any>,
    default: () => [],
  },
  subIndex: {
    type: Number as PropType<number>,
    default: 0,
  },
  question: {
    type: Object as PropType<any>,
    default: () => {},
  },
  mainQuestion: {
    type: Object as PropType<any>,
    default: () => {},
  },
})
const emit = defineEmits(["getList"])

let showDialog = defineModel<boolean>("show", { required: true })
let searchValue = $ref<any>("")
let treeData = $ref<any[]>([])
const route = useRoute()
let loading = $ref(false)
let list = $ref<any[]>([])
let subQuestion = $ref<any>({})
let Tree2Ref = $ref<any>(null)
/* 左侧树点击 */
function nodeClick(data) {
  const index = subQuestion.knowledgePoints.findIndex((e: any) => {
    return e.sysKnowledgePointId == data.sysKnowledgePointId
  })
  if (index == -1) {
    subQuestion.knowledgePoints.push({
      ...data,
    })
  }
}
/* 获取左侧树 */
async function initData() {
  try {
    subQuestion = $g._.cloneDeep(props.subQuestions[props.subIndex])
    loading = true
    let res = await getKnowledgeTree({
      sysCourseId: route.query.sysCourseId,
    })
    loading = false
    treeData = res
  } catch (err) {
    console.log(err)
    loading = false
    treeData = []
  }
}
/* 删除知识点 */
function deleteKnowledge(index) {
  subQuestion.knowledgePoints.splice(index, 1)
}
async function confirm() {
  await updateSubQuestionKnowledge({
    subQuestionId: subQuestion.subQuestionId,
    sysKnowledgePointIdList: subQuestion.knowledgePoints.map(
      (v) => v?.sysKnowledgePointId,
    ),
  })
  $g.msg("更新成功")
  props.subQuestions[props.subIndex].knowledgePoints = $g._.cloneDeep(
    subQuestion.knowledgePoints,
  )
  showDialog.value = false
}
/* 关键词检索 */
function treeSearch() {
  let node = Tree2Ref
  node.getFilterNode(searchValue)
}
function close() {
  showDialog.value = false
}
async function syncOtherQuestion() {
  await syncKnowledgeToOtherSubQuestion({
    subQuestionId: props.currentSubQuestion?.subQuestionId ?? "",
    sysKnowledgePointIdList:
      subQuestion.knowledgePoints?.map((item) => item?.sysKnowledgePointId) ??
      [],
  })
  showDialog.value = false
  $g.msg("同步成功")
  emit("getList")
}
watch(
  () => showDialog.value,
  (val) => {
    if (val) {
      $g.tool.renderMathjax()
    }
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped></style>
