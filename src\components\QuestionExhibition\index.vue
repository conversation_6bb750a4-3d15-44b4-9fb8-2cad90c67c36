<template>
  <div class="p-16px rounded-[18px] my-20px" style="border: 1px solid #3398f7">
    <!-- 头部 -->
    <div
      class="flex items-center justify-between pb-10px flex-wrap gap-y-[15px]"
      style="border-bottom: 1px solid #666"
    >
      <div class="flex gap-x-[15px] items-center">
        <slot name="start-header"></slot>
        <div>序号：{{ mainQuestion.ordinal ?? "-" }}</div>
        <div>ID：{{ mainQuestion.questionId ?? "-" }}</div>
        <div>题型：{{ mainQuestion.sysQuestionTypeName ?? "-" }}</div>
        <div v-if="mainQuestion?.sysQuestionDifficultyName">
          难度：{{ mainQuestion.sysQuestionDifficultyName }}
        </div>
        <slot name="left-header"></slot>
      </div>
      <slot name="action"></slot>
    </div>
    <!-- 大题部分 -->
    <div class="flex my-10px" v-if="mainQuestion.questionTitle">
      <div class="flex flex-1 items-start">
        <div
          class="w-21px h-21px"
          v-if="mainQuestion.questionTitle && showAudio"
        >
          <!-- 播放音频 -->
          <div
            class="loader"
            v-if="getPlaybackStatus(1, mainQuestion) == 'loading'"
          ></div>
          <g-icon
            v-else
            :name="
              getPlaybackStatus(1, mainQuestion) == 'start'
                ? 'ri-pause-circle-line'
                : 'ri-play-circle-line'
            "
            size="20"
            color="#00ce9b"
            class="mt-[-6px] mr-6px"
            @click="playAudio(1, mainQuestion)"
          />
        </div>
        <g-mathjax :text="mainQuestion?.questionTitle" />
      </div>
      <!-- 自定义插槽 -->
      <div class="flex-shrink-0 ml-6px"><slot name="title-right"> </slot></div>
    </div>
    <!-- 子题部分 -->
    <div class="flex gap-x-[5px] my-10px">
      <template v-if="mainQuestion?.subQuestions?.length > 1">
        <div
          v-for="item in mainQuestion?.subQuestions?.length"
          :key="item"
          class="w-30px leading-[30px] border rounded-[5px] text-center cursor-pointer"
          :class="{ 'bg-[#3398f7] text-white': currentSubIndex === item }"
          @click="changeSubIndex(item)"
        >
          {{ item }}
        </div>
      </template>
    </div>
    <!-- 子题题目 -->
    <div>
      <div class="flex items-start">
        <div
          class="w-21px h-21px"
          v-if="
            (currentSubQuestion?.subQuestionTitle ||
              [1, 2, 3].includes(currentSubQuestion?.subQuestionType)) &&
            showAudio
          "
        >
          <!-- 播放音频 -->
          <div
            class="loader"
            v-if="getPlaybackStatus(2, currentSubQuestion) == 'loading'"
          ></div>
          <g-icon
            v-else
            :name="
              getPlaybackStatus(2, currentSubQuestion) == 'start'
                ? 'ri-pause-circle-line'
                : 'ri-play-circle-line'
            "
            size="20"
            color="#00ce9b"
            class="mt-[-6px] mr-6px"
            @click="playAudio(2, currentSubQuestion)"
          />
        </div>
        <g-mathjax :text="currentSubQuestion?.subQuestionTitle" />
      </div>
      <!-- 选择题 -->
      <template v-if="[1, 2, 3].includes(currentSubQuestion?.subQuestionType)">
        <div
          v-for="option in currentSubQuestion?.options"
          :key="option.name"
          class="flex"
        >
          <div class="w-20px mr-6px">{{ option.name }}.</div>
          <div>
            <g-mathjax :text="option.title"></g-mathjax>
          </div>
        </div>
      </template>
    </div>
    <!-- 知识点 -->
    <div>
      <div
        style="border-top: 1px solid #666"
        class="flex justify-between items-center pt-10px"
      >
        <div class="flex items-center">
          <div class="text-[#999] flex-shrink-0">知识点：</div>
          <div class="flex items-center">
            <n-button
              v-if="showAddKnowledge"
              type="primary"
              text
              class="mr-10px"
              :disabled="disabled"
              @click="showAddKnowledgeDialog = true"
            >
              <g-icon name="ri-add-line" size="" color="" />
              添加知识点
            </n-button>
            <div
              class="flex flex-wrap items-center"
              v-if="currentSubQuestion?.knowledgePoints?.length"
            >
              <n-tag
                v-for="item in currentSubQuestion?.knowledgePoints"
                :key="item"
                type="primary"
                class="mr-8px br-[4px] mt-4px"
                closable
                :disabled="disabled"
                @close="deleteKnowledge(item)"
                >{{ item.sysKnowledgePointName }}</n-tag
              >
            </div>
            <n-tag
              v-else
              class="br-[4px]"
              :color="{
                color: '#aaa',
                textColor: '#fff',
                borderColor: '#aaa',
              }"
              >暂无知识点</n-tag
            >
          </div>
        </div>
        <n-button textColor="#00ce9b" @click="load">
          <img
            :src="
              expandParse
                ? $g.tool.getFileUrl('<EMAIL>')
                : $g.tool.getFileUrl('<EMAIL>')
            "
            class="w-22px mr-6px"
            alt=""
          />
          答案及解析</n-button
        >
      </div>
      <!-- 自定义插槽 -->
      <slot name="custom"> </slot>
    </div>
    <!-- 解析答案 -->
    <transition>
      <div class="overflow-hidden mt-10px" v-if="expandParse">
        <div class="flex items-center mb-10px">
          <div class="flex flex-shrink-0 justify-between items-start w-full">
            <div class="flex flex-1 items-start">
              <div class="flex-shrink-0 w-80px text-primary">【详解】</div>
              <div class="flex-1">
                <template
                  v-if="currentSubQuestion?.subQuestionParseList?.length > 1"
                >
                  <div class="flex gap-x-5px mb-10px">
                    <div
                      v-for="item in currentSubQuestion?.subQuestionParseList
                        .length"
                      :key="item.subQuestionParseId"
                      class="w-30px leading-[30px] border rounded-[5px] text-center cursor-pointer"
                      :class="{
                        'bg-success text-white': currentSubParseIndex === item,
                      }"
                      @click="changeParse(item)"
                    >
                      {{ item }}
                    </div>
                  </div>
                </template>
                <n-button
                  type="primary"
                  text
                  class="my-10px"
                  v-if="currentSubParse?.backgroundMaterials"
                  @click="openDialog"
                  >背景资料(有)</n-button
                >
                <div class="flex items-start">
                  <div class="w-21px h-21px" v-if="showAudio">
                    <div
                      class="loader"
                      v-if="
                        getPlaybackStatus(4, {
                          ...currentSubParse,
                          questionId: mainQuestion.questionId,
                        }) == 'loading'
                      "
                    ></div>
                    <!-- 播放音频 -->
                    <g-icon
                      v-else
                      :name="
                        getPlaybackStatus(4, {
                          ...currentSubParse,
                          questionId: mainQuestion.questionId,
                        }) == 'start'
                          ? 'ri-pause-circle-line'
                          : 'ri-play-circle-line'
                      "
                      size="20"
                      color="#00ce9b"
                      class="mt-[-6px] mr-6px"
                      @click="
                        playAudio(4, {
                          ...currentSubParse,
                          questionId: mainQuestion.questionId,
                        })
                      "
                    />
                  </div>
                  <g-mathjax :text="currentSubParse?.content" />
                </div>
              </div>
            </div>
            <!-- AI讲解 -->
            <div
              v-if="urlList?.length && currentSubParse.subQuestionParseId"
              class="flex flex-shrink-0 justify-between ml-10px"
              :class="currentSubParse?.aiExplain ? 'w-[270px]' : 'w-[250px]'"
            >
              <n-button
                type="success"
                class="p-10px text-13px"
                @click.stop="openAiExplainDialog"
                :disabled="disabled"
                >AI讲解步骤
                <span v-if="currentSubParse?.aiExplain">(有)</span></n-button
              >
              <n-button
                type="success"
                class="p-10px text-13px"
                v-if="getIframeUrl('小启老师')"
                @click.stop="openAIDialog(getIframeUrl('小启老师').url)"
                :disabled="disabled"
                >小启老师
              </n-button>
              <n-button
                type="success"
                v-if="getIframeUrl('小鸣老师')"
                class="p-10px text-13px"
                @click.stop="openAIDialog(getIframeUrl('小鸣老师').url)"
                :disabled="disabled"
                >小鸣老师
              </n-button>
            </div>
          </div>
        </div>
        <div class="flex items-center">
          <span class="flex-shrink-0 w-80px text-primary"> 【答案】 </span>
          <div class="flex items-start">
            <div class="w-21px h-21px" v-if="showAudio">
              <div
                class="loader"
                v-if="getPlaybackStatus(3, currentSubQuestion) == 'loading'"
              ></div>
              <!-- 播放音频 -->
              <g-icon
                v-else
                :name="
                  getPlaybackStatus(3, currentSubQuestion) == 'start'
                    ? 'ri-pause-circle-line'
                    : 'ri-play-circle-line'
                "
                size="20"
                color="#00ce9b"
                class="mt-[-6px] mr-6px"
                @click="playAudio(3, currentSubQuestion)"
              />
            </div>
            <g-mathjax
              :text="currentSubQuestion?.subQuestionAnswer"
            ></g-mathjax>
          </div>
        </div>
      </div>
    </transition>
    <!-- <div
      :class="['auto-height-box', expandParse ? 'expand' : '']"
      class="mt-10px"
    ></div> -->
    <BackgroundDataDialog v-model:show="showDialog" :data="backgroundData" />
    <AddKnowledgeDialog
      v-model:show="showAddKnowledgeDialog"
      :current-sub-question="currentSubQuestion"
      :sub-questions="question.subQuestions"
      :sub-index="currentSubIndex - 1"
      :question="question"
      :mainQuestion="mainQuestion"
      @getList="getList"
    />
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
import BackgroundDataDialog from "./components/BackgroundDataDialog.vue"
import { useSpeakerStore } from "@/stores/modules/speaker"
import AddKnowledgeDialog from "./components/AddKnowledgeDialog.vue"
import { updateSubQuestionKnowledge } from "@/api/bookMgt"
let speakerStore = useSpeakerStore()
let { getIdByType, isPlay } = $(storeToRefs(useSpeakerStore()))
const props = defineProps({
  question: {
    type: Object as PropType<any>, //试题信息
    required: true,
  },
  urlList: {
    type: Array as PropType<any>,
    default: () => [],
  },
  showAudio: {
    type: Boolean,
    default: false,
  },
  showAddKnowledge: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(["fastGpt", "openAiExplain", "playAudio", "getList"])

let currentSubIndex = $ref(1) //默认选中子题的索引
let currentSubParseIndex = $ref(1) //默认选中解析的索引
let expandParse = $ref(false)
let showDialog = $ref(false) //背景弹窗
let showAddKnowledgeDialog = $ref(false) //添加知识点弹窗
let backgroundData = $ref<any>(null) //背景数据
const mainQuestion = $computed(() => {
  let question = $g._.cloneDeep(props.question)
  question?.subQuestions?.forEach((v) => {
    if ([1, 2].includes(v.subQuestionType)) {
      v.options = Object.keys(v)
        .filter(
          (key) =>
            key.includes("option") &&
            v[key] &&
            key !== "optionNumbers" &&
            key != "options" &&
            key != "optionsAnswer",
        )
        .map((realKey) => {
          return {
            name: realKey.charAt(realKey.length - 1).toLocaleUpperCase(),
            title: v[realKey],
          }
        })
    } else if (v.subQuestionType == 3) {
      v.options = [
        {
          id: 1,
          name: "√",
          title: null,
        },
        {
          id: 2,
          name: "×",
          title: null,
        },
      ]
    }
  })
  return question
})
/* 当前选中子题 */
const currentSubQuestion = $computed(() => {
  return mainQuestion.subQuestions.length
    ? mainQuestion.subQuestions[currentSubIndex - 1]
    : {}
})
/* 当前选中解析 */
const currentSubParse = $computed(() => {
  return currentSubQuestion?.subQuestionParseList?.length
    ? currentSubQuestion.subQuestionParseList[currentSubParseIndex - 1]
    : { content: currentSubQuestion.subQuestionParse ?? "" }
})
/* 打开背景弹窗 */
function openDialog() {
  backgroundData = currentSubParse.backgroundMaterials
  showDialog = true
}
/* 切换子题 */
function changeSubIndex(item) {
  currentSubIndex = item
  currentSubParseIndex = 1
  props.question.currentClickSubIdx = currentSubIndex - 1
  setTimeout(() => {
    $g.tool.renderMathjax()
  }, 0)
}
/* 切换解析 */
function changeParse(item) {
  currentSubParseIndex = item
  setTimeout(() => {
    $g.tool.renderMathjax()
  }, 0)
}
function openAIDialog(url) {
  emit("fastGpt", props.question.questionId, url, {
    subQuestionId: currentSubParse.subQuestionId,
    subQuestionParseId: currentSubParse?.subQuestionParseId,
  })
}

/* 答案解析 */
function load() {
  expandParse = !expandParse
  if (expandParse) {
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
}
function openAiExplainDialog() {
  if (!currentSubParse.aiExplain) {
    $g.msg("当前解析无AI解题步骤，无法编辑查看！", "error")
    return
  }
  emit("openAiExplain", currentSubParse)
}
function getIframeUrl(name) {
  return props.urlList.find((item) => item.name == name)
}
function getPlaybackStatus(type, item) {
  let map = {
    1: item?.questionId, // 题干，返回subQuestionId
    2: item?.subQuestionId + "S", // 答案，返回subQuestionId + 'A'
    3: item?.subQuestionId + "A", // 答案，返回subQuestionId + 'A'
    4: item?.subQuestionParseId + "P", // 解析，返回subQuestionParseId
  }

  if (map[type] == getIdByType) {
    return isPlay
  } else {
    return "pause"
  }
}
async function playAudio(type, data) {
  speakerStore.getAudioList(type, data)
}
/* 删除知识点 */
async function deleteKnowledge(item) {
  $g.confirm({
    title: "提示",
    content: "确定删除该知识点吗？",
  })
    .then(async () => {
      let sub_question = props.question.subQuestions[currentSubIndex - 1]
      sub_question.knowledgePoints = sub_question.knowledgePoints.filter(
        (v) => v.sysKnowledgePointId !== item.sysKnowledgePointId,
      )
      await updateSubQuestionKnowledge({
        subQuestionId: sub_question.subQuestionId,
        sysKnowledgePointIdList: sub_question.knowledgePoints.map(
          (v) => v?.sysKnowledgePointId,
        ),
      })
      $g.msg("删除成功")
    })
    .catch(() => {})
}
function getList() {
  emit("getList")
}
onMounted(() => {
  $g.tool.renderMathjax()
})
</script>

<style lang="scss" scoped>
:deep() {
  .circular {
    width: 25px;
    height: 25px;
  }
}
.auto-height-box {
  display: grid;
  grid-template-rows: 0fr;
  transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.expand {
  grid-template-rows: 1fr;
}
.v-enter-active,
.v-leave-active {
  transition: opacity 0.3s linear;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
/* From Uiverse.io by Fernando-sv */
.loader {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: transparent;
  border-radius: 50%;
}

.loader {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: transparent;
  width: 20px;
  height: 20px;
}

.loader {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: transparent;
  width: 20px;
  height: 20px;
  animation: spin89345 1s linear infinite;
}

@keyframes spin89345 {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
