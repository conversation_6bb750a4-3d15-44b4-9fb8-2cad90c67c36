<script lang="ts" setup>
const props = defineProps({
  url: {
    type: String,
    default: "",
    required: true,
  },
  live: {
    type: Boolean,
    default: false,
    required: false,
  },
})

// 如果是学科网的视频，返回true
const liveComputed = $computed(
  () => props.url.includes("xkw.com") || props.live,
)
</script>

<template>
  <g-dialog title="视频预览" :show-footer="false" v-bind="$attrs">
    <g-video :url="url" :live="liveComputed"></g-video>
  </g-dialog>
</template>
