<template>
  <n-modal
    v-model:show="show"
    :mask-closable="maskClosable"
    preset="card"
    :style="{ width: `${width}px`, height: height ? `${height}px` : '' }"
    v-bind="$attrs"
    @after-leave="handleClose"
    :auto-focus="false"
    :trap-focus="false"
    :on-close="closeX"
  >
    <template #header>
      <slot name="header">
        {{ title }}
      </slot>
    </template>
    <div class="mb-20px">
      <slot></slot>
    </div>
    <slot name="footer" v-if="showFooter">
      <div class="flex justify-end">
        <n-button @click="cancel" class="mr-20px">
          {{ cancelName }}
        </n-button>
        <n-button
          type="primary"
          @click="confirm"
          :loading="formOptions?.loading"
        >
          {{ confirmName }}
        </n-button>
      </div>
    </slot>
  </n-modal>
</template>

<script setup lang="ts" name="GDialog">
// todo:后续将表单的一些方法移植到g-form 防止写2份重复代码 ，也方便单独g-form的使用和移植到抽屉
// !在g-dialog配合g-form做编辑回填的时候，需要先打开弹窗，等待nextTick完成后再回填数据，否则会导致数据初始化失败
const props = defineProps({
  // v-model:show双向绑定
  show: {
    type: Boolean,
    default: false,
  },
  width: {
    type: [Number, String],
    default: 600,
  },
  height: {
    type: [Number, String],
    default: "",
  },
  confirmName: {
    type: String,
    default: "确定",
  },
  cancelName: {
    type: String,
    default: "取消",
  },
  formOptions: {
    type: Object,
  },
  showFooter: {
    type: Boolean,
    default: true,
  },
  autoClose: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: "新增",
  },
  maskClosable: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  "update:show",
  "confirm",
  "openDialog",
  "handleClose",
  "cancel",
  "closeX",
])
const show = useVModel(props, "show", emit)

const hasForm = $computed(() => {
  const flag = $g.tool.isTrue(props.formOptions)
  return flag
})

function confirm() {
  return new Promise((resolve, reject) => {
    if (hasForm) {
      const formData = $g._.cloneDeep(props.formOptions?.data)
      props.formOptions?.ref.validate((errors) => {
        let uploadFlag = props.formOptions?.ref?.checkFileUpload()
        if (!errors && uploadFlag) {
          props!.formOptions!.loading =
            props.formOptions?.loading === undefined ? undefined : true
          emit("confirm", formData)
          resolve(formData)
        } else {
          reject(new Error("表单验证失败或文件上传未完成"))
        }
      })
    } else {
      if (props.autoClose) emit("update:show", false)
      emit("confirm")
      resolve(true) // 解决（resolve）Promise
    }
  })
}

function handleClose() {
  emit("handleClose")
  if (hasForm) {
    props.formOptions?.ref.restoreValidation()
    props.formOptions?.ref.resetFormData()
    props!.formOptions!.loading =
      props.formOptions?.loading === undefined ? undefined : false
    $g.bus.emit("handleClose")
  }
}
function closeX() {
  emit("closeX")
}
function cancel() {
  emit("cancel")
  emit("update:show", false)
}

defineExpose({
  confirm,
})
</script>
