<template>
  <Editor
    v-model="content"
    :api-key="apiKey"
    :init="init"
    v-bind="$attrs"
    class="tox-tinymce-aux"
    @blur="editorBlur"
  />
  <!-- loading -->
  <Teleport to="body">
    <div class="overlay relative" v-if="showLoading">
      <div class="loading">
        <div class="spinner"></div>
        <div class="text">{{ loadingMassage }}</div>
      </div>
    </div>
  </Teleport>
</template>

<script setup name="GEditor">
/**
 * @description: 富文本编辑器
 * 通过v-model绑定数据
 *  */
import Editor from "@tinymce/tinymce-vue"
import AliOss from "@/plugins/OSS"
import { reactive, ref } from "vue"

const props = defineProps({
  oss: {
    type: Object,
    default: function () {
      return {
        params: { application: "banner" },
      }
    },
  },
  videoOss: {
    type: Object,
    default: function () {
      return {
        params: { application: "video" },
      }
    },
  },
  // 接受上传的文件大小 单位:字节
  fileSize: {
    type: [Number, String],
  },
  initProps: {
    type: Object,
    default: () => ({}),
  },
})

const showLoading = ref(false)
let loadingMassage = ref("上传中")

const content = $ref("")
const apiKey = ref("w7fzobxwkrnmf6b8qjkb4jkivbxenlq7wz55homxvhptlcda")
// const init = reactive()

const init = $computed(() => {
  return {
    language: "zh-Hans", //语言类型
    promotion: false,
    placeholder: "请输入...",
    min_width: 300,
    min_height: 350,
    height: 500, //注：引入autoresize插件时，此属性失效
    resize: "both", //编辑器宽高是否可变，false-否,true-高可变，'both'-宽高均可，注意引号
    branding: false, //tiny技术支持信息是否显示
    // statusbar: false,  //最下方的元素路径和字数统计那一栏是否显示
    elementpath: false, //元素路径是否显示
    file_picker_types: "media",
    // 字体样式
    font_size_formats: "11px 12px 14px 16px 18px 24px 36px 48px",
    font_family_formats:
      "微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;",
    // 插件配置 axupimgs indent2em
    plugins:
      "ax_wordlimit autolink  fullscreen image insertdatetime link lists media preview table wordcount code searchreplace",
    // "preview searchreplace autolink directionality visualblocks visualchars fullscreen image link media template code codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount autosave emoticons",
    //工具栏配置，设为false则隐藏
    toolbar: [
      "undo redo | blocks | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | forecolor backcolor removeformat | link image media table insertdatetime searchreplace | preview code fullscreen",
      // "fullscreen undo redo restoredraft | forecolor backcolor bold italic underline strikethrough link anchor | alignleft aligncenter alignright alignjustify outdent indent | bullist numlist | blockquote subscript superscript removeformat ",
      // "fontfamily fontsize styles | table image axupimgs media emoticons charmap hr pagebreak insertdatetime  selectall visualblocks searchreplace | code print preview | indent2em lineheight formatpainter ",
      // "testBtn",
    ],
    ax_wordlimit_num: props.initProps.max || 500,
    ax_wordlimit_callback: function (editor, txt, num) {
      if (txt.length > num) {
        $g.msg(`限制${num}字`, "info")
        editor.execCommand("undo")
        // editor.setContent(txt.slice(0, num))
        goEnd(editor)
      }
    },
    images_upload_handler(blobInfo, progress) {
      return new Promise(async (resolve, reject) => {
        let file = blobInfo.blob()
        file = new File([file], blobInfo?.file?.name || blobInfo.filename())
        const sizeFlag = limitSize(file, "img")
        const typeFlag = limitType(file, "img")
        if (!sizeFlag || !typeFlag) {
          resolve("")
        }
        const aliOss = new AliOss()
        await aliOss.on("onProgress", (value, id) => {
          progress(value)
        })
        await aliOss
          .uploadFile({ name: file.name, size: file.size, file })
          .then((res) => {
            resolve(
              res.resource_url.includes("http")
                ? res.resource_url
                : res.host + "/" + res.resource_url,
            )
          })
          .catch((err) => {
            $g.msg("上传失败", "error")
          })
      })
    },
    file_picker_callback: function (callback, value, meta) {
      if (meta.filetype == "media") {
        const attributes = [
          { name: "type", value: "file" },
          { name: "accept", value: ".mp4" },
        ]
        let input = document.createElement("input") //创建一个隐藏的input
        // 批量设置属性
        for (let i = 0; i < attributes.length; i++) {
          const { name, value } = attributes[i]
          input.setAttribute(name, value)
        }
        input.onchange = () => {
          showLoading.value = true
          try {
            getUploadFileUrl(input.files[0])
              .then((url) => {
                showLoading.value = false
                callback(url)
              })
              .catch(() => {
                showLoading.value = false
              })
          } catch {
            showLoading.value = false
          }
        }
        //触发点击
        input.click()
      }
    },
    //菜单栏配置，设为false则隐藏，不配置则默认显示全部菜单，也可自定义配置--查看 http://tinymce.ax-z.cn/configure/editor-appearance.php --搜索“自定义菜单”
    // menubar: "file edit my1",
    // menubar: false,

    setup: (editor) => {},
    ...props.initProps,
  }
})
function editorBlur() {
  $g.bus.emit("form.change", { type: "editor" })
}

function goEnd(editor) {
  editor.execCommand("selectAll")
  editor.selection.getRng().collapse(false)
  editor.focus()
}
// 文件上传的逻辑
function getUploadFileUrl(file) {
  return new Promise(async (resolve) => {
    const aliOss = new AliOss()
    const sizeFlag = limitSize(file, "video")
    const typeFlag = limitType(file, "video")

    if (!sizeFlag || !typeFlag) {
      showLoading.value = false
      return
    }

    aliOss.on("onProgress", (value) => {
      loadingMassage.value = `上传中 ${value.val}% ${value.speed}`
    })

    aliOss
      .uploadFile({
        name: file.name,
        file: file,
        id: file.size || 1,
        size: file.size,
      })
      .then((res) => {
        resolve(
          res.resource_url.includes("http")
            ? res.resource_url
            : res.host + "/" + res.resource_url,
        )
      })
      .catch(() => {
        $g.msg("上传中断", "info")
      })

    // 获取弹窗的取消操作
    let domArr = document.getElementsByClassName("tox-button")
    let closeButton = domArr[0]
    if (closeButton.title == "关闭") {
      closeButton.addEventListener("click", () => {
        aliOss.cancelUpload(file.size || 1)
        showLoading.value = false
      })
    }
  })
}
/* 校验文件大小 */
function limitSize(file, type) {
  const fileSize = Number(file.size)
  const maxSizeMap = {
    img: {
      maxSize: 20 * 1024 * 1024, // 图片最大 20MB
      title: "图片",
    },
    video: {
      maxSize: 5 * 1024 * 1024 * 1024, // 视频最大 5GB
      title: "视频",
    },
    zip: {
      maxSize: 5 * 1024 * 1024 * 1024, // ZIP 文件最大 5GB
      title: "压缩包",
    },
    default: {
      maxSize: 200 * 1024 * 1024,
      title: "默认",
    }, // 其他文件最大 200MB
  }
  if (fileSize > maxSizeMap[type].maxSize) {
    $g.msg(
      `${
        maxSizeMap[type].title
      }类型文件大小超过最大限制${$g.tool.formatFileSize(
        maxSizeMap[type].maxSize,
      )}`,
      "error",
    )
    return false
  } else {
    return true
  }
}

/* 校验文件格式 */
function limitType(file, type) {
  if (type == "img") {
    if (file.type) {
      $g.msg(`请上传${type}格式文件`, "error")
      return false
    } else {
      return true
    }
  } else {
    if (!file.type.includes(type)) {
      $g.msg(`请上传${type}格式文件`, "error")
      return false
    } else {
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  height: 200px;
  width: 480px;
  transform: translate(-50%, -39%);
  background-color: rgba(255, 255, 255, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999 !important;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: #1ea0f0;
}

.spinner {
  border: 4px solid white;
  border-top: 4px solid #1ea0f0;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}
.text {
  font-size: 13px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
