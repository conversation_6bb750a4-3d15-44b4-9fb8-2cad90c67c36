<template>
  <el-empty
    :image="image"
    :image-size="size"
    :description="description"
  ></el-empty>
</template>

<script lang="ts" setup name="GEmpty">
import config from "@/config"

interface Props {
  image?: string
  size?: number
  description?: string
}

withDefaults(defineProps<Props>(), {
  image: $g.tool.getFileUrl("status/empty.png"),
  size: 300,
  description: "暂无数据",
})
</script>

<style lang="scss" scoped>
.el-empty {
  width: 100%;
  padding: 0 !important;
}
:deep() {
  .el-empty__description {
    margin-top: 0;
    p {
      position: relative;
      top: -10px;
    }
  }
}
</style>
