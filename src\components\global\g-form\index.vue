<template>
  <n-form
    ref="formRef"
    inline
    :label-placement="formOptions.labelPlacement || 'left'"
    :label-width="formOptions.labelWidth || '100px'"
    :label-align="formOptions.labelAlign || 'right'"
    :model="formOptions.data"
    :rules="formOptions.rules"
    v-bind="$attrs"
    require-mark-placement="right"
    :class="{
      'flex-wrap form-filter mb-20px': formOptions.filter,
      'form-view': formOptions.mode == 'view',
    }"
  >
    <!-- 分为普通模式和filter表格筛选模式  针对filter模式可以做到小屏幕自适应 -->
    <component
      :is="box1"
      :cols="formOptions.cols || 24"
      :x-gap="formOptions.xGap || 24"
    >
      <template v-for="(item, key) in formOptions.items">
        <component
          :is="box2"
          :key="key"
          v-if="item.show === false && item.type !== 'slot' ? false : true"
          :label="item.noLabel ? '' : item.label ? item.label + '：' : ' '"
          :path="String(key)"
          :class="[item.className, key, { 'form-item-view': item.view }]"
          :rule="handleRule(item, key)"
          :span="item.span || 24"
          :label-width="item.labelWidth || formOptions.labelWidth"
          :show-label="item.showLabel === false ? false : true"
          :show-feedback="item.showFeedback === false ? false : true"
          :labelPlacement="item.labelPlacement || formOptions.labelPlacement"
          :labelAlign="item.labelAlign || formOptions.labelAlign"
          :labelStyle="item.labelStyle"
          :style="componentStyle(item)"
          :ref="setComponentRef"
        >
          <!-- 这个slot用于定义label后面内容 -->
          <template v-if="item.slot || item.view">
            <slot :name="key">
              <div
                :style="{ width: item.width, ...item.style }"
                v-if="['text', 'password', 'textarea'].includes(item.type)"
                class="px-8px"
                :class="{
                  'view border w-[100%] min-h-50px p-6px':
                    item.type == 'textarea',
                }"
              >
                {{ formOptions.data[key] || "-" }}
              </div>
              <div v-else-if="item.type == 'select'" class="px-8px">
                {{ selectViewFormat(item, formOptions.data[key]) }}
              </div>
              <div
                v-else-if="
                  [
                    'date',
                    'datetime',
                    'daterange',
                    'datetimerange',
                    'year',
                    'quarter',
                  ].includes(item.type)
                "
              >
                {{ selectDateFormat(item, formOptions.data[key]) }}
              </div>
              <div v-else-if="item.type === 'number'">
                {{ formOptions.data[key] + item.suffix }}
              </div>
            </slot>
          </template>
          <!-- 文本输入 -->
          <n-input
            v-else-if="['text', 'password', 'textarea'].includes(item.type)"
            :type="item.type"
            v-model:value="formOptions.data[key]"
            :placeholder="item.placeholder || getPlaceholder(item)"
            show-password-on="click"
            :maxlength="item.maxlength"
            :style="{ width: item.width || '100%', ...item.style }"
            :clearable="item.clearable === false ? false : true"
            :show-count="item.showCount || false"
            :disabled="item.disabled ?? item.view"
            :loading="item.loading || undefined"
            :rows="item.rows || 3"
            :allow-input="
              !item.noSideSpace && (item.allowInput || item.type == 'textarea')
                ? undefined
                : noSideSpace
            "
            @keyup.enter="handleEnterKey"
            :autosize="item.autosize || false"
          >
            <template #password-visible-icon>
              <g-icon name="ri-eye-line" size="14" color="" />
            </template>
            <template #password-invisible-icon>
              <g-icon name="ri-eye-off-line" size="14" color="" />
            </template>
          </n-input>
          <!-- 数字输入 -->
          <n-input-number
            class="input-number"
            v-else-if="item.type === 'number'"
            v-model:value="formOptions.data[key]"
            :button-placement="item.buttonPlacement || 'right'"
            :placeholder="item.placeholder || getPlaceholder(item)"
            :precision="item.precision || 0"
            :style="{
              width: item.width || '150px',
              textAlign: item.showButton === false ? 'left' : 'center',
            }"
            :max="item.max"
            :show-button="item.showButton === false ? false : true"
            :min="item.min"
            :disabled="item.disabled ?? item.view"
          >
            <template #prefix v-if="item.prefix"> {{ item.prefix }} </template>
            <template #suffix v-if="item.suffix">{{ item.suffix }} </template>
            <template #minus-icon>
              <g-icon name="ri-subtract-line" size="20" color="#333639" />
            </template>
            <template #add-icon>
              <g-icon name="ri-add-line" size="20" color="#333639" />
            </template>
          </n-input-number>
          <!-- 选择器 -->
          <n-select
            v-else-if="item.type == 'select'"
            v-model:value="formOptions.data[key]"
            :placeholder="item.placeholder || getPlaceholder(item)"
            :options="item.options"
            :clearable="item.clearable === false ? false : true"
            :style="{ width: item.width || '100%' }"
            :multiple="item.multiple || false"
            :disabled="item.disabled ?? item.view"
            :filterable="item.filterable ?? true"
            :label-field="item.labelField"
            :value-field="item.valueField"
            :max-tag-count="item.maxTagCount"
            @scroll="item.scroll"
            :renderOption="
              item.tooltip
                ? (params) => renderTooltipOption(params, item)
                : item.renderOption
            "
            :reset-menu-on-options-change="
              typeof item.resetMenuOnOptionsChange === 'boolean'
                ? item.resetMenuOnOptionsChange
                : true
            "
          />
          <!-- 日期选择器 -->

          <n-date-picker
            :disabled="item.disabled ?? item.view"
            v-else-if="
              [
                'date',
                'datetime',
                'daterange',
                'datetimerange',
                'year',
                'quarter',
              ].includes(item.type)
            "
            :value-format="
              item.valueFormat
                ? item.valueFormat
                : item.type == 'daterange'
                ? 'yyyy-MM-dd'
                : 'yyyy-MM-dd HH:mm:ss'
            "
            :format="item.format"
            v-model:formatted-value="formOptions.data[key]"
            :type="item.type"
            :style="{ width: item.width || '100%' }"
            :clearable="item.clearable === false ? false : true"
            :is-date-disabled="item.isDateDisabled"
            :is-time-disabled="item.isTimeDisabled"
            :start-placeholder="item.startPlaceholder"
            :end-placeholder="item.endPlaceholder"
          />
          <!-- 富文本编辑器 -->
          <g-editor
            :disabled="item.disabled ?? item.view"
            v-else-if="item.type == 'editor'"
            v-model="formOptions.data[key]"
            :initProps="item.initProps ? item.initProps : {}"
          >
          </g-editor>
          <!-- 单选 -->
          <n-radio-group
            v-else-if="item.type == 'radio'"
            v-model:value="formOptions.data[key]"
            name="radiogroup"
            :disabled="item.disabled ?? item.view"
          >
            <n-space v-if="item.options.length">
              <n-radio
                v-for="e in item.options"
                :key="e.value"
                :value="e.value"
              >
                {{ e.label }}
              </n-radio>
            </n-space>
            <div v-else>-</div>
          </n-radio-group>
          <!-- 多选 -->
          <n-checkbox-group
            v-else-if="item.type == 'checkbox'"
            v-model:value="formOptions.data[key]"
            name="checkboxgroup"
            :disabled="item.disabled ?? item.view"
          >
            <n-space v-if="item.options.length">
              <n-checkbox
                v-for="e in item.options"
                :key="e.value"
                :value="e.value"
              >
                {{ e.label }}
              </n-checkbox>
            </n-space>
            <div v-else>-</div>
          </n-checkbox-group>
          <!-- switch  -->
          <n-switch
            v-else-if="item.type == 'switch'"
            :rail-style="item.railStyle"
            v-model:value="formOptions.data[key]"
            :checked-value="item.checkedValue"
            :unchecked-value="item.uncheckedValue"
            :disabled="item.disabled ?? item.view"
          >
            <template #checked> {{ item.checked }} </template>
            <template #unchecked> {{ item.unchecked }} </template>
          </n-switch>
          <!-- 树，目前默认多选，如有单选再改造 -->
          <g-tree2
            v-else-if="item.type == 'tree'"
            :treeData="item.options"
            :filterable="item.filterable"
            show-checkbox
            :style="{
              width: item.width || '100%',
              minHeight: item.minHeight + 'px',
            }"
            :multiple="item.multiple || true"
            :defaultProps="{ label: item.labelField }"
            default-expand-all
            :default-checked-keys="formOptions.data[key]"
            @check-change="
              (data, checked) =>
                handleCheckChange(data, checked, key, item.valueField || 'id')
            "
            :placeholder="item.placeholder || getPlaceholder(item)"
            :node-key="item.valueField || 'id'"
            :maxHeight="item.maxHeight"
          ></g-tree2>
        </component>
      </template>

      <n-form-item
        v-if="
          props.formOptions.filter &&
          props.formOptions.showFilterButton !== false
        "
      >
        <n-space>
          <n-button class="btn-search" type="primary" @click="search">
            搜索
          </n-button>
          <n-button class="btn-reset" @click="resetFormData"> 重置 </n-button>
        </n-space>
      </n-form-item>
    </component>
  </n-form>
</template>

<script setup lang="ts" name="GForm">
import { NGrid, NFormItemGi, NFormItem, NTooltip } from "naive-ui"
import FilterBox from "./FilterBox.vue"

/**
 * todo:1.优化upload改变更新验证，upload上传中验证应该提示并且终止后续操作
 *  */
const props = defineProps({
  // !默认值空状态建议优先使用null,而不是"",否者对于某些表单组件会出现异常
  formOptions: {
    type: Object,
    default: () => {
      return {
        // !ref属于n-form ref,另额外添加下面方法：
        // !resetFormData-重置表单为初始值
        // !validateField-验证指定字段
        ref: null,
        // !分为两种模式：view和edit  默认edit
        // !view模式下，所有表单项都会变为只读,且针对大部分通用情况进行样式和数据处理
        mode: "edit",
        // !type不再支持slot，只支持slot:true
        items: {},
        data: {},
      }
    },
  },
  tableOptions: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(["reset", "search"])

setFormMode()
function setFormMode() {
  if (!(props.formOptions.mode === "view"))
    return props.formOptions.mode == "edit"
  Object.keys(props.formOptions.items).forEach((key) => {
    props.formOptions.items[key].view = true
  })
}

let box1 = props.formOptions.filter ? FilterBox : NGrid
let box2 = props.formOptions.filter ? NFormItem : NFormItemGi

const formRef = $ref<any>(null)
let initFormData = $ref({})
if (!$g.tool.isTrue(initFormData)) {
  initFormData = $g._.cloneDeep(props.formOptions?.data)
}

onMounted(() => {
  initFormRef()
})
let componentRefArr = $ref<any>([])
const setComponentRef = (el) => {
  if (el && !componentRefArr.includes(el)) {
    componentRefArr.push(el)
  }
}
function initFormRef() {
  formRef.resetFormData = resetFormData
  formRef.validateField = validateField
  formRef.checkFileUpload = checkFileUpload
  props.formOptions.ref = formRef
}

const inputType = $ref(["text", "textarea", "password", "number", "editor"])

function getPlaceholder(item: any) {
  let prefixStr = !inputType.includes(item.type) ? "请选择" : "请输入"
  prefixStr += item.label
  if (item.maxlength) {
    prefixStr += `（${item.maxlength}个字符以内）`
  }
  return prefixStr
}

/* 重置数据 可用于filter表单重置 */
function resetFormData() {
  props.formOptions!.data = $g._.cloneDeep(initFormData)
  props.formOptions?.ref.restoreValidation()
  if (props.formOptions?.filter) {
    if (props.tableOptions.pageOptions) {
      props.tableOptions.pageOptions.page = 1
    }
    props.formOptions.filterData = {}
  }
  emit("reset")
}

function handleRule({ rule, type, label, multiple }: any, key) {
  if (!rule) return
  // !rule:true 默认代表必填
  // !使用validator不要写message ,通过 reject(Error("非正确名字"))抛出错误提示 或者 new Error('年龄应该超过十八岁')
  // !validator是个函数,返回 return new Promise<void>((resolve, reject) => {}）
  const newRule: any = {
    key,
    required: rule.required ?? (typeof rule === "boolean" && rule),
    trigger: rule.trigger || ["input", "blur", "change"],
    type: rule.type,
  }
  const numberArr = ["number", "date", "datetime", "year", "quarter", "radio"]
  const arrayArr = [
    "upload",
    "daterange",
    "datetimerange",
    "select",
    "array",
    "checkbox",
    "tree",
  ]

  if (!rule.type) {
    if (numberArr.includes(type)) {
      newRule.type = "number"
    } else if (arrayArr.includes(type)) {
      newRule.type = "array"
      if (type == "select" && !multiple) {
        newRule.type = "number"
      }
    }
  }

  if (rule.validator) {
    newRule.validator = rule.validator
  } else {
    newRule.message = rule.message || getPlaceholder({ label, type })
  }
  return newRule
}

$g.bus.on("form.change", ({ type, key }) => {
  const items = props.formOptions?.items
  const arr: any[] = []
  Object.keys(items).forEach((key2: any) => {
    if (items[key2].type == type && key == key2 && !arr.includes(key2)) {
      arr.push(key2)
    }
  })
  if (arr.length) {
    validateField(arr)
  }
})

/* 验证指定字段 */
function validateField(fieldArr) {
  return new Promise((resolve, reject) => {
    props.formOptions.ref?.validate(
      (errors) => {
        if (errors) {
          reject(errors)
          console.error("validate-errors", errors)
        } else {
          resolve(true)
        }
      },
      (rule) => {
        return fieldArr.includes(rule.key)
      },
    )
  })
}

/* 验证文件是否上传完毕 */
function checkFileUpload() {
  let { items, data } = props.formOptions
  let flag = true
  $g._.forEach(Object.keys(items), (key) => {
    if (items[key].type == "upload") {
      flag = data[key].every((e) => {
        return e.status == "finished"
      })
      if (!flag) return flag
    }
  })
  if (!flag) $g.msg("请等待文件上传完毕", "error")
  return flag
}

function selectViewFormat(item, val) {
  if (!val) return "无"
  if ($g.tool.typeOf(val) !== "array") {
    val = [val]
  }
  const items = $g._.filter(item.options, (o) => {
    return val.includes(o.value)
  }).map((o) => o.label)
  return items.join(",")
}

function selectDateFormat(item, val) {
  if (!val) return "无"
  if (item.type == "datetimerange") {
    return val.join(" - ")
  }
}

function noSideSpace(value: string) {
  return !value.startsWith(" ") && !value.endsWith(" ")
}

function handleEnterKey() {
  if (props.formOptions?.filter) {
    search()
  }
}
function search() {
  if (props.tableOptions.pageOptions) {
    props.tableOptions.pageOptions.page = 1
  }
  props.formOptions.filterData = $g._.cloneDeep(props.formOptions.data)
  emit("search", props.formOptions.filterData)
}

function getUnit(str) {
  if (/-?\d+(?:\.\d+)?px$/.test(str)) {
    return 1
  } else if (/-?\d+(?:\.\d+)?%$/.test(str)) {
    return 2
  } else {
    return 0
  }
}

function componentStyle(item) {
  if (!props.formOptions.filter || getUnit(item.width) == 1) return {}
  if (getUnit(item.width) == 2) {
    return { width: item.width }
  }
}

function renderTooltipOption(
  { node, option }: { node: any; option: any },
  item,
) {
  return h(NTooltip, null, {
    trigger: () => node,
    default: () => option[item.labelField] || option.label,
  })
}
// 有问题暂时不要
// let newTree = $ref<any>()
// //处理全选选项
// const dealAll = (checked, key, nodeKey) => {
//   if (checked) {
//     props.formOptions.data[key] = props.formOptions.items[key].allData.map(
//       (item) => item[nodeKey],
//     )
//   } else {
//     props.formOptions.data[key].length = 0
//     newTree = new Date().getTime() //强制刷新树
//   }
// }
// //处理全选回显
// const dealPush = (key, nodeKey) => {
//   if (
//     props.formOptions.data[key].length ==
//     props.formOptions.items[key].allData?.length
//   ) {
//     //处理全选选项的回显，目前默认将全选放在第一项

//     props.formOptions.data[key].push(
//       props.formOptions.items[key].options[0][nodeKey],
//     )
//     newTree = new Date().getTime() //强制刷新树
//   }
// }
// const dealPop = (key, nodeKey) => {
//   //处理全选选项的回显，目前默认将全选放在第一项
//   let allIndex = props.formOptions.data[key].indexOf(
//     props.formOptions.items[key].options[0][nodeKey],
//   )
//   if (allIndex > -1) {
//     props.formOptions.data[key].splice(allIndex, 1)
//     newTree = new Date().getTime() //强制刷新树
//   }
// }
/**
 * !todo
 * 树形checkbox交互处理以及校验
    目前默认多选 单选需改造  
 */
const handleCheckChange = (data, checked, key, nodeKey) => {
  //普通多选
  let index = props.formOptions.data[key].indexOf(data[nodeKey])
  if (checked) {
    if (index > -1) return
    props.formOptions.data[key].push(data[nodeKey])
  } else {
    if (index < 0) return
    props.formOptions.data[key].splice(index, 1)
  }
  //校验
  if (props.formOptions.items[key].rule) {
    let treeRef = componentRefArr.find((el) => el.path == key)
    if (treeRef) treeRef.validate()
  }
}
</script>

<style lang="scss" scoped>
:deep() {
  .n-input-number-prefix {
    line-height: 1.15;
  }
  .n-input-number-suffix {
    line-height: 1.15;
  }
  .n-form-item-blank--error {
    .n-upload-trigger--image-card {
      .n-upload-dragger {
        border: 1px solid var(--g-error);
      }
    }
  }
  .n-form-item .n-form-item-blank {
    width: 100%;
  }
  .n-input-word-count {
    bottom: -20px !important;
  }
  .n-form-item.n-form-item--top-labelled .n-form-item-label {
    display: block;
  }
}

.form-view {
  :deep() {
    .n-form-item-label__asterisk {
      display: none !important;
    }
    .form-item-view
      > .n-form-item
      > .n-form-item-label
      > .n-form-item-label__text {
      font-weight: bold;
    }
  }
}
</style>
