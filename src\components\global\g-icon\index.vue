<template>
  <el-image
    v-if="isExternal"
    :src="name"
    class="img-icon"
    :class="[`!${size}`]"
  />
  <svg
    v-else-if="isSvg"
    :style="{ color, fontSize: size + 'px' }"
    :class="['g-icon', name]"
    aria-hidden="true"
  >
    <use :xlink:href="'#' + name" />
  </svg>
  <i
    v-else
    :style="{ color, fontSize: size + 'px' }"
    :class="['ri-' + name, 'ri-icon', name]"
    aria-hidden="true"
  ></i>
</template>

<script>
/**
 * 以http开头的默认为图片
 * 以icon-开头的默认svg,一般指iconfont的svg图标,本地的svg图标以icon-svg-开头
 * 否则使用remix-icon图标
 */
import { isExternal, isSvg } from "@/utils/validate"

export default {
  name: "GIcon",
  props: {
    name: {
      type: String,
      default: "",
      required: true,
    },
    size: {
      type: String,
    },
    color: {
      type: String,
    },
  },
  data() {
    return {}
  },
  computed: {
    isExternal() {
      return isExternal(this.name)
    },

    isSvg() {
      return isSvg(this.name)
    },
  },
}
</script>

<style lang="scss" scoped>
.img-icon {
  display: inline-block;
  width: 2em;
  height: 2em;
  vertical-align: middle;
}

.g-icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  overflow: hidden;
  vertical-align: -0.1em;
  fill: currentColor;
  cursor: pointer;
  transition: all 0.3s;
  &:hover {
    transition: all 0.3s;
    opacity: 0.8;
  }
}

[class*="ri"] {
  display: inline-block;
  font-size: 20px;
  text-align: center;
  cursor: pointer;
  vertical-align: -3.5px;
  transition: all 0.3s;
  &:hover {
    opacity: 0.8;
    transition: all 0.3s;
  }
}
</style>
