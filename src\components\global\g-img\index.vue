<template>
  <div
    class="flex relative justify-center items-center"
    :style="{
      width: width + 'px',
      height: height + 'px',
    }"
  >
    <!-- 加载中 -->
    <div
      v-show="loadFlag"
      :style="{
        width: width + 'px',
        height: height + 'px',
      }"
      class="bg-[#93999f]"
    ></div>

    <n-image
      :src="newImgUrl"
      :width="width"
      :height="height"
      @error="error"
      @load="load"
      :object-fit="objectFit"
      :preview-disabled="previewDisabled"
      :previewed-img-props="previewedImgProps"
      :preview-src="
        optimization
          ? src + `?x-oss-process=image/quality,q_80/format,webp`
          : src
      "
    >
      <template #placeholder> </template>
    </n-image>
    <!-- 加载失败 -->
    <img
      v-if="errorFlag"
      :width="findLongEdge()"
      :height="findLongEdge()"
      :src="$g.tool.getFileUrl('status/error.png')"
    />
  </div>
</template>

<script setup lang="ts" name="GImg">
import type { PropType } from "vue"
/**
 * !默认禁用点击图像预览
 * 增加预览模式 减少图片大小
 *  */
const props = defineProps({
  src: {
    default: "",
  },
  previewDisabled: {
    type: Boolean,
    default: true,
  },
  // 质量
  quality: {
    type: [String, Boolean],
    default: "70",
  },
  // oss图片处理参数 以 / 结尾 https://help.aliyun.com/document_detail/410762.html
  // 示例：resize,w_500/
  ossParams: {
    type: String,
    default: "",
  },
  width: {
    type: [Number, String],
    default: 100,
  },
  height: {
    type: [Number, String],
    default: 100,
  },
  // 视频自己加?x-oss-process=video/snapshot,t_1000,f_jpg,w_800,h_600,m_fast
  optimization: {
    type: Boolean,
    default: true,
  },
  objectFit: {
    type: String as PropType<
      "cover" | "fill" | "none" | "contain" | "scale-down" | undefined
    >,
    default: "cover",
  },
  // 预览图片时 img 元素的属性
  previewedImgProps: {
    type: Object,
    default: () => ({}),
  },

  showPreview: {
    type: Boolean,
    default: true,
  },
})

let errorFlag = $ref(false)
let successFlag = $ref(false)
let loadFlag = $ref(true)

function error() {
  errorFlag = true
  loadFlag = false
}

function load() {
  successFlag = true
  loadFlag = false
}

const newImgUrl = $computed(() => {
  if (typeof props.src !== "string") return props.src

  let url = props.src
  if (props.optimization)
    url += `?x-oss-process=image/${props.ossParams}quality,q_${props.quality}/interlace,1/format,webp`
  return url
})

// 找短边
function findLongEdge() {
  if (props.width > props.height) {
    return props.height
  } else {
    return props.width
  }
}
</script>

<style lang="scss" scoped></style>
