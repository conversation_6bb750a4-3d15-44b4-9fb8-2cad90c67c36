<template>
  <div class="flex relative flex-col justify-center items-center py-20px">
    <div class="relative flex flex-col items-center !h-full">
      <g-lottie :options="lottieOptions" @animCreated="animCreated"> </g-lottie>
      <!-- <div class="loading"></div> -->
    </div>
    <slot>
      <div
        class="loading-text text-14px absolute bottom-[26px] w-100px text-center"
        :style="{ color: textColor }"
      >
        {{ msg }}
      </div>
    </slot>
  </div>
</template>

<script setup>
import feiji from "./feiji.json" //引入下载的动效json

defineProps({
  textColor: {
    type: String,
    default: "#969799",
  },
  msg: {
    type: String,
    default: " 加载中...",
  },
})

const lottieOptions = {
  animationData: feiji,
  // path: 'https://assets9.lottiefiles.com/packages/lf20_fyye8szy.json',
  loop: true,
  renderer: "svg",
  autoplay: true,
  speed: 20,
}

function animCreated(anim) {
  anim.setSpeed(1.4)
}
</script>

<style lang="scss" scoped></style>
