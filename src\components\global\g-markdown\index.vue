<template>
  <div class="mdEditor-container">
    <template v-if="mode === 'edit'">
      <div v-loading="loading" class="w-full h-600px relative loading-box">
        <v-md-editor
          v-model="text"
          :height="height"
          :left-toolbar="editorConfig.leftToolbar"
          :right-toolbar="editorConfig.rightToolbar"
          :toolbar="editorConfig.toolbar"
          :disabled-menus="[]"
          v-bind="$attrs"
          @upload-image="handleUploadImage"
        ></v-md-editor>
        <input type="file" accept="video/*" style="display: none" />
      </div>
      <!-- 插入GPT文字块弹窗 -->
      <g-dialog
        title="插入GPT文字块"
        width="700"
        v-model:show="dialogVisible"
        :autoClose="false"
        @confirm="handleInsert"
      >
        <n-input
          v-model:value="gptText"
          type="textarea"
          :rows="10"
          placeholder="请输入内容"
        />
      </g-dialog>
      <!-- 插入视频链接弹窗 -->
      <g-dialog
        title="插入视频链接"
        width="700"
        v-model:show="videoDialogVisible"
        :autoClose="false"
        @confirm="handleInsertVideo"
      >
        <n-input
          v-model:value="videoText"
          type="textarea"
          :rows="10"
          placeholder="请输入视频链接地址"
        />
      </g-dialog>
      <g-dialog
        title="上传本地视频"
        width="700"
        v-model:show="videoUploadVisible"
        :form-options="formOptions"
        :autoClose="false"
        @confirm="handleUploadVideo"
      >
        <g-form :formOptions="formOptions">
          <template #files>
            <suspense>
              <g-upload
                accept=".wav,.mp4,.webm,.flv,.mov"
                ref="videoUpload"
                v-model:fileList="formOptions.data.files"
                type="drag"
                :max="1"
              >
              </g-upload>
            </suspense>
          </template>
        </g-form>
      </g-dialog>
    </template>
    <v-md-preview
      v-else-if="mode === 'preview'"
      class="custom-md-preview"
      :text="text"
      :height="height"
      v-bind="$attrs"
    ></v-md-preview>
    <!-- 流式预览 -->
    <v-md-preview-stream
      v-else-if="mode === 'stream'"
      class="custom-md-preview"
      :text="text"
      :show-cursor="cursor"
      :height="height"
      v-bind="$attrs"
    >
    </v-md-preview-stream>
  </div>
</template>

<script setup lang="ts">
import AliOss from "@/plugins/OSS"

defineProps({
  mode: {
    type: String,
    default: "edit", // edit-读写 preview-只读
  },
  height: {
    type: String,
    default: "600px",
  },
  cursor: {
    type: Boolean,
    default: false,
  },
})

// 富文本内容
let text = defineModel({ type: String, default: "" })

let aliOss: any = new AliOss({})
// gpt文字块的信息
let gptText = $ref("")
// 上传图片加载状态
let loading = $ref(false)
// 插入GPT文字块的弹窗是否可见
let dialogVisible = $ref(false)
// 插入视频弹框
let videoDialogVisible = $ref<boolean>(false)
// 视频弹框的文字信息
let videoText = $ref("")
// 编辑器配置
const editorConfig = {
  leftToolbar:
    "emoji clear undo redo | h bold italic strikethrough quote underline | ul ol table tip hr | link image video code",
  rightToolbar: "preview sync-scroll fullscreen chatGpt",
  toolbar: {
    chatGpt: {
      icon: "ri-robot-fill text-18px",
      title: "插入GPT文字块",
      action: (editor) => {
        dialogVisible = true
      },
    },
    video: {
      icon: "ri-video-add-fill text-18px",
      title: "插入视频",
      menus: [
        {
          name: "addVideoLink",
          text: "添加视频链接",
          action: (editor) => {
            videoText = ""
            videoDialogVisible = true
          },
        },
        {
          name: "uploadVideo",
          text: "上传本地视频",
          action: (editor) => {
            formOptions.data.files = []
            formOptions.loading = false
            videoUploadVisible = true
          },
        },
      ],
    },
  },
}

const formOptions = $ref<any>({
  ref: "uploadVideo",
  filter: false,
  loading: false,
  items: {
    files: {
      type: "upload",
      label: "",
      span: 20,
      slot: true,
    },
  },
  data: {
    files: [],
  },
})

let videoUpload = $ref()

// 控制视频上传的弹窗
let videoUploadVisible = $ref<boolean>(false)
// 插入视频链接
function handleVideoSrc(videoUrl) {
  let videoSrc = `<video width="320" height="240" src="${videoUrl}" controls></video>`
  return videoSrc
}

// 上传视频确认
function handleUploadVideo() {
  formOptions.loading = true
  if (
    formOptions.data.files &&
    formOptions.data.files?.[0]?.status == "finished"
  ) {
    text.value += handleVideoSrc(formOptions.data.files[0].fullUrl)
  }
  formOptions.loading = false
  videoUploadVisible = false
}

// 插入视频链接
function handleInsertVideo() {
  let res = isVideoLink(videoText)
  if (res) {
    text.value += handleVideoSrc(videoText)
    videoDialogVisible = false
  } else {
    $g.msg("当前视频链接格式有误，请检查链接是否正确！", "error")
  }
}

// 校验是否为视频链接
function isVideoLink(url) {
  const videoExtensions = ["mp4", "webm", "mov", "avi", "mkv", "flv", "wmv"]
  const regex = new RegExp(`^https?:\/\/.*\.(${videoExtensions.join("|")})$`)
  return regex.test(url)
}

// 插入GPT文字块到编辑器中
function handleInsert() {
  let res = replaceSpecialStrings(gptText)
  if (res) {
    text.value += res
  }
  gptText = ""
  dialogVisible = false
}

// 替换GPT中复制出来的公式
function replaceSpecialStrings(mathJaxText) {
  // 匹配行内公式 \( ... \)
  let inlineMathRegex = /\\\(\s*(.+?)\s*\\\)/g
  // 匹配行间公式 \[ ... \]
  let blockMathRegex = /\\\[(.+?)\\\]/gs

  // 将行内公式包裹在 $...$ 中
  mathJaxText = mathJaxText.replace(inlineMathRegex, function (match, p1) {
    return `$${p1}$`
  })

  // 将行间公式包裹在 $$...$$ 中
  mathJaxText = mathJaxText.replace(blockMathRegex, function (match, p1) {
    return `$$${p1}$$`
  })

  return mathJaxText
}

// 上传图片处理
function handleUploadImage(event, insertImage, files) {
  console.log(files)
  // 检查图片格式
  for (const file of files) {
    // 校验文件类型
    let ext = file.name.split(".").pop()
    if ($g.tool.getFileType(ext) !== "img") {
      $g.msg(`${file.name}  文件类型错误，请选择图片文件！`, "error")
      return
    }

    // 校验文件大小
    if (file.size > 20 * 1024 * 1024) {
      $g.msg(`${file.name}  文件大小超过限制，请选择小于 20M 的文件！`, "error")
    }
  }

  loading = true
  let uploadList = files.map((file, index) =>
    aliOss.uploadFile(
      { name: file.name, size: file.size, file, id: index },
      undefined,
    ),
  )

  Promise.all(uploadList)
    .then((res) => {
      console.log(res)
      res.forEach((fileUrl, index) => {
        insertImage({
          url: fileUrl.fullUrl,
          desc: files[index].name,
          width: "auto",
          height: "auto",
        })
      })
    })
    .finally(() => {
      loading = false
    })
}
</script>

<style lang="scss" scoped>
.loading-box {
  :deep() {
    .el-loading-mask {
      .el-loading-spinner {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

:deep() {
  .custom-md-preview .github-markdown-body {
    padding: 5px;
  }
  .katex-block {
    overflow-x: auto;
    overflow-y: hidden;
  }
}
</style>
