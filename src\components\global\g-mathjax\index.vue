<template>
  <div class="mathjax g-mathjax" v-html="text"></div>
</template>

<script setup>
defineProps({
  text: {
    type: String,
    default: "",
  },
})
</script>

<style lang="scss" scoped>
.mathjax {
  width: 100%;
  word-break: normal;
  word-wrap: break-word;
  font-size: 13px;
  font-family: 宋体;
  :deep() {
    img {
      max-width: 680px;
      display: inline;
      vertical-align: middle;
    }

    span[wave] {
      text-decoration-style: wavy;
      text-decoration-line: underline;
      text-underline-position: auto;
      white-space: pre-wrap;
    }

    video {
      max-width: 680px;
      width: 80%;
    }
    table {
      border-collapse: collapse;
      th {
        border: 1px solid #333; /* 设置边框 */
        background: #ccc;
        padding-left: 10px;
      }
      td {
        border: 1px solid #333; /* 设置边框 */
        padding-left: 10px;
      }
    }

    table {
      border-collapse: collapse;
      th {
        border: 1px solid #333; /* 设置边框 */
        background: #ccc;
        padding-left: 10px;
      }
      td {
        border: 1px solid #333; /* 设置边框 */
        padding-left: 10px;
      }
    }

    p {
      margin: 0 0 7px;
    }
  }
}

:global(.MJX-TEX) {
  white-space: normal !important;
  line-height: 6px;
}
</style>
