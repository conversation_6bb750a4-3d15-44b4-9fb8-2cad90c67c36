<template>
  <div class="page-box flex mt-20px text-14px" :class="[alignStyle]">
    <n-pagination
      id="pageRef"
      ref="pageRef"
      v-model:page="pageOptions.page"
      v-model:page-size="pageOptions.page_size"
      :page-count="pageCount"
      show-quick-jumper
      :show-size-picker="false"
      :page-sizes="pageOptions.pageSizes || pageSizes"
      v-bind="$attrs"
      @update:page="changePage"
      @update:page-size="changePage($event, 'size')"
    >
      <template v-if="showPrefix" #prefix>
        <slot name="prefix"> 共 {{ pageOptions.total }} 项 </slot>
      </template>
      <template #suffix="{}">
        <slot name="suffix"> 页 </slot>
      </template>
    </n-pagination>
  </div>
</template>

<script setup lang="ts">
import { getScrollParent } from "seemly"
const props = defineProps({
  pageOptions: {
    type: Object,
    default() {
      return { page: 1, page_size: 10, total: 200 }
    },
  },
  showPrefix: {
    type: <PERSON><PERSON>an,
    default: true,
  },
  align: {
    type: String,
    default: "center",
  },
  autoTop: {
    type: <PERSON><PERSON>an,
    default: true,
  },
})
const emit = defineEmits(["change"])
let pageRef = $ref(null)
const pageSizes = [
  {
    label: "10条/每页",
    value: 10,
  },
  {
    label: "20条/每页",
    value: 20,
  },
  {
    label: "30条/每页",
    value: 30,
  },
]

const pageCount = $computed(() => {
  let { page_size, total } = props.pageOptions
  return parseInt(String((total + page_size - 1) / page_size))
})

const alignStyle = $computed(() => {
  const alignObj = {
    left: "justify-start",
    center: "justify-center",
    right: "justify-end",
  }
  return alignObj[props.align]
})

function changePage(page: number, type?: string) {
  if (type === "size") {
    props.pageOptions.page_size = page
    if (props.pageOptions.page !== 1) props.pageOptions.page = 1
  }
  if (props.autoTop) {
    let scrollEl = getScrollParent(document.querySelector("#pageRef"))
    scrollEl!.scrollTop = 0
  }

  emit("change")
}
</script>

<style lang="scss" scoped>
:deep() {
  .n-input--resizable {
    text-align: center;
    width: 50px !important;
  }
  .n-pagination-item {
    min-width: 28px;
    height: 28px;
    font-size: 14px;
  }
  .n-pagination-prefix,
  .n-pagination-suffix,
  .n-pagination-quick-jumper {
    font-size: 14px;
  }
  .n-input--resizable {
    height: 28px;
    line-height: 28px;
  }
  .n-pagination-item--active {
    background-color: var(--g-primary) !important;
    color: #fff !important;
  }
}
</style>
