<template>
  <div ref="containerRef">
    <div :style="rootStyle" v-bind="$attrs" ref="root">
      <!-- 隐藏区域 -->
      <div v-show="!isSticky" ref="hidden">
        <slot name="hidden"></slot>
      </div>
      <div ref="visibleRoot">
        <slot :isSticky="isSticky"></slot>
      </div>
    </div>
    <div
      v-if="isSticky"
      class="placeholder"
      :style="{
        width: oldStyle.width,
        height: oldStyle.height,
      }"
    ></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  container: string
  top: number
  triggerTop: number
}>()

let root = $ref<any>(null)
let hidden = $ref<any>(null)
let visibleRoot = $ref<any>(null)
let isSticky = $ref(false)
let rootStyle = $ref<any>({})
let oldStyle = $ref<any>({})
let containerRef = $ref<any>(null)
let container
let oldOffsetTop

const resizeObserver = new ResizeObserver((entries) => {
  for (const entry of entries) {
    // 重新初始化
    initSticky()
  }
})

onMounted(() => {
  container = document.querySelector(props.container)
  container?.addEventListener("scroll", handleScroll)

  resizeObserver.observe(containerRef)
  initSticky()
})

async function initSticky() {
  rootStyle = {}
  oldStyle = {}
  isSticky = false
  await nextTick()
  oldOffsetTop = visibleRoot.offsetTop
  const rootOldStyle = window.getComputedStyle(root, null)

  oldStyle = {
    width: rootOldStyle.width,
    height: rootOldStyle.height,
  }

  handleScroll()
}

onBeforeUnmount(() => {
  container?.removeEventListener("scroll", handleScroll)
  resizeObserver.unobserve(containerRef)
})

function handleScroll() {
  if (
    container?.scrollTop >=
    oldOffsetTop - (props?.top ?? 0) - (props?.triggerTop ?? 0)
  ) {
    isSticky = true
    rootStyle = {
      width: oldStyle?.width ?? "auto",
      position: "absolute",
      top: props.top + "px",
      zIndex: "999",
    }
  } else {
    isSticky = false
    rootStyle = {}
  }
}
</script>
