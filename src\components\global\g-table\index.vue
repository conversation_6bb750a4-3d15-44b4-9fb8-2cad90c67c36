<template>
  <div class="relative g-table">
    <!-- 导出工具栏区域||按钮区域 -->
    <div class="flex justify-between">
      <n-space>
        <n-button v-if="showSelectAll" @click="selectAll2">
          {{ isSelectAll ? "取消" : "全选" }}
        </n-button>
        <n-button
          type="primary"
          v-if="showExport"
          :loading="downloadLoading"
          @click="triggerExport"
        >
          导出表格
        </n-button>
        <slot name="header-left">
          <div></div>
        </slot>
      </n-space>
      <n-space>
        <slot name="header-right"></slot>
      </n-space>
    </div>

    <el-table
      id="gTable"
      ref="tableRef"
      :class="[
        tableOptions?.loading && !tableOptions?.data?.length
          ? '!h-[650px]'
          : '',
      ]"
      :data="tableOptions.data"
      :border="true"
      style="width: 100%"
      @sort-change="sortChange"
      class="mt-10px"
      :row-key="tableOptions.key"
      v-loading="tableOptions.loading"
      :element-loading-text="tableOptions?.loadingText || '正在加载中...'"
      element-loading-background="hsla(0,0%,100%,0.85)"
      highlight-current-row
      @select="checkboxChange"
      @select-all="selectAll"
      @selection-change="handleSelectionChange"
      v-bind="$attrs"
    >
      <template v-for="(item, key) in tableOptions.column" :key="key">
        <!-- index-序号  selection-多选-->
        <el-table-column
          :type="item.type"
          :prop="item.prop"
          :label="item.label"
          :index="item.index || 1"
          :show-overflow-tooltip="
            item.tooltip ??
            (item.type == 'selection' || item.prop == 'cz' ? false : true)
          "
          :selectable="item.selectable"
          :sortable="item.sort ? 'custom' : false"
          :formatter="item.formatter || formatEmpty"
          :filters="item.filters"
          :filter-multiple="item.filterMultiple"
          :width="
            ['index', 'selection'].includes(item.type) ? '64px' : item.width
          "
          :min-width="item.minWidth"
          :align="item.align || tableOptions.align || 'center'"
          :reserve-selection="Boolean(tableOptions.key)"
          :fixed="item.fixed"
          :header-align="item.headerAlign"
          :class-name="item.className"
        >
          <template #header="{ column, $index }" v-if="item.headerSlot">
            <slot
              :index="$index"
              :column="column"
              :item="item"
              :name="'header-' + item.prop"
            >
            </slot>
          </template>
          <template #default="{ row, $index, column }">
            <component
              :is="item.render"
              :row="row"
              :index="$index"
              :column="column"
              v-if="item.render"
            >
            </component>
            <slot
              v-else-if="item.slot"
              :row="row"
              :index="$index"
              :column="column"
              :name="item.prop"
              :item="item"
            >
              <div v-html="row[item.prop]"></div>
            </slot>
          </template>
        </el-table-column>
      </template>
      <template #empty>
        <g-empty
          v-show="!tableOptions.loading"
          :description="tableOptions.description"
          :size="emptySize"
        ></g-empty>
      </template>
    </el-table>
    <g-page
      v-if="$g.tool.isTrue(tableOptions.pageOptions)"
      :pageOptions="tableOptions.pageOptions"
      v-bind="$attrs"
      @change="changePage"
    ></g-page>
  </div>
</template>

<script lang="ts">
export default {
  inheritAttrs: false,
}
</script>
<script setup lang="ts" name="g-table">
// exportExcel点击导出事件，需要手动开启loading
const emit = defineEmits(["sortChange", "changePage", "exportExcel"])
interface Props {
  /** 显示全选按钮 */
  showSelectAll: boolean
  /** 显示导出按钮 */
  showExport: boolean
  /* g-table配置 */
  tableOptions: {}
  emptySize: string
}

// !需要手动开启tableOptions的 loading:boolean
const props = defineProps({
  tableOptions: {
    type: Object,
    default: () => {
      return {
        // !ref属于el-table ref,另额外添加下面方法：
        // !exportExcel-导出excel方法
        ref: null,
        // 唯一id,多选的话必填
        key: "",
        loading: false,
        // 多选获取数据接口
        export: "",
        // 多选选中的数据
        chooseTableIds: [],
        pageOptions: {},
        column: [],
        data: [],
      }
    },
  },
  showHeader: {
    type: Boolean,
    default: false,
  },
  showSelectAll: {
    type: Boolean,
    default: false,
  },
  showExport: {
    type: Boolean,
    default: false,
  },
  emptySize: {
    type: Number,
  },
})
const tableRef: any = $ref(null)
let isSelectAll = $ref(false)
let downloadLoading = $ref(false)
onMounted(() => {
  tableRef.exportExcel = exportExcel
  tableRef.tableBackfill = tableBackfill
  props.tableOptions.ref = tableRef
})

watch(
  () => props.tableOptions.data,
  async (newVal) => {
    if (newVal.length != 0) {
      await nextTick()
      tableBackfill()
    }
  },
)

/* 排序发生变化触发事件 */
function sortChange({ column, order, prop }) {
  const orderMap = {
    descending: "desc",
    ascending: "asc",
  }
  emit("sortChange", {
    column,
    order: orderMap[order],
    prop,
  })
}

function formatEmpty(row, column, cellValue) {
  let value = cellValue ?? "-"
  value = cellValue === "" ? "-" : value
  return value
}

function changePage() {
  emit("changePage")
}

/* 如果有回填需求，外部tableOptions.chooseTableIds = [{[tableOptions.key]:id}]或者[1,2,3] */
function tableBackfill() {
  props.tableOptions.data.forEach((e1, index) => {
    let flag = $g._.findIndex(props.tableOptions.chooseTableIds, (e2) => {
      return $g.tool.typeOf(e2) == "object"
        ? e2[props.tableOptions.key] == e1[props.tableOptions.key]
        : e2 == e1[props.tableOptions.key]
    })
    if (flag >= 0 && props.tableOptions.key) {
      props.tableOptions.chooseTableIds[flag] = e1
      tableRef.toggleRowSelection(e1, true)
    }
  })
}

function handleSelectionChange(val) {
  // 先注释 有逻辑冲突
  // console.log("🎃 val ==> ", val)
  // console.log(
  //   "🎃 props.tableOptions.chooseTableIds==> ",
  //   props.tableOptions.chooseTableIds,
  // )
  // props.tableOptions.chooseTableIds = val
}

function selectAll2() {
  if (isSelectAll) {
    props.tableOptions.chooseTableIds =
      props.tableOptions.chooseTableIds.filter((e) => {
        return !props.tableOptions.data
          .map((e) => e[props.tableOptions.key])
          .includes(e[props.tableOptions.key])
      })
  } else {
    props.tableOptions.chooseTableIds = $g._.uniqBy(
      [...props.tableOptions.chooseTableIds, ...props.tableOptions.data],
      props.tableOptions.key,
    )
    props.tableOptions.data.forEach((e) => {
      tableRef.toggleRowSelection(e, true)
    })
  }
  isSelectAll = !isSelectAll
}

function selectAll(val) {
  let flag = false
  if ($g.tool.isTrue(val)) {
    flag = props.tableOptions.data.some((e) => {
      return !props.tableOptions.chooseTableIds.some((e2) => {
        return e[props.tableOptions.key] == e2[props.tableOptions.key]
      })
    })
  }
  isSelectAll = !flag
  selectAll2()
}

function checkboxChange(rows, row) {
  try {
    let selected = rows.length && rows.indexOf(row) !== -1
    if (selected) {
      props.tableOptions.chooseTableIds.push(row)
    } else {
      props.tableOptions.chooseTableIds =
        props.tableOptions.chooseTableIds.filter(
          (item) =>
            item[props.tableOptions.key] !== row[props.tableOptions.key],
        )
    }
  } catch (error) {
    console.error(new Error("检查tableOptions.chooseTableIds是否存在且为数组"))
  }
}

function triggerExport() {
  if (!props.tableOptions.chooseTableIds.length) {
    $g.msg("请选择需要导出的数据", "warning")
    return
  }
  emit("exportExcel", { data: props.tableOptions.chooseTableIds })
}

function exportExcel({
  data,
  fileName = "",
  autoWidth = true,
  bookType,
  filterArr,
  multiHeader = [],
  merges = [],
}) {
  downloadLoading = true
  import("@/plugins/export2Excel").then((excel) => {
    filterArr = filterArr || ["操作", "序号"]
    const tHeader: any[] = []
    const propArr: any[] = []
    props.tableOptions.column.forEach((e) => {
      if (e.label && !filterArr.includes(e.label)) {
        tHeader.push(e.label)
        propArr.push(e.prop)
        if (e.formatter) {
          if (e.formatter) {
            data.forEach((e2, index) => {
              e2[e.prop] = e.formatter(data[index])
            })
          }
        }
      }
    })
    data = formatJson(propArr, data)
    console.log("⚡  ==> ", {
      header: tHeader,
      data,
      filename: fileName
        ? `${fileName} ${$g.dayjs().format("YYYY-MM-DD")}`
        : `Excel文件 ${$g.dayjs().format("YYYY-MM-DD")}`,
      autoWidth: true,
      bookType: bookType || "xlsx",
    })
    excel.export_json_to_excel({
      header: tHeader,
      data,
      filename: fileName
        ? `${fileName} ${$g.dayjs().format("YYYY-MM-DD")}`
        : `Excel文件 ${$g.dayjs().format("YYYY-MM-DD")}`,
      autoWidth: true,
      bookType: bookType || "xlsx",
    })

    downloadLoading = false
  })
}

function formatJson(propArr, jsonData) {
  return jsonData.map((v) =>
    propArr.map((j) => {
      return v[j]
    }),
  )
}

async function onError({ reason }) {
  props.tableOptions.loading = false
}

window.addEventListener("unhandledrejection", onError, true)

onBeforeUnmount(() => {
  window.removeEventListener("unhandledrejection", onError)
})

onMounted(() => {})
</script>

<style lang="scss" scoped>
:deep() {
  thead {
    height: 48px;
    th {
      background-color: #f8f8f9 !important;
      color: #606266;
    }
  }
  tbody {
    .el-table__cell {
      height: 65px;
    }
  }
  .el-table .cell.el-tooltip {
    white-space: pre-wrap;
  }
}

.el-table {
  --el-table-border-color: #e8edf8;
}
</style>
