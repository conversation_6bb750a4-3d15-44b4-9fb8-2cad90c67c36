<template>
  <div class="text-tooltip">
    <el-tooltip
      class="text-truncate"
      effect="dark"
      :disabled="isShowTooltip"
      :content="content"
      placement="top"
      v-bind="$attrs"
    >
      <p class="over-flow" :class="className" @mouseover="onMouseOver(refName)">
        <span :ref="refName" class="ellipsis">
          <g-mathjax :text="content || ''" />
        </span>
      </p>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: "textTooltip",
  props: {
    // 显示的文字内容
    content: {
      type: String,
      default: () => {
        return ""
      },
    },
    // 外层框的样式，在传入的这个类名中设置文字显示的宽度
    className: {
      type: String,
      default: () => {
        return ""
      },
    },
    // 为页面文字标识（如在同一页面中调用多次组件，此参数不可重复）
    refName: {
      type: String,
      default: () => {
        return ""
      },
    },
  },
  data() {
    return {
      isShowTooltip: true,
    }
  },
  mounted() {
    //判断宽度是否需要出现toooltip
    this.checkTooltipVisibility()
    //优化resize
    window.addEventListener("resize", this.checkTooltipVisibility)
    this.$g.tool.renderMathjax()
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.checkTooltipVisibility)
  },
  methods: {
    onMouseOver(refName) {
      //这里的refName是子组件的refName不是从父组件传来的，所以需要在@mouseover里传递，使得页面能找到这个dom
      let parentWidth = this.$refs[refName]?.parentNode?.offsetWidth
      let contentWidth = this.$refs[refName]?.offsetWidth
      // 判断是否开启tooltip功能
      if (contentWidth > parentWidth) {
        this.isShowTooltip = false
      } else {
        this.isShowTooltip = true
      }
    },
    checkTooltipVisibility() {
      const spanEl = this.$refs[this.refName]
      if (spanEl) {
        const parentWidth = spanEl.parentNode.offsetWidth
        const contentWidth = spanEl.offsetWidth
        this.isShowTooltip = contentWidth > parentWidth
      }
    },
  },
  watch: {
    content() {
      this.$g.tool.renderMathjax()
    },
  },
}
</script>

<style lang="scss" scoped>
.over-flow {
  overflow: hidden;
  white-space: nowrap;
  width: 100%; /* 设置父元素的宽度 */
  text-overflow: ellipsis; /* 添加省略号 */
}
.ellipsis {
  width: 100%; /* 设置子元素的宽度，确保父元素的宽度能容纳子元素*/
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 溢出隐藏 */
  text-overflow: ellipsis; /* 添加省略号 */
}
//这里还可以将外层的className对应的class写在这里
p {
  margin: 0;
}
</style>
