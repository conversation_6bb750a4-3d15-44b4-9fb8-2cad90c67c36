<template>
  <n-space vertical>
    <el-input
      v-model="filterText"
      v-if="$attrs.filterable"
      :placeholder="placeholder"
    />
    <n-scrollbar :style="{ maxHeight: maxHeight + 'px' }">
      <el-tree
        :data="treeData"
        node-key="id"
        empty-text="暂无数据"
        :props="defaultProps"
        :indent="treeIndent"
        ref="treeRef"
        v-bind="$attrs"
        :filter-node-method="filterNode"
      ></el-tree>
    </n-scrollbar>
  </n-space>
</template>
<script setup lang="ts">
interface Tree {
  [key: string]: any
}
const props = defineProps({
  treeData: {
    type: Array,
    default: () => [],
  },
  defaultProps: {
    type: Object,
    default: () => {
      return {
        children: "children",
        label: "label",
      }
    },
  },
  customNodeClass: {
    type: Function,
    default: () => {},
  },
  treeIndent: {
    type: Number,
    default: 18,
  },
  placeholder: {
    type: String,
    default: "",
  },
  maxHeight: {
    type: Number,
    default: null,
  },
})
const filterText = ref("")
const treeRef = ref<any>()
watch(filterText, (val) => {
  treeRef.value!.filter(val)
})
const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data?.[props.defaultProps.label]?.includes(value)
}
</script>

<style lang="scss" scoped>
:deep() {
  .element-tree-node-label-wrapper {
    display: flex;
    justify-content: space-between;
  }
}
</style>
