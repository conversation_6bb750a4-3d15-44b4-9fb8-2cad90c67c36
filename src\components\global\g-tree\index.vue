<template>
  <div
    :class="[
      'g-tree',
      { 'highlight-check': highlightCheck, 'hidden-check-box': !multiple },
    ]"
  >
    <el-tree
      :ref="treeName"
      :node-key="nodeKey"
      :expand-on-click-node="expandOnClickNode"
      :data="treeData"
      :style="{ width, border: border ? `1px solid #DCDFE6` : 'none' }"
      @node-click="handleNodeClick"
      @check="setSelectedNode"
      :check-strictly="!multiple"
      show-checkbox
      :highlight-current="highlightCurrent"
      :check-on-click-node="checkOnClickNode"
      empty-text="暂无数据"
      v-bind="$attrs"
      :filter-node-method="filterNode"
    >
      <template #default="{ node, data }">
        <div class="custom-tree-node">
          <element-tree-line v-if="treeLine" :node="node" :indent="18">
            <slot name="body" :node="node" :data="data">
              <span>{{ node.label }}</span>
            </slot>
          </element-tree-line>
          <template v-else>
            <slot name="body" :node="node" :data="data">
              <span>{{ node.label }}</span>
            </slot>
          </template>
        </div>
      </template>
    </el-tree>
  </div>
</template>

<script>
import { getElementLabelLine } from "element-tree-line"
export default {
  name: "GTree",
  inheritAttrs: false,
  components: {
    ElementTreeLine: getElementLabelLine(h),
  },
  props: {
    treeName: {
      type: String,
      default: "tree",
    },
    width: {
      type: String,
      default: "100%",
    },
    treeData: {
      type: Array,
      default: () => {
        return []
      },
    },
    border: {
      type: Boolean,
      default: true,
    },
    // 多选
    multiple: {
      type: Boolean,
      default: false,
    },
    max: {
      type: [Number, String],
      default: "",
    },
    nodeKey: {
      type: String,
      default: "id",
    },
    // 是否点击节点选中复选框
    checkOnClickNode: {
      type: Boolean,
      default: true,
    },
    // 是否点击节点展开子节点
    expandOnClickNode: {
      type: Boolean,
      default: false,
    },
    // 是否高亮当前行
    highlightCurrent: {
      type: Boolean,
      default: false,
    },
    highlightCheck: {
      type: Boolean, // 是否开启勾选高亮整行
      default: true,
    },
    // 节点虚线
    treeLine: {
      type: Boolean,
      default: true,
    },
    defaultProps: {
      type: Object,
      default() {
        return {
          children: "children",
          label: "name",
        }
      },
    },
  },
  data() {
    return {
      filterText: "",
      treeProps: {
        showLabelLine: true,
      },
    }
  },
  watch: {
    filterText(val) {
      this.$refs[this.treeName].filter(val)
    },
  },
  mounted() {},
  methods: {
    // 节点被点击时的回调
    handleNodeClick(data, node) {
      this.$emit("handleNodeClick", data, node)
    },
    // 外部调用筛选方法
    getFilterNode(val) {
      this.$refs[this.treeName].filter(val)
    },
    // 搜索筛选
    filterNode(value, node) {
      if (!value) return true

      return node[this.defaultProps.label].includes(value)
    },
    // 单选
    setSelectedNode(data, obj) {
      if (this.multiple) {
        const length = obj.checkedKeys.length
        if (this.max && length > this.max) {
          this._.remove(obj.checkedKeys, (n, index) => {
            return n == data[this.nodeKey]
          })
          $g.msg(`最多选择${this.max}个`, "warning")
          this.$refs[this.treeName].setCheckedKeys(obj.checkedKeys)
        }
        this.$emit("radioChange", { data, obj })
        return
      }
      let checked = false
      if (obj.checkedNodes.length) {
        this.setCheckedNodes([data])
        checked = true
      }
      this.$emit("radioChange", { data, checked })
    },
    // 通过key设置回填 默认id  key为数组 里面多个字符串或数字
    setCheckedKeys(key) {
      this.$refs[this.treeName].setCheckedKeys(key)
    },
    // 获取选中的节点数据 leafOnly,includeHalfChecked半选 halfCheckedKeys 返回半选状态
    getCheckedData(
      leafOnly = true,
      includeHalfChecked = false,
      halfCheckedKeys = false,
    ) {
      let keys = this.$refs[this.treeName].getCheckedKeys(
        leafOnly,
        includeHalfChecked,
      )

      const nodes = this.$refs[this.treeName].getCheckedNodes(
        leafOnly,
        includeHalfChecked,
      )
      // 半选状态父级的值
      let semiSelective = this.$refs[this.treeName].getHalfCheckedKeys()
      if (halfCheckedKeys) {
        keys = [...new Set([...semiSelective, ...keys])]
      }
      return { keys, nodes }
    },
    // 获取半选状态
    getHalfChecked() {
      const halfKeys = this.$refs[this.treeName].getHalfCheckedKeys()

      const halfNodes = this.$refs[this.treeName].getHalfCheckedNodes()
      // 半选状态父级的值
      /*   let semiSelective = this.$refs[this.treeName].getHalfCheckedKeys()
        keys = [...new Set([...keys, ...semiSelective])] */
      return { halfKeys, halfNodes }
    },
    // 通过node设置  node为数组 数组里是多个对象
    setCheckedNodes(node) {
      this.$refs[this.treeName].setCheckedNodes(node)
    },
  },
}
</script>

<style lang="scss" scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.highlight-check {
  :deep() {
    .is-checked > .el-tree-node__content {
      color: #1ea0f0;
      background-color: #f1f8ff;
      .el-icon {
        color: #1ea0f0;
      }
      .ri-icon {
        color: #1ea0f0 !important;
      }
    }
    .el-tree-node__expand-icon.is-leaf {
      color: transparent !important;
    }
  }
}
.hidden-check-box {
  :deep() {
    .el-checkbox {
      display: none;
    }
  }
}

:deep() {
  // 不可全选样式
  .el-tree-node {
    .el-checkbox__input.is-disabled {
      display: none;
    }
  }
  .el-tree-node__content {
    .el-tree-node__expand-icon {
      font-size: 16px;
    }
  }
}
</style>
