<template>
  <g-dialog
    title="公式图片转译"
    v-model:show="showDialog"
    width="1100"
    @confirm="questionInfoConfirm"
  >
    <div>
      <div class="flex mb-[10px]">
        <div class="w-[80px] text-right">原始内容：</div>
        <g-mathjax
          :text="initQuestion ?? ''"
          class="max-h-[120px] overflow-auto"
        />
      </div>
      <div class="flex">
        <div class="w-[80px] text-right">替换后：</div>
        <g-mathjax
          v-if="tableOptions.pageOptions.total > 0"
          :text="overViewQuestion"
          class="max-h-[120px] overflow-auto"
        />
        <div class="text-[#999]" v-else>暂无内容</div>
      </div>
      <g-table
        :tableOptions="tableOptions"
        class="max-h-[400px] overflow-auto"
        @changePage="changePage"
      >
        <template #imageUrl="{ row }">
          <n-image width="100" :src="row?.imageUrl ?? ''" />
        </template>
        <template #lastFormula="{ row }">
          <g-mathjax :text="formatValue(row?.lastTimeFormula)" />
        </template>
        <template #formula="{ row }">
          <g-mathjax :text="formatValue(row?.formula)" />
        </template>
        <template #cz="{ row, index }">
          <div class="flex gap-[5px] flex-wrap justify-center">
            <n-button type="primary" text @click="againIdentify(row)">
              再次识别
            </n-button>
            <n-button type="primary" text @click="editFormula(row, index)">
              编辑识别结果
            </n-button>
            <n-button type="primary" text @click="replaceImag2Formula(row)">
              替换
            </n-button>
          </div>
        </template>
      </g-table>
      <g-dialog
        title="编辑"
        v-model:show="innerShowDialog"
        width="500"
        @confirm="editFormulaConfirm"
      >
        <div class="flex items-center">
          <div
            class="flex flex-shrink-0 items-center whitespace-nowrap text-14px mr-10px"
          >
            <span class="text-danger mr-5px">*</span>公式内容:
          </div>
          <n-input
            v-model:value="textContent"
            type="textarea"
            placeholder="请输入公式内容"
            :on-input="mathRender"
            :rows="5"
          ></n-input>
        </div>
        <div class="p-10px mt-[10px] max-h-[200px] overflow-auto">
          <el-scrollbar>
            <g-mathjax :text="showContent" />
          </el-scrollbar>
        </div>
      </g-dialog>
    </div>
  </g-dialog>
</template>

<script setup lang="ts">
import { getFormulaList } from "@/api/bookMgt"
import { getFormulaOcr, getEditFormula } from "@/api/resourceMgt"
import { replaceNthOccurrence2, countOccurrences2, formatValue } from "./tools"

let props = defineProps({
  Img2FormulaInfo: {
    type: Object,
    default: () => {},
  },
})
let showDialog = defineModel<any>("show")
let textContent = $ref("")
let updateFormulaImg = $ref<string>("") //编辑公式需要的图片地址
let innerShowDialog = $ref<boolean>(false)
let initQuestion = $ref<string>("") //原题
let overViewQuestion = $ref<string>("") //预览题
let editFormulaIndex = $ref(-1)

const tableOptions = $ref<any>({
  ref: null as any,
  loading: true,
  loadingText: "公式识别中...",
  column: [
    {
      prop: "index",
      label: "序号",
      width: 60,
      formatter: (row) => {
        return row.index + 1
      },
    },
    {
      prop: "imageUrl",
      label: "原图片",
      slot: true,
    },

    // {
    //   prop: "lastFormula",
    //   label: "转公式前渲染效果",
    //   slot: true,
    // },
    {
      prop: "formula",
      label: "转公式后渲染效果",
      slot: true,
    },
    // {
    //   prop: "replaceTimes",
    //   label: "替换次数",
    // },
    {
      prop: "cz",
      label: "操作",
      slot: true,
      width: 230,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 5,
    total: 0,
  },
})
let allTableData = $ref<any>([])

let showContent = $computed(() => (textContent ? `$$ ${textContent} $$` : ""))

const emit = defineEmits(["confirm"])
function questionInfoConfirm() {
  emit("confirm", overViewQuestion)
}
function mathRender() {
  $g.tool.renderMathjax()
}
//公式列表
async function getFormulaListApi() {
  try {
    tableOptions.loading = true
    let res = await getFormulaList({
      questionId: props?.Img2FormulaInfo?.questionId,
      text: props?.Img2FormulaInfo?.content,
    })

    allTableData =
      res.map((item) => {
        return {
          ...item,
          //   isEdit: false, //是否修改
          replaceTimes: 0, //
          editStack: [], //
          lastTimeReplace: "",
          isReplace: false,
          //   lastTimeFormula: "",
          //   replaceTimes: 0,
          //   editTimes: 0,
          //   lastTimeFormulaStack: [],
        }
      }) ?? []
    tableOptions.pageOptions.total = allTableData?.length ?? 0
    changePage()
  } finally {
    tableOptions.loading = false
  }
}
//分页
function changePage() {
  const startIndex =
    (tableOptions.pageOptions.page - 1) * tableOptions.pageOptions?.page_size
  const endIndex = startIndex + tableOptions.pageOptions?.page_size
  tableOptions.data = allTableData.slice(startIndex, endIndex)
  mathRender()
}
/* 公式识别 */
function againIdentify(row) {
  $g.confirm({ content: "是否再次识别该图片？" }).then(async () => {
    try {
      tableOptions.loading = true
      let res = await getFormulaOcr({
        imageUrl: row.imageUrl,
      })
      if (row.isReplace) {
        row.lastTimeFormula = row.formula
      }
      row.formula = res?.formula
      //   row.replaceTimes = 0
      row.editStack.push(res?.formula)
      row.isReplace = false
      $g.msg("再次识别完成", "success")
      tableOptions.loading = false
      mathRender()
    } catch (error) {
      tableOptions.loading = false
    }
  })
}

//替换公式
const replaceImag2Formula = $g._.debounce(async (row) => {
  if (row.isReplace) {
    $g.msg("公式已替换", "warning")
    return
  }
  let tempAllList = $g._.cloneDeep(allTableData)
  let searchVal, newVal, compliantList, formulaCount, updateIndex
  console.log("⚡ tempAllList ==> ", tempAllList)
  //公式未编辑
  if (!row.editStack.length) {
    newVal = `$${row?.formula}$` //替换的公式（代码）
    searchVal = `${row?.imgHtmlTag}` //被替换的公式（图片）
    formulaCount = countOccurrences2(overViewQuestion, searchVal) //被替换的在公式中出现的次数
    // console.log("出现次数", formulaCount, "替换的", searchVal)
    if (row.replaceTimes == 0) {
      console.log("⚡ 1没编辑没替换==> ", formulaCount)
      if (formulaCount == 1) {
        overViewQuestion = overViewQuestion.replace(searchVal, newVal)
      } else {
        compliantList = tempAllList.filter(
          (it) => it.imgHtmlTag == searchVal && !it.isReplace,
        )
        console.log("⚡   多个==> ", updateIndex)
        updateIndex = compliantList.findIndex((it) => it.index == row.index)
        if (updateIndex != -1) {
          overViewQuestion = replaceNthOccurrence2(
            overViewQuestion,
            searchVal,
            newVal,
            updateIndex + 1,
          )
        }
      }
      row.isReplace = true
      row.replaceTimes++
      row.lastTimeReplace = newVal
    }
  } else {
    // 公式已经编辑
    if (row.replaceTimes == 0) {
      newVal = `$${row?.formula}$` //替换的公式（代码）
      searchVal = `${row?.imgHtmlTag}` //被替换的公式（图片）
      formulaCount = countOccurrences2(overViewQuestion, searchVal) //被替换的在公式中出现的次数
      console.log("⚡  2已经编辑未替换==> ", formulaCount)
      if (formulaCount == 1) {
        overViewQuestion = overViewQuestion.replace(searchVal, newVal)
      } else {
        compliantList = tempAllList.filter(
          (it) => it.imgHtmlTag == searchVal && !it.isReplace,
        )
        updateIndex = compliantList.findIndex((it) => it.index == row.index)
        console.log(
          "compliantList 多个  ==> ",
          compliantList,
          "修改第",
          updateIndex,
        )
        overViewQuestion = replaceNthOccurrence2(
          overViewQuestion,
          searchVal,
          newVal,
          updateIndex + 1,
        )
      }
      row.isReplace = true
      row.replaceTimes++
      row.lastTimeReplace = newVal
    } else {
      newVal = `$${row.editStack.slice(-1)[0]}$` //替换的公式（代码）
      searchVal = `${row.lastTimeReplace}` //被替换的公式（图片）
      formulaCount = countOccurrences2(overViewQuestion, searchVal) //被替换的在公式中出现的次数
      console.log("⚡  3已编辑已替换==> ", formulaCount)
      //   console.log("⚡  ==> ", row.lastTimeReplace)
      //   console.log("⚡  ==> ", row.editStack.slice(-1)[0])
      if (formulaCount == 1) {
        overViewQuestion = overViewQuestion.replace(searchVal, newVal)
      } else {
        compliantList = tempAllList.filter(
          (it) => it.lastTimeReplace == searchVal,
        )
        updateIndex = compliantList.findIndex((it) => it.index == row.index)
        console.log(
          "compliantList 多个  ==> ",
          compliantList,
          "修改第",
          updateIndex,
        )
      }
      overViewQuestion = replaceNthOccurrence2(
        overViewQuestion,
        searchVal,
        newVal,
        updateIndex + 1,
      )
      row.isReplace = true
      row.replaceTimes++
      row.lastTimeReplace = newVal
    }
  }
  $g.msg("公式替换成功")
  mathRender()
}, 500)

function editFormula(row, index) {
  innerShowDialog = true
  textContent = row?.formula ?? ""
  updateFormulaImg = row?.imageUrl ?? ""
  editFormulaIndex = index
}
//公式编辑弹框确认
async function editFormulaConfirm() {
  if (!textContent) {
    $g.msg("公式内容不能为空", "warning")
    return
  }
  //学科网编辑此题无questionId，需要手动更新外面的列表数据
  if (!props?.Img2FormulaInfo?.questionId) {
    let index = editFormulaIndex
    tableOptions.data[index].editStack.push(textContent)
    tableOptions.data[index].isReplace = false
    tableOptions.data[index].formula = textContent?.trim()
    changePage()
    $g.msg("公式内容保存成功")
    mathRender()
    return
  }
  try {
    getEditFormula({
      questionId: props?.Img2FormulaInfo?.questionId,
      imageUrl: updateFormulaImg,
      formula: textContent?.trim(),
    }).then(() => {
      let index = editFormulaIndex
      tableOptions.data[index].editStack.push(textContent)
      tableOptions.data[index].isReplace = false
      tableOptions.data[index].formula = textContent?.trim()
      $g.msg("公式内容保存成功")
      mathRender()
      innerShowDialog = false
    })
  } catch (error) {
    console.log(error)
  }
}

watch(
  () => innerShowDialog,
  (val) => {
    if (val) {
      mathRender()
    }
  },
)
watch(
  () => overViewQuestion,
  (val) => {
    if (val) {
      mathRender()
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
onMounted(async () => {
  initQuestion = props?.Img2FormulaInfo?.content ?? ""
  overViewQuestion = props?.Img2FormulaInfo?.content ?? ""
  await getFormulaListApi()
  mathRender()
})
</script>

<style lang="scss" scoped></style>
