<template>
  <g-dialog
    title="上传音频"
    :formOptions="formOptions"
    v-model:show="showDialog"
    @confirm="confirm"
  >
    <g-form :formOptions="formOptions">
      <!-- 讲师照片 -->
      <template #files>
        <g-upload
          v-model:fileList="formOptions.data.files"
          multiple
          type="drag"
          accept=".mp3,.wav"
          tips="支持.mp3,.wav格式文件，单文件最大100MB"
          :fileConfig="{
            audio: {
              maxSize: 100 * 1024 * 1024,
              title: '音频',
            },
          }"
        ></g-upload>
      </template>
    </g-form>
  </g-dialog>
</template>

<script setup lang="ts">
let showDialog = defineModel<any>("show")
let formOptions = $ref<any>({
  ref: null as any,
  loading: false,
  items: {
    files: {
      type: "upload",
      showLabel: false,
      slot: true,
    },
  },
  data: {
    files: [],
  },
})
const emit = defineEmits(["confirm"])
function confirm() {
  try {
    emit("confirm", formOptions.data)
  } catch (err) {
    console.log(err)
  } finally {
    formOptions.loading = false
  }
}
</script>

<style lang="scss" scoped></style>
