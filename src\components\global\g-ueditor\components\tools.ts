//替换对应位置的公式
export function replaceNthOccurrence2(
  str,
  target,
  replacement,
  targetCount = 2,
) {
  let count = 0
  let result = ""
  let startIndex = 0
  while (startIndex < str.length) {
    const index = str.indexOf(target, startIndex)
    if (index === -1) {
      // 如果没有更多匹配项，添加剩余部分并退出循环
      result += str.substring(startIndex)
      break
    }
    // 增加匹配计数
    count++
    // 添加从当前起始位置到匹配位置的部分
    result += str.substring(startIndex, index)
    if (count === targetCount) {
      // 如果是第二次匹配，添加替换字符串
      result += replacement
    } else {
      // 否则添加原始目标字符串
      result += target
    }
    // 更新起始位置，跳过已处理的目标字符串
    startIndex = index + target.length
  }
  return result
}

//判断出现的次数
export function countOccurrences2(str, target) {
  let count = 0
  let startIndex = 0

  while (startIndex < str.length) {
    const index = str.indexOf(target, startIndex)
    if (index === -1) {
      break
    }
    count++
    startIndex = index + target.length
  }

  return count
}

//格式化
export function formatValue(text) {
  return text ? `$$ ${text} $$` : ""
}
