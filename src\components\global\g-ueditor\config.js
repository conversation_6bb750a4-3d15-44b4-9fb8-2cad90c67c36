export default {
  toolbars: [
    [
      "fullscreen",
      "source",
      "|",
      "undo", //撤销
      "redo", //重做
      "|",
      // 'forecolor', //字体颜色
      // 'backcolor', //背景色
      "bold",
      "italic",
      "underline",
      "strikethrough", //删除线
      "fontborder", //字符边框
      "emphasis-button", //强调
      "|",
      "fontsize",
      "fontfamily",
      "|",
      "kityformula",
      // 'preview',
      // '|',
      "indent", //首行缩进
      "justifyleft",
      "justifycenter",
      "justifyright",
      "justifyjustify",
      "|",
      "touppercase", //字母大写
      "tolowercase", //字母小写
      "|",
      "removeformat", //清除格式
      "insertimage", //多图上传
      "|",
      "inserttable", //表格添加
    ],
  ],
  fontfamily: [
    { label: "", name: "songti", val: "宋体,SimSun,Songti SC" },
    { label: "", name: "kaiti", val: "楷体,楷体_GB2312, <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>" },
    { label: "", name: "ya<PERSON><PERSON>", val: "微软雅黑,Microsoft YaHei" },
    { label: "", name: "heiti", val: "黑体, SimHei,Heiti SC" },
    { label: "", name: "lishu", val: "隶书, SimLi" },
    // { label: '', name: 'andaleMono', val: 'andale mono' },
    { label: "", name: "arial", val: "arial, helvetica,sans-serif" },
    // { label: '', name: 'arialBlack', val: 'arial black,avant garde' },
    // { label: '', name: 'comicSansMs', val: 'comic sans ms' },
    // { label: '', name: 'impact', val: 'impact,chicago' },
    { label: "", name: "timesNewRoman", val: "times new roman" },
  ],
  // 编辑器不自动被内容撑高
  autoHeightEnabled: true,
  elementPathEnabled: false, //是否启用元素路径，默认是true显示
  // 初始容器高度
  initialFrameHeight: 150,
  // 初始容器宽度
  initialFrameWidth: "100%",
  // UEditor 资源文件的存放路径，如果你使用的是 vue-cli 生成的项目，通常不需要设置该选项，vue-ueditor-wrap 会自动处理常见的情况，如果需要特殊配置，参考下方的常见问题2
  UEDITOR_HOME_URL:
    import.meta.env.VITE_APP_ENV == "development" ? "/ueditor/" : "./ueditor/",
  // 是否自动聚焦
  focus: false,
  autoFloatEnabled: false,
  // 是否只读
  readonly: false,
  // serverUrl: `${process.env.VUE_APP_BASE_API}/oss/upload?application=question-img`,
  serverUrl: `${
    import.meta.env.VITE_APP_UEDITOR_URL
  }/api/oss/upload?application=question-img`,
  enableAutoSave: false,
  retainOnlyLabelPasted: true,
  pasteplain: true, //是否默认为纯文本粘贴。false为不使用纯文本粘贴，true为使用纯文本粘贴
  //  纯文本粘贴模式下的过滤规则
  filterTxtRules: (function () {
    function transP(node) {
      node.tagName = ""
      node.setStyle()
      node.setAttr("style", "")
    }
    return {
      //直接删除及其字节点内容
      "-": "script style object iframe embed input select",
      p: function (node) {
        node.setAttr("style", "")
        node.setStyle("font-family", "宋体,SimSun,Songti SC")
      },
      br: { $: {} },
      div: function (node) {
        node.setAttr("style", "")
        node.setStyle("font-family", "宋体,SimSun,Songti SC")
      },
      li: "",
      img: "img",
      "&nbsp;": { $: {} },
      caption: transP,
      table: function (node) {},
      tbody: function (node) {},
      th: function (node) {},
      tr: function (node) {},
      td: function (node) {},
      h1: transP,
      h2: transP,
      h3: transP,
      h4: transP,
      h5: transP,
      h6: transP,
      // td: function (node) {
      //   //没有内容的td直接删掉
      //   var txt = !!node.innerText()
      //   if (txt) {
      //     node.parentNode.insertAfter(UE.uNode.createText('    '), node)
      //   }
      //   node.parentNode.removeChild(node, node.innerText())
      // },
    }
  })(),
}
