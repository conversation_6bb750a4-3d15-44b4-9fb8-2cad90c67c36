<template>
  <div>
    <vue-ueditor-wrap
      :config="myConfig"
      :editor-id="id"
      @beforeInit="addCustomButtom"
      @ready="ready"
      v-bind="$attrs"
    ></vue-ueditor-wrap>
    <PreviewDialog
      :content="content"
      v-model:show="showPreview"
    ></PreviewDialog>
    <UploadDialog v-model:show="showUpload" @confirm="handleUploadConfirm" />
    <Img2FormulaDialog
      v-if="showImg2Formula"
      v-model:show="showImg2Formula"
      :Img2FormulaInfo="Img2FormulaInfo"
      @confirm="updateContent"
    />
  </div>
</template>

<script>
import initConfig from "./config"
import UploadDialog from "./components/UploadDialog.vue"
import Img2FormulaDialog from "./components/Img2FormulaDialog.vue"

export default {
  props: {
    config: {
      type: Object,
      default: () => {},
    },
    // 是否开启图片转换公式
    img2Formula: {
      type: Boolean,
      default: false,
    },
    // 问题id(图片转换公式开启时，需要传入问题id)
    questionId: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      editor: "",
      showPreview: false,
      myConfig: {},
      content: "",
      preview: false, // 预览开关
      id: "",
      myValue: "",
      showUpload: false,
      showImg2Formula: false,
      Img2FormulaInfo: {},
    }
  },
  created() {
    this.id = this.randomStr(4)
    this.myConfig = this.mergeConfigurations()
  },
  methods: {
    ready(editor) {
      this.editor = editor
      UE.getEditor(this.id).addListener("contentChange", (ueditor) => {
        this.editor.body.style.overflow = "auto"
      })
      // UE.getEditor(this.id).addListener('beforepaste', (ueditor) => {})
      UE.getEditor(this.id).addListener("afterpaste", () => {
        let html = this.editor.getContent()
        this.editor.setContent(html.replace("<p><br/></p>", ""))
        this.editor.focus(true)
      })
      UE.getEditor(this.id).addListener("blur", (editor) => {
        this.$emit("blur")
      })
      // 工具栏鼠标移入移出事件
      UE.getEditor(this.id).addListener("mouseenterEvent", (editor) => {
        this.$emit("mouseenter")
      })
      UE.getEditor(this.id).addListener("mouseleaveEvent", (editor) => {
        this.$emit("mouseleave")
      })
      this.$emit("ready")
    },
    mergeConfigurations() {
      return { ...initConfig, ...this.config }
    },
    addCustomButtom(editorId) {
      // 注册emphasis命令
      window.UE.commands["emphasis"] = {
        execCommand: function (cmdName) {
          var me = this,
            range = me.selection.getRange()
          range.applyInlineStyle("span", {
            style: "position: relative;",
            innerHTML:
              '<span style="position: absolute; bottom: -6px; left: 50%; transform: translateX(-50%); width: 4px; height: 4px; background: #666666; border-radius: 50%;"></span>',
          })
        },
      }

      window.UE.registerUI(
        "emphasis-button",
        (editor, uiName) => {
          editor.registerCommand(uiName, {
            execCommand: function () {
              editor.execCommand("emphasis")
            },
          })
          var btn = new window.UE.ui.Button({
            name: uiName,
            title: "强调",
            cssRules: `background-image: url(https://qm-cloud.oss-cn-chengdu.aliyuncs.com/public/img/ueditor/文字加点.png) !important;background-size: cover;`,
            onclick: () => {
              editor.execCommand(uiName)
            },
          })
          return btn
        },
        11,
        editorId,
      )

      window.UE.registerUI(
        "my-button1",
        (editor, uiName) => {
          // 注册按钮执行时的 command 命令，使用命令默认就会带有回退操作
          editor.registerCommand(uiName, {
            execCommand: function () {},
          })
          // 创建一个 button
          var btn = new window.UE.ui.Button({
            // 按钮的名字
            name: uiName,
            // 提示
            title: "预览",
            // 需要添加的额外样式，可指定 icon 图标，图标路径参考常见问题 2
            cssRules: `background-image: url(https://frontend-store.oss-cn-chengdu.aliyuncs.com/zi-yuan-yun/yulan1.png) !important;background-size: cover;`,
            // 点击时执行的命令
            onclick: () => {
              this.content = this.editor.getContent()
              this.showPreview = true
              // 这里可以不用执行命令，做你自己的操作也可
            },
          })
          // 因为你是添加 button，所以需要返回这个 button
          return btn
        },
        2 /* 指定添加到工具栏上的哪个位置，默认时追加到最后 */,
        editorId /* 指定这个 UI 是哪个编辑器实例上的，默认是页面上所有的编辑器都会添加这个按钮 */,
      )

      window.UE.registerUI(
        "my-button2",
        (editor, uiName) => {
          // 注册按钮执行时的 command 命令，使用命令默认就会带有回退操作
          editor.registerCommand(uiName, {
            execCommand: function () {
              let range = this.selection.getRange()
              /*  range.applyInlineStyle('span', {
                  style: 'text-decoration-line: underline;text-decoration-style: wavy;',
                }) */
              range.applyInlineStyle("span", {
                style: `background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 4'%3E%3Cpath fill='none' stroke='%23333' d='M0 3.5c5 0 5-3 10-3s5 3 10 3 5-3 10-3 5 3 10 3'/%3E%3C/svg%3E") 0px 100% / 15px repeat-x;`,
              })
            },
          })
          // 创建一个 button
          var btn = new window.UE.ui.Button({
            // 按钮的名字
            name: uiName,
            // 提示
            title: "波浪线",
            // 需要添加的额外样式，可指定 icon 图标，图标路径参考常见问题 2
            cssRules: `background-image: url(https://frontend-store.oss-cn-chengdu.aliyuncs.com/zi-yuan-yun/wave3.png) !important;background-size: cover;background-size: 12px;background-repeat: no-repeat;background-position: center 4px;`,
            // 点击时执行的命令
            onclick: () => {
              editor.execCommand(uiName)
              // 这里可以不用执行命令，做你自己的操作也可
            },
          })
          // 因为你是添加 button，所以需要返回这个 button
          return btn
        },
        10 /* 指定添加到工具栏上的哪个位置，默认时追加到最后 */,
        editorId /* 指定这个 UI 是哪个编辑器实例上的，默认是页面上所有的编辑器都会添加这个按钮 */,
      )
      window.UE.registerUI("my-button3", (editor, uiName) => {
        editor.registerCommand(uiName, {
          execCommand: function () {},
        })
        var btn = new window.UE.ui.Button({
          name: uiName,
          title: "上传音频",
          cssRules: `width: 20px !important;
                       height: 20px !important;
                       background-position: -18px -40px;`,
          onclick: () => {
            this.showUpload = true
          },
        })
        return btn
      })
      if (this.img2Formula === true) {
        window.UE.registerUI(
          "my-button4",
          (editor, uiName) => {
            // 注册按钮执行时的 command 命令，使用命令默认就会带有回退操作
            editor.registerCommand(uiName, {
              execCommand: function () {},
            })
            // 创建一个 button
            var btn = new window.UE.ui.Button({
              // 按钮的名字
              name: uiName,
              // 提示
              title: "公式识别",
              // 需要添加的额外样式，可指定 icon 图标，图标路径参考常见问题 2
              cssRules: `background-image: url(${$g.tool.getFileUrl(
                "fx.png",
              )}) !important;background-size: cover;`,
              // 点击时执行的命令
              onclick: async () => {
                try {
                  if (!this.editor.getContent().trim()) {
                    this.$g.msg("编辑内容为空，无法进行公式识别！", "error")
                    return
                  }
                  this.Img2FormulaInfo = {
                    questionId: this?.questionId || null,
                    content: this.editor.getContent(),
                  }
                  this.showImg2Formula = true
                } catch (error) {
                  console.log(error)
                }
              },
            })
            return btn
          },
          36 /* 指定添加到工具栏上的哪个位置，默认时追加到最后 */,
          editorId /* 指定这个 UI 是哪个编辑器实例上的，默认是页面上所有的编辑器都会添加这个按钮 */,
        )
      }
    },
    insertAPicture(strHtml) {
      this.showImgUpload = false
      this.editor.execCommand("inserthtml", strHtml)
    },
    randomStr(num) {
      const str = "aporhjbmvncjrovmbxvzzoeclolmqlpvsdffgfgf"
      let data = "ueditor-"
      for (let i = 0; i < num; i++) {
        const aa = Math.floor(Math.random() * str.length)
        data += str.substring(aa, aa + 1)
      }
      return data
    },
    handleUploadConfirm(formData) {
      const { files } = formData
      // const isAllUploaded = files.every((e) => e.url)
      // if (!isAllUploaded) {
      //   this.$msg("请等待文件上传完成", "warning")
      //   return
      // }
      this.editor.ready(() => {
        for (const file of files) {
          // 使用 UEditor API 插入音频标签或通过<audio>标签插入音频
          this.editor.execCommand(
            "inserthtml",
            `<span contenteditable="false"><audio controls controlsList="nodownload noplaybackrate" preload="none" src="${file.resource_url}"></audio><span style="display: none;">.</span> </span><br/>`,
          )
        }
      })
      // 关闭上传对话框
      this.showUpload = false
    },
    updateContent(val) {
      this.editor.setContent(val)
    },
  },
}
</script>

<style lang="scss" scoped>
:deep() {
  .edui-editor-imagescale {
    box-sizing: content-box !important;
  }

  .emphasis-btn {
    background-image: url(https://frontend-store.oss-cn-chengdu.aliyuncs.com/zi-yuan-yun/emphasis.png) !important;
    background-size: cover;
    width: 20px;
    height: 20px;
    background-position: center;
    background-repeat: no-repeat;
  }
}
</style>
