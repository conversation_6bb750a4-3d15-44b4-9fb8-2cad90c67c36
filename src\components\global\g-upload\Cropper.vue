<template>
  <div>
    <g-dialog
      :width="800"
      title="裁剪图片"
      @confirm="confirm"
      v-bind="$attrs"
      @handleClose="handleClose"
      @closeX="closeX"
      @cancel="cancel"
    >
      <div class="flex">
        <VueCropper
          :imgStyle="{
            width: '700px',
            height: '300px',
          }"
          ref="vueCropperRef"
          :src="imgSrc"
          alt="Source Image"
          preview=".img-preview"
          v-bind="cropperOptions"
          @crop="onCrop"
        />
        <div class="flex-[0_0_160px] ml-10px">
          <div class="flex item"></div>
          <div class="img-preview preview-lg mb-20px"></div>
          <div>
            <n-input-group>
              <n-input-group-label>高度</n-input-group-label>
              <n-input disabled v-model:value="imgData.newHeight" />
              <n-input-group-label>px</n-input-group-label>
            </n-input-group>
            <n-input-group class="mt-10px">
              <n-input-group-label>宽度</n-input-group-label>
              <n-input disabled v-model:value="imgData.newWidth" />
              <n-input-group-label>px</n-input-group-label>
            </n-input-group>
          </div>
          <n-radio-group
            v-show="cropperOptions.showAspectRatio"
            v-model:value="cropperOptions.aspectRatio"
            name="radiogroup"
            class="mt-10px"
          >
            <n-space>
              <n-radio
                v-for="song in [
                  { label: '16:9', value: 16 / 9 },
                  { label: '4:3', value: 4 / 3 },
                  { label: '1:1', value: 1 },
                  { label: '2:3', value: 2 / 3 },
                  { label: '无限制', value: 0 },
                ]"
                :key="song.value"
                :value="song.value"
              >
                {{ song.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </div>
      </div>

      <div class="flex mt-20px">
        <div class="flex flex-wrap">
          <div class="flex items-center">
            <el-button-group class="mr-20px">
              <el-button @click="vueCropperRef?.setDragMode('crop')">
                <g-icon name="ri-shape-2-line" size="" color="" />
              </el-button>
              <el-button @click="vueCropperRef?.setDragMode('move')">
                <g-icon name="ri-drag-drop-line" size="" color="" />
              </el-button>
              <el-button @click="vueCropperRef?.reset()">
                <g-icon name="ri-refresh-line" size="18" color="" />
              </el-button>
            </el-button-group>
            <el-button-group class="mr-20px">
              <el-button @click="vueCropperRef?.zoom(-0.1)">
                <g-icon name="ri-zoom-out-line" size="" color="" />
              </el-button>
              <el-button @click="vueCropperRef?.zoom(0.1)">
                <g-icon name="ri-zoom-in-line" size="" color="" />
              </el-button>
            </el-button-group>
            <el-button-group class="mr-20px">
              <el-button @click="vueCropperRef?.move(0, -10)">
                <g-icon name="ri-arrow-up-line" size="" color="" />
              </el-button>
              <el-button @click="vueCropperRef?.move(0, 10)">
                <g-icon name="ri-arrow-down-line" size="" color="" />
              </el-button>
              <el-button @click="vueCropperRef?.move(-10, 0)">
                <g-icon name="ri-arrow-left-line" size="" color="" />
              </el-button>
              <el-button @click="vueCropperRef?.move(10, 0)">
                <g-icon name="ri-arrow-right-line" size="" color="" />
              </el-button>
            </el-button-group>
            <el-button-group>
              <el-button @click="vueCropperRef?.rotate(-90)">
                <g-icon name="ri-anticlockwise-2-line" size="" color="" />
              </el-button>
              <el-button @click="vueCropperRef?.rotate(90)">
                <g-icon name="ri-clockwise-2-line" size="" color="" />
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import VueCropper from "@ballcat/vue-cropper"
import "cropperjs/dist/cropper.css"

const props = defineProps({
  imgSrc: {
    type: String,
    default: "",
  },
  cropper: {
    type: [Boolean, Object],
    default: false,
  },
})
const emit = defineEmits(["update:show"])

let cropperBlob = $ref(null)
let imgType = $ref()

const vueCropperRef: any = $ref(null)
let imgData: any = $ref({})

let cropperOptions: any = $ref({
  aspectRatio: 0,
  showAspectRatio: true,
  viewMode: 0,
  responsive: true,
  restore: true,
  checkCrossOrigin: true,
  checkOrientation: true,
  modal: true,
  guides: true,
  center: true,
  highlight: true,
  background: true,
  autoCrop: true,
  movable: true,
  rotatable: true,
  scalable: true,
  zoomable: true,
  zoomOnTouch: true,
  zoomOnWheel: true,
  cropBoxMovable: true,
  cropBoxResizable: true,
  toggleDragModeOnDblclick: true,
})

const attrs = useAttrs()

initCropper()
function initCropper() {
  if (typeof props.cropper === "object") {
    const { width, height } = props.cropper
    if (width && height) {
      cropperOptions.aspectRatio = width / height
      cropperOptions.showAspectRatio = false
    }
  }
}

function onCrop(e) {
  e.detail.newWidth = String(Math.round(e.detail.width))
  e.detail.newHeight = String(Math.round(e.detail.height))
  imgData = e.detail
}

function confirm() {
  vueCropperRef.getCroppedCanvas().toBlob((blob) => {
    cropperBlob = blob
    emit("update:show", false)
  }, imgType)
}

let timer: any = $ref(null)
function waitConfirm(type) {
  imgType = type
  return new Promise((resolve, reject) => {
    timer = setInterval(() => {
      if (cropperBlob) {
        resolve(cropperBlob)
        cropperBlob = null
        clearInterval(timer)
      }
    }, 500)
  })
}

function handleClose() {
  if (cropperBlob) {
    setTimeout(() => {
      clearInterval(timer)
    }, 500)
  }
}

function closeX() {
  emit("update:show", false)
}

function cancel() {
  emit("update:show", false)
}

onMounted(() => {})

defineExpose({
  waitConfirm,
})
</script>

<style lang="scss" scoped>
.preview-lg {
  width: 100%;
  height: 100px;
  overflow: hidden;
}
.el-button {
  padding-left: 11px;
  padding-right: 11px;
}
</style>
