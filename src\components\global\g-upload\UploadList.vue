<template>
  <Draggable
    class="mt-10px"
    :list="fileList"
    ghost-class="ghost"
    item-key="id"
    handle=".handle"
  >
    <template #item="{ element, index }">
      <div
        class="file-list-item flex items-center justify-between p-6px hover:bg-[#f3f3f5]"
      >
        <div class="flex items-center flex-1">
          <g-icon
            name="ri-line-height"
            class="mr-4px handle text-[#767c82]"
          ></g-icon>
          <img class="w-26px mr-10px" :src="element.thumbnailUrl" alt="" />
          <div class="flex-1">
            <span class="break-all">
              {{ element.name || element.resource_title }}
            </span>
            <div class="flex" v-show="element.status == 'uploading'">
              <n-progress
                :height="4"
                type="line"
                :percentage="element.percentage"
              />
              <div class="text-12px text-primary ml-10px flex-[0_0_50px]">
                {{ element.speed }}
              </div>
            </div>
          </div>
        </div>
        <div class="flex items-center ml-10px">
          <g-icon
            name="eye-line mr-6px text-[#767c82]"
            @click="previewFile(element, index)"
          ></g-icon>
          <g-icon
            name="download-2-line mr-6px text-[#767c82]"
            @click="downFile(element, index)"
          ></g-icon>
          <g-icon
            name="ri-delete-bin-5-line text-[#767c82]"
            @click="removeFile(element, index)"
          ></g-icon>
        </div>
      </div>
    </template>
  </Draggable>

  <el-image-viewer
    v-if="showImgDialog"
    :url-list="imgList"
    :z-index="9999"
    teleported
    @close="closePreview"
  />

  <VideoDialog :url="videoUrl" v-model:show="showVideoDialog"></VideoDialog>
  <AudioDialog :url="audioUrl" v-model:show="showAudioDialog" />
</template>

<script setup lang="ts">
import Draggable from "vuedraggable"
const props = defineProps({
  // v-model:fileList 双向绑定
  fileList: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(["update:fileList", "removeFile"])

const fileList: any = useVModel(props, "fileList", emit)
let audioUrl = $ref("")
let videoUrl = $ref("")
let showVideoDialog = $ref(false)
let showAudioDialog = $ref(false)
let showImgDialog = $ref(false)
let imgList: any = $ref([])
function previewFile(file, index) {
  let type = $g.tool.getFileType(file.suffix)
  if (type == "img") {
    showImgDialog = true
    imgList = [file.fullUrl]
  } else if (["word", "pdf", "xlsx", "txt", "ppt"].includes(type)) {
    let url = $g.tool.getWeb365Url(file.fullUrl)
    window.open(url)
  } else if (type == "video") {
    showVideoDialog = true
    videoUrl = file.fullUrl
  } else if (type == "audio") {
    showAudioDialog = true
    audioUrl = file.fullUrl
  } else if (type == "zip") {
    $g.msg("暂不支持压缩包预览", "warning")
  } else {
    $g.msg("暂不支持该文件预览", "warning")
  }
}

function closePreview() {
  showImgDialog = false
  imgList = []
}

function downFile(file, index) {
  $g.tool.downloadFile(file.fullUrl, file.name)
}

function removeFile(file, index) {
  fileList.value.splice(index, 1)
  emit("removeFile", { file, index })
}
</script>

<style lang="scss" scoped>
:deep() {
  .n-progress-icon--as-text {
    font-size: 12px !important;
  }
}
</style>
