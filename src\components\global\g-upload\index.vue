<template>
  <div :style="{ width: width }" class="relative">
    <n-upload
      :id="uploadID"
      v-model:file-list="modelList"
      :default-file-list="fileList"
      :custom-request="customRequest"
      :directory-dnd="type == 'drag'"
      @file-list="handleFileListChange"
      @change="fileChange"
      @before-upload="beforeUpload"
      @remove="handleRemove"
      :accept="accept"
      :list-type="typeFormat"
      :max="max"
      v-bind="$attrs"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      :file-list-style="{
        display:
          showFileList && !['drag', 'button'].includes(type) ? '' : 'none',
      }"
    >
      <n-upload-dragger class="bg-white" v-if="type == 'drag'">
        <div style="margin-bottom: 12px">
          <g-icon name="icon-48upload" size="50" color="" />
        </div>
        <n-text style="font-size: 16px">
          点击或者拖动文件到该区域来上传
        </n-text>
        <n-p
          depth="3"
          style="margin: 8px 0 0 0; word-break: break-all"
          class="text-12px break-keep"
          v-if="tips !== false || !newTip"
        >
          {{ tips || newTip }}
        </n-p>
      </n-upload-dragger>
      <template v-if="['image', 'button'].includes(type)">
        <slot>
          <n-button>上传文件</n-button>
        </slot>
      </template>
    </n-upload>
    <div
      class="tips text-[#767c82] text-12px relative top-4px break-all flex items-center"
      v-if="
        !['drag'].includes(type) && tips !== false && (newTip || tips.length)
      "
    >
      <g-icon name="ri-error-warning-fill" size="16" color="" />
      {{ tips || newTip }}
    </div>
    <UploadList
      v-if="showCustomFileList && ['drag', 'button'].includes(type)"
      v-model:fileList="fileList"
      @removeFile="handleRemove"
    ></UploadList>
    <slot name="fileList"></slot>
    <Cropper
      ref="cropperRef"
      :cropper="cropper"
      v-if="$g.tool.isTrue(cropper)"
      v-model:show="showCropper"
      :img-src="cropperImgUrl"
      :autoClose="false"
    ></Cropper>
  </div>
</template>

<script setup name="g-upload">
import OSS from "@/plugins/OSS"
import UploadList from "./UploadList.vue"
import Cropper from "./Cropper.vue" // 引入裁剪组件
import CryptoJS from "crypto-js"
const props = defineProps({
  // 在g-form有多个上传组件需要校验时，需要设置此属性，用于区分上传组件进行精准校验
  uploadKey: {
    type: String,
    default: "",
  },
  // v-model:fileList 双向绑定
  // warn 回填只需要传入id,url,name即可
  // warn 回填内容切换还需要调用ref的initListFormat方法
  fileList: {
    type: Array,
    default: () => [],
  },
  // 视频分辨率限制 例:["1920×1080", "1280×720"]
  videoResolution: {
    type: Array,
    default: () => [],
  },
  width: {
    type: String,
    default: "100%",
  },
  // button | drag | image | image-card
  type: {
    type: String,
    default: "drag",
  },
  // 接受上传的文件类型
  accept: {
    type: String,
  },
  // 接受上传的文件大小 单位:字节
  fileSize: {
    type: [Number, String],
  },
  // tips  字符串-自定义提示  false-关闭tips
  tips: {
    type: [String, Boolean],
    default: "",
  },
  // 隐藏默认文件列表
  showFileList: {
    type: Boolean,
    default: true,
  },
  // 显示自定义文件列表
  showCustomFileList: {
    type: Boolean,
    default: true,
  },
  // 是否限制重名文件上传
  isFileNameDuplicate: {
    type: Boolean,
    default: false,
  },
  // 是否需要裁剪,如果为对象则是配置项
  // 例如: cropper: { width: 100, height: 100 } 传入设计图上图片的宽高,不传则不限制裁剪尺寸比例
  cropper: {
    type: [Boolean, Object],
    default: false,
  },
  //  在上传前插入额外逻辑，函数需返回Promise
  beforeUploadOther: {
    type: Function,
  },
  // 最大上传数量
  max: {
    type: Number,
  },
  // 是否开启粘贴上传功能
  copyUpload: {
    type: Boolean,
    default: true,
  },
  ossConfig: {
    type: Object,
    default: () => ({
      timeout: 0,
    }),
  },
  fileConfig: {
    type: Object,
    default: () => ({}),
  },
  //获取自定义资源路径签名 api:获取签名的api,parentPath:自定义资源父级路径
  customPolicyParams: {
    type: Object,
    default: () => ({}),
  },
  needCustomUpload: {
    type: Function,
    default: null,
  },
  customUpload: {
    type: Function,
    default: null,
  },
})
// 文件类型和大小限制配置
const FILE_CONFIG = {
  img: {
    maxSize: 20 * 1024 * 1024, // 20MB
    title: "图片",
  },
  video: {
    maxSize: 5 * 1024 * 1024 * 1024, // 5GB
    title: "视频",
  },
  zip: {
    maxSize: 5 * 1024 * 1024 * 1024, // 5GB
    title: "压缩包",
  },
  default: {
    maxSize: 200 * 1024 * 1024, // 200MB
    title: "默认",
  },
}

const newFileConfig = $computed(() => {
  // 先进行配置合并
  const mergedConfig = $g.tool.mergeConfigs(FILE_CONFIG, props.fileConfig)

  // 遍历合并后的配置，只对没有 message 的项添加 message
  const finalConfig = {}
  for (const [key, value] of Object.entries(mergedConfig)) {
    finalConfig[key] = {
      ...value,
      message:
        value.message ||
        `${value.title}最大${$g.tool.formatFileSize(value.maxSize)}`,
    }
  }

  return finalConfig
})

const uploadID = $ref(`upload-${$g.tool.uuid(10)}`)
const videoTypeArr = ["mp4", "avi", "rmvb", "flv", "wmv", "mkv", "mov"]
const cropperRef = ref(null)
const modelList = $computed(() => {
  return props.fileList
})

defineExpose({
  manualOpeningUpload,
  handleRemove,
  // warn:初始化列表,常用于切换upload里内容时,手动进行列表初始化(重要:需要先将文件赋值给fileList,然后调用initListFormat)
  initListFormat,
})
const emit = defineEmits(["update:fileList", "onChange"])
const fileList = useVModel(props, "fileList", emit)

let aliOss = $ref({})
let newTip = $ref("")
let initList = $ref(false)
watch(
  () => props.fileList,
  async (n, o) => {
    if (o?.length && o) return
    await initListFormat(true)
  },
  { immediate: true },
)
async function initListFormat(auto) {
  if (!$g.tool.isTrue(aliOss)) {
    await initOSS()
  }
  nextTick(() => {
    if ((!initList && props.fileList.length) || !auto) {
      props.fileList.forEach((e) => {
        if (e.url || e.url === "") {
          e.id = $g.tool.uuid(10)
          e.init = "true"
          e.status = "finished"
          e.ext = $g.tool.getExt(e.url)
          e.suffix = e.suffix || e.ext.substring(1)
          if (
            ["drag", "button"].includes(props.type) &&
            props.showCustomFileList
          ) {
            e.thumbnailUrl = $g.tool.getFileTypeIcon(e.suffix)
          }
          e.resource_url = e?.url
          e.fullUrl = e?.url
        }
      })
      initList = true
    }
  })
}

if (!$g.tool.isTrue(aliOss)) await initOSS()
async function initOSS() {
  aliOss = new OSS(props.ossConfig)
  initListFormat(true)
  aliOss.on("onProgress", ({ val, speed }, id) => {
    props.fileList.forEach((e) => {
      if (e?.id == id) {
        e.percentage = val
        e.speed = speed
      }
    })
  })
  aliOss.on("removeFile", (file) => {
    props.fileList.splice(
      $g._.findIndex(props.fileList, (e) => {
        e.id == file.id
      }),
      1,
    )
    handleRemove({ file })
  })
}

const typeFormat = $computed(() => {
  let type = ""
  switch (props.type) {
    case "image":
    case "image-card":
      type = props.type
      break
    case "drag":
      type = "image"
      break
    default:
      type = "text"
      break
  }
  return type
})

/* 获取提示信息 */
tipsFormat()
function tipsFormat() {
  let str = ""
  let accept = props.accept
  if (accept) {
    str += `只能上传${accept}文件`
    if (props.type == "drag") {
      const suffixArr = accept.split(",")
      const typeArr = [
        ...new Set(
          suffixArr.map((v) => $g.tool.getFileType(v.replace(".", ""))),
        ),
      ]

      // 添加每种文件类型的大小限制提示
      typeArr.forEach((fileType) => {
        const config = newFileConfig[fileType]
        if (config) {
          str += `,${config.message}`
        }
      })

      // 如果包含其他类型文件
      if (typeArr.length > Object.keys(newFileConfig).length - 1) {
        // -1 排除default
        str += `,${newFileConfig.default.message}`
      }
    }
  }
  newTip = str
}

let showCropper = $ref(false)
let cropperImgUrl = $ref()

async function limitVideoResolution(file) {
  return new Promise(async (resolve) => {
    if (!props.videoResolution.length) {
      resolve(true)
      return
    }
    let { width, height } = await getVideoInfo(file.file)
    // 遍历目标分辨率数组，检查是否匹配
    let isValid = props.videoResolution.some((resolution) => {
      const [targetWidth, targetHeight] = resolution.split("×").map(Number)
      return width === targetWidth && height === targetHeight
    })
    if (!isValid) {
      $g.msg(
        `视频分辨率不符合要求，请上传${props.videoResolution.join(
          "、",
        )}分辨率的视频`,
        "error",
      )
    }
    // 如果匹配则返回 true，否则返回 false
    resolve(isValid)
  })
}

async function beforeUpload({ file, fileList }) {
  const typeFlag = limitType(file)
  const sizeFlag = limitSize(file)
  // 适配分辨率检测
  const limitVideoResolutionFlag = await limitVideoResolution(file)
  if (props.cropper) {
    cropperImgUrl = URL.createObjectURL(file.file)
    showCropper = true
    await nextTick()
    let blob = await cropperRef.value.waitConfirm(file.file.type)
    showCropper = false
    file.thumbnailUrl = URL.createObjectURL(blob)
    file.file = new File([blob], file.file.name, { type: file.file.type })
  }
  if (props.beforeUploadOther) await props.beforeUploadOther()
  if (props.isFileNameDuplicate) await checkRestrictName(file, fileList)

  return typeFlag && sizeFlag && limitVideoResolutionFlag
}

async function fileChange({ file, fileList }) {
  fileList.value = fileList
  // 判断是否属于删除
  if (props.fileList.length > fileList.length) {
    $g._.remove(props.fileList, (e) => {
      return e.id == file.id
    })
    emit("update:fileList", props.fileList)
    $g.bus.emit("form.change", { type: "upload", key: props.uploadKey })
    return
  }
  if (["drag", "button"].includes(props.type) && props.showCustomFileList) {
    file.thumbnailUrl = $g.tool.getFileTypeIcon(file.suffix)
  }
  emit("update:fileList", [...props.fileList, file])
  file.status = "uploading"
  if (
    videoTypeArr.includes(file.suffix) ||
    ["wav", "mp3"].includes(file.suffix)
  ) {
    let videoInfo = await getVideoInfo(file.file)
    file.time_length = videoInfo?.duration
    file.videoInfo = videoInfo
  }
  if ($g.tool.isTrue(props.customPolicyParams)) {
    file.parentPath = props.customPolicyParams.parentPath
  }
  // 计算文件hash
  // file.hash = await calculateFileHash(file.file)
  const useCustom = await (props.needCustomUpload
    ? props.needCustomUpload(file)
    : false)

  const uploadFn =
    useCustom && props.customUpload
      ? props.customUpload(file)
      : aliOss.uploadFile(file, undefined, props.customPolicyParams.api)

  await uploadFn
    .then(({ resource_url, fullUrl }) => {
      const item = $g._.find(props.fileList, (e) => e.id == file.id)
      item.status = "finished"
      // 上传成功后返回的文件路径(传给后端的地址)
      item.resource_url = resource_url
      item.fullUrl = fullUrl
    })
    .catch((err) => {
      console.log(err)
      const index = $g._.findIndex(props.fileList, (e) => e.id == file.id)
      if (index >= 0) props.fileList.splice(index, 1)
    })
  $g.bus.emit("form.change", { type: "upload", key: props.uploadKey })
  emit("onChange", file, fileList)
}

/* 校验文件格式 */
function limitType(file) {
  file.ext = $g.tool.getExt(file.name)
  file.suffix = file.ext.substring(1)
  file.resource_title = file.name.substring(0, file.name.lastIndexOf("."))
  if (!props.accept) return true
  let accept = props.accept
  accept = accept.split(",")
  const flag = accept.some((e) => {
    return file.ext.toLowerCase() === e
  })
  if (!flag) {
    $g.msg(`请上传${props.accept}格式文件`, "error")
  }
  return flag
}

/* 校验上传重名文件 */
function checkRestrictName(file, fileList) {
  return new Promise((resolve, reject) => {
    if (fileList.some((item) => item.name == file.name)) {
      $g.msg("文件已存在", "warning")
      reject()
    } else {
      resolve(true)
    }
  })
}

/* 处理删除 */
function handleRemove({ file, fileList, index }) {
  if (file.status == "uploading") {
    aliOss.cancelUpload(file.id)
  }
}

/* 校验文件大小 */
function limitSize(file) {
  if (!file?.file?.size) return false
  file.size = file.file.size
  const fileSize = file.file.size
  const fileType = $g.tool.getFileType(file?.suffix)
  const config = newFileConfig[fileType] || newFileConfig.default

  if (fileSize > config.maxSize) {
    $g.msg(
      `${config.title}类型文件的大小超过最大限制${$g.tool.formatFileSize(
        config.maxSize,
      )}`,
      "error",
    )
    return false
  }

  return true
}

function getVideoInfo(file) {
  return new Promise((resolve, reject) => {
    let url = URL.createObjectURL(file)
    let videoElement = document.createElement("video")

    videoElement.addEventListener("loadedmetadata", () => {
      let duration = parseInt(videoElement.duration)
      let width = videoElement.videoWidth
      let height = videoElement.videoHeight
      // 释放 URL 对象
      URL.revokeObjectURL(url)

      resolve({ duration, width, height })
    })

    // 处理视频加载错误
    videoElement.addEventListener("error", (e) => {
      URL.revokeObjectURL(url)
      reject("视频加载失败：" + e.message)
    })

    // 设置视频文件源
    videoElement.src = url
  })
}

/* 手动打开上传框 */
function manualOpeningUpload() {
  document.querySelector(`#${uploadID} .n-upload-trigger`).click()
}

function customRequest() {}

function handleFileListChange() {}

// async function calculateFileHash(file, algorithm = "SHA-256") {
//   // 分块大小设置为 2MB
//   const chunkSize = 10 * 1024 * 1024
//   const chunks = Math.ceil(file.size / chunkSize)
//   let currentChunk = 0

//   // 创建一个 CryptoJS 哈希对象
//   const hasher =
//     algorithm === "SHA-256"
//       ? CryptoJS.algo.SHA256.create()
//       : CryptoJS.algo.MD5.create()

//   return new Promise((resolve, reject) => {
//     const reader = new FileReader()

//     // 处理每个分块
//     reader.onload = (e) => {
//       try {
//         const wordArray = CryptoJS.lib.WordArray.create(e.target.result)
//         hasher.update(wordArray)
//         currentChunk++

//         if (currentChunk < chunks) {
//           // 继续读取下一块
//           loadNext()
//         } else {
//           // 完成所有块的处理，计算最终哈希值
//           const hash = hasher.finalize()
//           resolve(hash.toString(CryptoJS.enc.Hex))
//         }
//       } catch (err) {
//         reject(err)
//       }
//     }

//     reader.onerror = (err) => reject(err)

//     // 加载下一个分块
//     function loadNext() {
//       const start = currentChunk * chunkSize
//       const end = Math.min(start + chunkSize, file.size)
//       const chunk = file.slice(start, end)
//       reader.readAsArrayBuffer(chunk)
//     }

//     // 开始读取第一个分块
//     loadNext()
//   })
// }

/* ============粘贴上传============ */
/* 粘贴事件 */
function handlePaste(event) {
  // 获取文件对象
  const items = event.clipboardData.items
  let fileArr = []
  for (let item of items) {
    if (item.kind == "file") {
      fileArr.push(item.getAsFile())
    }
  }

  if (!fileArr.length) return
  if (fileArr.length + props.fileList.length > props.max)
    return $g.msg("超出文件上传数量", "error")
  for (let item of fileArr) {
    // 初始化文件
    let file = new File([item], item.name, { type: item.type })
    let data = {
      id: $g.tool.uuid(8),
      name: file.name,
      status: "uploading",
      percentage: 0,
      file,
      thumbnailUrl: $g.tool.getFileTypeIcon(
        $g.tool.getExt(file.name).substring(1),
      ),
      suffix: $g.tool.getExt(file.name).substring(1),
      size: file.size,
    }
    // 校验文件允许类型与文件大小
    if (!limitType(data) || !limitSize(data)) return
    fileChange({ file: data, fileList: props.fileList })
  }
}
function handleMouseEnter() {
  if (props.type == "drag" && props.copyUpload)
    document.addEventListener("paste", handlePaste)
}
function handleMouseLeave() {
  if (props.type == "drag" && props.copyUpload)
    document.removeEventListener("paste", handlePaste)
}
</script>

<style lang="scss" scoped>
:deep() {
  .n-progress-graph-line-fill {
    background: var(--g-primary) !important;
  }
}

.tips {
  background: #f4f4f5;
  display: inline-block;
  padding: 0px 12px;
  color: #909399;
  border-radius: 4px;
}
</style>
