<script setup>
const props = defineProps({
  url: {
    type: String,
    default: "",
    required: true,
  },
})
onMounted(() => {
  if (Hls.isSupported()) {
    var video = document.getElementById("video")
    var hls = new Hls()

    // URL to your HLS stream
    var streamUrl = props.url

    hls.loadSource(streamUrl)
    hls.attachMedia(video)

    hls.on(Hls.Events.MANIFEST_PARSED, function () {
      video.play()
    })
  } else if (video.canPlayType("application/vnd.apple.mpegurl")) {
    // For Safari and Edge without hls.js
    video.src = props.url
    video.addEventListener("loadedmetadata", function () {
      video.play()
    })
  }
})
</script>

<template>
  <video
    style="width: 100%; height: 100%; object-fit: fill"
    id="video"
    class="video-js vjs-default-skin"
    controls
  ></video>
</template>

<style lang="scss"></style>
