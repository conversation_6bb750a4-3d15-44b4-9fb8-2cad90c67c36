<script lang="ts" setup>
import Player from "xgplayer/dist/simple_player"
import volume from "xgplayer/dist/controls/volume"
import playbackRate from "xgplayer/dist/controls/playbackRate"
import FlvJsPlayer from "xgplayer-flv.js"
import LiveHlsPlayer from "xgplayer-hls.js"
import HlsPlayer from "xgplayer-hls"

const props = defineProps({
  url: {
    type: String,
    default: "",
    required: true,
  },
  live: {
    type: Boolean,
    default: false,
    required: false,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
})

let player: any = $ref()
let id = $g.tool.uuid(4)

function initVideo() {
  let PlayerExample = Player
  let ext = $g.tool.getExt(props.url)
  if (ext == ".m3u8" || ext == ".hls") {
    PlayerExample = props.live ? LiveHlsPlayer : HlsPlayer
  } else if (ext == ".flv") {
    PlayerExample = FlvJsPlayer
  }
  player = new PlayerExample({
    id: `video${id}`,
    url: props.url,
    controlPlugins: [".flv", ".m3u8", ".hls"].includes(ext)
      ? []
      : [volume, playbackRate],
    playbackRate: [0.5, 0.75, 1, 1.5, 2], //传入倍速可选数组
    fluid: true,
    videoInit: true,
    autoplay: true,
    lang: "zh-cn",
    ...props.config,
  })
}

onMounted(() => {
  initVideo()
})

watch(
  () => props.url,
  async (newVal) => {
    if (player) {
      player?.destroy &&
        player.destroy().then(() => {
          player = null
          initVideo()
        })
    } else {
      initVideo()
    }
  },
)
</script>

<template>
  <div :id="`video${id}`"></div>
</template>

<style lang="scss">
.xgplayer-skin-default .xgplayer-progress-played {
  background-image: linear-gradient(90deg, #2a9feb, #2a9feb) !important;
}
// 音量条的颜色
.xgplayer-skin-default .xgplayer-drag {
  background: #2a9feb !important;
}
</style>
