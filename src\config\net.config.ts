export default {
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 三层接口
  threeBaseURL: import.meta.env.VITE_APP_THREE_LEVEL_API,
  // v3接口
  v3BaseURL: import.meta.env.VITE_APP_V3_API,
  // 配后端数据的接收方式application/json;charset=UTF-8 或 application/x-www-form-urlencoded;charset=UTF-8
  contentType: "application/json;charset=UTF-8",
  // 最长请求时间
  requestTimeout: 30 * 1000,
  // OSS基础地址
  ossBaseURL: "https://frontend-store.oss-cn-chengdu.aliyuncs.com/",
  baseTOrigin: import.meta.env.VITE_APP_BASE_TORIGIN,
  baseTUserOrigin: import.meta.env.VITE_APP_BASE_TUORIGIN,
  baseOrigin: import.meta.env.VITE_APP_BASE_ORIGIN,
  baseOssImg: "https://frontend-cdn.qimingdaren.com/cdn/img/",
  baseNewQuestionOrigin: import.meta.env.VITE_APP_BASE_NEWQUESTION_ORIGIN,
}
