const VITE_APP_SSO = import.meta.env.VITE_APP_SSO == "false" ? false : true
export default {
  // 标题，此项修改后需要重启项目！！！ (包括初次加载雪花屏的标题 页面的标题 浏览器的标题)
  title: "金字塔后台管理",
  // 标题分隔符
  titleSeparator: " - ",
  // 不经过token校验的路由，白名单路由建议配置到与login页面同级
  routesWhiteList: [
    "/login",
    "/register",
    "/callback",
    "/404",
    "/403",
    "/demo",
    "/third/authRedirect",
    "/teacherIframe",
    "/teacherUserIframe",
    "/third/teacherURedirect",
    "/newQuestionBankIframe",
    "/third/newQuestionBankRedirect",
  ],
  // 是否开启登录拦截
  loginInterception: true,
  // 是否开启roles字段进行角色权限控制(如果是all模式后端完全处理角色并进行json组装，可设置false不处理路由中的roles字段)
  rolesControl: true,
  // intelligence(前端导出路由)和 all(后端导出路由)两种方式
  authentication: "all",
  // token存储位置 localStorage sessionStorage cookie
  storage: "cookie",
  // token在localStorage、sessionStorage、cookie存储的key的名称
  tokenTableName: "jzt_token",
  // 三层 token在localStorage、sessionStorage、cookie存储的key的名称
  threeTokenTableName: "three_token",
  // token失效回退到登录页时是否记录本次的路由
  recordRoute: true,
  SSO: VITE_APP_SSO,
  // 是否再 iframe 中
  inIframe: window.self !== window.top,
}
