import chroma from "chroma-js"
import { zhCN, enUS, dateZhCN, dateEnUS, useThemeVars } from "naive-ui"
import type { GlobalThemeOverrides } from "naive-ui"
import { useSettingsStore } from "@/stores/modules/settings"

export function useNaive() {
  const settings = useSettingsStore()

  // 语系
  const getNaiveLocale = computed(() => {
    const lang = settings.getLang
    switch (lang) {
      case "zh-CN":
        return zhCN
      default:
        return enUS
    }
  })

  // 日期插件语系
  const getNaiveDateLocale = computed(() => {
    const lang = settings.getLang
    switch (lang) {
      case "zh-CN":
        return dateZhCN
      default:
        return dateEnUS
    }
  })

  //日期插件语系
  const getNaiveThemeOverrides = computed(() => {
    const primaryColor = getCssVariable("primary")
    const infoColor = getCssVariable("info")
    const successColor = getCssVariable("success")
    const warningColor = getCssVariable("warning")
    const errorColor = getCssVariable("error")
    const deep = 0.3
    const lightenPrimaryColor = brightenColor(primaryColor, deep)

    const lightenInfoColor = brightenColor(infoColor, deep)
    const lightenSuccessColor = brightenColor(successColor, deep)
    const lightenWarningColor = brightenColor(warningColor, deep)
    const lightenErrorColor = brightenColor(errorColor, deep)

    const size = {
      heightMedium: "32px",
      heightSmall: "24px",
      fontSizeSmall: "12px",
      borderRadius: "4px",
    }
    const color = {
      primaryColor: primaryColor,
      primaryColorSuppl: lightenPrimaryColor,
      primaryColorHover: lightenPrimaryColor,
      primaryColorPressed: lightenPrimaryColor,
      infoColor: infoColor,
      infoColorHover: lightenInfoColor,
      infoColorPressed: lightenInfoColor,
      infoColorSuppl: lightenInfoColor,
      successColor: successColor,
      successColorHover: lightenSuccessColor,
      successColorPressed: lightenSuccessColor,
      successColorSuppl: lightenSuccessColor,
      warningColor: warningColor,
      warningColorHover: lightenWarningColor,
      warningColorPressed: lightenWarningColor,
      warningColorSuppl: lightenWarningColor,
      errorColor: errorColor,
      errorColorHover: lightenErrorColor,
      errorColorPressed: lightenErrorColor,
      errorColorSuppl: lightenErrorColor,
    }
    const overrides: GlobalThemeOverrides = {
      common: { ...color, ...size },
      Layout: {},
      Card: {},
    }
    return overrides
  })

  return {
    getNaiveDateLocale,
    getNaiveLocale,
    themeVars: useThemeVars().value,
    getNaiveThemeOverrides,
  }
}

/**
 * 更亮的颜色
 * @param color - 颜色
 * @param deep - 效果层次
 */
function brightenColor(color: string, deep: number = 0.5) {
  return chroma(color).brighten(deep).hex()
}

/* 获取css变量 */
function getCssVariable(variable) {
  let color = getComputedStyle(document.documentElement).getPropertyValue(
    `--g-${variable}`,
  )
  color = color.replace(/\s*/g, "")
  return color
}
