<template>
  <div>
    <n-layout position="absolute" class="min-w-[1400px]">
      <l-header></l-header>
      <n-layout has-sider position="absolute" class="layout-container">
        <l-sider></l-sider>
        <n-layout-content :native-scrollbar="false">
          <!-- <n-layout-header v-if="settings.showTabsBar" bordered>
            <g-tabs></g-tabs>
          </n-layout-header> -->
          <n-layout
            id="right-main"
            class="right-main"
            :native-scrollbar="false"
            position="absolute"
            :content-style="{
              padding: '20px',
            }"
          >
            <l-main></l-main>
            <n-back-top
              id="backTop"
              :right="10"
              :bottom="120"
              style="z-index: 1999"
            />
          </n-layout>
        </n-layout-content>
      </n-layout>
    </n-layout>
    <!-- <n-layout
      v-else
      class="right-main"
      :native-scrollbar="false"
      position="absolute"
      :content-style="{
        padding: '20px',
      }"
    >
      <l-main></l-main>
      <n-back-top
        id="backTop"
        :right="40"
        :bottom="120"
        style="z-index: 1999"
      />
    </n-layout> -->
  </div>
</template>

<script setup lang="ts">
import config from "@/config"
import LHeader from "../components/l-header/index.vue"
import LSider from "../components/l-sider/index.vue"
import LMain from "../components/l-main/index.vue"
import { useSettingsStore } from "@/stores/modules/settings"
const { headerHeight, tabHeight, footerHeight } = config
const settings = useSettingsStore()
</script>

<style lang="scss" scoped>
:deep() {
  .right-main {
    .n-scrollbar-container {
      background: #f3f3f3;
    }
  }
}

.layout-container {
  top: v-bind(headerHeight);
  margin-top: 1px;
}

.content {
  top: v-bind(tabHeight);
}

.footer {
  bottom: v-bind(footerHeight);
}
</style>
