<template>
  <n-layout position="absolute">
    <l-header class="flex justify-center">
      <l-menu
        class="top-nav-menu pl-60px"
        mode="horizontal"
        :dropdown-props="dropdownProps"
      ></l-menu>
    </l-header>
    <n-layout
      has-sider
      position="absolute"
      class="layout-container flex justify-center"
    >
      <n-layout-content :native-scrollbar="false">
        <n-layout
          id="right-main"
          class="right-main"
          :native-scrollbar="false"
          position="absolute"
          :content-style="{
            padding: '20px',
            display: 'flex',
            justifyContent: 'center',
          }"
        >
          <l-main></l-main>
          <n-back-top
            id="backTop"
            :right="40"
            :bottom="120"
            style="z-index: 1999"
          />
        </n-layout>
      </n-layout-content>
    </n-layout>
  </n-layout>
</template>

<script setup lang="ts">
import config from "@/config"
import LHeader from "../components/l-header/index.vue"
import LMenu from "../components/l-menu/index.vue"
import LMain from "../components/l-main/index.vue"
import { useSettingsStore } from "@/stores/modules/settings"
const { headerHeight, tabHeight, footerHeight } = config
const settings = useSettingsStore()
const dropdownProps = {
  animated: false,
  "menu-props": () => ({
    class: "top-menu-name",
  }),
}
</script>

<style lang="scss" scoped>
:deep() {
  .right-main {
    .n-scrollbar-container {
      background: #f3f3f3;
    }
  }
  .layout-header {
    box-shadow: none;
    width: 1200px;
  }
  .top-nav-menu {
    height: 60px;
    .n-menu-item {
      height: 100%;
      .n-menu-item-content {
        height: 100%;
        .n-menu-item-content-header {
          font-size: 16px;
          padding: 0 14px;
          display: flex;
          align-items: center;
        }
      }
    }
    .n-submenu {
      .n-menu-item-content-header {
        &::after {
          display: inline-block;
          content: "";
          width: 6px;
          height: 6px;
          border: 1px solid var(--n-item-text-color);
          border-left: none;
          border-top: none;
          transform: rotate(45deg) translateY(-4px);
          margin-left: 6px;
        }
      }

      .n-menu-item-content--hover {
        .n-menu-item-content-header {
          &::after {
            border-color: theme("colors.primary");
            transform: rotate(-134deg) translateX(-2px) translateY(0px);
          }
        }
      }

      .n-menu-item-content--child-active {
        .n-menu-item-content-header {
          &::after {
            border-color: theme("colors.primary");
          }
        }
      }
    }
  }
}

.layout-container {
  top: v-bind(headerHeight);
  margin-top: 1px;
}

.content {
  top: v-bind(tabHeight);
}

.footer {
  bottom: v-bind(footerHeight);
}
</style>

<style lang="scss">
.top-menu-name {
  margin-top: 0 !important;
}
</style>
