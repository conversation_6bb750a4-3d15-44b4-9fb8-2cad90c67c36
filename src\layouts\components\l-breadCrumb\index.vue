<template>
  <div v-show="!$route.meta.breadcrumbHidden" class="flex breadcrumb-container">
    <g-icon
      @click="backPage"
      name="ri-arrow-left-line"
      size="28"
      color="#5f6368"
      class="mr-10px"
    />
    <el-breadcrumb class="breadcrumb" separator="/">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="index">
        <a class="text-14px" @click.prevent="handleLink(item, index)">
          <g-icon v-if="item.meta && item.meta.icon" :icon="item.meta.icon" />
          {{ item.meta?.breadcrumb || item.meta?.title }}
        </a>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script lang="ts" setup name="LBreadCrumb">
import { useRoute, useRouter } from "vue-router"
import { useRouterStore } from "@/stores/modules/routes"
const route = useRoute()
const router: any = useRouter()
const routeStore = useRouterStore()
const enterMode = ref(1) // 1-正常方式进入 2-返回方式进入路由 3-侧边菜单栏方式进入 4-点击面包屑进入 5-点击面包屑返回上一级
const breadcrumbBack = ref(false)
const levelList = ref<any[]>([])

watch(
  () => router.currentRoute.value,
  (n, to) => {
    if (n?.name == to?.name) {
      return
    }
    // 移除逻辑后置
    if (enterMode.value == 2) {
      levelList.value.splice(levelList.value.length - 1)
    }

    // popstate事件晚于route变化 暂时添加异步解决
    setTimeout(() => {
      if (
        // todo:!n.redirectedFrom 将这个判断取消，因为在刷新页面时，会导致面包屑显示不完整，后续有问题再优化
        // ((!to || enterMode.value == 4) && !n.redirectedFrom) ||
        !to ||
        enterMode.value == 4 ||
        breadcrumbBack.value
      ) {
        breadcrumbBack.value = false
        levelList.value = routeStore.levelList2
        if ([2, 3, 4].includes(enterMode.value)) enterMode.value = 1
        return
      }
      handleBreak(n, to)
      routeStore.levelList2 = levelList.value
    }, 100)
  },
  { immediate: true },
)

onMounted(async () => {
  window.addEventListener("popstate", browserReturn, false)
  await nextTick()
  $g.bus.on("changeMenu", () => {
    enterMode.value = 3
  })
})

function handleBreak(n, to) {
  levelList.value = getBreadcrumb(n, to)

  if (
    (to &&
      !to.meta.breadcrumbHiddenTitle &&
      !$g._.includes(levelList.value, to?.meta?.title) &&
      enterMode.value == 1 &&
      !n.redirectedFrom &&
      levelList.value.length > 1) ||
    n.name == "GlobalPreview"
  ) {
    levelList.value = [
      ...routeStore.levelList2,
      {
        name: n.name,
        meta: n.meta,
        query: n.query,
      },
    ]
  }
  if ([2, 3, 4].includes(enterMode.value)) enterMode.value = 1
}
function browserReturn() {
  enterMode.value = 2
}
function getBreadcrumb(n, to) {
  let levelList: any[] = []
  route.matched.forEach((item) => {
    if (item.meta && item.meta.title && !item.meta.breadcrumbHiddenTitle) {
      levelList.push({
        name: item.name,
        meta: item.meta,
        query: item.name == route.name ? route.query : {},
        redirect: item.redirect,
        path: item.path,
      })
    }
  })
  if (n.meta.breadcrumbParent && n.meta.breadcrumbParent.name !== to.name) {
    levelList.splice(levelList.length - 1, 0, {
      ...n.meta.breadcrumbParent,
      addParent: true,
    })
  }
  return levelList
}

function handleLink(item, index) {
  if (item.redirect) {
    if (route.path == item.redirect) return
    router.push({
      name: item.name,
      query: item.query,
    })
    levelList.value = []
    routeStore.levelList2 = levelList.value
    return
  }
  if (
    index == levelList.value.length - 2 &&
    !item.addParent &&
    (item.redirect != levelList.value.at(-1).path ||
      !levelList.value.at(-1).path)
  ) {
    breadcrumbBack.value = true
    // levelList.value.splice(index + 1)
    return backPage()
  }

  enterMode.value = 4

  if (!item.redirect) {
    levelList.value.splice(index + 1)
  }

  routeStore.levelList2 = levelList.value

  router.push({
    name: item.name,
    query: item.query,
  })
}

function backPage(mode = 2) {
  enterMode.value = mode
  router.back()
}
</script>

<style lang="scss" scoped>
.breadcrumb-container {
  height: 40px;
  font-size: 16px;
  line-height: 40px;

  .breadcrumb {
    line-height: 40px;
  }
}

.el-breadcrumb__item {
  .el-breadcrumb__inner {
    a {
      font-weight: bold;
      color: #999999;
    }
  }
  .el-breadcrumb__separator {
    margin: 0 4px;
    margin-right: 8px;
    font-weight: 700;
    color: #c0c4cc;
  }
  &:not(:last-child) {
    .el-breadcrumb__inner {
      a:hover {
        color: theme("colors.primary");
      }
    }
  }
  &:last-child {
    .el-breadcrumb__inner {
      a {
        font-size: 17px;
        line-height: 17px;
        font-weight: bold;
        color: #333 !important;
      }
    }
  }
}
</style>
