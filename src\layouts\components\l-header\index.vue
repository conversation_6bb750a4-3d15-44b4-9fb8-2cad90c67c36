<template>
  <n-layout-header bordered class="relative">
    <n-el class="layout-header px-20px flex justify-between">
      <div class="flex items-center">
        <img
          class="w-30px mr-10px"
          src="https://qm-cloud.oss-cn-chengdu.aliyuncs.com/cloud_test/application/202206/20220617/cc4944c39eb092bca3391c1ae58a67e9.png"
          alt=""
        />
        <span class="text-18px whitespace-nowrap !text-primary">
          {{ title }}
        </span>
      </div>
      <div class="flex-1">
        <slot>
          <LBreadCrumb class="absolute left-240px top-9px"></LBreadCrumb>
          <!-- <OBreadCrumb class="absolute left-240px top-9px"></OBreadCrumb> -->
        </slot>
      </div>
      <div class="flex items-center">
        <n-avatar
          round
          :size="32"
          :src="userStore?.userInfo?.headPic || avatarUrl"
          class="mr-6px border"
        />
        <n-dropdown
          trigger="hover"
          :options="options"
          size="small"
          @select="select"
        >
          <div class="flex items-center">
            <div class="cursor-pointer">
              {{ userStore?.userInfo?.userName }}
            </div>
            <g-icon
              class="relative bottom-2px"
              name="ri-arrow-down-s-line"
              size=""
              color=""
            />
          </div>
        </n-dropdown>
      </div>
    </n-el>
  </n-layout-header>
</template>

<script setup lang="ts">
import config from "@/config"
import { useUserStore } from "@/stores/modules/user"
import GIcon from "@/components/global/g-icon/index.vue"
import LBreadCrumb from "../l-breadCrumb/index.vue"
import OBreadCrumb from "../o-breadCrumb/index.vue"
import namedavatar from "namedavatar"

const userStore = useUserStore()
const { headerHeight, title } = config

const renderIcon = (icon?: string) => {
  return () => {
    // @ts-ignore
    return h(GIcon, { name: "ri-logout-circle-r-line", size: "14" })
  }
}
let options = $ref([
  {
    label: "退出登录",
    key: "logout",
    icon: renderIcon(),
  },
])

function select(val) {
  switch (val) {
    case "logout":
      userStore.logout()
      break
    default:
      break
  }
}

const avatarUrl = $computed(() => {
  const svg = namedavatar.getSVGString(userStore?.userInfo?.userName)
  const url = namedavatar.getDataURI(svg)
  return url
})
</script>

<style lang="scss" scoped>
.test {
  background: var(--primary-color);
  height: 100px;
}
.layout-header {
  display: flex;
  align-items: center;
  height: v-bind(headerHeight);
  box-shadow: 0px 2px 4px 0px #e5e5e5;

  .left {
    height: v-bind(headerHeight);
    display: flex;
    align-items: center;
    flex: 1;
  }

  .right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: v-bind(headerHeight);

    .item {
      width: 30px;
      height: 100%;
      display: flex;
      padding: 0 5px 0 5px;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      &:hover {
        background: var(--hover-color);
      }
    }
  }
}
</style>
