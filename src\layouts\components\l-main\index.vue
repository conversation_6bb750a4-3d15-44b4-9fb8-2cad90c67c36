<template>
  <div class="app-main" :class="{ show: ifShowLayout }">
    <!-- <LBreadCrumb></LBreadCrumb> -->
    <router-view v-slot="{ Component }" v-if="showView">
      <keep-alive :include="keepAliveArr">
        <component :is="Component" :key="$route.name" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { useRouterStore } from "@/stores/modules/routes"
// import LBreadCrumb from "../l-breadCrumb/index.vue"
// const router = useRouter()
const routerStore = useRouterStore()
const { keepAliveArr } = $(storeToRefs(routerStore))
const router: any = useRouter()
let ifShowLayout = computed(() => {
  return router.currentRoute.value.meta.notShowLayout
})
let showView = $ref(true)

$g.bus.on("reload", async () => {
  showView = false
  await nextTick()
  showView = true
})
watchEffect(() => {})
</script>

<style lang="scss" scoped>
.app-main {
  width: 100%;
  height: 100%;
  overflow: hidden;
  :deep() {
    [class*="-container-main"] {
      min-height: calc(100vh - 57px - 20px * 2);
      background: #fff;
      padding: 20px;
      border-radius: 15px;
    }
  }
}
.show {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  background: #f3f3f3;
  width: 100%;
  min-height: calc(100vh - 250px);
  overflow-x: hidden;
  bottom: 0;
}
</style>
