<template>
  <n-menu
    class="l-menu"
    :options="routesStore.getRoutes"
    key-field="name"
    label-field="name"
    :default-expanded-keys="defaultExpandedKeys"
    :render-label="renderMenuLabel"
    :value="defaultValue"
    @update:value="onMenuSelect"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import { useRouterStore } from "@/stores/modules/routes"

const route = useRoute()
const router = useRouter()
const routesStore = useRouterStore()
const defaultExpandedKeys = $computed<any[]>(() => {
  return route.matched.map((v) => v.name)
})
const defaultValue = $computed<any>(() => {
  const parentRoute = route.matched[0]
  const currentRoute = route.matched[route.matched.length - 1]
  return (
    route.query.activeName ||
    currentRoute?.meta?.activeName ||
    currentRoute?.name ||
    parentRoute?.name ||
    ""
  )
})

//渲染菜单文字
function renderMenuLabel(option) {
  return option?.meta?.title || option.name
}

function onMenuSelect(key: string, item: any) {
  router.push({
    name: item.name,
  })
  $g.bus.emit("changeMenu")
}
</script>
<style lang="scss">
.n-menu.n-menu--horizontal .n-menu-item-content {
  padding: 0 10px !important;
}
.n-menu .n-menu-item-content.n-menu-item-content--selected::before {
  background: theme("colors.primary") !important;
  color: #fff !important;
}

.n-menu-item-content--child-active {
  .n-menu-item-content-header,
  .n-menu-item-content__arrow {
    color: theme("colors.primary") !important;
  }
}

.MixVertitalLayout {
  .n-menu-item-content--selected {
    .n-menu-item-content-header {
      color: #fff !important;
    }
  }
}

.TopNavLayout {
  .n-menu-item-content--selected {
    .n-menu-item-content-header {
      color: theme("colors.primary") !important;
    }
  }
  .n-dropdown-menu {
    .n-dropdown-option {
      &:not(:last-of-type) {
        margin-bottom: var(--n-space);
      }
    }
  }
}
</style>
