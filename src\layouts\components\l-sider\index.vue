<template>
  <n-layout-sider
    bordered
    :collapsed="false"
    collapse-mode="transform"
    :width="settings.siderWidth"
    :native-scrollbar="false"
  >
    <l-menu></l-menu>
  </n-layout-sider>
</template>
<script setup lang="ts">
import lMenu from "../l-menu/index.vue"
import { useSettingsStore } from "@/stores/modules/settings"
const settings = useSettingsStore()
</script>
<style lang="css" scoped></style>
