<template>
  <div class="flex items-center">
    <g-icon
      @click="backPage"
      name="ri-arrow-left-line"
      size="28"
      color="#5f6368"
      class="mr-10px"
    />
    <el-breadcrumb class="breadcrumb-container" separator=">">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="index">
        <a @click.prevent="handleLink(item.redirect)">
          <g-icon v-if="item.meta && item.meta.icon" :icon="item.meta.icon" />
          {{ item.meta.title }}
        </a>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup>
import { useRouterStore } from "@/stores/modules/routes"
const route = useRoute()
const router = useRouter()
const routeStore = useRouterStore()

let levelList = $ref([])
watch(
  () => router.currentRoute.value,
  (n, to) => {
    levelList = getBreadcrumb()
  },
  { immediate: true },
)

function getBreadcrumb() {
  return route.matched.filter((item) => item.meta && item.meta.title)
}

function handleLink() {
  router.push(redirect)
}

function backPage(mode = 2) {
  enterMode.value = mode
  router.back()
}
</script>

<style lang="scss" scoped>
.breadcrumb-container {
  height: 30px;
  font-size: 16px;
  line-height: 30px;

  :deep() {
    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        a {
          font-weight: normal;
          color: #515a6e;
          font-weight: bold !important;
        }
      }

      &:last-child {
        .el-breadcrumb__inner {
          a {
            color: #999;
            font-weight: bold !important;
          }
        }
      }
    }
  }
}
</style>
