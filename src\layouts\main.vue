<template>
  <Suspense>
    <template #default>
      <component
        :class="settings.layout"
        :is="layoutComponents[settings.layout]"
      />
    </template>
    <template #fallback>
      <div class="app-loading">
        <div class="app-loading-wrap">
          <img src="/static/img/logo.png" class="app-loading-logo" alt="Logo" />
          <div class="app-loading-dots">
            <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
          </div>
          <div class="app-loading-title">金字塔后台管理</div>
        </div>
      </div>
    </template>
  </Suspense>
</template>

<script setup lang="ts">
import MixVertitalLayout from "./MixVertitalLayout/index.vue"
import TopNavLayout from "./TopNavLayout/index.vue"
import { useSettingsStore } from "@/stores/modules/settings"

const settings = useSettingsStore()

const layoutComponents = {
  MixVertitalLayout,
  TopNavLayout,
}
</script>
