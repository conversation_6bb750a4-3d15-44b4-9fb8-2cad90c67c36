import { createApp } from "vue"
import { setupStore } from "@/stores"
import { setupRouter } from "@/router"
import router from "@/router/index"
import { setupPlugins } from "@/plugins/index"
import "@/plugins/tokenSync"

import App from "./App.vue"

const app = createApp(App)

setupStore(app)
setupRouter(app)
setupPlugins(app)

app.mount("#app")

// app.config.errorHandler = (err: any, vm: any, info: any) => {
//   console.log("⚡ app errorHandler ==> ", { err, vm, info })
//   if (err.message.includes("No match for")) {
//     // 路由不匹配错误
//     router.push("/404")
//   }
// }

window.addEventListener("error", (err) => {
  console.log("⚡ app errorHandler2 ==> ", err)
  if (err.message.includes("No match for")) {
    // 路由不匹配错误
    router.push("/404")
  }
})
