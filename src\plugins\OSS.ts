import axios from "axios"
import EventEmitter from "eventemitter3"
import { getOssSignature } from "@/api/common"

class OSS extends EventEmitter {
  OSSConfig
  CancelTokenArr = []
  // 允许上传的文件类型
  allow_file_extension
  // 允许文件大小
  allow_file_size
  // 走云点播
  cloudOnDemand
  uploader
  ossOtherConfig
  constructor(config?) {
    super()
    this.ossOtherConfig = config || {}
    $g.bus.on("handleClose", (id) => {
      this.cancelUpload(id)
    })
  }

  /* 获取oss签名 */
  async getSignature({ params, api }: { params: any; api?: any }) {
    try {
      console.log("💊 params ==> ", params)
      const getOssApi = api || getOssSignature
      await getOssApi({ ...params }).then((data) => {
        this.OSSConfig = {
          OSSAccessKeyId: data.accessId,
          policy: data.policy,
          signature: data.signature,
          dir: data.uploadDir,
          host: data.host,
          file_name: data.fileName,
          absolute_path: data.absolutePath,
        }
        this.allow_file_size = data.allowFileSize
        this.allow_file_extension = data.allowFileExtension
          ? String(
              data.allowFileExtension.map((e) => {
                return "." + e
              }),
            )
          : ""
      })
    } catch (error: any) {
      if (error?.data?.code == 40010) {
        $g.msg(`${params.parentPath}下已经存在${params.fileName}文件!`, "error")
      }
      return { error }
    }
  }

  /* 单个上传文件至oss,基础上传 */
  async uploadFile(fileObj, form, api?) {
    const { file, id, name, size, parentPath } = fileObj
    let lastTime = 0 //上一次计算时间
    let lastSize = 0 //上一次计算的文件大小
    const type = $g.tool.getExt(name)
    await this.getSignature({
      params: { fileSize: size, fileName: name, parentPath },
      api,
    })
    const dir = this.OSSConfig.dir + this.OSSConfig.file_name + type
    const formData = this.handleFormData({ file, type, name, dir })
    const CancelToken = axios.CancelToken

    return new Promise((resolve, reject) => {
      axios
        .post(this.OSSConfig.host, formData, {
          timeout: this.ossOtherConfig?.timeout || 0, // 0表示无超时时间
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          onUploadProgress: (event: any) => {
            const nowTime = new Date().getTime()
            const intervalTime = (nowTime - lastTime) / 1000
            const intervalSize = event.loaded - lastSize

            lastTime = nowTime
            lastSize = event.loaded

            let speed = intervalSize / intervalTime
            let units = "b/s" //单位名称
            if (speed / 1024 > 1) {
              speed = speed / 1024
              units = "k/s"
            }
            if (speed / 1024 > 1) {
              speed = speed / 1024
              units = "M/s"
            }
            const val = Math.floor((event.loaded / event.total) * 100)
            this.emit(
              "onProgress",
              { val, speed: speed.toFixed(1) + units },
              id,
            )
          },
          cancelToken: new CancelToken(
            function executor(c) {
              // @ts-ignore
              this.CancelTokenArr.push({ id, c })
            }.bind(this),
          ),
        })
        .then(() => {
          // 返回给后端地址
          let resource_url = dir
          const fullUrl = this.OSSConfig.host + "/" + dir
          resource_url = this.OSSConfig.host + "/" + dir
          resolve({ resource_url, fullUrl })
        })
        .catch((err) => {
          if (err.code === "ECONNABORTED" && err.message.includes("timeout")) {
            const timeoutMsg =
              this.ossOtherConfig?.timeoutMsg || "上传超时，请重试"
            $g.msg(timeoutMsg, "warning")
          }
          reject({ file, err, form, isCancel: axios.isCancel(err) })
        })
        .finally(() => {
          this.CancelTokenArr.splice(
            $g._.findIndex(this.CancelTokenArr, ["id", id]),
            1,
          )
        })
    })
  }

  /* 处理上传数据 */
  handleFormData({ file, type, name, dir }) {
    const formData = new FormData()
    formData.set(
      "Content-Disposition",
      `attachment;filename="${name || this.OSSConfig.file_name}"`,
    )
    formData.set("OSSAccessKeyId", this.OSSConfig.OSSAccessKeyId)
    formData.set("policy", this.OSSConfig.policy)
    formData.set("signature", this.OSSConfig.signature)
    formData.set("dir", this.OSSConfig.dir)
    formData.set("success_action_status", "200")
    formData.set("key", dir)
    formData.set("file", file)
    return formData
  }

  /* 取消上传 */
  cancelUpload(id?: any) {
    if (!this.CancelTokenArr.length) return
    if (id) {
      const item: any = $g._.find(this.CancelTokenArr, ["id", id])
      item.c()
    } else {
      this.CancelTokenArr.forEach((e: any) => {
        e.c()
      })
    }
  }
}

export default OSS
