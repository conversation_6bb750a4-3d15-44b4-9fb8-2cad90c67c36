// 加载全局样式样式
import "@/styles/index.scss"
// import "remixicon/fonts/remixicon.css"
import "virtual:svg-icons-register"
// import { registerSentry } from "./sentry"
import { registeredVMdEditor } from "./vMdEditor"
import { logInfo } from "./logInfo"
export function setupPlugins(app: any) {
  // registerSentry(app)
  registeredVMdEditor(app)

  app.use({
    install(app, ...options) {},
  })
  // 打印框架
  console.log(
    "%cvite-admin-template 加载成功 🚀",
    `padding: 4px 20px;
      font-weight:bold;
      color:#fff;
      background:#61afef;
      border-radius: 10px;
      `,
  )
  // 打印信息
  logInfo()
}
