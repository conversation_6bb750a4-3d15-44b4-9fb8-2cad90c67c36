export function logInfo() {
  const VITE_APP_ENV = import.meta.env.VITE_APP_ENV

  if (VITE_APP_ENV == "development") return

  setTimeout(() => {
    logFn({
      title: "打包时间：",
      value: $g.dayjs(__APP_VERSION__).format("YYYY-MM-DD HH:mm"),
      bgColor: "#41b883",
    })
    logFn({
      title: "user",
      value: JSON.parse(localStorage?.getItem("user") as string)?.userInfo
        ?.accountName,
      bgColor: "#ff9f03",
    })
  }, 1500)
}

function logFn({ title, value, bgColor = "#ff9f03" }) {
  console.log(
    "%c " + `${title}` + " %c " + value + " " + "%c",
    "background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff",
    `background:${bgColor} ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff`,
    "background:transparent",
  )
}
