import { createDiscrete<PERSON><PERSON> } from "naive-ui"

const MessageProviderProps = computed(() => ({
  max: 3,
}))
const notificationProviderProps = computed(() => ({
  max: 3,
  containerStyle: {
    "--n-icon-color": "#278ef5",
  },
}))
const { message, notification, dialog, loadingBar } = createDiscreteApi(
  ["message", "dialog", "notification", "loadingBar"],
  {
    messageProviderProps: MessageProviderProps,
    notificationProviderProps: notificationProviderProps,
  },
)

function msg(
  msg: string,
  type: "success" | "error" | "info" | "warning" | "loading" = "success",
  duration: number = 3000,
) {
  return message[type](msg, {
    type: "success",
    duration,
  })
}

function confirm(config) {
  return new Promise<void>((resolve, reject) => {
    const {
      type = "warning",
      title = "温馨提示",
      content = "内容",
      positiveText = "确定",
      negativeText = "取消",
    } = config
    dialog[type]({
      title,
      content,
      positiveText,
      negativeText,
      onPositiveClick: () => {
        resolve()
      },
      onNegativeClick: () => {
        reject()
      },
    })
  })
}

const vant = {
  msg,
  confirm,
  notification,
  loadingBar,
}

// const meta = document.createElement("meta");
// meta.name = "naive-ui-style";
// document.head.appendChild(meta);

export default vant
