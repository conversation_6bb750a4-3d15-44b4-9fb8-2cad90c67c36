import {
  init,
  browserTracingIntegration,
  addIntegration,
  setUser,
  setTag,
} from "@sentry/vue"
import { useUserStore } from "@/stores/modules/user"

export function registerSentry(app) {
  if (process.env.NODE_ENV === "production") {
    // 只在生产环境中开启sentry，调试时可以先去掉
    init({
      app,
      dsn: "https://<EMAIL>/4507547916238848",
      integrations: [browserTracingIntegration()],
      ignoreErrors: [
        /request aborted/i,
        /timeout/i,
        /network error/i,
        /dynamically imported module/i,
        /Error: 请输入/i,
      ],
      environment: import.meta.env.VITE_APP_ENV,
      // 性能监控
      tracesSampleRate: 1.0, //  Capture 100% of the transactions
      // Session Replay
      replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
      replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
      normalizeDepth: 5,
      release: $g.dayjs(__APP_VERSION__).format("YYYY-MM-DD HH:mm"),
      beforeSend: (event, hint: any) => {
        const originalException = hint.originalException

        if (originalException?.data?.code) {
          const ignoreArr = [
            400207, 401, 400002, 400173, 40004, 40002, 400005, 40005, 40010,
            40001, 40017, 500, 40010,
          ]
          if (ignoreArr.includes(originalException?.data?.code)) {
            return null
          }
          // 给不同消息添加签名
          event.fingerprint = [
            "{{ default }}",
            String(originalException.config.url),
          ]
        }
        //过滤富文本的报错
        if (originalException?.stack) {
          if (originalException.stack?.includes("/ueditor/ueditor.all"))
            return null
        }

        return event
      },
    })

    setTimeout(async () => {
      const { userInfo } = $(storeToRefs(useUserStore()))
      const { replayIntegration } = await import("@sentry/vue")

      if (import.meta.env.VITE_APP_ENV === "production") {
        addIntegration(
          replayIntegration({
            maskAllText: false,
            blockAllMedia: false,
            networkDetailAllowUrls: [".qimingdaren.com"],
          }),
        )
      }

      setUser({
        账号信息: userInfo,
      })
      setTag("accountName", userInfo?.accountName)
    }, 2222)
  }
}
