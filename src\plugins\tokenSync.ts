import config from "@/config"
const { threeTokenTableName } = config

class TokenSync {
  private static tokenStorageKey = threeTokenTableName

  constructor() {
    window.parent.postMessage({ event: "ready" }, "*")
    window.addEventListener("message", this.handleMessage.bind(this))
  }

  public setToken(token: string): void {
    localStorage.setItem(TokenSync.tokenStorageKey, token)
  }

  private handleMessage(event: MessageEvent): void {
    if (event.data && event.data.token) {
      console.log("receive")
      this.setToken(event.data.token)
    }
  }
}
export const tokenSync = new TokenSync()
