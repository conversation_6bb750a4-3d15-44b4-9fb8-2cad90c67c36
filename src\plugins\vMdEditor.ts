import VueUeditorWrap from "vue-ueditor-wrap"
import VueMarkdownEditor from "@l9m/v-md-editor" // 编辑组件
import VMdPreview from "@l9m/v-md-editor/lib/preview" // 预览组件
import VMdPreviewStream from "@l9m/v-md-editor/lib/preview-stream" //
import githubTheme from "@l9m/v-md-editor/lib/theme/github.js" // 主题

import createTipPlugin from "@l9m/v-md-editor/lib/plugins/tip/index" // 提示插件
import createLineNumbertPlugin from "@l9m/v-md-editor/lib/plugins/line-number/index" // 代码行号
import createCopyCodePlugin from "@l9m/v-md-editor/lib/plugins/copy-code/index" // 快捷复制代码
import createEmojiPlugin from "@l9m/v-md-editor/lib/plugins/emoji/index" // emoji插件
import createKatexPlugin from "@l9m/v-md-editor/lib/plugins/katex/cdn" // katex公式插件
import createMermaidPlugin from "@l9m/v-md-editor/lib/plugins/mermaid/cdn" // mermaid插件
import createTodoListPlugin from "@l9m/v-md-editor/lib/plugins/todo-list/index" // to-do list插件
import createAlignPlugin from "@l9m/v-md-editor/lib/plugins/align" // 内容定位 // to-do list插件
import createIncrementalDomPlugin from "@l9m/v-md-editor/lib/plugins/incremental-dom/npm" // 增量渲染
import createCursorPlugin from "@l9m/v-md-editor/lib/plugins/cursor/index"

import "@l9m/v-md-editor/lib/style/base-editor.css"
import "@l9m/v-md-editor/lib/theme/style/github.css"
import "@l9m/v-md-editor/lib/style/preview.css"
import "@l9m/v-md-editor/lib/plugins/emoji/emoji.css"
import "@l9m/v-md-editor/lib/plugins/mermaid/mermaid.css"
import "@l9m/v-md-editor/lib/plugins/todo-list/todo-list.css"
import "@l9m/v-md-editor/lib/plugins/tip/tip.css"
import "@l9m/v-md-editor/lib/plugins/copy-code/copy-code.css"

export function registeredVMdEditor(app) {
  const commonPlugins = (editor, type) => {
    editor.use(githubTheme)
    editor.use(createTipPlugin())
    editor.use(createEmojiPlugin())
    editor.use(
      createKatexPlugin({
        enableMathBlockInHtml: true, //开启在 html 显示块级公式
        enableMathInlineInHtml: true, //开启在 html 显示行级公式
        strict: false, // 是否开启严格模式 关闭 katex 警告
        useWebWorker: ["editor", "preview"].includes(type), //是否开启webworker，编辑默认关闭，预览开启
        useSyncCache: ["editor"].includes(type), // 同步缓存，开启 useWebWorker 后生效
      }),
    )
    editor.use(createMermaidPlugin())
    editor.use(createTodoListPlugin())
    editor.use(createLineNumbertPlugin())
    editor.use(createCopyCodePlugin())
    editor.use(createAlignPlugin())
    editor.use(createCursorPlugin())

    editor.use(createIncrementalDomPlugin())
  }
  commonPlugins(VMdPreviewStream, "stream")
  commonPlugins(VueMarkdownEditor, "editor")

  commonPlugins(VMdPreview, "preview")

  function extendXss(app) {
    app.xss.extend({
      // 扩展白名单
      whiteList: {
        iframe: ["src", "width", "height", "frameborder"],
      },
    })
  }

  extendXss(VMdPreview)
  extendXss(VMdPreviewStream)

  app.use(VueMarkdownEditor)
  app.use(VMdPreview)
  app.use(VueUeditorWrap)
  app.use(VMdPreviewStream)
}
