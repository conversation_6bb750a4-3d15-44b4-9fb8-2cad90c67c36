import config from "@/config"
import Layout from "@/layouts/main.vue"
import type { RouteRecordName, RouteRecordRaw } from "vue-router"
import { createRouter, createWebHashHistory } from "vue-router"
import { setupPermissions } from "./permissions"

/*
 **  meta参数lock: true  书籍/试卷管理下的页面需添加
 */
export const constantRoutes = [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录页面",
      hidden: true,
    },
  },

  {
    path: "/:catchAll(.*)",
    component: () => import("@/views/error/404.vue"),
    meta: {
      hidden: true,
    },
  },
  {
    path: "/demo",
    name: "demo",
    component: () => import("@/views/demo/index.vue"),
    meta: {
      title: "demo",
      hidden: true,
    },
  },
  {
    path: "/star",
    name: "Star",
    hidden: true,
    component: () => import("@/views/star/index.vue"),
    meta: {
      title: "星空图",
      icon: "",
      hidden: true,
    },
  },
  {
    path: "/iframe",
    name: "Iframe",
    component: () => import("@/views/iframe/index.vue"),
    meta: {
      title: "学科网",
      hidden: true,
      notShowLayout: true,
    },
  },
  {
    path: "/teacherIframe",
    name: "TeacherIframe",
    component: () => import("@/views/teacherIframe/index.vue"),
    meta: {
      title: "教师端学科网",
      hidden: true,
      notShowLayout: true,
    },
  },
  {
    path: "/teacherUserIframe",
    name: "TeacherUserIframe",
    component: () => import("@/views/teacherUserIframe/index.vue"),
    meta: {
      title: "教师用户端学科网",
      hidden: true,
      notShowLayout: true,
    },
  },
  {
    path: "/newQuestionBankIframe",
    name: "NewQuestionBankIframe",
    component: () => import("@/views/newQuestionBankIframe/index.vue"),
    meta: {
      title: "新题库学科网",
      hidden: true,
      notShowLayout: true,
    },
  },
  {
    path: "/third",
    name: "Third",
    meta: {
      title: "学科网",
      hidden: true,
    },
    children: [
      {
        path: "authRedirect",
        name: "AuthRedirect",
        component: () => import("@/views/authRedirect/index.vue"),
        meta: {
          title: "授权成功回调页面",
          keepAlive: true,
          notShowLayout: true,
        },
      },
      {
        path: "teacherURedirect",
        name: "TeacherURedirect",
        component: () => import("@/views/teacherURedirect/index.vue"),
        meta: {
          title: "教师用户端授权成功回调页面",
          keepAlive: true,
          notShowLayout: true,
        },
      },
      {
        path: "newQuestionBankRedirect",
        name: "NewQuestionBankRedirect",
        component: () => import("@/views/newQuestionBankRedirect/index.vue"),
        meta: {
          title: "新题库授权成功回调页面",
          keepAlive: true,
          notShowLayout: true,
        },
      },
    ],
  },
]
export const asyncRoutes = [
  {
    path: "/",
    name: "Root",
    component: Layout,
    meta: {
      title: "用户管理",
    },
    redirect: "StudentType",
    children: [
      {
        path: "studentType",
        name: "StudentType",
        component: () =>
          import("@/views/decisionSystemConfig/studentType/index.vue"),
        meta: {
          title: "学生账号",
          keepAlive: true,
        },
      },
      {
        path: "role",
        name: "Role",
        component: () => import("@/views/decisionSystemConfig/role/index.vue"),
        meta: {
          title: "角色管理",
        },
      },
      {
        path: "backgroundType",
        name: "BackgroundType",
        component: () =>
          import("@/views/decisionSystemConfig/backgroundType/index.vue"),
        meta: {
          title: "后台账号",
          keepAlive: true,
        },
      },
      {
        path: "schoolList",
        name: "SchoolList",
        component: () =>
          import("@/views/decisionSystemConfig/school/index.vue"),
        meta: {
          title: "学校列表",
        },
      },
      {
        path: "xkwAuthList",
        name: "XkwAuthList",
        component: () =>
          import("@/views/decisionSystemConfig/xkwAuthList/index.vue"),
        meta: {
          title: "学科网授权列表",
          keepAlive: true,
        },
      },
      {
        path: "aiProject",
        name: "AiProject",
        component: () =>
          import("@/views/decisionSystemConfig/aiProject/index.vue"),
        meta: {
          title: "AI计划",
          hidden: true,
          activeName: "StudentType",
        },
      },
      {
        path: "projectResult",
        name: "ProjectResult",
        component: () =>
          import("@/views/decisionSystemConfig/projectResult/index.vue"),
        meta: {
          title: "AI计划生成结果",
          hidden: true,
          activeName: "StudentType",
        },
      },
      {
        path: "learningStatistics",
        name: "LearningStatistics",
        component: () =>
          import("@/views/decisionSystemConfig/learningStatistics/index.vue"),
        meta: {
          title: "学习情况统计",
        },
      },
    ],
  },
  {
    path: "/resourceMgt",
    name: "ResourceMgt",
    component: Layout,
    meta: {
      title: "资源管理",
    },
    redirect: "TeachMaterialMgt",
    children: [
      {
        path: "teachMaterialMgt",
        name: "TeachMaterialMgt",
        component: () =>
          import("@/views/resourceMgt/teachMaterialMgt/index.vue"),
        meta: {
          title: "版本管理",
        },
      },
      {
        path: "chapterManage",
        name: "ChapterManage",
        component: () => import("@/views/resourceMgt/chapterManage/index.vue"),
        meta: {
          title: "教材管理",
        },
      },
      {
        path: "knowledge",
        name: "Knowledge",
        component: () => import("@/views/resourceMgt/knowledge/index.vue"),
        meta: {
          title: "章节知识点绑定",
        },
      },
      {
        path: "knowledgeQuestion",
        name: "KnowledgeQuestion",
        component: () =>
          import("@/views/resourceMgt/knowledgeQuestion/index.vue"),
        meta: {
          title: "章节知识点题目数据",
        },
      },
      {
        path: "ability",
        name: "Ability",
        component: () => import("@/views/resourceMgt/ability/index.vue"),
        meta: {
          title: "能力/学科素养",
        },
      },
      {
        path: "resourceTree",
        name: "ResourceTree",
        component: () => import("@/views/resourceMgt/resourceTree/index.vue"),
        meta: {
          title: "资源总树",
        },
      },
      {
        path: "resMain",
        name: "ResMain",
        component: () => import("@/views/resourceMgt/resource/index.vue"),
        meta: {
          title: "资源详情",
          activeName: "ResourceTree",
          hidden: true,
        },
      },
      {
        path: "bookManage",
        name: "BookManage",
        component: () => import("@/views/resourceMgt/bookManage/index.vue"),
        meta: {
          title: "书籍管理",
          keepAliveArr: ["EnterResoure", "TypeManage"],
        },
      },
      {
        path: "paperManage",
        name: "PaperManage",
        component: () => import("@/views/resourceMgt/paperManage/index.vue"),
        meta: {
          title: "试卷管理",
          keepAliveArr: ["EnterResoure", "TypeManage"],
        },
      },
      {
        path: "enterResoure",
        name: "EnterResoure",
        component: () =>
          import("@/views/resourceMgt/bookManage/enterResoure/index.vue"),
        meta: {
          title: "书籍/试卷管理详情",
          hidden: true,
          activeName: "BookManage",
          keepAliveArr: [
            "PaperEdit",
            "BindChapter",
            "AnswerDetails",
            "AutoCorrecting",
            "PaperDescription",
            "AiGuideDesign",
          ],
          lock: true,
        },
      },
      {
        path: "autoCorrecting",
        name: "AutoCorrecting",
        component: () =>
          import("@/views/resourceMgt/bookManage/autoCorrecting/index.vue"),
        meta: {
          title: "自动批改",
          hidden: true,
          activeName: "BookManage",
          lock: true,
        },
      },
      {
        path: "bookDetail",
        name: "BookDetail",
        component: () => import("@/views/resourceMgt/bookDetail/index.vue"),
        meta: {
          title: "教材详情",
          hidden: true,
          activeName: "ResourceTree",
        },
      },
      {
        path: "chapterMaintenance",
        name: "ChapterMaintenance",
        component: () =>
          import("@/views/resourceMgt/chapterMaintenance/index.vue"),
        meta: {
          title: "章节维护",
          hidden: true,
          activeName: "ChapterManage",
        },
      },
      {
        path: "typeManage",
        name: "TypeManage",
        component: () => import("@/views/resourceMgt/typeManage/index.vue"),
        meta: {
          title: "类型维护",
          hidden: true,
          activeName: "BookManage",
        },
      },
      {
        path: "paperEdit",
        name: "PaperEdit",
        component: () => import("@/views/resourceMgt/paperEdit/index.vue"),
        meta: {
          title: "试卷编辑",
          hidden: true,
          activeName: "BookManage",
          lock: true,
        },
      },
      {
        path: "aiGuideDesign",
        name: "AiGuideDesign",
        component: () => import("@/views/resourceMgt/aiGuideDesign/index.vue"),
        meta: {
          title: "流程编辑",
          hidden: true,
          activeName: "BookManage",
          lock: true,
        },
      },
      {
        path: "paperDescription",
        name: "PaperDescription",
        component: () =>
          import("@/views/resourceMgt/paperDescription/index.vue"),
        meta: {
          title: "试题描述",
          hidden: true,
          activeName: "BookManage",
          lock: true,
        },
      },
      {
        path: "questionSearch",
        name: "QuestionSearch",
        component: () =>
          import("@/views/resourceMgt/bookManage/searchQuestion/index.vue"),
        meta: {
          title: "题目搜索",
          hidden: true,
          activeName: "BookManage",
          keepAliveArr: ["PaperEdit"],
        },
      },
      {
        path: "aiRecognition",
        name: "AiRecognition",
        component: () => import("@/views/resourceMgt/aiRecognition/index.vue"),
        meta: {
          title: "AI单题识别",
          activeName: "AiRecognition",
          hidden: true,
          lock: true,
        },
      },
      {
        path: "bindChapter",
        name: "BindChapter",
        component: () =>
          import("@/views/resourceMgt/bookManage/bindChapter/index.vue"),
        meta: {
          title: "关联总树章节",
          hidden: true,
          lock: true,
        },
      },
      {
        path: "answerDetails",
        name: "AnswerDetails",
        component: () =>
          import("@/views/resourceMgt/bookManage/answerDetails/index.vue"),
        meta: {
          title: "作答详情",
          hidden: true,
          lock: true, //书籍/试卷管理下的页面需添加
        },
      },
    ],
  },
  {
    path: "/activityManage",
    name: "ActivityManage",
    component: Layout,
    meta: {
      title: "活动管理",
    },
    redirect: "/activityManage/index",
    children: [
      {
        path: "index",
        name: "ActivityManageIndex",
        component: () =>
          import("@/views/activityManage/activityMain/index.vue"),
        meta: {
          title: "活动管理首页",
          breadcrumbHiddenTitle: true,
          hidden: true,
          activeName: "ActivityManage",
          keepAliveArr: ["ActivityDetail"],
        },
      },
      {
        path: "activityDetail",
        name: "ActivityDetail",
        component: () =>
          import("@/views/activityManage/activityDetail/index.vue"),
        meta: {
          hidden: true,
          activeName: "ActivityManage",
          title: "活动详情",
          keepAliveArr: ["ActivityModuleDetail"],
        },
      },
      {
        path: "activityModuleDetail",
        name: "ActivityModuleDetail",
        component: () =>
          import("@/views/activityManage/moduleDetail/index.vue"),
        meta: {
          hidden: true,
          activeName: "ActivityManage",
          title: "板块详情",
        },
      },
      {
        path: "reportDetail",
        name: "ReportDetail",
        component: () =>
          import("@/views/activityManage/reportDetail/index.vue"),
        meta: {
          title: "报告详情",
          hidden: true,
          activeName: "ActivityManage",
        },
      },
    ],
  },
  {
    path: "/statistics",
    name: "Statistics",
    component: Layout,
    meta: {
      title: "学生纠错统计",
    },
    redirect: "/statistics/index",
    children: [
      {
        path: "index",
        name: "StatisticsIndex",
        component: () => import("@/views/statistics/correction/index.vue"),
        meta: {
          title: "学生纠错首页",
          breadcrumbHiddenTitle: true,
          hidden: true,
          activeName: "Statistics",
          keepAliveArr: ["QuestionEdit"],
        },
      },
      {
        path: "questionEdit",
        name: "QuestionEdit",
        component: () => import("@/views/statistics/questionEdit/index.vue"),
        meta: {
          title: "修正详情",
          hidden: true,
          activeName: "Statistics",
        },
      },
    ],
  },
  {
    path: "/clientMgt",
    name: "ClientMgt",
    component: Layout,
    meta: {
      title: "客户端管理",
    },
    redirect: "HardwareMgt",
    children: [
      {
        path: "hardwareMgt",
        name: "HardwareMgt",
        component: () => import("@/views/clientMgt/hardwareMgt/index.vue"),
        meta: {
          title: "固件管理",
        },
      },
      {
        path: "protocolMgt",
        name: "ProtocolMgt",
        component: () => import("@/views/clientMgt/protocolMgt/index.vue"),
        meta: {
          title: "协议管理",
          keepAliveArr: ["ProtocolMgtAdd"],
        },
      },
      {
        path: "protocolMgtAdd",
        name: "ProtocolMgtAdd",
        component: () => import("@/views/clientMgt/protocolMgt/add/index.vue"),
        meta: {
          title: "新增协议",
          hidden: true,
          activeName: "ProtocolMgt",
        },
      },
      {
        path: "aiReport",
        name: "AiReport",
        component: () => import("@/views/clientMgt/aiReport/index.vue"),
        meta: {
          title: "AI老师讲解举报管理",
          activeName: "AiReport",
          keepAliveArr: ["AiReportDetail"],
        },
      },
      {
        path: "aiReportDetail",
        name: "AiReportDetail",
        component: () => import("@/views/clientMgt/aiReport/detail/index.vue"),
        meta: {
          title: "查看详情",
          activeName: "AiReport",
          hidden: true,
        },
      },
      {
        path: "trilateralApplication",
        name: "TrilateralApplication",
        component: () =>
          import("@/views/clientMgt/trilateralApplication/index.vue"),
        meta: {
          title: "第三方应用（梦派）",
        },
      },
      {
        path: "addOrEditTrilateralApp",
        name: "AddOrEditTrilateralApp",
        component: () =>
          import("@/views/clientMgt/trilateralApplication/add/index.vue"),
        meta: {
          title: "",
          activeName: "TrilateralApplication",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/fingerSearch",
    name: "FingerSearch",
    component: Layout,
    meta: {
      title: "指尖识别结果",
    },
    redirect: "/fingerSearch/index",
    children: [
      {
        path: "index",
        name: "FingerSearchIndex",
        component: () => import("@/views/fingerSearch/index.vue"),
        meta: {
          title: "指尖识别结果主页",
          breadcrumbHiddenTitle: true,
          hidden: true,
          activeName: "FingerSearch",
        },
      },
    ],
  },
  {
    path: "/audioMgt",
    name: "AudioMgt",
    component: Layout,
    meta: {
      title: "音频管理",
    },
    redirect: "HardwareMgt",
    children: [
      {
        path: "audioInfo",
        name: "AudioInfo",
        component: () => import("@/views/audioMgt/audioInfo/index.vue"),
        meta: {
          title: "音频管理",
          keepAliveArr: ["AudioInfoDetail"],
        },
      },
      {
        path: "audioInfoDetail",
        name: "AudioInfoDetail",
        component: () => import("@/views/audioMgt/audioInfo/detail/index.vue"),
        meta: {
          title: "音频管理详情",
          hidden: true,
          activeName: "AudioInfo",
        },
      },
      {
        path: "serviceConfiguration",
        name: "ServiceConfiguration",
        component: () =>
          import("@/views/audioMgt/serviceConfiguration/index.vue"),
        meta: {
          title: "服务配置",
        },
      },
      {
        path: "realTimeAudioManage",
        name: "RealTimeAudioManage",
        component: () =>
          import("@/views/audioMgt/realTimeAudioManage/index.vue"),
        meta: {
          title: "实时语音管理",
        },
      },
      {
        path: "realTimeAudioInfoDetail",
        name: "RealTimeAudioInfoDetail",
        component: () =>
          import("@/views/audioMgt/realTimeAudioManage/detail/index.vue"),
        meta: {
          title: "实时语音详情",
          hidden: true,
          activeName: "RealTimeAudioManage",
        },
      },
    ],
  },
  {
    path: "/autoCorrect",
    name: "AutoCorrect",
    component: Layout,
    meta: {
      title: "自动批改管理",
    },
    redirect: "AutoCorrectList",
    children: [
      {
        path: "autoCorrectList",
        name: "AutoCorrectList",
        component: () => import("@/views/autoCorrect/list/index.vue"),
        meta: {
          title: "自动批改管理",
          keepAliveArr: ["AutoCorrectDetail"],
        },
      },
      {
        path: "autoCorrectDetail",
        name: "AutoCorrectDetail",
        component: () => import("@/views/autoCorrect/detail/index.vue"),
        meta: {
          title: "自动批改详情",
          hidden: true,
          activeName: "AutoCorrectList",
        },
      },
      {
        path: "autoCorrectServiceConfiguration",
        name: "AutoCorrectServiceConfiguration",
        component: () =>
          import("@/views/autoCorrect/serviceConfiguration/index.vue"),
        meta: {
          title: "服务配置",
        },
      },
    ],
  },
  {
    path: "/fontResourceUpload",
    name: "FontResourceUpload",
    component: Layout,
    meta: {
      title: "前端资源文件上传",
    },
    redirect: "/fontResourceUpload/uploadMain",
    children: [
      {
        path: "uploadMain",
        name: "UploadMain",
        component: () => import("@/views/fontResourceUpload/index.vue"),
        meta: {
          title: "前端资源文件上传",
          breadcrumbHiddenTitle: true,
          hidden: true,
          activeName: "FontResourceUpload",
        },
      },
    ],
  },
  {
    path: "/teacherMgt",
    name: "TeacherMgt",
    component: Layout,
    meta: {
      title: "教师端管理",
    },
    redirect: "SchoolLogin",
    children: [
      {
        path: "schoolLogin",
        name: "SchoolLogin",
        component: () => import("@/views/teacherMgt/schoolLogin/index.vue"),
        meta: {
          title: "学校登录管理",
          keepAliveArr: ["EditRecord"],
        },
      },
      {
        path: "editRecord",
        name: "EditRecord",
        component: () =>
          import("@/views/teacherMgt/schoolLogin/editRecord/index.vue"),
        meta: {
          title: "修改记录",
          hidden: true,
          activeName: "SchoolLogin",
        },
      },
      {
        path: "learnTaskMgt",
        name: "LearnTaskMgt",
        component: () => import("@/views/teacherMgt/learnTaskMgt/index.vue"),
        meta: {
          title: "学习任务管理",
          keepAliveArr: ["LearnTaskMgtRecord"],
        },
      },
      {
        path: "learnTaskMgtRecord",
        name: "LearnTaskMgtRecord",
        component: () =>
          import("@/views/teacherMgt/learnTaskMgt/record/index.vue"),
        meta: {
          title: "修改记录",
          hidden: true,
          activeName: "LearnTaskMgt",
        },
      },
      {
        path: "diagnosisAndPlan",
        name: "DiagnosisAndPlan",
        component: () =>
          import("@/views/teacherMgt/diagnosisAndPlan/index.vue"),
        meta: {
          title: "诊断与计划",
        },
      },
      {
        path: "diagnosis",
        name: "Diagnosis",
        component: () =>
          import("@/views/teacherMgt/diagnosisAndPlan/diagnosis/index.vue"),
        meta: {
          title: "提示词",
          hidden: true,
          activeName: "DiagnosisAndPlan",
        },
      },
      {
        path: "studyPlan",
        name: "StudyPlan",
        component: () =>
          import("@/views/teacherMgt/diagnosisAndPlan/diagnosis/index.vue"),
        meta: {
          title: "提示词",
          hidden: true,
          activeName: "DiagnosisAndPlan",
        },
      },
    ],
  },
  {
    path: "/quickQuestion",
    name: "QuickQuestion",
    component: () => import("@/views/resourceMgt/quickQuestion/index.vue"),
    meta: {
      title: "快速录题",
      hidden: true,
      // notShowLayout: true,
    },
  },

  {
    path: "/paperBlocking",
    name: "PaperBlocking",
    component: Layout,
    redirect: "/paperBlocking/index",
    meta: {
      title: "考试屏蔽处理",
    },
    children: [
      {
        path: "index",
        name: "PaperBlockingIndex",
        component: () => import("@/views/teacherMgt/paperBlocking/index.vue"),
        meta: {
          title: "考试屏蔽处理",
          activeName: "PaperBlocking",
          breadcrumbHiddenTitle: true,
          hidden: true,
        },
      },
    ],
  },
  // {
  //   path: "/modelManagement", learnTaskMgt
  //   name: "ModelManagement",
  //   component: Layout,
  //   meta: {
  //     title: "大模型管理",
  //   },
  //   redirect: "/modelManagement/ModelMain",
  //   children: [
  //     {
  //       path: "modelMain",
  //       name: "ModelMain",
  //       component: () => import("@/views/modelManagement/index.vue"),
  //       meta: {
  //         title: "大模型管理",
  //         breadcrumbHiddenTitle: true,
  //         hidden: true,
  //         activeName: "ModelManagement",
  //         keepAliveArr: ["PromptWords"],
  //       },
  //     },
  //     {
  //       path: "promptWords",
  //       name: "PromptWords",
  //       component: () =>
  //         import("@/views/modelManagement/promptWords/index.vue"),
  //       meta: {
  //         title: "提示词",
  //         hidden: true,
  //         activeName: "ModelManagement",
  //         keepAliveArr: ["PromptEdit"],
  //       },
  //     },
  //     {
  //       path: "promptEdit",
  //       name: "PromptEdit",
  //       component: () => import("@/views/modelManagement/promptEdit/index.vue"),
  //       meta: {
  //         title: "提示词编辑",
  //         hidden: true,
  //         activeName: "ModelManagement",
  //       },
  //     },
  //   ],
  // },
]

export const iframeRoutes = []

function addRouter(routes) {
  routes.forEach((route) => {
    if (!router.hasRoute(route.name)) {
      router.addRoute(route)
    }
    if (route.children) addRouter(route.children)
  })
}

const router: any = createRouter({
  history: createWebHashHistory(""),
  routes: constantRoutes as RouteRecordRaw[],
})
function fatteningRoutes(routes) {
  return routes.flatMap((route) => {
    return route.children ? fatteningRoutes(route.children) : route
  })
  // let res: any = []
  // const fatten = (data) => {
  //   res = [...res, ...data]
  //   data.forEach((route) => {
  //     if (route.children) fatten(route.children)
  //   })
  // }
  // fatten(routes)
  // return res
}

export function resetRouter(routes = constantRoutes) {
  routes.forEach((route: any) => {
    if (route.children) route.children = fatteningRoutes(route.children)
  })
  router.getRoutes().forEach(({ name }) => {
    router.hasRoute(<RouteRecordName>name) &&
      router.removeRoute(<RouteRecordName>name)
  })
  addRouter(routes)
}

export async function setupRouter(app: any) {
  if (config.authentication === "intelligence") addRouter(asyncRoutes)
  setupPermissions(router)
  app.use(router)
  await router.isReady()
}

/* 处理后的路由,用于添加到权限配置平台路由表，需要时放开拿到完整路由接口 */
// setTimeout(() => {
//   const backEndRouting = stringifyMeta($g._.cloneDeep(asyncRoutes))
//   console.log("💊 backEndRouting ==> ", backEndRouting)
//   function stringifyMeta(tree) {
//     return tree.map((node) => {
//       if ($g.tool.typeOf(node.component) == "object") {
//         node.component = "Layout"
//       } else {
//         node.component = node.component.toString()
//         node.component = node.component.replace(
//           /\(\) => import\("(.+?\.vue)(\?[^"]*)?"\)/,
//           (match, p1) => {
//             // 替换路径前缀
//             const newPath = p1.replace("/src/", "@/")
//             return `"${newPath}"`
//           },
//         )
//         node.component = node.component.replace(/^"|"$/g, "")
//       }
//       if (node.meta) {
//         node.meta = JSON.stringify(node.meta)
//       }
//       if (node.children) {
//         node.children = stringifyMeta(node.children)
//       }
//       return node
//     })
//   }
// }, 2000)

export default router
