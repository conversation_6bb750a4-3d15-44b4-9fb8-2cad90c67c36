import NProgress from "nprogress"
import "nprogress/nprogress.css"
import { useRouterStore } from "@/stores/modules/routes"
import { useUserStore } from "@/stores/modules/user"
import { useResourceMgtStore } from "@/stores/modules/resourceMgt"
import { jumpNavigationLogin, getPageTitle } from "@/utils/routes"
import { getToken, removeToken, setToken } from "@/utils/token"
import cookie from "js-cookie"
import config from "@/config"
const { authentication, loginInterception, routesWhiteList, inIframe } = config
import axios from "axios"
import { hasLock } from "@/api/bookMgt"

/**
 * @description 路由守卫，目前两种模式：all模式与intelligence模式
 */
import type { Router } from "vue-router"
// 是否显示初始loading
let isShowLoading = true
let routeObj: any = {}
export function setupPermissions(router: Router) {
  NProgress.configure({
    easing: "ease",
    speed: 500,
    trickleSpeed: 200,
    showSpinner: false,
  })

  router.beforeEach(async (to: any, from: any, next: any) => {
    const { setRoutes, setIframeRoutes } = useRouterStore()
    const { routes } = storeToRefs(useRouterStore())
    const { token, exchangeToken, resetAll, setToken } = useUserStore()
    NProgress.start()
    routeObj = { to, from }
    versionCheck()
    // 如果路由上携带token,那么先兑换token登录
    if (to.query.token) await exchangeToken(to.query)

    let hasToken: string | boolean = token
    if (!loginInterception) hasToken = true
    if (inIframe) {
      if (routes.value.length) {
        next()
      } else {
        await setIframeRoutes()
        next({ ...to, replace: true })
      }
    } else if (hasToken) {
      if (routes.value.length) {
        //上锁与解锁
        // const shouldPreventNavigation = await lock(to, from)
        // if (shouldPreventNavigation === true) {
        //   next(false)
        //   return
        // } else if (shouldPreventNavigation) {
        //   next({ path: shouldPreventNavigation, replace: true })
        //   return
        // }
        // 禁止已登录用户返回登录页
        // if (to.path === "/login") {
        //   next({ path: "/" })
        //   NProgress.done()
        // } else {
        // 指定页面缓存
        restoreRollingBar(to, from, 1)
        keepAlive(to, from)
        next()
        // }
      } else {
        try {
          // if (loginInterception) await getUserInfo()
          // 根据路由模式获取路由并根据权限过滤
          await setRoutes(authentication)
          const authRouts = routes.value?.filter(
            (h) =>
              ![
                "/login",
                "/demo",
                "/:catchAll(.*)",
                "/star",
                "/iframe",
                ...config.routesWhiteList,
              ].includes(h.path),
          )
          if (!authRouts.length) {
            $g.msg("该用户没有权限，请重新登录", "info")
            const { resetAll } = useUserStore()
            await resetAll()
          }
          next({ ...to, replace: true })
        } catch (err: any) {
          //打印设置路由时的报错信息，方便排错
          console.error("路由设置出现错误", err)
          await resetAll()

          setTimeout(() => {
            config.SSO
              ? next(jumpNavigationLogin(to.path))
              : next({ path: "/login", replace: true })
          }, 1000)
          NProgress.done()
        }
      }
    } else {
      if (routesWhiteList.includes(to.path)) {
        next()
      } else {
        config.SSO
          ? next(jumpNavigationLogin(to.path))
          : next({ path: "/login", replace: true })
      }
    }
  })

  router.afterEach((to: any, from: any) => {
    document.title = getPageTitle(to.meta.title)
    NProgress.done()
    removeLoading()
    restoreRollingBar(to, from, 2)
    nextTick(() => {
      if (to.meta.keepAlive) return
      const rightMain: any = document.getElementById("right-main")
      rightMain &&
        (rightMain.getElementsByClassName(
          "n-scrollbar-container",
        )[0].scrollTop = 0)
    })
  })
}

document.addEventListener("visibilitychange", async () => {
  if (document.hidden) {
    // 页面被隐藏
  } else {
    // 页面被激活
    const { setRoutes } = useRouterStore()
    const { token } = useUserStore()
    if (!$g._.isEqual(cookie.get("jzt_token"), token)) {
      try {
        await setRoutes(authentication)
      } catch (err) {
        console.error("重新获取路由失败", err)
      }
    }
  }
})

/* 版本监控,路由跳转监测+每60分钟执行一次 */
async function versionCheck() {
  if (import.meta.env.VITE_APP_ENV.includes("development")) return false

  const response = await axios.get(
    "version.json?timestamp=" + new Date().getTime(),
  )
  if (__APP_VERSION__ != response?.data?.version) {
    if (localStorage.getItem("RELOAD")) {
      localStorage.removeItem("RELOAD")
      $g.bus.emit("changeMenu")
      location.reload()
      return
    }
    $g.confirm({
      type: "info",
      title: "更新提示",
      content: "发现新内容，为了更好的体验，请点击确认刷新当前页面",
      positiveText: "确定",
      negativeText: "取消",
    }).then(() => {
      localStorage.setItem("RELOAD", "true")
      window.location.href =
        window.location.origin + "/#" + routeObj.to.fullPath
    })
  }
}
/* 指定页面缓存 */
function keepAlive(to: any, from: any) {
  const routerStore = useRouterStore()
  routerStore.changeMenuMeta({
    name: from.name,
    meta: {
      keepAlive:
        from.meta.keepAliveArr?.includes(to.name) ||
        ["PreviewDoc", "AnswerCard"].includes(to.name),
    },
  })
  routerStore.changeMenuMeta({
    name: to.name,
    meta: {
      keepAlive:
        to.meta.keepAliveArr?.includes(from.name) ||
        ["PreviewDoc", "AnswerCard"].includes(from.name),
    },
  })
}
/* 关闭loading */
function removeLoading() {
  // 移除loading
  if (isShowLoading) {
    const appLoadingId = document.getElementById("appLoadingId") as HTMLElement
    appLoadingId?.remove()
    isShowLoading = false
  }
}
/**
 * 描述 keepalive滚动条恢复
 * @param {any} to
 * @param {any} from
 * @param {any} mode 1-离开 2-进入
 */

function restoreRollingBar(to, from, mode = 1) {
  const routerStore = useRouterStore()
  const App = document?.querySelector("#right-main .n-scrollbar-container")
  if (!App) return
  if (mode == 1) {
    // 是否需记录滚动条位置
    if (from.meta.keepAlive || from.meta.keepAliveArr?.includes(to.name)) {
      routerStore.changeMenuMeta({
        name: from.name,
        meta: {
          scrollTop: App?.scrollTop || 0,
        },
      })
    }
  } else if (mode == 2) {
    nextTick(() => {
      if (to.meta.keepAlive && to.meta?.scrollTop) {
        App.scrollTop = to.meta?.scrollTop
      } else {
        App.scrollTop = 0
      }
    })
  }
}

/**
 * @description 书籍/试卷管理详情锁逻辑
 * @param {any} to
 * @param {any} from
 * meta?.lock 书籍/试卷管理详情页以及子页面（与上锁有关的页面）
 * shouldPreventNavigation 禁止跳转
 * isLocked 是否有锁
 * noBookId 是否有资源id
 * lockData 读取/存储上锁数据（存储在sessionStorage中）
 * accountAdminId 上锁用户
 */
async function lock(to, from) {
  const resourceMgtStore = useResourceMgtStore()
  const { lockData } = useResourceMgtStore()

  //解锁以及重置锁逻辑
  if (!to.meta?.lock && from.name && from.meta?.lock) {
    //退出书籍/试卷管理详情页时重置数据
    resourceMgtStore.closeLocal()
    document.removeEventListener("mousemove", resourceMgtStore.mouseThrottle)
    return false
  }

  const noBookId = !to.query?.bookId && !lockData?.bookId
  //前往页面不是加锁页或者bookId不存在则正常进入
  if (!to.meta?.lock || noBookId) return false

  //通过to.query.bookId判断是否有锁
  const { hasOk, accountAdminId } = await hasLock({
    bookId: to.query.bookId || lockData.bookId,
  })
  const isLocked = hasOk != 1 //是否上锁

  //解决该用户上锁后又由于特殊情况被解锁又被其他用户上锁的情况
  const isConvert =
    lockData.currentSet &&
    isLocked &&
    lockData.lockUser != accountAdminId &&
    to.meta?.lock
  if (isConvert) {
    $g.msg("该资源已被占用", "warning")
    resourceMgtStore.init()
    lockData.currentSet = false
    return lockData.category == 1
      ? "/resourceMgt/bookManage"
      : "/resourceMgt/paperManage"
  }

  //本设备已经设置锁不再继续后续流程-----刷新页面后重新绑定事件，重启计时
  if (lockData.currentSet) {
    setupMouseTracking(resourceMgtStore)
    return false
  }

  /**
   * @description 禁止跳转-----防止地址栏或者导航条跳转页面
   * isLocked---有锁
   * to.name != "EnterResoure"--- 除开EnterResoure页面
   * !from.meta?.lock || lockData.isDisabled--- 跳转前的页面不带lock或者处于禁止编辑状态
   * to.meta?.lock---前往的页面必须是带lock
   */
  const shouldPreventNavigation =
    isLocked &&
    (!from.meta?.lock || lockData.isDisabled) &&
    to.name != "EnterResoure" &&
    to.meta?.lock

  if (shouldPreventNavigation) {
    $g.msg("该试卷有用户正在编辑，请稍后再试!", "warning")
    return true
  }

  if (to.query.bookId) lockData.bookId = to.query.bookId

  //进入页面后有锁且不是当前设备设置的锁直接禁用编辑功能
  if (isLocked && !lockData.currentSet) {
    lockData.isDisabled = true
    return false
  }

  setupMouseTracking(resourceMgtStore)
  return false
}

/**
 * @description 添加鼠标监听以及上锁
 */
function setupMouseTracking(resourceMgtStore) {
  document.addEventListener("mousemove", resourceMgtStore.mouseThrottle)
  resourceMgtStore.openEvent()
}
