import { heartbeat, deleteLock, setLock } from "@/api/bookMgt"
import router from "@/router/index"
import { useUserStore } from "./user"

export const useResourceMgtStore = defineStore("resourceMgt", {
  state: () => ({
    questionId: "",
    question: {},
    timerId: null as any,
    timer: null as any,
    lockData: {
      mouseTime: 0, //心跳接口请求（大于60s可以请求）
      bookId: "",
      currentSet: false, //是否是当前设备设置锁
      category: 1,
      isDisabled: false, //禁止编辑
      endTime: 0, //最大停留时间
      lockUser: "", //上锁人id
    },
  }),
  getters: {
    getQuestionId(): string {
      return this.questionId
    },
    getQuestion(): object {
      return this.question
    },
    getDisabled(): boolean {
      return this.lockData.isDisabled
    },
  },
  actions: {
    setQuestionId(value: string): void {
      this.questionId = value
    },
    setQuestion(value: object): void {
      this.question = value
    },
    init() {
      this.lockData.mouseTime = 0
      this.lockData.endTime = 0
      this.lockData.lockUser = ""
      clearTimeout(this.timer)
      clearTimeout(this.timerId)
    },
    //关闭锁
    async closeLocal() {
      try {
        this.init()
        this.lockData.isDisabled = false
        //有锁并且有编辑功能则解锁
        if (this.lockData.currentSet && this.lockData.bookId) {
          await deleteLock({ bookId: this.lockData.bookId })
        }
        this.lockData.currentSet = false
        this.lockData.bookId = ""
      } catch (error) {
        console.log("解锁失败！")
        console.log("⚡ error ==> ", error)
      }
    },
    stopPolling() {
      clearTimeout(this.timerId)
      this.timerId = null
    },
    mouseTimeAuto() {
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        //1小时后未操作直接退出
        if (new Date().getTime() >= this.lockData.endTime) {
          this.init()
          this.outPage("由于您长时间占用该资源")
          return
        } else {
          this.lockData.mouseTime++
          this.mouseTimeAuto()
        }
      }, 1000)
    },
    outPage(text) {
      $g.msg(`${text}，3s后自动退出！`, "warning")
      setTimeout(() => {
        router.push({
          name: `${this.lockData.category == 1 ? "BookManage" : "PaperManage"}`,
        })
        $g.bus.emit("changeMenu")
      }, 3000)
    },
    async mouseThrottle() {
      //每隔60秒钟才能执行
      if (this.lockData.mouseTime >= 30) {
        this.lockData.mouseTime = 0
        this.lockData.endTime = new Date().getTime() + 60 * 60 * 1000
        const { renewalOk, message } = await heartbeat({
          bookId: this.lockData.bookId,
        })
        //续期失败
        if (renewalOk == 1) this.pageJump(message)
      }
    },
    async openEvent() {
      try {
        //判断资源是否有锁
        this.mouseTimeAuto()
        if (this.lockData.currentSet) return
        const { userInfo } = useUserStore()
        this.lockData.currentSet = true
        this.lockData.isDisabled = false
        this.lockData.lockUser = userInfo.accountId
        this.lockData.endTime = new Date().getTime() + 60 * 60 * 1000
        await setLock({ bookId: this.lockData.bookId })
      } catch (error) {
        console.log("上锁失败！")
        console.log("⚡ error ==> ", error)
      }
    },
    //发送心跳时续锁失败的操作
    pageJump(text) {
      this.init()
      this.lockData.currentSet = false
      this.outPage(text.split(",")[0])
    },
  },
  // persist: [
  //   {
  //     paths: ["lockData"],
  //     storage: sessionStorage,
  //   },
  // ],
})
