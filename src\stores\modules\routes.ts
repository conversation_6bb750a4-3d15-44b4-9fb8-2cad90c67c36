import {
  asyncRoutes,
  constantRoutes,
  iframeRoutes,
  resetRouter,
  default as router,
} from "@/router"
import { filterRoutes2, convertRouter, dealMeta } from "@/utils/routes"
import { getFrontRoute } from "@/api/user"
import { useUserStore } from "./user"
import config from "@/config"
import type { RouteRecordNormalized } from "vue-router"
const { authentication, rolesControl, inIframe } = config

interface RouterState {
  routes: any[]
  levelList2: any[]
}

export const useRouterStore = defineStore("routes", {
  state: (): RouterState => ({
    /**
     * 一级菜单
     */
    routes: [],
    levelList2: [],
  }),
  getters: {
    keepAliveArr: (state) => {
      const newArr: string[] = []

      deepFn(state.routes)
      function deepFn(routerArr) {
        routerArr.forEach((e) => {
          if (e?.meta?.keepAlive) newArr.push(e.name)
          if (e?.children?.length) {
            deepFn(e.children)
          }
        })
      }
      return newArr
    },
    getRoutes: (state) => {
      function handleRoutes(routes) {
        return routes.map((route) => {
          route.show = route.meta.hidden ? false : true
          // if (
          //   (route.children &&
          //     route.children.length == 1 &&
          //     route.children[0].meta.hidden) ||
          //   route.meta.top
          // ) {
          //   delete route.children
          // }
          if (
            (route.children &&
              route.children.every((child) => child.meta.hidden)) ||
            route.meta.top
          )
            delete route.children

          if (route.children && route.children.length) {
            route.children = handleRoutes(route.children)
          }
          if (
            [
              "FingerSearch",
              "ActivityManage",
              "Statistics",
              "FontResourceUpload",
              "PaperBlocking",
              // "ModelManagement",
            ].includes(route.name)
          ) {
            // 对着一个路由做特殊处理，这样做不会影响之前的代码逻辑，如果之后不需要这样可以删掉。
            route.show = true
          }
          return route
        })
      }

      return handleRoutes($g._.cloneDeep(state.routes))
    },
  },
  actions: {
    clearRoutes() {
      this.routes = []
    },
    /**
     * 处理路由错误并登出
     * @param message 错误提示信息
     * @param type 消息类型
     */
    async handleRouteErrorAndLogout(
      message: string,
      type: "error" | "info" = "error",
    ): Promise<void> {
      $g.notification[type]({
        content: type === "error" ? "错误" : "提示",
        meta: message,
        duration: 3000,
      })
    },
    /**
     * @description 多模式设置路由
     * @param mode
     * @returns
     */
    async setRoutes(mode = "none"): Promise<void> {
      const { logout } = useUserStore()
      // 默认前端路由
      let routes = [...asyncRoutes]
      // 设置游客路由关闭路由拦截
      const control = mode === "visit" ? false : rolesControl
      // 设置后端路由
      if (authentication === "all") {
        const list = await getFrontRoute()
        if ($g.tool.typeOf(list) != "array") {
          $g.notification.error({
            content: "错误",
            meta: "路由格式返回有误！",
            duration: 3000,
          })
        }
        // 学科网选题会为空 目前路由为空不做处理
        if (!list.length) {
          // alert(list.length)
          // $g.msg("该用户没有权限，请重新登录", "info")
          // setTimeout(async () => {
          //   await logout()
          // }, 2000)
          // return
        }
        const ifRoot = list.find((route) => route.path === "/")
        if (!ifRoot) {
          //没有根路由默认置根路由，重定向通过后续meta重新设置
          list.push({
            path: "/",
            redirect: "",
            component: "Layout",
            name: "Root",
            meta: { hidden: true, title: "首页" },
          })
        }
        routes = convertRouter(list) //递归处理路由，返回更新meta后的路由
        routes = dealMeta(routes) //根据最新meta处理菜单显隐
        /**
         * 处理重定向
         */
        const Root = routes.find((route) => route.path == "/")
        if (Root) {
          const filterData = routes.filter((route: any) => !route.meta.hidden)
          if (!filterData.length) {
            routes = []
          } else {
            const findChild = (filterData[0].children as any).find(
              (child: any) => !child.meta.hidden,
            )
            Root.redirect =
              Root.redirect &&
              Root.children.length &&
              Root.children.some((child) => Root.redirect.includes(child.path))
                ? Root.redirect
                : filterData[0]?.path == "/"
                ? "/" + findChild?.path
                : [filterData[0]?.path, findChild?.path]
                    .filter(Boolean)
                    .join("/")
          }
        }
      }

      // 根据权限和rolesControl过滤路由
      const accessRoutes = filterRoutes2(
        [...constantRoutes, ...routes],
        control,
      )
      this.routes = JSON.parse(JSON.stringify(accessRoutes))
      // 根据可访问路由重置Vue Router
      await resetRouter(accessRoutes)
    },

    /**
     * @description 设置iframe 路由
     * @returns
     */
    async setIframeRoutes(): Promise<void> {
      // iframe 路由
      const accessRoutes = filterRoutes2(iframeRoutes, false)
      this.routes = JSON.parse(JSON.stringify(accessRoutes))
      await resetRouter(accessRoutes)
    },
    changeMenuMeta(options) {
      function handleRoutes(routes) {
        return routes.map((route) => {
          if (route.name === options.name) {
            Object.assign(route.meta, options.meta)
            const current: RouteRecordNormalized | undefined = router
              .getRoutes()
              .find((v) => v.name === options.name)
            if (current) {
              current.meta = route.meta
            }
          }
          if (route.children && route.children.length)
            route.children = handleRoutes(route.children)
          return route
        })
      }
      this.routes = handleRoutes(this.routes)
    },
    updateRouteKeepAlive(name, val) {
      function deepFn(routerArr) {
        routerArr.forEach((e) => {
          if (e.name === name && e.meta) {
            e.meta.keepAlive = val
          } else {
            if (e?.children?.length) {
              deepFn(e.children)
            }
          }
        })
      }

      deepFn(this.routes)
    },
  },
  persist: [
    {
      paths: ["levelList2"],
      storage: localStorage,
    },
  ],
  // persist: {
  //   storage: sessionStorage,
  // },
})
