import config from "@/config"
const { inIframe } = config
export const useSettingsStore = defineStore("settings", {
  state: () => ({
    layout: inIframe ? "InIframe" : "MixVertitalLayout",
    lang: "zh-CN",
    showBreadCrumb: true,
    siderWidth: 220,
    inIframe,
  }),
  getters: {
    getLang(): string {
      return this.lang
    },
    getLayout(): string {
      return this.layout
    },
    getShowBreadCrumb(): boolean {
      return this.showBreadCrumb
    },
    getSiderWidth(): number {
      return this.siderWidth
    },
  },
  actions: {
    setShowBreadCrumb(value: boolean): void {
      this.showBreadCrumb = value
    },
    setLayout(value: string): void {
      this.layout = value
    },
    setSiderWidth(value: number): void {
      this.siderWidth = value
    },
  },
})
