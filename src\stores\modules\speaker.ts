import { defineStore } from "pinia"
import config from "@/config/index"
import { fetchEventSource } from "@microsoft/fetch-event-source"
import { useUserStore } from "@/stores/modules/user"
const { baseURL } = config
const userStore = useUserStore()
export const useSpeakerStore = defineStore("speaker", {
  state: () => ({
    audio: null as any, //audio实例
    urlList: [] as any, //音频列表
    isPlay: "pause", //播放状态 loading/start/pause
    currentIndex: 0, //当前播放index
    type: null as any, // 播放类型
    question: {
      data: {}, // 题目数据
      params: {}, // 请求参数
      ids: [], //多个音频id
    } as any,
    showSubTitle: false, //显示字幕
    subtitle: "", //字幕
    controller: null as any, //控制器
    isRequestCompleted: false, //接口请求状态
  }),
  getters: {
    getIdByType(state) {
      const typeMap = {
        1: state.question.data?.questionId, // 题干，返回subQuestionId
        2: state.question.data?.subQuestionId + "S", // 子题，返回subQuestionId + 'A'
        3: state.question.data?.subQuestionId + "A", // 子题答案，返回subQuestionParseId
        4: state.question.data?.subQuestionParseId + "P", // ，返回subQuestionId + 'A'
        5: state.question.data?.subQuestionParseId + "S",
        6: state.question.data?.parseFastGptSolutionStepId,
        10: state.question.data?.questionTtsId, //音频管理内容
      }
      return typeMap[state.type] // 返回对应的id，如果没有匹配到type，则返回null
    },
  },
  actions: {
    /**
     * 请求音频
     * @param {any} sourceId  请求音频Id
     * @param {any} sourceType 音频类型
     */
    async fetchAudioStream(sourceId, sourceType) {
      try {
        const url = `${baseURL}/tutoring/admin/audio/question/tts/stream`
        const headers = {
          "Content-Type": "application/json;charset=UTF-8",
          Accept: "text/event-stream",
          token: userStore.token,
        }
        await fetchEventSource(url, {
          method: "POST",
          headers,
          body: JSON.stringify({
            sourceId: sourceId,
            sourceType: sourceType,
          }),
          openWhenHidden: true,
          signal: this.controller.signal,
          onmessage: (e: any) => {
            const data = JSON.parse(e.data)
            if (data.code == 200) {
              this.urlList.push(data.data)
              if (this.urlList.length > 2 && !this.audio) {
                this.init()
              }
            } else {
              $g.msg(data.msg, "error")
              this.type = null
              this.controller.abort()
              return
            }
          },
          onerror(err) {
            throw new Error(err)
          },
          onclose: () => {
            if (!this.urlList.length) {
              $g.msg("暂无音频", "warning")
              this.isPlay = "pause"
              return
            }
            !this.audio && this.init()
            this.isRequestCompleted = true
            console.log("接口请求完成")
          },
        })
      } catch (err) {
        this.type = null
        console.log(err)
      }
    },
    /**
     * Description
     * @param {any} item  题目数据
     * @param {any} type  类型 1: 阅读题干 2: 阅读子题题干 3: 阅读答案 4: 阅读解析 5: 其他或者跟题目无关类型
     * @returns {any}
     */
    async getAudioList(type, item?) {
      try {
        if ([1, 2, 3, 4, 5, 6].includes(type)) {
          const idMap = {
            1: item.questionId,
            2: item.subQuestionId,
            3: item.subQuestionId,
            4: item.subQuestionParseId,
            5: item.subQuestionParseId,
            6: item.parseFastGptSolutionStepId,
          }
          const params = {
            sourceId: idMap[type],
            sourceType: type,
          }

          if (this.audio && $g._.isEqual(params, this.question.params)) {
            this.isPlay == "start" ? this.pause() : this.play()
            return
          } else {
            this.controller && this.controller.abort()
            this.reset()
          }
          this.isRequestCompleted = false
          this.controller = new AbortController()
          this.type = type
          this.question.data = item
          this.question.params = params
          this.isPlay = "loading"
          await this.fetchAudioStream(
            this.question.params.sourceId,
            this.question.params.sourceType,
          )
        }
      } catch (err) {
        this.type = null
        console.log(err)
      }
    },
    /* 多个音频请求 */
    async getMultipleAudioList(ids, type) {
      try {
        if (this.audio && $g._.isEqual(ids, this.question.ids)) {
          this.isPlay == "start" ? this.pause() : this.play()
          return
        } else {
          this.controller && this.controller.abort()
          this.reset()
        }
        this.controller = new AbortController()
        this.type = type
        this.question.ids = ids
        this.isPlay = "loading"
        for (const id of this.question.ids) {
          await this.fetchAudioStream(id, this.type)
        }
      } catch (err) {
        console.log(err)
        this.type = null
        this.isPlay = "start"
      }
    },
    /* 已有音频列表 */
    setUrlList(type, data) {
      if ($g._.isEqual(data.audioUrlList, this.urlList) && this.audio) {
        this.isPlay == "start" ? this.pause() : this.play()
        return
      } else {
        this.reset()
        this.type = type
        this.question.data = data
        this.urlList = data.audioUrlList
        this.init()
      }
    },
    /* 播放 */
    async play() {
      if (this.audio && this.audio.pause) {
        this.isPlay = "start"
        this.showSubTitle = true
        try {
          await this.audio.play()
        } catch (err) {
          console.log("播放请求被中断", err)
        }
      }
    },
    /* 暂停 */
    pause() {
      if (this.audio && !this.audio.paused) {
        this.audio.pause()
        this.isPlay = "pause"
        this.showSubTitle = false
      }
    },
    /* 重置 */
    reset() {
      if (this.audio) {
        this.audio.pause()
        this.audio.removeEventListener("ended", this.handleNext)
        this.audio = null
        this.showSubTitle = false
        this.subtitle = ""
      }

      this.$reset()
    },
    /* 初始化 */
    init() {
      this.audio = new Audio()
      this.audio.src = this.urlList[this.currentIndex].tts
      this.subtitle = this.urlList[this.currentIndex].text
      this.audio.play()
      this.isPlay = "start"
      this.showSubTitle = true
      this.audio.addEventListener("ended", this.handleNext)
    },
    /* 顺序播放 */
    handleNext() {
      this.currentIndex++
      if (this.currentIndex < this.urlList.length) {
        this.audio.src = this.urlList[this.currentIndex].tts
        this.subtitle = this.urlList[this.currentIndex].text
        this.audio.play()
      } else {
        console.log("播放完毕")
        this.reset()
      }
    },
  },
})
