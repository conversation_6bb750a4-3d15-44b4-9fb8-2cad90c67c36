import { getToken, removeToken, setToken } from "@/utils/token"
import {
  getUserInfo,
  logout,
  getUserDetail,
  getEncrypt,
  getEncryptLogin,
} from "@/api/user"
import { resetRouter } from "@/router"
import { useRouterStore } from "./routes"
import router from "@/router/index"
import { deleteAllLock } from "@/api/bookMgt"

export const useUserStore = defineStore("user", {
  state: () => ({
    token: getToken() as string,
    username: "",
    avatar: "https://i.gtimg.cn/club/item/face/img/2/15922_100.gif",
    userInfo: {} as any,
    studyProject: null as any,
  }),
  getters: {
    getToken: (state) => state.token,
    getUsername: (state) => state.username,
    getAvatar: (state) => state.avatar,
  },
  actions: {
    /**
     * @description 设置token
     * @param {*} token
     */
    setToken(token: string) {
      this.token = token
      setToken(token)
    },
    /**
     * @description 设置用户名
     * @param {*} username
     */
    setUsername(username: string) {
      this.username = username
    },
    setUserInfo(userInfo: any) {
      this.setToken(userInfo.token)
      this.userInfo = userInfo
    },
    /**
     * @description 登录
     * @param {*} userInfo
     */
    async login(userInfo: any) {},
    /**
     * @description 获取用户信息接口 这个接口非常非常重要，如果没有明确底层前逻辑禁止修改此方法，错误的修改可能造成整个框架无法正常使用
     * @returns
     */
    async getUserInfo(data) {
      // const data = await getUserInfo()
      // this.setUserInfo(data)
      // 如不使用username用户名,可删除以下代码
      // if (data.nickname) {
      //   this.setUsername(data.nickname)
      //   this.setUserInfo(data)
      // }
      const res = await getUserInfo(data)
      this.setUserInfo(res)
    },

    async exchangeToken(query: any) {
      const encryptRes = await getEncrypt(query)
      const loginRes = await getEncryptLogin({
        encryptedStr: encryptRes,
      })
      this.setUserInfo(loginRes)
      // 把url上的参数去除
      const url = new URL(window.location.href)
      url.searchParams.delete("encryptedStr")
      window.history.replaceState({}, "", url.toString())
    },
    /**
     * @description 退出登录
     */
    async logout() {
      //退出登录之前解除用户添加的所有锁
      try {
        await deleteAllLock()
      } catch (error) {
        console.log("⚡ error ==> ", error)
      }
      await this.resetAll()
      location.reload()
    },
    /**
     * @description 重置
     */
    async resetAll() {
      removeToken() //清除所有需要先清除token 不然后面userStore的reset赋初始值还是会给token赋值
      const userStore = useUserStore()
      const routeStore = useRouterStore()
      routeStore.$reset()
      userStore.$reset()
      localStorage.clear()
      sessionStorage.clear()
      await resetRouter()
    },
    /**
     * @description 根据token获取用户详细信息
     */
    async getUserDetail() {
      const res = await getUserDetail()
      res.token = getToken()
      this.setUserInfo(res)
    },
  },
  persist: {
    // storage: sessionStorage,
    storage: localStorage,
  },
})
