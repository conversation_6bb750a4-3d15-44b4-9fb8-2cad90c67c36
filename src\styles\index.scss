@import "./normalize.scss";
@import "./tool.scss";
@import "./naiveui.reset.scss";
@import "./variables/variables.module.scss";
@import "@/assets/icon-font/iconfont.css";

$--element-tree-line-color: #dcdfe6;
$--element-tree-line-style: dashed;
$--element-tree-line-width: 1.5px;
@import "element-tree-line/dist/style.scss";
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
}

@font-face {
  font-family: "HarmonyOS";
  src: url("https://frontend-cdn.qimingdaren.com/cdn/HarmonyOSHans-Medium.woff2")
    format("woff2");
  font-style: normal;
}

body {
  font-family: "HarmonyOS", "Arial", "sans-serif", "PingFang SC",
    "Hiragino Sans GB", "Helvetica", "Droid Sans", "思源黑体 CN",
    "Microsoft YaHei";
}

.tox-tinymce {
  border-radius: 0 !important;
}

.ghost {
  outline: 2px dashed var(--g-primary);
}

.page-title {
  font-size: 22px;
  margin-bottom: 20px;
}

.form-label {
  font-size: 16px;
  margin-bottom: 10px;
}

.n-radio-button--focus {
  outline: none !important;
  box-shadow: none !important;
}
.tox-tinymce-aux {
  z-index: 3000 !important ;
}

#edui_fixedlayer {
  z-index: 3000 !important;
}
