/* button */
.n-button {
  line-height: 1.15 !important;
}

/* form */
.form-filter {
  .n-form-item-feedback-wrapper {
    min-height: 10px !important;
  }
}

/* Notification */
.message-before {
  content: "";
  width: 4px;
  left: 0;
  top: 0;
  bottom: 0;
  position: absolute;
}
.n-notification-main__content {
  font-size: 16px !important;
  font-weight: bold;
}
.n-notification {
  border-radius: 8px !important;
  width: 320px !important;
  &::before {
    content: "";
    width: 10px;
    left: 0;
    top: 0;
    bottom: 0;
    position: absolute;
    // background: var(--n-icon-color);
    background: var(--g-error);
  }
}
.n-notification__avatar {
  top: 8px !important;
  font-size: 24px !important;
  left: 18px !important;
  .n-base-icon {
    color: var(--g-error) !important;
  }
}
.n-notification-main-footer__meta {
  font-size: 14px !important;
  color: #606266;
}
.n-notification-main {
  padding: 10px 0 !important;
}
.n-notification-container .n-notification {
  padding-left: 12px !important;
}

/* Confirm */
.n-dialog {
  padding: 15px 20px 15px 20px !important;
  width: 420px !important;
  .n-dialog__icon {
    position: relative;
    left: -4px;
    font-size: 22px !important;
  }
  .n-dialog__close {
    right: -10px !important;
  }
  .n-dialog__content {
    color: #606266;
    margin: 16px 0 16px 0;
  }
}

.n-modal-container {
  z-index: 3000 !important;
}

.n-image-preview-container {
  z-index: 9999 !important;
}
/* message */
.message-before {
  content: "";
  width: 4px;
  left: 0;
  top: 0;
  bottom: 0;
  position: absolute;
}
.n-message {
  position: relative;
  padding-left: 14px;
  &--success-type {
    .n-base-icon {
      color: var(--g-success) !important;
    }
    &::before {
      @extend .message-before;
      background: var(--g-success);
    }
  }
  &--warning-type {
    .n-base-icon {
      color: var(--g-warning) !important;
    }
    &::before {
      @extend .message-before;
      background: var(--g-warning);
    }
  }
  &--error-type {
    .n-base-icon {
      color: var(--g-error) !important;
    }
    &::before {
      @extend .message-before;
      background: var(--g-error);
    }
  }
}

/* radio */
.n-radio-group--button-group {
  .n-radio-button {
    margin-right: 10px;
    border: 1px solid var(--n-button-border-color);
  }
  .n-radio-group__splitor {
    display: none;
  }
}
.n-radio-group {
  .n-radio-button {
    border-radius: 4px !important;
  }
  .n-radio-button--checked {
    background-color: var(--g-primary) !important;
    border-color: var(--g-primary) !important;
    color: #fff !important;
  }
  .n-radio-button--focus {
    box-shadow: none !important;
    animation-play-state: initial;
    .n-radio-button__state-border {
      display: none;
    }
  }
}
