/* start--文本行数限制--start */
.line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.line-2 {
  -webkit-line-clamp: 2;
}

.line-3 {
  -webkit-line-clamp: 3;
}

.line-4 {
  -webkit-line-clamp: 4;
}

.line-5 {
  -webkit-line-clamp: 5;
}

.line-2,
.line-3,
.line-4,
.line-5 {
  flex: 1;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box; // 弹性伸缩盒
  -webkit-box-orient: vertical; // 设置伸缩盒子元素排列方式
}

.border {
  border: 1px solid var(--g-border-color);
}

/* start--自定义滚动条--start */
.custom-scrollbar {
  overflow: auto;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}
