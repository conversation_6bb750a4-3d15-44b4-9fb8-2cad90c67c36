import stroke from './stroke'
import EventEmitter from 'eventemitter3'

export const DotType = { Down: 0, Move: 1, Up: 2, Hover: 3 }

export interface PaperSize {
  width: number
  height: number
}

export const PaperType: { [key: string]: PaperSize } = {
  b5: {
    width: 182,
    height: 256,
  },
  a4: {
    width: 210,
    height: 297,
  },
  a3: {
    width: 210 * 2,
    height: 297 * 2,
  },
}

export interface Dot {
  x: number
  y: number
  fx: number
  fy: number
  force: number
  pageId: number
  bookId: number
  owner: string
  angle: number
  dotType: 'PEN_DOWN' | 'PEN_MOVE' | 'PEN_UP'
}

export interface Point {
  x: number
  y: number
  force: number
  pageId: number
  bookId: number
  angle: number
  lineWidth?: number
  lineColor?: string
  type: number | string
  ownerId: string
}

/**
 * TQL笔迹绘制类
 */
export default class TqlCanvas extends EventEmitter {
  /** canvas 缩放比例 */
  static canvasScaling: number = 2
  /** b5纸 宽度 */
  static x_point_size: number = 1.524
  /** 码点宽度 */
  static y_point_size: number = 1.524
  /** 码点高度 */

  canvas: any
  context: any
  width: number
  height: number
  osCanvas: any
  osContext: any
  lineWidth: number
  lineColor: string
  pageId: number
  pageList: any[]
  bookId: number
  penDataMap: Map<number, Point[]>
  penImageMap: Map<number, HTMLImageElement>
  lastPoint: Point
  backroundMap: Map<number, HTMLImageElement | null>
  paperType: keyof typeof PaperType
  isReplay: boolean
  isPauseReplay: boolean
  replayTimer: any
  replayIndex: number
  replayPercentage: number
  replayBeforeImage: HTMLImageElement | null
  isFilter: boolean
  isStroke: boolean

  /**
   * 创建离屏canvas
   * @param width canvas的宽度
   * @param height canvas的高度
   * @returns HTMLCanvasElement
   */
  static offScreenCanvas(width: number, height: number): HTMLCanvasElement {
    const canvas = document.createElement('canvas')
    canvas.width = width
    canvas.height = height
    return canvas
  }

  /**
   * dotType转number
   * @param dotType 点类型
   * @returns number
   */
  static dotType2Number(dotType: 'PEN_DOWN' | 'PEN_MOVE' | 'PEN_UP'): number {
    return ['PEN_DOWN', 'PEN_MOVE', 'PEN_UP'].indexOf(dotType)
  }

  /**
   * 四舍五入
   * @param number 需四舍五入的数字
   * @returns number
   */
  static roundNumber(number: number): number {
    return Math.round(number * Math.pow(10, 15)) / Math.pow(10, 15)
  }

  /**
   * 构造函数
   * @param canvas 传入的显屏canvas
   * @param paperType 纸张类型
   */
  constructor({
    canvas,
    paperType,
  }: {
    canvas: HTMLCanvasElement | HTMLCanvasElement[]
    paperType: keyof typeof PaperType
  }) {
    super()
    //显屏canvas
    this.canvas = Array.isArray(canvas) ? canvas : [canvas]
    //显屏canvas的context
    this.context = this.canvas.map((e) => e.getContext('2d'))
    //离屏canvas 宽
    this.width = this.canvas[0].width * TqlCanvas.canvasScaling
    //离屏canvas 高
    this.height = this.canvas[0].height * TqlCanvas.canvasScaling
    //离屏canvas
    this.osCanvas = TqlCanvas.offScreenCanvas(this.width, this.height)
    //离屏canvas的context
    this.osContext = this.osCanvas.getContext('2d')
    //线宽
    this.lineWidth = 2
    //线色
    this.lineColor = '#000000'
    //当前页码Id
    this.pageId = 0
    //页码列表
    this.pageList = []
    //当前书本Id
    this.bookId = 0
    //笔迹数据列表
    this.penDataMap = new Map()
    //笔迹图片列表
    this.penImageMap = new Map()
    //上一个点数据
    this.lastPoint = this.point({
      x: 0,
      y: 0,
      fx: 0,
      fy: 0,
      force: 1,
      pageId: 0,
      bookId: 0,
      owner: '',
      angle: 0,
      dotType: 'PEN_DOWN',
    })
    //背景图数据
    this.backroundMap = new Map([[100, null]])
    //纸张类型
    this.paperType = paperType
    //是否回放
    this.isReplay = false
    //是否暂停
    this.isPauseReplay = false
    //回放定时器
    this.replayTimer = null
    //回放下标
    this.replayIndex = 0
    //回放百分比
    this.replayPercentage = 0
    //回放的当前页的图片
    this.replayBeforeImage = null
    //是否开启过滤算法
    this.isFilter = false
    //是否开启笔锋算法
    this.isStroke = false
    //绘制背景图
    this.drawImage(this.backroundMap.get(100))
  }

  /**
   * 解析数据 将后端传递数据转换为前端绘制数据
   * @param dot 点数据
   * @returns Point
   */
  parseData(dot: Dot): Point {
    let x = parseInt(dot.x.toString()) + parseInt(dot.fx.toString()) / 100
    let y = parseInt(dot.y.toString()) + parseInt(dot.fy.toString()) / 100

    const { width, height } = PaperType[this.paperType]

    const dotCountH = width / TqlCanvas.x_point_size
    const dotCountV = height / TqlCanvas.y_point_size

    x = (x / dotCountH) * this.width
    y = (y / dotCountV) * this.height

    return {
      type: dot.dotType,
      x,
      y,
      force: dot.force,
      pageId: dot.pageId,
      bookId: dot.bookId,
      ownerId: dot.owner,
      angle: dot.angle,
      lineWidth: this.lineWidth,
      lineColor: this.lineColor,
    }
  }

  /**
   * 点数据
   * @param data 数据
   * @returns Point
   */
  point(data: any): any {
    return { ...data, lineWidth: this.lineWidth, lineColor: this.lineColor }
  }

  /**
   * 处理点
   * @param data 数据
   */
  handlePoint(data: Dot): void {
    if (this.isReplay) {
      return
    }

    const currentPoint = this.point(this.parseData(data))
    //pageId bookId 与当前点不同时
    if (
      currentPoint.pageId !== this.pageId ||
      currentPoint.bookId !== this.bookId
    ) {
      this.drawImage(this.backroundMap.get(100))
      this.cutPage(currentPoint.pageId)
      this.drawCurrentPage()
    }
    //存储笔迹数据 和 页码数据
    this.savePageData(currentPoint)
    //更新 pageId 和 bookId
    this.pageId = currentPoint.pageId
    this.bookId = currentPoint.bookId
    //绘制
    this.draw(currentPoint)
  }

  /**
   * 绘制
   * @param point 点数据
   */
  draw(point: Point): void {
    if (
      point.type !== DotType.Down &&
      this.lastPoint.x === 0 &&
      this.lastPoint.y === 0
    ) {
      this.lastPoint = point
    }
    //笔锋绘制
    if (this.isStroke) {
      stroke(
        point.type,
        point.x,
        point.y,
        point.force,
        this.osContext,
        point.lineWidth,
        point.lineColor,
      )
      this.synchronousDraw()
      // 滚动事件

      if (point.type == DotType.Down) {
        this.emit('onPendown', point)
      }
    } else {
      switch (point.type) {
        case DotType.Down:
          this.lastPoint = point
          this.emit('onPendown', this.lastPoint)
          break
        case DotType.Move:
        case DotType.Up:
          this.drawLine(this.lastPoint, point)
          this.lastPoint = point
          this.emit('onPendown', this.lastPoint)
          break
      }
    }
  }

  /**
   * 绘制线
   * @param lastPoint 上一个点数据
   * @param currentPoint 当前点数据
   */
  drawLine(lastPoint: Point, currentPoint: Point): void {
    this.osContext.beginPath()
    this.osContext.lineWidth = currentPoint.lineWidth
    this.osContext.strokeStyle = currentPoint.lineColor
    this.osContext.moveTo(lastPoint.x, lastPoint.y)
    this.osContext.lineTo(currentPoint.x, currentPoint.y)
    this.osContext.stroke()
    this.synchronousDraw()
  }

  /**
   * 离屏canvas和显屏canvas同步绘制
   */
  synchronousDraw(): void {
    this.context.forEach((ctx) => {
      //显屏canvas清空
      ctx.clearRect(0, 0, this.width, this.height)
      //离屏canvas画入显屏canvas
      ctx.drawImage(this.osCanvas, 0, 0, this.width / 2, this.height / 2)
    })
  }

  /**
   * 存储页码数据
   * @param point 点数据
   */
  savePageData(point: Point): void {
    const pageId = point.pageId
    if (this.penDataMap.has(pageId)) {
      //存在 将点添加即可
      this.penDataMap.get(pageId)?.push(point)
    } else {
      //不存在 添加新的pageId数据
      this.penDataMap.set(pageId, [point])
      //更新pagelist
      this.pageList = [...this.penDataMap.keys()]
    }
  }

  /**
   * 缓存回放数据
   * @param dots 点数据列表
   */
  saveReplayData(dots: Dot[]): void {
    for (let i = 0; i < dots.length; i++) {
      const point = this.parseData(dots[i])
      this.savePageData(point)
    }
  }

  /**
   * 生成图片
   * @returns HTMLImageElement
   */
  saveImageData(): HTMLImageElement {
    const src = this.osCanvas.toDataURL('image/png')
    const image = new Image()
    image.src = src
    return image
  }

  /**
   * 切页
   * @param pageId 页码ID
   */
  cutPage(pageId: number): void {
    //回放 禁止操作
    if (this.isReplay) return
    //清除当前屏幕
    this.osContext.clearRect(0, 0, this.width, this.height)

    //更新pageId
    this.pageId = pageId
    //显屏同步
    this.synchronousDraw()
  }

  /**
   * 绘制背景图
   * @param imageElement 图片元素
   */
  drawImage(imageElement: any): void {
    this.osContext.clearRect(0, 0, this.width, this.height)
    if (imageElement) {
      this.osContext.drawImage(imageElement, 0, 0, this.width, this.height)
    }
    this.synchronousDraw()
  }

  /**
   * 清屏
   * @returns Promise<{ status: number; msg: string; }>
   */
  clearScreen(): Promise<{ status: number; msg: string }> {
    return new Promise((resolve) => {
      if (this.isReplay) {
        resolve({ status: 0, msg: '正在回放，请勿清屏' })
      } else {
        this.drawImage(this.backroundMap.get(100))
        this.penDataMap.clear()
        this.penImageMap.clear()
        resolve({ status: 1, msg: '清屏成功' })
      }
    })
  }

  /**
   * 回放
   * @returns Promise<{ operate: number; status: number; msg: string; error?: string; }>
   * operate: 0-回放 / 1-暂停 / 2-继续, status: 0-失败/1-成功 ,msg: 提示信息, error: 错误信息
   */
  replay(): Promise<{
    operate: number
    status: number
    msg: string
    error?: string
  }> {
    if (this.isReplay) {
      //回放
      if (this.isPauseReplay) {
        //暂停
        return new Promise((resolve) => {
          this.continueReplay()
            .then(() => {
              resolve({ operate: 2, status: 1, msg: '继续回放成功' })
            })
            .catch((error) => {
              resolve({
                operate: 2,
                status: 0,
                msg: '继续回放失败，程序出现异常。',
                error: error.toString(),
              })
            })
        })
      } else {
        //非暂停
        return new Promise((resolve) => {
          this.pauseReplay()
            .then(() => {
              resolve({ operate: 1, status: 1, msg: '暂停回放成功' })
            })
            .catch((error) => {
              resolve({
                operate: 1,
                status: 0,
                msg: '暂停回放失败，程序出现异常。',
                error: error.toString(),
              })
            })
        })
      }
    } else {
      return new Promise((resolve) => {
        this.startReplay()
          .then((data) => {
            resolve(data)
          })
          .catch((error) => {
            resolve({
              operate: 0,
              status: 0,
              msg: '回放失败，程序出现异常。',
              error: error.toString(),
            })
          })
      })
    }
  }

  /**
   * 计算回放百分比
   * @param currentIndex 当前索引
   * @param total 总数
   * @returns number
   */
  calcReplayPercentage(currentIndex: number, total: number): number {
    return Math.ceil((currentIndex / total) * 100)
  }

  /**
   * 开始回放 回放完返回
   * @returns Promise<{ operate: number; status: number; msg: string; }>
   */
  startReplay(): Promise<{ operate: number; status: number; msg: string }> {
    console.log('start replay')
    return new Promise((resolve) => {
      const currentPageData = this.penDataMap.get(this.pageId)
      if (!currentPageData || currentPageData.length === 0) {
        resolve({ operate: 0, status: 0, msg: '回放失败，当前页无数据！' })
      } else {
        this.isReplay = true
        this.drawImage(this.backroundMap.get(100))
        this.replayTimer = setInterval(() => {
          // console.log(currentPageData[this.replayIndex])
          this.draw(currentPageData[this.replayIndex])

          this.replayPercentage = this.calcReplayPercentage(
            this.replayIndex,
            currentPageData.length,
          )
          this.replayIndex++
          if (this.replayIndex === currentPageData.length - 1) {
            this.cancelReplay().then(() => {
              resolve({ operate: 0, status: 1, msg: '回放完毕' })
            })
          }
        }, 10)
      }
    })
  }

  /**
   * 暂停回放
   * @returns Promise<void>
   */
  pauseReplay(): Promise<void> {
    return new Promise((resolve) => {
      //打开暂停开关
      this.isPauseReplay = true
      //清除定时器
      if (this.replayTimer) {
        clearInterval(this.replayTimer)
      }
      resolve()
    })
  }

  /**
   * 继续回放
   * @returns Promise<void>
   */
  continueReplay(): Promise<void> {
    return new Promise((resolve) => {
      //关闭暂停开关
      this.isPauseReplay = false
      const currentPageData = this.penDataMap.get(this.pageId)
      this.replayTimer = setInterval(() => {
        if (currentPageData) {
          this.draw(currentPageData[this.replayIndex])
          this.replayPercentage = this.calcReplayPercentage(
            this.replayIndex,
            currentPageData.length,
          )
          this.replayIndex++
          if (this.replayIndex === currentPageData.length - 1) {
            this.cancelReplay().then(() => {
              resolve()
            })
          }
        }
      }, 10)
    })
  }

  /**
   * 取消回放
   * @returns Promise<{ status: number; msg: string; }>
   */
  cancelReplay(): Promise<{ status: number; msg: string }> {
    return new Promise((resolve) => {
      //关闭回放开关
      this.isReplay = false
      //关闭暂停开关
      this.isPauseReplay = false
      //清零回放下标
      this.replayIndex = 0
      //清除定时器
      if (this.replayTimer) {
        clearInterval(this.replayTimer)
      }
      //无论是正常结束回放 或 手动结束回放 都需将本页数据重新绘制一遍
      // this.drawCurrentPage()
      resolve({ status: 1, msg: '取消回放成功' })
    })
  }

  /**
   * 绘制笔迹
   * @param dots 点数据列表
   */
  drawStrokes(dots: Dot[]): void {
    dots.forEach((dot) => this.handlePoint(dot))
  }

  /**
   * 切当前页回显
   * @param pageId 页码ID
   */
  drawCurrentPage(): void {
    const points = this.penDataMap.get(this.pageId) || []
    points?.forEach((point) => this.draw(point))
  }
}
