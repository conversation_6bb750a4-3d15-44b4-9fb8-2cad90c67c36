import { useUserStore } from "@/stores/modules/user"

export function hasAccess(roles: number[]) {
  const { userInfo } = useUserStore()
  const currentRole = userInfo.account_roles || []
  return hasIntersection(roles, currentRole)
}

// 判断两个数组是否存在交集
function hasIntersection(arr1: number[], arr2: number[]) {
  const set1 = new Set(arr1)
  for (const elem of arr2) {
    if (set1.has(elem)) {
      return true
    }
  }
  return false
}
