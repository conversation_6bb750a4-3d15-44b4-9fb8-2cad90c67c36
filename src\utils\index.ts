/* 全局方法 通过 $g 调用 */
import tool from "./tool"
import dayjs from "./dayjs"
import naiveui from "@/plugins/naiveui"
import _ from "./lodash"
import EventEmitter from "eventemitter3"
import math from "./math"
import config from "@/config"

dayjs.locale("zh-cn")

const $g = {
  ...naiveui,
  tool,
  dayjs,
  _: _,
  bus: new EventEmitter(),
  config,
  math: (num) => {
    return new math(num)
  },
}

export default $g
