import {
  cloneDeep,
  forEach,
  filter,
  remove,
  find,
  findIndex,
  chunk,
  pick,
  omit,
  includes,
  every,
  debounce,
  throttle,
  forEachRight,
  random,
  findLastIndex,
  uniqBy,
  map,
  uniqueId,
  isEqual,
  mergeWith,
  isArray,
} from "lodash-es"

const lodash = {
  cloneDeep,
  forEach,
  filter,
  remove,
  find,
  findIndex,
  chunk,
  pick,
  omit,
  includes,
  every,
  debounce,
  throttle,
  forEachRight,
  random,
  findLastIndex,
  uniqBy,
  map,
  uniqueId,
  isEqual,
  mergeWith,
  isArray,
}

export default lodash
