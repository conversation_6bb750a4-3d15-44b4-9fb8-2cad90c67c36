// @ts-nocheck
import BigNumber from "bignumber.js"

class BigMath {
  constructor(value) {
    const type = $g.tool.typeOf(Number(value))
    if (type !== "number") {
      throw new Error(
        `value必须是一个数字,但是你传入的${$g.tool.typeOf(value)}`,
      )
    }
    this.bigNumber = new BigNumber(value)
  }

  /* 加 */
  add(value) {
    this.bigNumber = this.bigNumber.plus(value)
    return this
  }

  /* 减 */
  sub(value) {
    this.bigNumber = this.bigNumber.minus(value)
    return this
  }

  /* 乘 */
  multiply(value) {
    this.bigNumber = this.bigNumber.times(value)
    return this
  }

  /* 除 */
  divide(value) {
    this.bigNumber = this.bigNumber.div(value)
    return this
  }

  /* 四舍五入 precision-精度 取值范围默认四舍五入 */
  toFixed(precision = 0) {
    this.bigNumber = this.bigNumber.dp(precision, BigNumber.ROUND_HALF_UP)
    return this
  }

  /* 获取值 */
  value() {
    return this.bigNumber.toNumber()
  }
}

export default BigMath
