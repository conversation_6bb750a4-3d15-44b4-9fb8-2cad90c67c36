export const TypeSet = async function (className: string = "g-mathjax") {
  if (!window.MathJax) {
    return
  }
  const node = document.getElementsByClassName(className)
  window.MathJax.startup.promise = window.MathJax.startup.promise
    .then(() => {
      return window.MathJax.typesetPromise(node)
    })
    .catch((err) => console.log("Typeset failed: " + err.message))
  return window.MathJax.startup.promise
}
