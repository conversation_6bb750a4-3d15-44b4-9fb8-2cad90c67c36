import { jumpNavigationLogin } from "@/utils/routes"
import config from "@/config"
import { useUserStore } from "@/stores/modules/user"
import router from "@/router/index"
const { loginInterception } = config

const CODE_MESSAGE = {
  400001: "token不存在",
  400002: "登录已过期,请重新登录",
  400003: "设置token失败",
  400004: "登录已过期,请重新登录",
  40002: "登录已过期，请重新登录",
  20014: "登录已过期,请重新登录",
  403: "没有权限，请重新登录",
  500: "服务器错误",
}
const successCode = [200, 0, "200", "0"]
// 异常需要重置code
const resetArr = [401, 40001, 400002, 400004, 40007, 20014, 403, 40002]

async function handleData({ config, data, status, statusText }) {
  const { resetAll } = useUserStore()

  // 若data.code存在，覆盖默认code
  let code = data && data["code"] ? data["code"] : status

  // 若code属于操作正常code，则status修改为200
  if (successCode.includes(data["code"])) code = 200
  // if (config.url.includes("/operate/user/list")) {
  //   code = 20014
  // }
  switch (code) {
    case 200:
      if (config.returnAll) {
        return Promise.resolve(data)
      } else {
        return Promise.resolve(data.data)
      }
    case 403:
      break
  }
  // 异常处理
  // 若data.msg存在，覆盖默认提醒消息
  let errMsg = `${
    data && data["msg"]
      ? data["msg"]
      : CODE_MESSAGE[code]
      ? CODE_MESSAGE[code]
      : statusText
  }`

  if (resetArr.includes(Number(code)) && loginInterception) {
    await resetAll()
    if (config.SSO) {
      errMsg += ",2s后将跳转至登录页"

      setTimeout(() => {
        jumpNavigationLogin()
      }, 2000)
    } else {
      location.reload()
      // router.replace({ name: "Login" })
    }
    // !todo 重置
  }

  $g.notification.error({
    content: "错误",
    meta: errMsg,
    duration: 3000,
  })
  return Promise.reject({ data, config, msg: errMsg })
}

export default handleData
