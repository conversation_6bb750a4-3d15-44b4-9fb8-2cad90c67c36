import { useUserStore } from "@/stores/modules/user"
import axios from "axios"
import config from "@/config"
const { inIframe, threeTokenTableName } = config
import handleData from "./handleData"
import { getToken, removeToken, setToken } from "@/utils/token"
const CancelToken = axios.CancelToken
let cancelArr: any = []
const currentRequestList: any = []
/**
 * @description axios初始化
 */
const server = axios.create({
  timeout: 60 * 1000,
  headers: {
    // 配后端数据的接收方式application/json;charset=UTF-8 或 application/x-www-form-urlencoded;charset=UTF-8
    "Content-Type": "application/json;charset=UTF-8",
  },
  replace: false, // 是否替换旧的请求，相同url新请求替换旧请求
  returnAll: false, // false-只返回data数据 true-返回全部数据(包括code,msg...)
  addUrlQuery: false, // 是否在url后面拼接查询参数，可以为对象，通过addUrlQuery.newUrl配置是否全新url,默认true
} as any)

server.interceptors.request.use(
  async (config: any) => {
    const userStore = useUserStore()
    const { token } = userStore
    const dataLocation = config.method === "get" ? "params" : "data"

    if (config.addUrlQuery && $g.tool.isTrue(config[dataLocation])) {
      $g.tool.paramsToUrl(
        config[dataLocation],
        config.addUrlQuery.newUrl ?? true,
      )
    }
    // todo: 临时处理
    if (config.Authorization) {
      config.headers["Authorization"] = config.Authorization
    }

    if (!config.headers["token"]) {
      config.headers["token"] = !inIframe
        ? getToken()
        : localStorage.getItem(threeTokenTableName)
    }

    // 从 URL hash 中获取参数
    const hashParams: any = $g.tool.paramObj(location.hash) || {}
    if (hashParams?.jztToken) {
      // 如果 URL 查询参数中包含 jztToken，则使用该值作为 token
      config.headers["token"] = hashParams.jztToken
    }

    // config.headers["token"] =
    //   "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************.CkqELsRkw_AIhEV_ouyafmLib2OZxIe02OTrqOAyjrI"
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

server.interceptors.response.use(
  (response: any) => {
    // 删除取消请求
    const index = cancelArr.findIndex((item) => {
      return item.key == response?.config?.url
    })
    deleteCurrentRequest(response?.config)
    return handleData(response)
  },
  (error) => {
    if (axios.isCancel(error)) return Promise.reject(error)
    let message = error.message
    if (message === "Network Error") {
      message = "服务器开小差了~~"
    } else if (message.includes("timeout")) {
      message = "请求超时"
    } else if (message.includes("Request failed with status code")) {
      message = `请求${message.substr(message.length - 3)}异常`
    } else if (!window.navigator.onLine) {
      message = `网络错误，请检查网络连接`
    }

    $g.notification.error({
      content: "错误",
      meta: message,
      duration: 3000,
    })
    return Promise.reject(error)
  },
)

/**
 * @description axios请求兼容 争对业务处理 取消重复请求
 */
function request(config) {
  config.method = config.method || "get"
  if (config.method.toLowerCase() === "get") {
    config.params = config.data
    /* 删除分页接口多余参数 */
    if ($g.tool.typeOf(config.data) == "object") {
      delete config.data.total
    }
    delete config.data
  }
  const params = getAxiosParams(config)

  const item = {
    url: config.url,
    params,
  }
  currentRequestList.push(item)
  return server({
    ...config,
    cancelToken: new CancelToken(function executor(c) {
      // 如果replace为true，取消cancelArr重复请求，只保留最后一次请求
      if (config.replace) {
        // 取消请求 并删除item
        cancelArr = cancelArr.filter((item) => {
          if (item.key == config.url) {
            item.c() // 取消请求
            return false // 从数组中删除该项
          }
          return true // 保留其他项
        })
        cancelArr.push({
          key: config.url,
          c,
        })
      }
    }),
  })
}

const methods = ["get", "post", "delete", "put", "patch"]
methods.forEach((type) => {
  request[type] = (url, data, options) => {
    return request({ url, method: type, data, ...options })
  }
})

function getAxiosParams(config) {
  const dataType = ["post"]
  return dataType.includes(config.method) ? config.data : config.params
}

function deleteCurrentRequest(config: any = {}) {
  $g._.remove(currentRequestList, (n) => {
    return n.url == config.url
  })
}

export default request as any
