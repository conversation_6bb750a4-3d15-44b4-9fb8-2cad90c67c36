import { isExternal } from "@/utils/validate"
import config from "@/config"
import { hasAccess } from "./hasAccess"
import Layout from "@/layouts/main.vue"
const { recordRoute, title, titleSeparator } = config

/**
 * @description 根据roles数组拦截路由
 */
export function filterRoutes(routes, rolesControl: boolean, baseUrl = "/") {
  return routes
    .flatMap((route) =>
      baseUrl !== "/" && route.children && route.meta.levelHidden
        ? [...route.children]
        : route,
    )
    .map((route) => {
      route = { ...route }
      // route.path =
      //   route.path !== "*" && !isExternal(route.path) ? route.path : route.path
      if (route.children && route.children.length > 0) {
        route.children = filterRoutes(route.children, rolesControl, route.path)
        if (route.children.length > 0) {
          route.childrenPathList = route.children.flatMap(
            (_) => <string[]>_.childrenPathList,
          )
          if (!route.redirect)
            route.redirect =
              route.children[0].redirect || route.children[0].path
        }
      } else route.childrenPathList = [route.path]
      return route
    })
}

export function filterRoutes2(routes, rolesControl: boolean, baseUrl = "/") {
  return routes
    .filter((route) => {
      if (rolesControl && route.meta && route.meta.roles) {
        return hasAccess(route.meta.roles)
      } else return true
    })
    .flatMap((route) =>
      baseUrl !== "/" && route.children && route.meta.levelHidden
        ? [...route.children]
        : route,
    )
    .map((route) => {
      route = { ...route }
      // route.path =
      //   route.path !== "*" && !isExternal(route.path) ? route.path : route.path
      if (route.children && route.children.length > 0) {
        route.children = filterRoutes2(route.children, rolesControl, route.path)
        if (route.children.length > 0) {
          route.childrenPathList = route.children.flatMap(
            (_) => <string[]>_.childrenPathList,
          )
          if (!route.redirect)
            route.redirect =
              route.children[0].redirect || route.children[0].path
        }
      } else route.childrenPathList = [route.path]
      return route
    })
}

/**
 * 获取当前跳转登录页的Route
 * @param currentPath 当前页面地址
 */
export function toLoginRoute(currentPath: string) {
  if (recordRoute && currentPath !== "/")
    return {
      path: "/login",
      query: { redirect: currentPath },
      replace: true,
    }
  else return { path: "/login", replace: true }
}

/**
 * 跳转到导航页
 * @param currentPath 当前页面地址
 */
export function jumpNavigationLogin(currentPath?: string) {
  window.location.href = `${import.meta.env.VITE_APP_NAVIGATION_URL}/#/login`
}

/**
 * @description 设置标题
 * @param pageTitle
 * @returns {string}
 */
export function getPageTitle(pageTitle: string | undefined) {
  const newTitles: any[] = []
  if (pageTitle) newTitles.push(pageTitle)
  if (title) newTitles.push(title)
  return newTitles.join(titleSeparator)
}

/**
 * @description all模式渲染后端返回路由,支持包含views路径的所有页面
 * @param asyncRoutes
 * @returns {*}
 */

export function convertRouter(asyncRoutes) {
  const viewsModules = import.meta.glob("../views/**/*.{vue,tsx}")
  return asyncRoutes.map((route: any) => {
    const { component, name, children } = route
    if (component) {
      component === "Layout"
        ? (route.component = Layout)
        : (route.component = dynamicImport(viewsModules, component as string))
    } else {
      throw `后端路由加载失败，请输入'Layout'或以'@/'开头的本地组件地址: ${component}`
    }
    route.meta = {
      ...route.meta,
      ...(route.metaExtend ? JSON.parse(route.metaExtend) : {}),
    }
    if (route.children) {
      route.children.length
        ? (route.children = convertRouter(route.children))
        : delete route.children
    }

    return route
  })
}

/**
 * 动态导入
 * */
export const dynamicImport = (viewsModules: any, component: string) => {
  const keys = Object.keys(viewsModules)

  component = component.replace(/^@\/views(.*)\.vue$/, "$1")

  const matchKeys = keys.filter((key) => {
    let k = key.replace("../views", "")
    const lastIndex = k.lastIndexOf(".")
    k = k.substring(0, lastIndex)
    return k === component
  })
  if (matchKeys?.length === 1) {
    const matchKey = matchKeys[0]

    return viewsModules[matchKey]
  }
  if (matchKeys?.length > 1) {
    console.warn(
      "views 文件夹下的同一个层级目录中，请不要创建同名的 .vue 和 .tsx 文件。这样做会导致动态引入失败",
    )
    return
  }
}

/**
 * 重新处理菜单显隐
 *
 */

export function dealMeta(asyncRoutes) {
  return asyncRoutes.map((route) => {
    route.meta.hidden =
      route.meta.hidden ||
      (route.children?.length &&
        route.children.every((child) => child.meta.hidden))
    if (route.children) {
      route.children.length
        ? (route.children = dealMeta(route.children))
        : delete route.children
    }
    return route
  })
}
