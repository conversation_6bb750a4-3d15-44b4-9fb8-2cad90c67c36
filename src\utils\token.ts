import config from "@/config"
import cookie from "js-cookie"
const { storage, tokenTableName } = config
/**
 * @description 获取token
 * @returns {string|ActiveX.IXMLDOMNode|Promise<any>|any|IDBRequest<any>|MediaKeyStatus|FormDataEntryValue|Function|Promise<Credential | null>}
 */
export function getToken() {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.getItem(tokenTableName)
    } else if ("sessionStorage" === storage) {
      return sessionStorage.getItem(tokenTableName)
    } else if ("cookie" === storage) {
      return cookie.get(tokenTableName)
    } else {
      return localStorage.getItem(tokenTableName)
    }
  } else {
    return localStorage.getItem(tokenTableName)
  }
}

/**
 * @description 存储token
 * @param token
 * @returns {void|*}
 */
export function setToken(token: string) {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.setItem(tokenTableName, token)
    } else if ("sessionStorage" === storage) {
      return sessionStorage.setItem(tokenTableName, token)
    } else if ("cookie" === storage) {
      return cookie.set(tokenTableName, token, {
        expires: 36500,
        path: "/",
        domain: ".qimingdaren.com",
      })
    } else {
      return localStorage.setItem(tokenTableName, token)
    }
  } else {
    return localStorage.setItem(tokenTableName, token)
  }
}

/**
 * @description 移除token
 * @returns {void|Promise<void>}
 */
export function removeToken() {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.removeItem(tokenTableName)
    } else if ("sessionStorage" === storage) {
      return sessionStorage.clear()
    } else if ("cookie" === storage) {
      console.log(1)
      return cookie.remove(tokenTableName, {
        path: "/",
        domain: ".qimingdaren.com",
      })
    } else {
      return localStorage.removeItem(tokenTableName)
    }
  } else {
    return localStorage.removeItem(tokenTableName)
  }
}
