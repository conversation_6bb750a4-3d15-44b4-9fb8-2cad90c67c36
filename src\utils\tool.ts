import ClipboardJS from "clipboard"
import { format } from "timeago.js"
import router from "@/router"
import config from "@/config"
import { isExternal } from "./validate"
import { TypeSet } from "./mathjax.js"
import FileSaver from "file-saver"
import J<PERSON><PERSON><PERSON> from "jszip"
const tool = {
  /**
   * 描述  获取文件后缀名
   * @param {String} filename
   */
  getExt(filename: string) {
    if (typeof filename === "string") {
      const fileNameArr = filename.split("?")
      return fileNameArr[0].substring(filename.lastIndexOf("."))?.toLowerCase()
    } else {
      throw new Error("filename must be a string type")
    }
  },

  /**
   * @description 将url请求参数转为json格式
   * @param url
   */
  paramObj(qs, sep, eq, options) {
    sep = sep || "&"
    eq = eq || "="
    const obj = {}

    if (typeof qs !== "string" || qs.length === 0) {
      return obj
    }
    if (qs.includes("?")) {
      qs = qs.substr(qs.indexOf("?") + 1)
    }
    const regexp = /\+/g
    qs = qs.split(sep)

    let maxKeys = 1000
    if (options && typeof options.maxKeys === "number") {
      maxKeys = options.maxKeys
    }

    let len = qs.length // maxKeys <= 0 means that we should not limit keys count
    if (maxKeys > 0 && len > maxKeys) {
      len = maxKeys
    }

    for (let i = 0; i < len; ++i) {
      const x = qs[i].replace(regexp, "%20")
      const idx = x.indexOf(eq)
      let kstr, vstr

      if (idx >= 0) {
        kstr = x.substr(0, idx)
        vstr = x.substr(idx + 1)
      } else {
        kstr = x
        vstr = ""
      }

      const k = decodeURIComponent(kstr)
      const v = decodeURIComponent(vstr)

      if (!Object.prototype.hasOwnProperty.call(obj, k)) {
        obj[k] = v
      } else if (Array.isArray(obj[k])) {
        obj[k].push(v)
      } else {
        obj[k] = [obj[k], v]
      }
    }

    return obj
  },

  /**
   * @description 获取随机id
   * @param length
   * @returns {string}
   */
  uuid(length = 32) {
    const num = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"
    let str = ""
    for (let i = 0; i < length; i++) {
      str += num.charAt(Math.floor(Math.random() * num.length))
    }
    return str
  },

  /* 判断是否为存在 */
  isTrue(a: any) {
    if (a === 0) return false //检验空字符串
    if (a === "") return false //检验空字符串
    if (a === "null") return false //检验字符串类型的null
    if (a === "undefined") return false //检验字符串类型的 undefined
    if (!a && a !== 0 && a !== "") return false //检验 undefined 和 null
    if (Array?.prototype?.isPrototypeOf(a) && a.length === 0) return false //检验空数组
    if (Object?.prototype?.isPrototypeOf(a) && Object.keys(a).length === 0) {
      return false
    } //检验空对象
    return true
  },

  /* 获取静态资源地址 */
  getFileUrl(url: string) {
    return new URL(`../assets/img/${url}`, import.meta.url).href
  },
  addSearchParams(url, params = {}) {
    return new URL(
      `${url.origin}${url.pathname}?${new URLSearchParams([
        ...(Array.from(url.searchParams.entries()) as any),
        ...Object.entries(params),
      ])}`,
    )
  },
  /* 判断精准类型 */
  typeOf(obj) {
    const toString = Object.prototype.toString
    const map = {
      "[object Boolean]": "boolean",
      "[object Number]": isNaN(obj) ? "NaN" : "number",
      "[object String]": "string",
      "[object Function]": "function",
      "[object Array]": "array",
      "[object Date]": "date",
      "[object RegExp]": "regExp",
      "[object Undefined]": "undefined",
      "[object Null]": "null",
      "[object Object]": "object",
    }
    return map[toString.call(obj)]
  },

  /* 点击复制 */
  copyData(target, text) {
    const clipboard = new ClipboardJS(target, {
      text() {
        return text
      },
    })
    clipboard.on("success", function () {
      $g.msg("链接已复制，请在电脑浏览器下载试卷包")
    })
  },

  /* 多久之前 */
  timeAgo(val) {
    const time = new Date(val?.replace(/-/g, "/")) //先将接收到的json格式的日期数据转换成可用的js对象日期
    return format(time, "zh_CN") //转换成类似于几天前的格式
  },

  /*
   * 将参数放到url上
   * @param {Object} query 参数
   * @param {Boolean} isNew 是否全新的参数
   *  */
  paramsToUrl(query, newUrl?: boolean) {
    // @ts-ignore
    const obj = newUrl
      ? query
      : Object.assign(router.currentRoute.value.query, query)
    Object.keys(obj).forEach((key) => {
      if (!tool.isTrue(obj[key]) && obj[key] !== 0) {
        delete obj[key]
      }
    })
    const params: any[] = []
    Object.entries(obj).forEach(([key, val]) => {
      params.push(key + "=" + val)
    })
    const path = location.hash.split("?")[0] + "?" + params.join("&")
    history.replaceState(history.state, "", path)
  },

  /* 动态加载js */
  loadJS(url: string) {
    return new Promise(function (resolve, reject) {
      const script = document.createElement("script")
      script.type = "text/javascript"
      script.src = url
      document.body.appendChild(script)
      script.onload = function () {
        resolve(`success: ${url}`)
      }
      script.onerror = function () {
        reject(Error(`${url} load error!`))
      }
    })
  },

  /* 存储单位转换 */
  formatFileSize(fileSize) {
    if (fileSize < 1024) {
      return fileSize + "B"
    } else if (fileSize < 1024 * 1024) {
      const temp = fileSize / 1024
      return temp.toFixed(0) + "KB"
    } else if (fileSize < 1024 * 1024 * 1024) {
      const temp = fileSize / (1024 * 1024)
      return temp.toFixed(0) + "MB"
    } else {
      const temp = fileSize / (1024 * 1024 * 1024)
      return temp.toFixed(2) + "GB"
    }
  },

  /* 通过文件后缀获取对应文件类型缩略图 */
  getFileTypeIcon(suffix) {
    suffix = suffix.toLowerCase()
    const fileTypeIcon = {
      word: ["doc", "docx"],
      pdf: ["pdf"],
      xlsx: ["xls", "xlsx"],
      txt: ["txt"],
      audio: ["mp3", "wav", "wma", "ape", "aac"],
      video: ["mp4", "avi", "rmvb", "flv", "wmv", "mkv", "mov"],
      img: ["jpg", "jpeg", "png", "gif", "bmp", "svg"],
      ppt: ["ppt", "pptx"],
    }
    let name = "unknown"
    Object.keys(fileTypeIcon).some((key, index) => {
      if (fileTypeIcon[key].includes(suffix)) {
        name = key
        return true
      }
    })
    return config.ossBaseURL + "qiming-school/file-" + name + ".png"
  },

  /* 重试 */
  retry(fn, times, delay) {
    return new Promise(function (resolve, reject) {
      const tFn = function () {
        fn(times)
          .then(resolve)
          .catch((e) => {
            if (times-- > 0) {
              setTimeout(tFn, delay)
            } else {
              reject(e)
            }
          })
      }
      return tFn()
    })
  },

  /**
   * 下载视频   目前赞支持mp4格式
   * @param url 视频地址
   * @param fileName 文件名
   * @param onProgress 下载进度
   */
  downloadVideoWithFetchProgress(
    url: string,
    fileName: string,
    onProgress?: (percent: number) => void,
  ): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await fetch(url)
        if (!response.body) return reject(new Error("ReadableStream 不支持"))
        const contentLength = response.headers.get("content-length")
        if (!contentLength) return reject(new Error("无法获取文件大小"))
        const total = parseInt(contentLength, 10)
        let loaded = 0
        const reader = response.body.getReader()
        const chunks: any = []
        // eslint-disable-next-line no-constant-condition
        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          if (value) {
            chunks.push(value)
            loaded += value.length
            if (onProgress) {
              const percent = Math.round((loaded / total) * 100)
              onProgress(percent)
            }
          }
        }
        const blob = new Blob(chunks)
        const blobUrl = window.URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = blobUrl
        a.download = fileName
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(blobUrl)
        resolve()
      } catch (err) {
        reject(err)
      }
    })
  },

  /**
   * 文件/视频下载
   */
  async downloadFile(url, filename, onProgress?: (percent: number) => void) {
    if (!url) return
    const suffix = this.getExt(url)
    if (suffix == ".mp4") {
      //文件名称需带上后缀，否则下载的文件会默认为.text
      await this.downloadVideoWithFetchProgress(
        url,
        filename + suffix,
        onProgress,
      )
    } else {
      const link = document.createElement("a") //创建a标签
      link.style.display = "none" //使其隐藏
      link.href = url //赋予文件下载地址
      link.setAttribute("download", filename) //设置下载属性 以及文件名
      document.body.appendChild(link) //a标签插至页面中
      link.click() //强制触发a标签事件
      document.body.removeChild(link)
    }
  },

  getFileData(fileUrl) {
    return new Promise((resolve, reject) => {
      const xmlhttp = new XMLHttpRequest()
      xmlhttp.open("GET", fileUrl, true)
      xmlhttp.responseType = "blob"
      xmlhttp.onload = function () {
        if (this.status == 200) {
          resolve(this.response)
        } else {
          reject(this.status)
        }
      }
      xmlhttp.send()
    })
  },

  /**
   * 打包多个文件为zip并下载
   */
  compressZipFile(files, exportName) {
    const zip = new JSZip()
    const promises: any = []
    files.forEach((item) => {
      if (item.fileUrl) {
        const promise = this.getFileData(item.fileUrl).then((data: any) => {
          zip.file(item.fileName, data, { binary: true })
        })
        promises.push(promise)
      }
    })

    //生成zip文件并下载
    return Promise.all(promises).then(() => {
      zip.generateAsync({ type: "blob" }).then((content) => {
        FileSaver.saveAs(content, `${exportName}.zip`)
      })
    })
  },

  /**
   * 通过文件后缀名获取文件类型
   */
  getFileType(suffix) {
    suffix = suffix.toLowerCase()
    const fileTypes = {
      word: ["doc", "docx"],
      pdf: ["pdf"],
      xlsx: ["xls", "xlsx"],
      txt: ["txt"],
      audio: ["mp3", "wav", "wma", "ape", "aac"],
      video: ["mp4", "avi", "rmvb", "flv", "wmv", "mkv", "mov"],
      img: ["jpg", "jpeg", "png", "gif", "bmp", "svg", "heic"],
      ppt: ["ppt", "pptx"],
      zip: [
        "rar",
        "zip",
        "7z",
        "cab",
        "arj",
        "lzh",
        "tar",
        "gz",
        "ace",
        "uue",
        "bz2",
        "jar",
        "iso",
        "mpq",
      ],
    }
    for (const key of Object.keys(fileTypes)) {
      if (fileTypes[key].includes(suffix)) return key
    }
  },

  /**
   * 根据链接地址获取对应的文档的外部预览地址，url: 链接地址，suffix:vip365额外参数
   * @param url
   * @param suffix
   * @returns
   */
  getWeb365Url(url) {
    let id = 28777
    const idMap = {
      "qm-cloud.oss-cn-chengdu.aliyuncs.com": 28777,
      "edu-jzt.oss-cn-chengdu.aliyuncs.com": 35356,
    }
    for (const [key, value] of Object.entries(idMap)) {
      if (url.includes(key)) {
        id = value
        break
      }
    }
    let web365Url = `//vip.ow365.cn/?i=${id}&ssl=1&time=${Date.now()}`
    const suffix: any = this.getExt(url)
    const fileType = {
      doc: {
        suffix: [".doc", ".docx"],
        params: "&n=4",
      },
      pdf: {
        suffix: [".pdf"],
        params: "&n=7",
      },
      ppt: {
        suffix: [".ppt", ".pptx"],
        params: "&n=5",
      },
    }
    for (const [key, value] of Object.entries(fileType)) {
      if (value.suffix.includes(suffix)) {
        web365Url += value.params
        break
      }
    }
    // 添加时间戳防止缓存
    web365Url = `${web365Url}&furl=${url}`
    return web365Url
  },
  /* 更新mathjax数据 */
  /**
   * 渲染数学公式
   * @param {string} className 需要渲染的元素类名，默认为'g-mathjax'
   */
  renderMathjax(className?: string) {
    nextTick(() => {
      TypeSet(className)
    })
  },
  getOSSUrl(url: string) {
    return config.baseOssImg + url
  },

  /**
   * 合并多个配置对象
   * @param {...Object} configs - 要合并的配置对象
   * @returns {Object} 合并后的配置对象
   */
  mergeConfigs(...configs) {
    return $g._.mergeWith({}, ...configs, (objValue, srcValue) => {
      // 特殊处理数组，使用源数组替换目标数组而不是合并它们
      if ($g._.isArray(srcValue)) {
        return srcValue
      }
      // 对于其他值，返回 undefined 让 lodash 使用默认合并策略
      return undefined
    })
  },
  /**
   * 判断两个对象值是否完全相同
   * @param a  比较值1
   * @param b  比较值2
   * @returns 相同true，不相同false
   */
  deepEqual(a, b) {
    if (a === b) return true
    if (typeof a !== "object" || a === null || b === null) return false

    // 优化：如果都是数组，且内容相同则返回true
    if (Array.isArray(a) && Array.isArray(b)) {
      if (a.length !== b.length) return false
      for (let i = 0; i < a.length; i++) {
        if (!tool.deepEqual(a[i], b[i])) return false
      }
      return true
    }

    // 如果一个是数组一个不是，直接返回false
    if (Array.isArray(a) !== Array.isArray(b)) return false

    // 对象比较
    const aKeys = Object.keys(a)
    const bKeys = Object.keys(b)
    if (aKeys.length !== bKeys.length) return false
    for (const key of aKeys) {
      if (
        !Object.prototype.hasOwnProperty.call(b, key) ||
        !tool.deepEqual(a[key], b[key])
      )
        return false
    }
    return true
  },
  /**
   * base64字符串转为File对象
   * @param {string} base64 base64字符串（可带data:前缀）
   * @param {string} filename 文件名
   * @param {string} [mimeType] 可选，指定MIME类型，优先级高于base64中的类型
   * @returns {File}
   */
  base64ToFile(base64, filename, mimeType) {
    if (!base64) return null
    // 兼容不带data:前缀的base64
    const arr = base64.split(",")
    const bstr = atob(arr.length > 1 ? arr[1] : arr[0])
    let type = mimeType
    if (!type) {
      if (arr[0].indexOf("data:") === 0) {
        type = arr[0].split(":")[1].split(";")[0]
      } else {
        type = ""
      }
    }
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n)
    }
    try {
      return new File([u8arr], filename, { type })
    } catch (e) {
      // 兼容不支持File构造函数的环境
      const blob: any = new Blob([u8arr], { type })
      blob.name = filename
      return blob
    }
  },
  /**
   * 获取应用信息
   * @param {File|Blob} file
   * @returns {Promise<object|null>} 解析成功返回应用信息对象，失败返回null
   */
  getAppInfo(file) {
    return new Promise((resolve) => {
      if (!file) return resolve(null)
      try {
        const parser = new (window as any).AppInfoParser(file)
        parser
          .parse()
          .then((result) => {
            const {
              versionCode,
              versionName,
              application,
              package: packageName,
              icon,
            } = result
            resolve({
              versionCode,
              versionName,
              appName: Array.isArray(application?.label)
                ? application?.label[0]
                : application?.label || "",
              packageName,
              icon: icon ? $g.tool.base64ToFile(icon, "appIcon.png") : null,
            })
          })
          .catch((err) => {
            console.error("getAppInfo error:", err)
            resolve(null)
          })
      } catch (err) {
        console.error("getAppInfo error:", err)
        resolve(null)
      }
    })
  },
  /**
   * 滚动到错误项
   * @param className
   */
  scrollToErrorItem(className = ".n-form-item-blank--error") {
    const errorNodes = document.querySelectorAll(className)
    console.log(errorNodes)

    if (errorNodes.length > 0)
      errorNodes[0].scrollIntoView({
        behavior: "smooth",
        block: "start",
        inline: "nearest",
      })
  },
}

export default tool
