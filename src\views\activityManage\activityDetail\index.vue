<template>
  <div class="activity-detail-container-main">
    <div class="flex items-center">
      <g-icon
        name="ri-arrow-left-line"
        size="20"
        color=""
        @click="$router.back()"
      />
      <div class="text-16px">{{ activityInfo.activityName }}</div>
    </div>
    <div class="h-40px bg-[#f8f8f8] mt-10px flex items-center pl-10px">
      <span>学段：{{ activityInfo.sysStageName }}</span
      ><span class="ml-40px">学科：{{ activityInfo.sysSubjectName }}</span>
    </div>
    <n-radio-group
      class="mt-10px"
      v-model:value="curCourseId"
      @update:value="getList()"
    >
      <n-radio-button
        v-for="subject in subjectList"
        :key="subject.sysCourseId"
        :value="subject.sysCourseId"
        >{{ subject.sysSubjectName }}</n-radio-button
      >
    </n-radio-group>
    <!-- 简介 -->
    <g-form :formOptions="descFormOptions" class="mt-10px">
      <template #introduceType>
        <el-radio-group
          v-model="descFormOptions.data.introduceType"
          @change="handleChange"
        >
          <el-radio
            v-for="item in descFormOptions.items.introduceType.options"
            :key="item.value"
            :value="item.value"
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </template>
      <template #introduce>
        <div class="w-full">
          <div class="flex justify-end mt-[-40px] mb-10px">
            <n-button type="primary" @click="updateDesc">保存简介</n-button>
          </div>
          <template v-if="currentType != 0">
            <div v-if="currentType == 1" class="h-[300px]">
              <g-editor
                v-model="descFormOptions.data.introduce"
                :config="{ initialFrameHeight: 160 }"
                :initProps="{
                  max: 99999,
                }"
              ></g-editor>
            </div>
            <g-markdown
              class="w-full"
              height="300px"
              v-if="currentType == 2"
              v-model="descFormOptions.data.introduce"
            ></g-markdown>
          </template>
          <div v-else>-</div>
        </div>
      </template>
    </g-form>
    <!-- <span class="mr-[10px] ml-20px">地区:</span>
    <el-cascader
      :teleported="false"
      cascaderPanelRef
      :props="areaProps"
      class="!w-[400px]"
      clearable
      v-model="sysAreaIdList"
      filterable
      :options="areaOptions"
      placeholder="地区选择"
    >
      <template #default="{ node }">
        <div>{{ node.label }}</div>
      </template>
    </el-cascader>
    <n-button
      class="ml-[10px]"
      :disabled="isDisabled"
      v-loading="saveLoading"
      type="primary"
      @click="saveArea"
      >保存</n-button
    > -->
    <n-divider />
    <g-table :tableOptions="tableOptions" @changePage="getList">
      <template #header-right>
        <n-button color="rgba(99, 161, 3, 1)" @click="create">
          <g-icon name="add-line" size="" color="" />
          新建板块
        </n-button>
      </template>
      <template #schoolList="{ row }">
        <n-tag
          v-if="row.schoolList?.length"
          class="mr-4px"
          v-for="item in row.schoolList"
          :key="item"
        >
          {{ item.schoolName }}
        </n-tag>
        <span v-else>/</span>
      </template>
      <template #cz="{ row }">
        <n-space justify="space-around">
          <n-button text type="primary" @click="edit(row)">编辑</n-button>
          <n-button
            text
            type="primary"
            @click="
              $router.push({
                name: 'ActivityModuleDetail',
                query: {
                  activityThemeId: row.activityThemeId,
                  schoolIdList: JSON.stringify(row.schoolIdList),
                },
              })
            "
            >详情</n-button
          >
          <n-button text type="error" @click="deleteRow(row)">删除</n-button>
        </n-space>
      </template>
    </g-table>
    <g-dialog
      :title="isEdit ? '编辑板块' : '新建板块'"
      :formOptions="formOptions"
      v-model:show="showDialog"
      @confirm="confirm"
    >
      <g-form :formOptions="formOptions"> </g-form>
    </g-dialog>
  </div>
</template>
<script setup lang="ts" name="ActivityDetail">
import {
  getActivitySubjectList,
  getBlockList,
  addBlock,
  editBlock,
  deleteBlock,
  getActivityDetail,
  editActivitySubjectDescribe,
  getAreaApi,
  editAreaApi,
} from "@/api/activity"
import { getSchoolList } from "@/api/studentType"
let showDesc = ref<any>(false)
let areaOptions = $ref<any>([])
let saveLoading = $ref<any>(false)
let sysAreaIdList = $ref<any>([])
const areaProps = reactive({
  label: "areaName",
  value: "sysAreaId",
  multiple: true,
  checkStrictly: true,
  emitPath: false,
})
let isDisabled = $computed(() => {
  return $g._.isEqual(activityInfo?.sysAreaIdList, sysAreaIdList)
})
const descFormOptions = $ref<any>({
  ref: null as any,
  loading: false,
  items: {
    introduceType: {
      type: "radio",
      label: "简介类型",
      options: [
        { label: "无", value: 0 },
        { label: "富文本", value: 1 },
        { label: "Markdown", value: 2 },
      ],

      slot: true,
    },
    introduce: {
      type: "text",
      label: "学科简介",
      slot: true,
    },
  },
  data: {
    introduceType: 0,
    introduce: "",
  },
})
const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: true,
  // pageOptions: {
  //   page: 1,
  //   page_size: 10,
  //   total: 20,
  // },
  column: [
    { prop: "activityThemeName", label: "板块名称" },
    { prop: "score", label: "高考分数" },
    { prop: "themeModuleTotal", label: "子模块数量" },
    { prop: "schoolList", label: "学校", slot: true },
    { prop: "cz", label: "操作", slot: true, width: "250px" },
  ],
  data: [{ a: 1, b: 2, c: 3 }],
})
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  items: {
    activityThemeName: {
      type: "textarea",
      label: "板块名称",
      width: "300px",
      maxlength: 100,
      rule: true,
    },
    score: {
      type: "text",
      label: "高考分值",
      width: "220px",
      maxlength: 10,
    },
    schoolIdList: {
      type: "select",
      multiple: true,
      label: "学校",
      width: "320px",
      options: [],
    },
    ordinal: {
      type: "number",
      label: "排序值",
      width: "150px",
      max: 99,
      min: 0,
    },
  },
  data: {
    activityThemeName: null,
    score: null,
    activityThemeId: null,
    ordinal: 99,
    schoolIdList: [],
  },
})
let showDialog = $ref(false)
let subjectList = $ref<any>([])
let curCourseId = $ref<any>(null)
let isEdit = $ref(false)
let activityInfo = $ref<any>({})
const route = useRoute()
let currentType = $ref(0)

watch(
  () => currentType,
  (val) => {
    if (val == 0) showDesc.value = false
    else showDesc.value = true
  },
)

async function saveArea() {
  saveLoading = true
  try {
    const res = await editAreaApi({
      activityId: route.query.activityId,
      sysAreaIdList: sysAreaIdList,
    })
    $g.msg("保存成功")
    activityInfo.sysAreaIdList = sysAreaIdList
    saveLoading = false
  } catch (err) {
    saveLoading = false
  }
}
/* 保存简介 */
async function updateDesc() {
  try {
    await editActivitySubjectDescribe({
      activityId: route.query.activityId,
      sysSubjectId: subjectList.find((v) => v.sysCourseId == curCourseId)
        .sysSubjectId,
      introduceType: descFormOptions.data.introduceType,
      introduce: descFormOptions.data.introduce,
    })
    $g.msg("保存成功")
    let res = await getActivitySubjectList({
      activityId: route.query.activityId,
    })
    subjectList = res || []
  } catch (err) {
    console.log(err)
  }
}
/* 切换编辑器 */
function handleChange(e) {
  if ($g.tool.isTrue(descFormOptions.data.introduce) || currentType == 2) {
    descFormOptions.data.introduceType = currentType
    $g.confirm({
      content: "切换编辑器会导致内容丢失，是否确认切换？",
    })
      .then(() => {
        descFormOptions.data.introduce = ""
        nextTick(() => {
          descFormOptions.data.introduceType = e
          currentType = e
        })
      })
      .catch(() => {})
  } else {
    descFormOptions.data.introduceType = e
    currentType = e
  }
}
function resetDesc() {
  let item: any = subjectList.find((v) => v.sysCourseId == curCourseId)
  descFormOptions.data.introduce = item.introduce || ""
  descFormOptions.data.introduceType = item.introduceType || 0
  currentType = item.introduceType || 0
}
async function getList() {
  resetDesc()
  tableOptions.loading = true
  let res = await getBlockList({
    activityId: route.query.activityId,
    sysCourseId: curCourseId,
  })
  tableOptions.data = res || []
  tableOptions.loading = false
}
async function getArea() {
  const res = await getAreaApi()
  areaOptions = res || []
}
async function init() {
  let res = await getActivitySubjectList({
    activityId: route.query.activityId,
  })
  subjectList = res || []
  descFormOptions.data.introduce = subjectList[0]?.introduce || ""
  descFormOptions.data.introduceType = subjectList[0]?.introduceType || 0
  curCourseId = subjectList[0].sysCourseId
}

/* 新增编辑板块 */
async function confirm() {
  try {
    formOptions.loading = true
    let params = {
      ...formOptions.data,
      sysCourseId: curCourseId,
      activityId: route.query.activityId,
    }
    if (!isEdit) {
      await addBlock(params)
    } else {
      await editBlock(params)
    }
    showDialog = false
    getList()
    $g.msg(isEdit ? "编辑成功" : "创建成功")
  } catch (err) {
    formOptions.loading = false
    console.log(err)
  }
}

async function getInfo() {
  let res = await getActivityDetail({
    activityId: route.query.activityId,
  })
  activityInfo = {
    ...res,
    sysStageName: route.query.sysStageName,
    sysSubjectName: subjectList.map((h) => h.sysSubjectName).join("、"),
  }
  sysAreaIdList = activityInfo?.sysAreaIdList || []
}
function edit(row) {
  isEdit = true
  let { activityThemeName, score, activityThemeId, ordinal } = row
  formOptions.data = {
    activityThemeName,
    score: score && String(score),
    activityThemeId,
    ordinal,
    schoolIdList: row.schoolIdList,
  }

  showDialog = true
}
function create() {
  isEdit = false
  Object.keys(formOptions.data).forEach((key) => {
    formOptions.data[key] = null
  })
  formOptions.data.ordinal = 99
  showDialog = true
}

async function getSchoolListApi() {
  let res = await getSchoolList()
  formOptions.items.schoolIdList.options = res.map((h) => ({
    label: h.schoolName,
    value: h.schoolId,
  }))
}
onBeforeMount(async () => {
  await init()
  getArea()
  getInfo()
  getList()
  getSchoolListApi()
})
onActivated(() => {
  getList()
})
async function deleteRow(row) {
  await deleteBlock({
    activityThemeId: row.activityThemeId,
  })
  $g.msg("删除成功")
  getList()
}
</script>
<style lang="scss" scoped>
:deep() {
  .tox-tinymce {
    height: 300px !important;
  }
  .el-cascader__tags {
    max-height: 200px;
    overflow-y: auto;
    display: block;
  }
}
</style>
