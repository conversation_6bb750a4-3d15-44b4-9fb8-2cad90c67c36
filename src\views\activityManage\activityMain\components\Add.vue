<template>
  <g-dialog
    :title="activityId ? '编辑活动' : '创建活动'"
    :formOptions="formOptions"
    v-model:show="showDialog"
    @confirm="confirm"
  >
    <g-form :formOptions="formOptions">
      <template #sysAreaIdList>
        <el-cascader
          :teleported="false"
          cascaderPanelRef
          :props="areaProps"
          clearable
          v-model="formOptions.data.sysAreaIdList"
          filterable
          :options="areaOptions"
          placeholder="地区选择"
        >
          <template #default="{ node }">
            <div>{{ node.label }}</div>
          </template>
        </el-cascader>
      </template>
    </g-form>
  </g-dialog>
</template>
<script setup lang="ts">
import {
  getStageSelect,
  getGradeSelect,
  getSubjectSelect,
  addActivity,
  getAreaApi,
  getSemesterList,
  getActivityDetail,
  editAreaApi,
} from "@/api/activity"
let areaOptions = $ref<any>([])
const areaProps = reactive({
  label: "areaName",
  value: "sysAreaId",
  multiple: true,
  checkStrictly: true,
  emitPath: false,
})
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  items: {
    activityName: {
      type: "textarea",
      label: "活动名称",
      width: "300px",
      placeholder: "最多100字",
      maxlength: 100,
      rule: true,
    },
    sysStageId: {
      type: "radio",
      label: "学段",
      width: "220px",
      rule: true,
      options: [],
    },
    sysGradeIdList: {
      type: "checkbox",
      label: "年级",
      width: "400px",
      rule: true,
      options: [],
    },
    sysCourseIdList: {
      type: "checkbox",
      label: "学科",
      width: "150px",
      rule: true,
      options: [],
    },
    sysTermId: {
      type: "radio",
      label: "学期",
      width: "150px",
      rule: true,
      options: [],
    },
    sysAreaIdList: {
      type: "",
      label: "地区",
      width: "150px",
      slot: true,
    },
  },
  data: {
    activityName: null,
    sysStageId: null,
    sysGradeIdList: null,
    sysCourseIdList: null,
    sysTermId: null,
    sysAreaIdList: null,
  },
})
const props = defineProps({
  show: {
    type: Boolean,
  },
  activityId: {
    type: Number,
  },
})
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)

let noChange = false

watch(
  () => formOptions.data.sysStageId,
  async (val) => {
    try {
      if (!val && !noChange) {
        formOptions.items.sysGradeIdList.options = []
        formOptions.items.sysCourseIdList.options = []
        formOptions.data.sysGradeIdList = null
        formOptions.data.sysCourseIdList = null
        return
      }
      await initGradeSelect()
      await initCourseSelect()
      if (noChange) {
        noChange = false
      }
    } catch (e) {
      noChange = false
      console.error(e)
    }
  },
)

async function initGradeSelect() {
  let res = await getGradeSelect({
    sysStageId: formOptions.data.sysStageId,
  })
  formOptions.items.sysGradeIdList.options = res?.map((h) => {
    return {
      ...h,
      label: h.title,
      value: h.id,
    }
  })
  if (!noChange) {
    formOptions.data.sysGradeIdList = null
  }
}

async function initCourseSelect() {
  let res = await getSubjectSelect({
    sysStageId: formOptions.data.sysStageId,
  })
  formOptions.items.sysCourseIdList.options = res?.map((h) => {
    return {
      ...h,
      label: h.sysSubjectName,
      value: h.sysCourseId,
    }
  })
  if (!noChange) {
    formOptions.data.sysCourseIdList = null
  }
}

async function initStageSelect() {
  let res = await getStageSelect()
  formOptions.items.sysStageId.options = res?.map((h) => {
    return {
      ...h,
      label: h.title,
      value: h.id,
    }
  })
}
async function confirm() {
  try {
    if (props.activityId) {
      await editAreaApi({
        ...formOptions.data,
        activityId: props.activityId,
      })
      $g.msg("编辑成功")
    } else {
      await addActivity(formOptions.data)
      $g.msg("创建成功")
    }
    showDialog.value = false
    emit("refresh")
  } catch {
    formOptions.loading = false
  }
}
async function getArea() {
  const res = await getAreaApi()
  areaOptions = res || []
}

async function getSemesterListApi() {
  try {
    const data = await getSemesterList()
    formOptions.items.sysTermId.options = data.map((h) => {
      return {
        label: h.sysTermName,
        value: h.sysTermId,
      }
    })
    if (!props.activityId) {
      formOptions.data.sysTermId = (
        data.find((v) => v.checked) || data[0]
      )?.sysTermId
    }
  } catch (e) {
    console.error(e)
  }
}

async function getActivityDetailApi() {
  try {
    noChange = true
    const data = await getActivityDetail({
      activityId: props.activityId,
    })
    formOptions.data = {
      activityName: data.activityName || null,
      sysStageId: data.sysStageId || null,
      sysTermId: data.sysTermId || null,
      sysAreaIdList: data.sysAreaIdList || null,
      sysGradeIdList:
        data.sysGradeIdList?.split(",").filter(Boolean).map(Number) || null,
      sysCourseIdList:
        data.sysCourseIdList?.split(",").filter(Boolean).map(Number) || null,
    }
  } catch (e) {
    noChange = false
    console.error(e)
  }
}

watch(
  () => props.show,
  async () => {
    if (props.show) {
      await getArea()
      await getSemesterListApi()
      if (props.activityId) {
        getActivityDetailApi()
      }
    }
  },
)
onBeforeMount(() => {
  initStageSelect()
})
</script>
<style scoped lang="scss">
:deep() {
  .el-cascader__tags {
    max-height: 200px;
    overflow-y: auto;
    display: block;
  }
  .el-input {
    width: 400px;
  }
}
</style>
