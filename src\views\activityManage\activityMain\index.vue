<template>
  <div class="activity-container-main">
    <g-form
      @search="getList"
      @reset="getList"
      :formOptions="filterFormOptions"
      :tableOptions="tableOptions"
    >
    </g-form>
    <g-table
      :tableOptions="tableOptions"
      @changePage="getList"
      :cell-class-name="rowStyle"
    >
      <template #header-right>
        <n-button type="primary" @click="handleEdit(null)">
          <g-icon name="add-line" size="" color="" />
          创建活动
        </n-button>
      </template>
      <template #cz="{ row }">
        <n-space justify="space-around">
          <n-button
            text
            type="primary"
            @click="
              $router.push({
                name: 'ActivityDetail',
                query: {
                  activityId: row.activityId,
                  sysStageName: row.sysStageName,
                },
              })
            "
            >详情</n-button
          >
          <n-button type="primary" text @click="toReport(row)">报告</n-button>
          <n-button
            type="success"
            text
            v-if="row.isDelete === 2"
            @click="update(row)"
            >启用</n-button
          >
          <n-button type="error" text v-else @click="update(row)"
            >禁用</n-button
          >
          <n-button
            text
            type="primary"
            @click="handleEdit(row)"
            :disabled="row.isDelete !== 2"
            >编辑</n-button
          >
        </n-space>
      </template>
    </g-table>
    <Add
      v-model:show="showDialog"
      @refresh="getList"
      :activityId="activityId"
    ></Add>
  </div>
</template>
<script setup lang="ts" name="ActivityManageIndex">
import { getActivityList, updateActivityStatus } from "@/api/activity"
import Add from "./components/Add.vue"
const filterFormOptions = reactive({
  ref: null as any,
  filter: true,
  labelWidth: "84px",
  items: {
    keyword: {
      type: "text",
      label: "活动名称",
      showLabel: true,
      width: "300px",
    },
  },
  // 列表接口使用到的筛选值,及时搜索时使用
  data: {
    keyword: null,
  },
  // 列表接口使用到的筛选值,非及时搜索时使用
  filterData: {},
})
let activityId = $ref<any>(null)
const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: true,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 20,
  },
  column: [
    { prop: "activityName", label: "活动名称" },
    { prop: "sysStageName", label: "学段" },
    {
      prop: "sysGradeList",
      label: "年级",
      formatter: (row) => {
        return row.sysGradeList?.map((h) => h.sysGradeName).join("、")
      },
    },
    {
      prop: "sysSubjectList",
      label: "学科",
      formatter: (row) => {
        return row.sysSubjectList?.map((h) => h.sysSubjectName).join("、")
      },
    },
    { prop: "createTime", label: "创建时间" },
    { prop: "activityUrl", label: "访问链接", width: "250px" },
    {
      prop: "isDelete",
      label: "状态",
      formatter: (row) => {
        return [null, "启用", "禁用"][row.isDelete]
      },
    },
    { prop: "cz", label: "操作", slot: true, width: "220px" },
  ],
  data: [],
})

let showDialog = $ref(false)
const router = useRouter()
function rowStyle({ row }) {
  if (row.isDelete == 2) {
    return "row-class"
  }
}
function toReport(row) {
  router.push({
    name: "ReportDetail",
    query: {
      activityId: row.activityId,
      activityName: row.activityName,
    },
  })
}
async function getList() {
  tableOptions.loading = true
  try {
    let res = await getActivityList({
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
      ...filterFormOptions.filterData,
    })
    tableOptions.data = res.list.map((h) => {
      return {
        ...h,
        activityUrl:
          import.meta.env.VITE_APP_THREE_LANDSCAPE_URL +
          "/#/student/sprintCamp/main?activityUniqueId=" +
          h.activityUniqueId,
      }
    })
    tableOptions.pageOptions.total = res.total
    tableOptions.loading = false
  } catch {
    tableOptions.loading = false
  }
}

async function update(row) {
  await updateActivityStatus({
    activityId: row.activityId,
  })
  row.isDelete = row.isDelete === 2 ? 1 : 2
  $g.msg("操作成功")
}

function handleEdit(row) {
  activityId = row?.activityId
  showDialog = true
}

onBeforeMount(() => {
  getList()
})
onActivated(() => {
  getList()
})
</script>
<style lang="scss" scoped>
:deep() {
  .row-class {
    background-color: #fcd7db !important;
  }
}
</style>
