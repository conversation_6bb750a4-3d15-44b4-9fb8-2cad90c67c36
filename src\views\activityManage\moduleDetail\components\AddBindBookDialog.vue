<template>
  <g-dialog
    title="选择书籍"
    v-model:show="showDialog"
    :on-after-enter="open"
    @handleClose="leave"
    width="1000"
    :show-footer="false"
  >
    <!-- 筛选条件 -->
    <g-form
      :formOptions="formOptions"
      :tableOptions="tableOptions"
      @reset="reset"
      @search="search"
    >
      <template #bookTypeId>
        <div class="flex gap-x-[10px]">
          <n-tree-select
            v-model:value="formOptions.data.bookTypeId"
            :options="formOptions.items.bookTypeId.options"
            clearable
            :render-label="renderLabel"
            placeholder="请选择书籍类型"
            class="w-[200px]"
          />
        </div>
      </template>
    </g-form>
    <!-- 表格数据 -->
    <g-table :tableOptions="tableOptions" @changePage="initData" :height="500">
      <template #bookTypeName="{ row }">
        <div>
          {{ row.bookTypeList?.map((v) => v.bookTypeName).join("、") }}
        </div>
      </template>

      <template #cz="{ row }">
        <n-space justify="center">
          <n-button
            type="error"
            text
            v-if="bindData.includes(row.bookId)"
            @click="handleBindBook(row)"
            >取消选择</n-button
          >
          <n-button type="primary" text v-else @click="handleBindBook(row)"
            >选择书籍</n-button
          >
        </n-space>
      </template>
      <template #createTime="{ row }">
        <div>{{ row.createTime.split(" ")[0] }}</div>
      </template>
    </g-table>
  </g-dialog>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
import { getNewTypeSelect, getListApi } from "@/api/bookMgt"
import { getNewStageListApi, getNewSubjectListApi } from "@/api/resourceMgt"
import GTooltip from "@/components/global/g-tooltip/index.vue"
const emit = defineEmits(["updateBindBookData"])
const props = defineProps({
  bookList: {
    type: Array as PropType<any>,
    default: () => [],
  },
})
let showDialog = defineModel<boolean>("show")
const formOptions = $ref<any>({
  ref: null as any,
  filter: true,
  loading: false,
  items: {
    sysStage: {
      type: "select",
      label: "学段",
      options: [],
      labelWidth: "60px",
      width: "150px",
    },
    sysSubjectId: {
      type: "select",
      label: "学科",
      options: [],
      width: "150px",
      labelWidth: "60px",
    },
    bookTypeId: {
      type: "select",
      label: "类型",
      options: [],
      width: "150px",
      labelWidth: "60px",
      slot: true,
    },
    keyword: {
      type: "text",
      label: "书籍名称检索",
      showLabel: false,
      width: "180px",
    },
  },
  data: {
    sysStage: null,
    sysSubjectId: null,
    bookTypeId: null,
    keyword: "",
  },
})
const tableOptions = $ref<any>({
  ref: null as any,
  loading: false,
  column: [
    {
      prop: "bookName",
      label: "书籍名称",
      align: "left",
      headerAlign: "center",
    },
    {
      prop: "bookTypeName",
      label: "类型",
      slot: true,
    },

    {
      prop: "sysSubjectName",
      label: "学科",
      width: "110px",
    },
    {
      prop: "sysGradeName",
      label: "年级",
      width: "110px",
    },
    {
      prop: "num",
      label: "试题",
      width: "100px",
    },
    {
      prop: "createTime",
      label: "创建时间",
      slot: true,
    },
    {
      prop: "year",
      label: "年份",
      width: "100px",
    },

    {
      prop: "cz",
      label: "操作",
      slot: true,
      width: "170px",
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})
let bindData = $ref<any>([])
function renderLabel({ option }) {
  return h(GTooltip, { content: option.label, refName: String(option.key) }, {})
}
async function reset() {
  formOptions.data.keyword = ""
  formOptions.data.bookTypeId = null
  formOptions.data.sysSubjectId = null
  formOptions.data.sysStage = null
  tableOptions.pageOptions.page = 1
  await initData()
}
async function search() {
  tableOptions.pageOptions.page = 1
  await initData()
}
/* 书籍列表 */
async function initData() {
  try {
    tableOptions.loading = true
    let res = await getListApi({
      ...formOptions.data,
      state: 2,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    tableOptions.data = res.list
    tableOptions.pageOptions.total = res.total
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
  }
}
/* 获取学段 */
async function getStage() {
  let res = await getNewStageListApi()
  formOptions.items.sysStage.options = res.map((v) => {
    return {
      value: v.id,
      label: v.title,
    }
  })
}
/* 获取学科 */
async function getSubject() {
  let res = await getNewSubjectListApi({
    sysStageId: formOptions.data.sysStage,
  })
  formOptions.items.sysSubjectId.options = res.map((v) => {
    return {
      value: v.sysSubjectId,
      label: v.sysCourseName,
    }
  })
}
/* 获取类型 */
async function getTypeSelectApi() {
  let res = await getNewTypeSelect({
    sysStageId: formOptions.data.sysStage,
  })
  formOptions.items.bookTypeId.options = transformDataStructure(res)
}
/* 递归处理数据 */
function transformDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.label = newItem.bookTypeName
      newItem.key = newItem.bookTypeId
      newItem.children = newItem.list.length
        ? transformDataStructure(newItem.list)
        : null

      return newItem
    })
  } else if (typeof data === "object" && data !== null) {
    return Object.keys(data).reduce((obj, key) => {
      obj[key] = transformDataStructure(data[key])
      return obj
    }, {})
  }
  return data
}
watch(
  () => formOptions.data.sysStage,
  async (val) => {
    if (val) {
      formOptions.items.sysSubjectId.options = []
      formOptions.data.sysSubjectId = null
      formOptions.data.bookTypeId = null
      formOptions.items.bookTypeId.options = []
      await getSubject()
      await getTypeSelectApi()
    }
  },
)
/* 选中、取消选中书籍 */
function handleBindBook(data) {
  if (bindData.includes(data.bookId)) {
    bindData.splice(bindData.indexOf(data.bookId), 1)
  } else {
    bindData.push(data.bookId)
  }
}
async function open() {
  bindData = $g._.cloneDeep(props.bookList)
  await initData()
  await getStage()
}
function leave() {
  if (!$g._.isEqual(bindData, props.bookList)) {
    emit("updateBindBookData", bindData)
  }
}
</script>

<style lang="scss" scoped></style>
