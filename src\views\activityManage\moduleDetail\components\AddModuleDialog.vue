<template>
  <g-dialog
    title="模块管理"
    :formOptions="formOptions"
    v-model:show="showDialog"
    :on-after-enter="open"
    width="1000"
    @handleClose="close"
    @confirm="confirm"
  >
    <n-scrollbar class="pr-20px h-[calc(100vh-200px)]">
      <g-form :formOptions="formOptions">
        <!-- 标签 -->
        <template #tagList>
          <n-space>
            <template
              v-for="(item, index) in formOptions.data.tagList"
              :key="item + index"
            >
              <n-tag
                closable
                type="primary"
                class="mr-8px br-[4px] mt-4px"
                @close="handleClose(index)"
              >
                {{ item }}
              </n-tag>
            </template>
            <n-button
              type="primary"
              @click="
                () => {
                  showAddTagDialog = true
                  tagName = ''
                }
              "
            >
              <g-icon name="ri-add-line" size="" color="" />
              新增标签
            </n-button>
          </n-space>
        </template>

        <!-- 关联书籍 -->
        <template #bookList>
          <div class="!w-[calc(100%)]">
            <div class="flex items-center">
              <n-button type="primary" text @click="openBookListDialog"
                ><g-icon
                  name="ri-add-line"
                  size=""
                  color=""
                />添加书籍</n-button
              >
              <div class="text-gray-default ml-10px">
                (请一次性选择完所有书籍)
              </div>
            </div>
            <div v-loading="bookLoading" class="min-h-[50px]">
              <Draggable
                v-model="treeData"
                group="people"
                item-key="bookId"
                @change="onMoveCallback"
              >
                <template #item="{ element, index }">
                  <div class="flex items-start">
                    <g-icon name="ri-draggable" size="" color="" />
                    <BookTreeItem
                      class="flex-1"
                      :tree-data="element"
                      :index="index"
                      :default-checked="element.defaultCheckedList"
                      @handle-check="handleCheck"
                      @remove="removeBook"
                    />
                  </div>
                </template>
              </Draggable>
            </div>
          </div>
        </template>
      </g-form>
    </n-scrollbar>
    <n-button
      type="primary"
      text
      @click="openSortDialog"
      :disabled="!$g.tool.isTrue(formOptions.data.bookList)"
    >
      <g-icon name="ri-sort-asc" size="" color="" />
      资源排序
    </n-button>
    <!-- 添加标签 -->
    <g-dialog
      title="添加标签"
      v-model:show="showAddTagDialog"
      @confirm="addTag"
    >
      <n-input
        v-model:value="tagName"
        type="text"
        placeholder="请输入标签名称"
      />
    </g-dialog>
    <!-- 书籍绑定弹窗 -->
    <AddBindBookDialog
      v-model:show="showAddBookDialog"
      :book-list="bindBookData"
      @updateBindBookData="updateBindBookData"
    />
    <!-- 资源排序 -->
    <SortDialog
      v-model:show="showSortDialog"
      :bindData="formOptions.data.bookList"
    />
  </g-dialog>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
import AddBindBookDialog from "./AddBindBookDialog.vue"
import SortDialog from "./SortDialog.vue"
import BookTreeItem from "./BookTreeItem.vue"
import {
  getBookChapterTree,
  addActivityModule,
  getActivityModuleDetail,
  editActivityModule,
} from "@/api/activity"
import Draggable from "vuedraggable"
let treeData = $ref<any>([]) //书籍树形结构数据
let bookLoading = $ref(false)
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  detailId: {
    type: [String, null, Number] as PropType<string | null | number>,
    default: "",
  },
})
const emit = defineEmits(["update:show", "refresh"])
let showDialog = useVModel(props, "show", emit)
let showAddTagDialog = $ref(false) //tag弹窗
let showSortDialog = $ref<boolean>(false) //排序弹窗
let tagName = $ref("")
let showAddBookDialog = $ref(false) //添加书籍弹窗
const validatePass = (rule, value, callback) => {
  if (!value || value.length == 0) {
    callback(new Error("请选择书籍或者章节"))
  }
}
let bindBookData = $ref<any>([]) //选择的书籍数据
/* 模块表单数据 */
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  items: {
    activityThemeModuleName: {
      type: "text",
      label: "模块名称",
      rule: true,
      maxlength: 50,
    },
    tagList: {
      type: "text",
      label: "标签",
      slot: true,
    },
    activityThemeModuleDesc: {
      type: "editor",
      label: "介绍",
    },
    ordinal: {
      type: "number",
      label: "排序值",
      width: "150px",
      max: 99,
      min: 0,
    },
    bookList: {
      type: "text",
      label: "关联书籍",
      rule: {
        validator: validatePass,
        type: "string",
        required: true,
      },
      slot: true,
    },
  },
  data: {
    activityThemeModuleName: "",
    tagList: [],
    activityThemeModuleDesc: "",
    bookList: [],
    ordinal: 99,
  },
})
const route = useRoute()
let detailInfo = $ref<any>({})
/* 打开弹窗 */
function open() {
  if (props.isEdit) {
    getActivityModuleDetailApi()
  }
}
/* 获取模块详情 */
async function getActivityModuleDetailApi() {
  let res = await getActivityModuleDetail({
    activityThemeModuleId: props.detailId,
  })
  detailInfo = res
  formOptions.data.activityThemeModuleName = res.activityThemeModuleName
  formOptions.data.tagList = res.tagList
  formOptions.data.activityThemeModuleDesc = res.activityThemeModuleDesc
  formOptions.data.ordinal = res.ordinal
  bindBookData = res.bookList.map((v) => v.bookId)
  await initData("detail")
}
/* 关联书籍弹窗 */
function openBookListDialog() {
  if ($g.tool.isTrue(formOptions.data.bookList)) {
    $g.confirm({
      content: "重新添加/移除书籍会更新资源数据，是否继续？",
    })
      .then(() => {
        showAddBookDialog = true
      })
      .catch(() => {})
  } else {
    showAddBookDialog = true
  }
}
/* 排序弹窗 */
function openSortDialog() {
  if (!formOptions.data.bookList.length)
    return $g.msg("请先选择关联书籍", "warning")
  showSortDialog = true
}
/* 排序书籍 */
function onMoveCallback() {
  //  更新treeData的sort
  treeData.forEach((v, i) => {
    v.sort = i
  })
  // 更新绑定数据sort并排序
  formOptions.data.bookList.forEach((v) => {
    treeData.forEach((vv) => {
      if (v.bookId == vv.bookId) {
        v.sort = vv.sort
      }
    })
  })
  formOptions.data.bookList.sort((a, b) => a.sort - b.sort)
}
/* 选择书籍章节树 */
function handleCheck(val) {
  let num = formOptions.data.bookList.findIndex(
    (item) => item.bookId == val.bookId,
  )
  if (num == -1) {
    formOptions.data.bookList.push(val)
  } else {
    if (val.checkedData.length == 0) {
      formOptions.data.bookList.splice(num, 1)
    } else {
      formOptions.data.bookList[num] = val
    }
  }
  onMoveCallback()
}
/* 删除书籍  删除展示书籍，删除选中的书籍,删除选中的书籍ID */
function removeBook(val, bookId) {
  $g.confirm({
    content: "是否移除该书籍",
  })
    .then(() => {
      let num = bindBookData.findIndex((item) => item == bookId)
      bindBookData.splice(num, 1)
      treeData.splice(val, 1)
      formOptions.data.bookList.splice(val, 1)
    })
    .catch(() => {})
}
/* 获取书籍章节树 */
async function initData(type?) {
  bookLoading = true
  for (let i of bindBookData) {
    let res = await getBookChapterTree({
      bookId: i,
      activityThemeModuleId:
        type == "detail" ? detailInfo.activityThemeModuleId : null,
    })
    /* 整书数据处理 */
    if (!$g.tool.isTrue(res.catalogList)) {
      res.catalogList = [
        {
          bookCatalogId: 0,
          bookId: res.bookId,
          name: res.name,
          nameAlias: res.nameAlias,
          children: [],
          resourceList: res.resourceList,
          questionTotal: res.questionTotal,
          check: type == "detail" ? true : false,
        },
      ]
    }
    treeData.push({
      ...res,
      sort: 0,
    })
  }

  bookLoading = false
}
/* 更新选中书籍 */
async function updateBindBookData(val) {
  bindBookData = val
  treeData = []
  formOptions.data.bookList = []
  //获取书籍章节列表
  await initData()
}

/* 删除tag */
function handleClose(index) {
  formOptions.data.tagList.splice(index, 1)
}
/* 添加标签 */
function addTag() {
  formOptions.data.tagList.push(tagName)
}
/* 关闭弹窗 */
function close() {
  bindBookData = []
  treeData = []
  detailInfo = {}
}
watch(
  () => props.show,
  (val) => {
    if (!val) {
      formOptions.data.activityThemeModuleDesc = ""
    }
  },
)
/* 保存 */
async function save() {
  let bookList = formOptions.data.bookList.map((v) => {
    return {
      ...v,
      bookId: v.bookId,
      catalogList: v.checkedData,
      checkedData: [],
    }
  })
  let data = {
    activityThemeId: route.query.activityThemeId,
    ...formOptions.data,
    bookList: bookList,
  }
  await addActivityModule(data)
  $g.msg("添加成功")
}
/* 编辑 */
async function edit() {
  let bookList = formOptions.data.bookList.map((v) => {
    return {
      ...v,
      bookId: v.bookId,
      catalogList: v.checkedData,
      checkedData: [],
    }
  })
  let data = {
    activityThemeId: route.query.activityThemeId,
    ...formOptions.data,
    bookList: bookList,
    activityThemeModuleId: detailInfo.activityThemeModuleId,
  }
  await editActivityModule(data)
  $g.msg("编辑成功")
}
async function confirm() {
  try {
    props.isEdit ? await edit() : await save()
    emit("refresh")
    emit("update:show", false)
  } catch (err) {
    console.log(err)
  } finally {
    formOptions.loading = false
  }
}
</script>

<style lang="scss" scoped>
:deep() {
  .tox-tinymce {
    height: 300px !important;
  }
}
</style>
