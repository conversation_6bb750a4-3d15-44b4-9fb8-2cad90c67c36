<template>
  <div>
    <!-- 标题 -->
    <div class="flex items-center justify-between pr-8px">
      <div
        class="flex items-center cursor-pointer"
        @click="expandTree = !expandTree"
      >
        <div v-if="mode == 1">{{ treeData.name }}</div>
        <div v-if="mode == 2">{{ treeData.nameAlias || treeData.name }}</div>
        <g-icon
          name="ri-arrow-right-s-line"
          :class="expandTree ? 'rotate-90' : 'rotate-0'"
          class="duration-100 transition-all"
          size=""
          color=""
        />
      </div>

      <n-space v-if="mode == 1">
        <n-button type="primary" text @click="selectAll">全选</n-button>
        <n-button type="error" text @click="removeBook">移除书籍</n-button>
      </n-space>
      <div class="flex justify-between w-[200px]" v-if="mode == 2">
        <n-button type="primary" text @click.stop="handleChangeName(treeData)">
          <g-icon name="ri-edit-line" size="" color="" />
          改名
        </n-button>

        <n-button type="primary" text @click.stop="resetName(treeData)">
          <g-icon name="ri-refresh-line" size="" color="" />
          重置名字
        </n-button>
      </div>
    </div>
    <!-- 树形结构 -->
    <transition>
      <div
        v-show="expandTree"
        class="duration-200 transition-all overflow-hidden"
        :class="expandTree ? 'h-auto' : '!h-1px'"
      >
        <g-tree
          ref="treeRef"
          class="!border-none w-full"
          :tree-line="false"
          :treeData="mode == 1 ? treeData?.catalogList : treeData?.checkedData"
          nodeKey="bookCatalogId"
          :highlight-check="false"
          :highlight-current="false"
          :default-expand-all="mode == 2"
          :default-checked-keys="defaultCheckedKeys"
          :multiple="mode == 1"
          @node-expand="
            () => {
              $g.tool.renderMathjax()
            }
          "
          @radioChange="radioChange"
        >
          <template #body="{ data }">
            <div class="w-[calc(100%)] overflow-hidden my-5px">
              <!-- 章节 -->
              <div
                class="flex items-center !text-[12px] gap-10px w-[calc(100%)] overflow-hidden justify-between"
              >
                <g-mathjax v-if="mode == 1" :text="data?.name" />
                <g-mathjax
                  v-if="mode == 2"
                  :text="data?.nameAlias || data?.name"
                />
                <div v-if="mode == 1" class="flex-shrink-0">
                  ({{ data.questionTotal ?? 0 }}题)
                </div>
                <div
                  class="flex justify-between !w-[200px] flex-shrink-0"
                  v-if="mode == 2"
                >
                  <n-button
                    type="primary"
                    text
                    @click.stop="handleChangeName(data)"
                  >
                    <g-icon name="ri-edit-line" size="" color="" />
                    改名
                  </n-button>
                  <n-button
                    v-if="data.parentBookCatalogId != 0"
                    :type="data.isHidden == 2 ? 'error' : 'primary'"
                    text
                    @click.stop="handleChangeIsHidden(data)"
                  >
                    <g-icon
                      :name="
                        data.isHidden == 2 ? 'ri-eye-off-line' : 'ri-eye-line'
                      "
                      size=""
                      color=""
                    />
                    {{ data.isHidden == 2 ? "隐藏" : "显示" }}
                  </n-button>

                  <n-button type="primary" text @click.stop="resetName(data)">
                    <g-icon name="ri-refresh-line" size="" color="" />
                    重置名字
                  </n-button>
                </div>
              </div>
              <!-- 资源 -->
              <div v-if="mode == 2">
                <Draggable
                  v-model="data.resourceList"
                  :group="data.bookCatalogId"
                  @start="draggable = true"
                  @end="draggable = false"
                  item-key="activityResourceFromId"
                >
                  <template #item="{ element }">
                    <div
                      class="flex items-center !text-[12px] gap-10px w-[calc(100%)] overflow-hidden justify-between my-10px bg-[rgba(238,238,238,0.3)]"
                    >
                      <div class="flex items-center">
                        <g-icon name="ri-draggable" size="" color="" />
                        <g-mathjax
                          :text="element.nameAlias || element.name"
                          class="mt-2px"
                        />
                        <div
                          v-if="element.activityResourceType == 1"
                          class="flex-shrink-0"
                        >
                          ({{ element.questionTotal }}题)
                        </div>
                      </div>
                      <!-- 操作 -->
                      <div class="flex justify-between !w-[200px]">
                        <n-button
                          type="primary"
                          text
                          @click.stop="handleChangeName(element)"
                        >
                          <g-icon name="ri-edit-line" size="" color="" />
                          改名
                        </n-button>
                        <n-button
                          :type="element.isDelete == 2 ? 'error' : 'primary'"
                          text
                          @click.stop="handleChangeIsDelete(element)"
                        >
                          <g-icon
                            :name="
                              element.isDelete == 2
                                ? 'ri-eye-off-line'
                                : 'ri-eye-line'
                            "
                            size=""
                            color=""
                          />
                          {{ element.isDelete == 2 ? "隐藏" : "显示" }}
                        </n-button>
                        <n-button
                          type="primary"
                          text
                          @click.stop="resetName(element)"
                        >
                          <g-icon name="ri-refresh-line" size="" color="" />
                          重置名字
                        </n-button>
                      </div>
                    </div>
                  </template>
                </Draggable>
              </div>
            </div>
          </template>
        </g-tree>
      </div>
    </transition>
    <n-divider />
    <!-- 改名弹窗 -->
    <g-dialog
      title="新名称"
      v-model:show="showEditDialog"
      :formOptions="formOptions"
      @confirm="confirm"
    >
      <g-form :formOptions="formOptions"> </g-form>
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import Draggable from "vuedraggable"
const props = defineProps({
  treeData: {
    type: Object,
    default: () => {},
  },
  mode: {
    type: Number,
    default: 1, //展示模式，2排序模式
  },
  index: {
    type: Number,
    default: 0,
  },
  defaultChecked: {
    type: Array,
    default: () => [],
  },
})
let treeRef = $ref<any>(null)
let draggable = $ref(false)
const emit = defineEmits(["remove", "handleCheck"])
let expandTree = $ref(true)
let showEditDialog = $ref(false)
let currentData = $ref<any>({}) //当前点击更名的数据
const formOptions = reactive<any>({
  loading: false,
  ref: null as any,
  items: {
    nameAlias: {
      type: "text",
      label: "新名称",
      placeholder: "最多50个字符",
      rule: true,
    },
  },
  data: {
    nameAlias: "",
  },
})
const defaultCheckedKeys = $computed(() => {
  if ($g.tool.isTrue(props.treeData.catalogList)) {
    let arr = findCheckedBookCatalogIds(props.treeData.catalogList)
    return arr
  } else {
    return []
  }
})

watch(
  () => defaultCheckedKeys,
  (val) => {
    if (val.length) {
      let selectTreeData = retainSpecificCatalogs(
        props.treeData?.catalogList,
        val,
      )
      emit(
        "handleCheck",
        $g._.cloneDeep({
          ...props.treeData,
          checkedData: selectTreeData,
        }),
      )
    }
  },
  {
    immediate: true,
  },
)
/* 递归查找树形结构选中 */
function findCheckedBookCatalogIds(data, checkedIds = [] as any) {
  data.forEach((item) => {
    if (item.check) {
      checkedIds.push(item.bookCatalogId)
    }
    if (item.children && item.children.length > 0) {
      findCheckedBookCatalogIds(item.children, checkedIds)
    }
  })
  return checkedIds
}
/* 切换显示隐藏 */
function handleChangeIsDelete(data) {
  if (data.isDelete == 2) {
    data.isDelete = 1
  } else {
    data.isDelete = 2
  }
}
/* 没有children的子集显示与隐藏 */
function handleChangeIsHidden(data) {
  if (data.isHidden == 2) {
    data.isHidden = 1
  } else {
    data.isHidden = 2
  }
}
/* 移除当前书籍 */
function removeBook() {
  emit("remove", props.index, props.treeData.bookId)
}
function radioChange(data) {
  if (props.mode == 2) return
  setAllIsDeleteTo1(data.data)

  // markAsDeleted(props.treeData?.catalogList, data.data.bookCatalogId)
  let selectTreeData = retainSpecificCatalogs(
    props.treeData?.catalogList,
    data.obj.checkedKeys,
  )

  emit(
    "handleCheck",
    $g._.cloneDeep({
      ...props.treeData,
      checkedData: selectTreeData,
    }),
  )
}
function setAllIsDeleteTo1(data) {
  function recursiveMark(node) {
    // 如果当前节点有resourceList，设置每一项的isDelete为1
    if (node.resourceList) {
      node.resourceList.forEach((item) => {
        item.isDelete = 1
      })
    }
    // 如果当前节点有children，递归处理每个children
    if (node.children) {
      node.children.forEach((child) => recursiveMark(child))
    } else {
      node.isHidden = 1
    }
  }
  // 从顶层数据开始递归
  recursiveMark(data)
}
function markAsDeleted(data, id) {
  // 递归函数用于遍历和标记
  function recursiveMark(node, id) {
    // 检查当前节点是否是目标节点或其子节点
    if (node.bookCatalogId == id || node.parentBookCatalogId == id) {
      // 如果有resourceList，则将每一项的isDelete属性设置为1
      if (node.resourceList) {
        node.resourceList.forEach((item) => {
          item.isDelete = 1
        })
      }
      // 递归处理子节点
      if (node.children) {
        node.children.forEach((child) => recursiveMark(child, id))
      }
    } else {
      if (node.children) {
        node.children.forEach((child) => recursiveMark(child, id))
      }
    }
  }

  // 从顶层数据开始递归
  data.forEach((item) => recursiveMark(item, id))
}
function retainSpecificCatalogs(data, ids) {
  // 辅助函数，用于递归查找并保留特定bookCatalogId及其父级节点和子节点
  function findAndKeep(node, ids, isTarget) {
    if (!node) return null
    let keepNode = null as any
    // 检查当前节点是否是目标ID之一或其子节点是目标ID之一
    if (ids.includes(node.bookCatalogId) || isTarget) {
      keepNode = {
        ...node,
        children: node.children
          ? node.children
              .map((child) => findAndKeep(child, ids, true))
              .filter((n) => n)
          : undefined,
      }
    } else if (node.children) {
      // 递归查找子节点
      const children = node.children.map((child) =>
        findAndKeep(child, ids, false),
      )
      const hasTargetInChildren = children.some((child) => child)
      // 如果目标节点在子节点中，保留当前节点
      if (hasTargetInChildren) {
        keepNode = { ...node, children: children.filter((n) => n) }
      }
    }
    return keepNode
  }

  // 递归调用辅助函数，并返回结果
  return data.map((root) => findAndKeep(root, ids, false)).filter((n) => n)
}

function selectAll() {
  treeRef.setCheckedNodes(props.treeData.catalogList)
  // 遍历所有节点，设置isDelete为1
  props.treeData.catalogList.forEach((v) => {
    setAllIsDeleteTo1(v)
  })
  emit(
    "handleCheck",
    $g._.cloneDeep({
      ...props.treeData,
      checkedData: props.treeData.catalogList,
    }),
  )
}
/* 打开改名弹窗 */
function handleChangeName(data) {
  formOptions.data.nameAlias = data.nameAlias || data.name
  currentData = data
  showEditDialog = true
}
/* 点击更名 */
function confirm() {
  try {
    currentData.nameAlias = formOptions.data.nameAlias
    showEditDialog = false
    $g.msg("操作成功")
  } catch (e) {
    console.log(e)
  } finally {
    formOptions.loading = false
  }
}
/* 点击重置 */
function resetName(data) {
  data.nameAlias = null
  nextTick(() => {
    $g.tool.renderMathjax()
  })
  $g.msg("操作成功")
}
onMounted(() => {
  $g.tool.renderMathjax()
})
</script>

<style lang="scss" scoped>
:deep() {
  .el-tree-node__content {
    height: auto !important;
    background-color: transparent !important;
    align-items: start;
  }
  .el-tree-node {
    white-space: break-spaces;
    .el-icon {
      padding: 0 6px;
      margin-top: 6px;
    }
  }
  .custom-tree-node {
    overflow: hidden;
    .element-tree-node-label-wrapper {
      overflow: hidden;
    }
  }

  .is-checked {
    .el-tree-node__content {
      background-color: #fff !important;
    }
    .el-tree-node__children {
      .el-tree-node__content {
        background-color: transparent !important;
      }
    }
  }
}
</style>
