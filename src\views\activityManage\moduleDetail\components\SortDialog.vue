<template>
  <div>
    <g-dialog
      title="资源排序"
      v-model:show="showDialog"
      :show-footer="false"
      width="1000"
    >
      <n-scrollbar class="pr-10px h-[700px] relative">
        <template v-if="!showLoading">
          <div v-for="item in data" :key="item.bookId">
            <BookTreeItem :tree-data="item" :mode="2" />
          </div>
        </template>
        <div
          class="loader absolute left-0 right-0 top-0 bottom-0 m-auto"
          v-else
        ></div>
      </n-scrollbar>
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
import BookTreeItem from "./BookTreeItem.vue"
const props = defineProps({
  bindData: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  show: {
    type: Boolean,
    default: false,
  },
})
let showDialog = defineModel<boolean>("show")
let showLoading = $ref(true)
let data = $ref<any>([])
/* 处理加载数据 */
let nextElement = $ref<any>(null)
watch(
  () => props.show,
  async (val) => {
    if (val) {
      nextElement = throwArrayElements(props.bindData, 50)
      processElements()
    } else {
      data = []
      nextElement = null
      showLoading = true
    }
  },
  {
    immediate: true,
  },
)

function processElements() {
  nextElement().then((element) => {
    if (element !== undefined) {
      data.push(element)
      processElements() // 递归调用以处理下一个元素
    } else {
      showLoading = false
    }
  })
}
function throwArrayElements(arr, interval) {
  let index = 0 // 用于跟踪当前要抛出的元素的索引
  return function next() {
    // 检查是否还有元素需要处理
    if (index < arr.length) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(arr[index++]) // 抛出当前元素，并准备下一个
        }, interval)
      })
    } else {
      return Promise.resolve(undefined)
    }
  }
}
</script>

<style lang="scss" scoped>
.loader {
  border: 4px solid rgba(67, 149, 249, 1);
  border-left-color: transparent;
  border-radius: 50%;
}

.loader {
  border: 4px solid rgba(67, 149, 249, 1);
  border-left-color: transparent;
  width: 30px;
  height: 30px;
}

.loader {
  border: 4px solid rgba(67, 149, 249, 1);
  border-left-color: transparent;
  width: 30px;
  height: 30px;
  animation: spin89345 1s linear infinite;
}

@keyframes spin89345 {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
