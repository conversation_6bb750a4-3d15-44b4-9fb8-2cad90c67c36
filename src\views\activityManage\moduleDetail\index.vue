<template>
  <div class="module-detail-container-main">
    <!-- 返回 -->
    <div class="flex items-center">
      <g-icon
        name="ri-arrow-left-line"
        size="20"
        color=""
        @click="$router.back()"
      />
      <div class="text-[18px]">{{ moduleName }}</div>
    </div>
    <!-- 简介 -->
    <g-form :formOptions="formOptions">
      <template #activityThemeFormatType>
        <el-radio-group
          v-model="formOptions.data.activityThemeFormatType"
          @change="handleChange"
        >
          <el-radio
            v-for="item in formOptions.items.activityThemeFormatType.options"
            :key="item.value"
            :value="item.value"
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </template>
      <template #activityThemeDesc>
        <div class="w-full">
          <div class="flex justify-end mt-[-25px] mb-10px">
            <n-button type="primary" @click="updateDesc">保存简介</n-button>
          </div>
          <div v-if="currentType == 1" class="h-[300px]">
            <g-editor
              v-model="formOptions.data.activityThemeDesc"
              :config="{ initialFrameHeight: 160 }"
              :initProps="{
                max: 99999,
              }"
            ></g-editor>
          </div>
          <g-markdown
            class="w-full"
            height="300px"
            v-if="currentType == 2"
            v-model="formOptions.data.activityThemeDesc"
          ></g-markdown>
        </div>
      </template>
    </g-form>
    <n-divider />
    <!-- 模块管理 -->
    <g-table
      :tableOptions="tableOptions"
      :highlight-current-row="false"
      :span-method="getSpanRange"
    >
      <template #header-left>
        <div>模块管理</div>
      </template>
      <template #header-right>
        <n-space>
          <n-button type="primary" @click="openDialog('add')">
            <g-icon name="ri-add-line" size="" color="" />
            新建模块
          </n-button>
        </n-space>
      </template>
      <template #tag="{ row }">
        <n-space justify="center">
          <n-tag
            type="primary"
            v-for="item in row.activityThemeModuleTagName"
            :key="item"
            >{{ item }}</n-tag
          >
        </n-space>
        <div v-if="!$g.tool.isTrue(row.activityThemeModuleTagName)">-</div>
      </template>
      <template #activityThemeModuleDesc="{ row }">
        <div v-html="row.activityThemeModuleDesc"></div>
        <div v-if="!$g.tool.isTrue(row.activityThemeModuleDesc)">-</div>
      </template>
      <template #bookName="{ row }">
        <div class="line-2">{{ row.bookName }}</div>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="success" text @click="toPreviewPage(row)"
            >预览目录</n-button
          >
          <n-button type="primary" text @click="openDialog('edit', row)"
            >管理</n-button
          >
          <n-button text type="primary" @click="updateResource(row)"
            >资源更新</n-button
          >
          <n-button type="error" text @click="deleteModule(row)">删除</n-button>
        </n-space>
      </template>
    </g-table>
    <!-- 新建/编辑模块 -->
    <AddModuleDialog
      v-model:show="showDialog"
      :isEdit="isEdit"
      :detailId="clickModuleId"
      @refresh="initData"
    />
  </div>
</template>

<script setup lang="ts" name="ActivityModuleDetail">
import {
  getActivityDescribe,
  editActivityDescribe,
  getActivityModuleList,
  deleteActivityModule,
  updateActivityResource,
} from "@/api/activity"
import AddModuleDialog from "./components/AddModuleDialog.vue"

const formOptions = $ref<any>({
  ref: null as any,
  loading: false,
  items: {
    activityThemeFormatType: {
      type: "radio",
      label: "类型",
      options: [
        { label: "富文本", value: 1 },
        { label: "Markdown", value: 2 },
      ],

      slot: true,
    },
    activityThemeDesc: {
      type: "text",
      label: "板块简介",
      slot: true,
    },
  },
  data: {
    activityThemeFormatType: 1,
    activityThemeDesc: "",
  },
})
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      prop: "activityThemeModuleName",
      label: "模块名称",
    },
    {
      prop: "tag",
      label: "标签",
      slot: true,
    },
    {
      prop: "activityThemeModuleDesc",
      label: "介绍",
      slot: true,
    },
    {
      prop: "bookName",
      label: "关联书籍",
      slot: true,
    },
    {
      prop: "bookCatalogTotal",
      label: "章节数量",
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
})
let currentType = $ref(1)
let showDialog = $ref(false)
let moduleName = $ref("")
let isEdit = $ref(false)
let clickModuleId = $ref<any>(null)
const route = useRoute()
onBeforeMount(() => {
  getDesc()
  initData()
})
/* 资源更新 */
async function updateResource(row) {
  $g.confirm({
    content: "是否重新拉取模块已关联的书籍资源？",
  })
    .then(async () => {
      await updateActivityResource({
        activityId: row.activityId,
        activityThemeModuleId: row.activityThemeModuleId,
      })
      $g.msg("更新成功")
    })
    .catch(() => {})
}
/* 删除模块 */
function deleteModule(row) {
  $g.confirm({
    content: "是否确认删除",
  })
    .then(async () => {
      await deleteActivityModule({
        activityThemeModuleId: row.activityThemeModuleId,
      })
      $g.msg("删除成功", "success")
      initData()
    })
    .catch(() => {})
}
/* 获取板块简介 */
async function getDesc() {
  try {
    let res = await getActivityDescribe({
      activityThemeId: route.query.activityThemeId,
    })
    formOptions.data.activityThemeFormatType = res.activityThemeFormatType
    currentType = res.activityThemeFormatType
    formOptions.data.activityThemeDesc = res.activityThemeDesc ?? ""
    moduleName = res.activityThemeName
  } catch (err) {
    console.log(err)
  }
}
/* 保存板块简介 */
async function updateDesc() {
  await editActivityDescribe({
    activityThemeId: route.query.activityThemeId,
    activityThemeDesc: formOptions.data.activityThemeDesc,
    activityThemeFormatType: formOptions.data.activityThemeFormatType,
    schoolIdList:
      typeof route.query.schoolIdList === "string"
        ? JSON.parse(route.query.schoolIdList)
        : route.query.schoolIdList || [],
  })
  $g.msg("保存成功")
}
/* 切换编辑器 */
function handleChange(e) {
  if ($g.tool.isTrue(formOptions.data.activityThemeDesc) || currentType == 3) {
    formOptions.data.activityThemeFormatType = currentType
    $g.confirm({
      content: "切换编辑器会导致内容丢失，是否确认切换？",
    })
      .then(() => {
        formOptions.data.activityThemeDesc = ""
        nextTick(() => {
          formOptions.data.activityThemeFormatType = e
          currentType = e
        })
      })
      .catch(() => {})
  } else {
    formOptions.data.activityThemeFormatType = e
    currentType = e
  }
}
// 表格布局计算
const colList = $computed<any>(() => {
  return tableOptions.data.reduce((prev, next) => {
    if (prev[next.activityThemeModuleId]) {
      prev[next.activityThemeModuleId].count += 1
    } else {
      prev[next.activityThemeModuleId] = {
        count: 1,
        row: next,
      }
    }
    return prev
  }, {})
})
// 表格布局计算
function getSpanRange({ row, columnIndex }) {
  if (columnIndex !== 3 && columnIndex !== 4) {
    const item = colList[row.activityThemeModuleId]
    if (row === item.row) {
      return [item.count, 1]
    } else {
      return [0, 0]
    }
  }
}
/* 初始化表格数据 */
async function initData() {
  try {
    tableOptions.loading = true
    let res = await getActivityModuleList({
      activityThemeId: route.query.activityThemeId,
    })
    tableOptions.data = res.reduce((pre, item, index) => {
      if (!item.bookList) {
        return [
          ...pre,
          {
            ...item,
            index: index + 1,
          },
        ]
      } else {
        pre = [
          ...pre,
          ...item.bookList.map((v) => {
            return {
              ...v,
              ...item,
            }
          }),
        ]
      }
      return pre
    }, [])
    tableOptions.loading = false
  } catch (err) {
    tableOptions.loading = false
    tableOptions.data = []
    console.log(err)
  }
}
/* 新建/编辑弹窗 */
function openDialog(type = "add", row?) {
  if (type == "edit") {
    isEdit = true
    clickModuleId = row.activityThemeModuleId
  } else {
    isEdit = false
  }
  showDialog = true
}

/** 跳转目录预览页面 */
function toPreviewPage(row) {
  let url = `${
    import.meta.env.VITE_APP_THREE_LANDSCAPE_URL
  }/#/student/sprintCamp/contents?activityThemeModuleId=${
    row.activityThemeModuleId
  }&preview=1`
  window.open(url, "_blank")
}
</script>

<style lang="scss" scoped>
:deep() {
  .tox-tinymce {
    height: 300px !important;
  }
}
</style>
