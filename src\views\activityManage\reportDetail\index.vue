<template>
  <div class="report-detail-container-main">
    <iframe
      class="w-full h-[calc(100vh-57px-40px-50px)]"
      id="iframeRef"
      :src="calSrc"
      frameborder="0"
    />
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/stores/modules/user"
const userStore = useUserStore()
const route = useRoute()
const calSrc = $computed(() => {
  return `${
    import.meta.env.VITE_APP_TEACHER_USER_URL
  }/#/teacher/ccyReport/reportDetail?activityId=${
    route.query.activityId
  }&activityName=${route.query.activityName}&source=jinzita`
})
</script>

<style lang="scss" scoped></style>
