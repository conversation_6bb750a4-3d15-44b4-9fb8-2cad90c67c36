<template>
  <div class="audio-info-detail-container-main" ref="detailContainerRef">
    <div
      class="flex gap-x-[20px] items-center text-info pb-40px"
      style="border-bottom: 1px solid #ccc"
    >
      <div>
        运行状态: <span class="text-[#000]">{{ audioInfo?.statusText }}</span>
      </div>
      <div>
        请求时间: <span class="text-[#000]">{{ audioInfo?.requestTime }}</span>
      </div>
      <div>
        响应时间: <span class="text-[#000]">{{ audioInfo?.responseTime }}</span>
      </div>
      <n-button
        type="primary"
        :disabled="buttonLoading"
        :loading="getPlaybackStatus() == 'loading'"
        @click="playVideo"
        >{{ getPlaybackStatus() == "start" ? "暂停" : "播放" }}</n-button
      >
      <n-button type="primary" @click="requestTest" :loading="buttonLoading"
        >测试请求</n-button
      >
    </div>
    <div class="flex gap-x-[150px]">
      <div class="flex-1">
        <div class="text-[20px] my-10px">发送请求</div>
        <JsonEditor v-model="requestData" />
      </div>
      <div class="flex-1">
        <div class="text-[20px] my-10px">接收请求</div>
        <JsonEditor v-model="responseData" :key="responseData.length" />
      </div>
    </div>
    <!-- 播放音频 -->
    <Speaker v-model:show="showAudio" :width="width" ref="speakerRef" />
  </div>
</template>

<script setup lang="ts" name="AudioInfoDetail">
import JsonEditor from "./components/JsonEditor.vue"
import { getAudioDetail } from "@/api/audioMgt"
import { useSpeakerStore } from "@/stores/modules/speaker"
import Speaker from "@/views/resourceMgt/bookManage/enterResoure/components/SubtitleSpeaker.vue"
import config from "@/config/index"
import { fetchEventSource } from "@microsoft/fetch-event-source"
import { useUserStore } from "@/stores/modules/user"
const { baseURL } = config
const userStore = useUserStore()
let speakerStore = useSpeakerStore()
let { getIdByType, isPlay } = $(storeToRefs(useSpeakerStore()))
let showAudio = $ref(false)
let detailContainerRef = $ref<any>(null)
let width = $ref(0)
const route = useRoute()
let audioInfo = $ref<any>({})
let requestData = $ref({})
let responseData = $ref<any>([])
let buttonLoading = $ref(false)
initData()
onBeforeUnmount(() => {
  speakerStore.audio && speakerStore.reset()
})
function playVideo() {
  if (!audioInfo.audioUrlList) return $g.msg("音频不存在", "warning")
  width = detailContainerRef.clientWidth - 50
  speakerStore.setUrlList(10, audioInfo)
}

/* 测试请求 */
async function requestTest() {
  try {
    buttonLoading = true
    audioInfo.requestTime = $g.dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")
    $g.msg("测试中，请等待...")
    const url = `${baseURL}/tutoring/admin/audio/tts/test`
    const headers = {
      "Content-Type": "application/json;charset=UTF-8",
      Accept: "text/event-stream",
      token: userStore.token,
    }
    responseData = []
    await fetchEventSource(url, {
      method: "POST",
      headers,
      body: JSON.stringify(requestData),
      onmessage: (e) => {
        console.log(JSON.parse(e.data))
        let data = JSON.parse(e.data)
        if (data.code == 200) {
          responseData.push(data)
        }
      },
      onerror: (err) => {
        throw new Error(err)
      },
    })
    audioInfo.audioUrlList = responseData.map((v) => v.data)
    audioInfo.responseTime = $g.dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")
    $g.msg("测试完成")
    buttonLoading = false
  } catch (err) {
    buttonLoading = false
    console.log(err)
  }
}
/* JSON 数据改变 */
function onJsonChange(val) {
  requestData = val
}
/* 音频状态 */
function getPlaybackStatus() {
  if (audioInfo.questionTtsId == getIdByType) {
    return isPlay
  } else {
    return "pause"
  }
}
/* 获取详情 */
async function initData() {
  try {
    let res = await getAudioDetail({
      questionTtsId: route.query.questionTtsId,
    })
    audioInfo = res
    audioInfo.questionTtsId = route.query.questionTtsId
    requestData = JSON.parse(res.request ?? {})
    responseData = res.response
      ? res.response?.map((v) => {
          return JSON.parse(v)
        })
      : []
  } catch (err) {
    console.log(err)
  }
}
</script>

<style lang="scss" scoped>
:deep() {
  .jsoneditor-vue {
    height: calc(100vh - 290px);
  }
  .jsoneditor-contextmenu {
    display: none;
  }
}
</style>
