<template>
  <div class="audio-info-container-main" ref="containerRef">
    <g-form :formOptions="formOptions" @search="search" @reset="search">
    </g-form>
    <g-table :tableOptions="tableOptions" @change-page="initData">
      <template #header-right>
        <n-button type="error" @click="openClearDialog">一键清除缓存</n-button>
      </template>
      <template #text="{ row }">
        <div class="flex">
          <g-markdown
            class="w-full line-2"
            height="450px"
            v-model="row.text"
            mode="stream"
          ></g-markdown>
          <n-button
            type="primary"
            text
            @click="openDialog(row)"
            v-if="row.text?.length > 20"
            >详情</n-button
          >
        </div>
      </template>
      <template #audioContent="{ row }">
        <n-space justify="center">
          <g-icon
            v-if="row.audioUrlList"
            :name="
              getPlaybackStatus(10, row) == 'start'
                ? 'ri-pause-circle-line'
                : 'ri-play-circle-line'
            "
            size="20"
            color="#00ce9b"
            class="mt-[-6px] mr-6px"
            @click="playAudio(10, row)"
          />
        </n-space>
      </template>
      <template #feedbackCountVO="{ row }">
        <n-space>
          <div>无操作({{ row.feedbackCountVO?.nothingNum ?? 0 }})</div>
          <div>点赞({{ row.feedbackCountVO?.praiseNum ?? 0 }})</div>
          <div>差评({{ row.feedbackCountVO?.badNum ?? 0 }})</div>
        </n-space>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="primary" text @click="viewDetails(row)"
            >查看详情</n-button
          >
          <n-button type="error" text @click="clearCache(row)"
            >清除缓存</n-button
          >
        </n-space>
      </template>
    </g-table>

    <g-dialog
      title="详情"
      v-model:show="showPreview"
      v-bind="$attrs"
      :show-footer="false"
    >
      <div class="max-h-[450px] overflow-auto">
        <g-markdown
          class="w-full"
          height="450px"
          v-model="currentText"
          mode="stream"
        ></g-markdown>
      </div>
    </g-dialog>
    <g-dialog
      title="一键清除缓存"
      :formOptions="dateFormOptions"
      v-model:show="showClearDialog"
      @confirm="confirm"
    >
      <g-form :formOptions="dateFormOptions"> </g-form>
    </g-dialog>
    <!-- 播放音频 -->
    <Speaker v-model:show="showAudio" :width="width" ref="speakerRef" />
  </div>
</template>

<script setup lang="ts" name="AudioInfo">
import { getAudioList, clearCacheAll, clearCacheOne } from "@/api/audioMgt"
import { useSpeakerStore } from "@/stores/modules/speaker"
import Speaker from "@/views/resourceMgt/bookManage/enterResoure/components/SubtitleSpeaker.vue"
let speakerStore = useSpeakerStore()
let { getIdByType, isPlay } = $(storeToRefs(useSpeakerStore()))
const router = useRouter()
let showPreview = $ref(false)
let showClearDialog = $ref(false)
let showAudio = $ref(false)
let width = $ref(0)
let containerRef = $ref<any>(null)
const dateFormOptions = reactive<any>({
  loading: false,
  ref: null as any,
  items: {
    date: {
      type: "daterange",
      label: "时间范围",
      rule: {
        required: true,
      },
    },
  },
  data: {
    date: null,
  },
})
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  filter: true,
  items: {
    status: {
      type: "select",
      label: "运行状态",
      options: [
        { label: "全部", value: 0 },
        { label: "成功", value: 2 },
        { label: "异常", value: 3 },
      ],
    },
    // schoolId: {
    //   type: "select",
    //   label: "学科",
    //   options: [],
    // },

    keyword: {
      type: "text",
      label: "语音文本检索",
      showLabel: false,
    },
  },
  data: {
    status: 0,
    keyword: "",
  },
})
let currentText = $ref("")
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      prop: "text",
      label: "文本内容",
      slot: true,
      tooltip: false,
    },
    {
      prop: "audioContent",
      label: "音频内容",
      slot: true,
    },
    {
      prop: "statusText",
      label: "运行状态",
    },
    {
      prop: "feedbackCountVO",
      label: "用户意见",
      slot: true,
    },
    {
      prop: "requestTime",
      label: "请求时间",
    },
    {
      prop: "responseTime",
      label: "响应时间",
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    total: 0,
    page: 1,
    page_size: 10,
  },
})
onBeforeUnmount(() => {
  speakerStore.audio && speakerStore.reset()
})
/* 打开清除缓存对话框 */
function openClearDialog() {
  showClearDialog = true
}
/* 一键清除缓存 */
function confirm() {
  try {
    $g.confirm({
      type: "warning",
      title: "提示",
      content: "确定清除全部缓存",
    })
      .then(async () => {
        await clearCacheAll({
          beginTime: dateFormOptions.data.date[0],
          endTime: dateFormOptions.data.date[1],
        })
        $g.msg("操作成功！")
        dateFormOptions.loading = false
        showClearDialog = false
        await initData()
      })
      .catch(() => {
        dateFormOptions.loading = false
      })
  } catch (err) {
    console.log(err)
    dateFormOptions.loading = false
  }
}
/* 音频播放 */
async function playAudio(type, data) {
  width = containerRef.clientWidth - 50
  speakerStore.setUrlList(type, data)
}
/* 音频状态 */
function getPlaybackStatus(type, item) {
  if (item.questionTtsId == getIdByType) {
    return isPlay
  } else {
    return "pause"
  }
}
initData()
/* 获取列表 */
async function initData() {
  try {
    tableOptions.loading = true
    let res = await getAudioList({
      keyword: formOptions.data.keyword,
      status: formOptions.data.status,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    tableOptions.data = res.list.map((v) => {
      return {
        ...v,
        previewText:
          v.text.length > 30 ? v.text.substring(0, 30) + "..." : v.text,
      }
    })
    tableOptions.pageOptions.total = res.total
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
  }
}
/* 详情 */
function openDialog(row) {
  showPreview = true
  currentText = row.text
}
/* 查看详情 */
function viewDetails(row) {
  speakerStore.audio && speakerStore.reset()
  router.push({
    name: "AudioInfoDetail",
    query: {
      questionTtsId: row.questionTtsId,
    },
  })
}
/* 清除缓存 */
function clearCache(row) {
  $g.confirm({
    type: "warning",
    title: "提示",
    content: "是否清除该条缓存",
  })
    .then(async () => {
      await clearCacheOne({
        questionTtsId: row.questionTtsId,
      })
      $g.msg("操作成功！")
      await initData()
    })
    .catch(() => {})
}
function search() {
  tableOptions.pageOptions.page = 1
  initData()
}
</script>

<style lang="scss" scoped></style>
