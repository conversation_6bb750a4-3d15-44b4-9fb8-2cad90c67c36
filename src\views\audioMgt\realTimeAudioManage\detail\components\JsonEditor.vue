<template>
  <div class="relative h-fit">
    <Vue3JsonEditor
      v-model="jsonData"
      :show-btns="false"
      :expandedOnStart="true"
      @json-change="onJsonChange"
    />

    <div
      class="text-white absolute top-5px left-[200px] cursor-pointer reflex select-none text-[12px]"
      @click="copyData"
    >
      复制JSON
    </div>
  </div>
</template>

<script setup lang="ts">
import { Vue3JsonEditor } from "vue3-json-editor"
import ClipboardJS from "clipboard"

const jsonData = defineModel<any>("modelValue")
/* 数据更新 */
function onJsonChange(val) {
  jsonData.value = val
}
/* 复制数据 */
function copyData() {
  let clipboard = new ClipboardJS(".reflex", {
    text: () => {
      //返回需要复制的字符串
      return JSON.stringify(jsonData.value)
    },
  })
  clipboard.on("success", () => {
    $g.msg("复制成功")
    clipboard.destroy()
  })
  clipboard.on("error", () => {
    $g.msg("复制失败", "error")
    clipboard.destroy()
  })
}
</script>

<style lang="scss" scoped>
.reflex {
  padding: 2px 5px;
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  &:active {
    color: #ccc;
  }
}
</style>
