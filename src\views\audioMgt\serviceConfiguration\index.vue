<template>
  <div class="service-configuration-container-main">
    <div class="flex items-center gap-x-[20px]">
      <div class="w-[120px]">语音接口BASEURL</div>
      <n-input
        v-model:value="baseUrl"
        type="text"
        class="!w-[600px]"
        placeholder="请输入接口地址"
      />
      <n-button type="primary" @click="update(1)">修改</n-button>
      <n-button type="warning" @click="test">测试</n-button>
      <div v-if="showStatus">{{ status ? "正常" : "异常" }}</div>
    </div>
    <div class="flex items-center gap-x-[20px] mt-[16px]">
      <div class="w-[120px]">实时音频Url</div>
      <n-input
        v-model:value="audioUrl"
        type="text"
        class="!w-[600px]"
        placeholder="请输入接口地址"
      />
      <n-button type="primary" @click="update(2)">修改</n-button>
      <n-button type="warning" @click="testOfAudio">测试</n-button>
      <div v-if="showStatusOfAudioUrl">
        {{ statusOfAudio ? "正常" : "异常" }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getBaseUrl, updateBaseUrl, testBaseUrl } from "@/api/audioMgt"
let baseUrl = $ref("")
let status = $ref<any>("")
let showStatus = $ref(false)
let audioUrl = $ref<any>("") //实时音频Url
let statusOfAudio = $ref<any>("") //实时音频状态
let showStatusOfAudioUrl = $ref<boolean>(false) //实时音频状态是否展示
fetchBaseUrl()
fetchAudioUrl()
/* 获取baseURL */
async function fetchBaseUrl() {
  let res = await getBaseUrl({ type: 1 })
  baseUrl = res ?? ""
}
/* 修改baseURL 1语音接口  2实时音频接口*/
function update(type: Number = 1) {
  let content = type == 1 ? "是否修改语音接口baseUrL?" : "是否修改实时音频Url?"
  $g.confirm({
    content,
  })
    .then(async () => {
      await updateBaseUrl({
        baseUrl: type == 1 ? baseUrl : audioUrl,
        type,
      })
      $g.msg("修改成功")
      await fetchBaseUrl()
    })
    .catch(() => {})
}
/* 测试接口 */
async function test() {
  status = ""
  showStatus = false
  let res = await testBaseUrl({
    baseUrl,
    type: 1,
  })
  showStatus = true
  status = res
}
//获取实时音频Url
async function fetchAudioUrl() {
  let res = await getBaseUrl({ type: 2 })
  audioUrl = res ?? ""
}
//测试实时音频url
async function testOfAudio() {
  statusOfAudio = ""
  showStatusOfAudioUrl = false
  let res = await testBaseUrl({
    baseUrl: audioUrl,
    type: 2,
  })
  showStatusOfAudioUrl = true
  statusOfAudio = res
}
</script>

<style lang="scss" scoped></style>
