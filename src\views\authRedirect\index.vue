<template><div></div></template>
<script lang="ts" setup>
import {
  fetchExamXKWOAuth,
  bindExamXKWApi,
  fetchExamXKWBind,
} from "@/api/common"
const route = useRoute()
let xkwOpenId = $ref<any>(null)
async function bindExamXKW() {
  const data: any = await bindExamXKWApi(
    {
      code: route.query?.code,
      state: route.query?.state,
    },
    { Authorization: route.query?.qmdrKey },
  )
  xkwOpenId = data?.openId
  if (data?.openId) {
    $g.msg("绑定成功", "success")
    getXKWBind()
    // location.replace(`${baseOrigin}/#/third/authRedirect`)
  }
}
async function goToXKWAuth() {
  const res = await fetchExamXKWOAuth(
    {
      redirectUri: `${location.protocol}//zujuan.qimingdaren.com/#/third/authRedirect`,
      xkwService: "COMPOSITION",
    },
    { Authorization: route.query?.qmdrKey },
  )
  if (res) {
    location.replace(res)
  }
}
function goToXKW() {
  const params = new URLSearchParams({
    _openid: xkwOpenId,
    _m: `${location.protocol}//zujuan.qimingdaren.com/#/third/paperComplete`,
  })
  const url = `${
    location.protocol
  }//zujuan.qimingdaren.com/#/teacherIframe?${params.toString()}`
  location.replace(url)
}
async function getXKWBind() {
  const data: any = await fetchExamXKWBind({
    Authorization: route.query?.qmdrKey,
  })
  xkwOpenId = data?.openId
  if (!xkwOpenId) {
    goToXKWAuth()
  } else {
    goToXKW()
  }
}
onMounted(() => {
  const { code, state } = route.query
  if (code && state) {
    bindExamXKW()
    return
  }
  getXKWBind()
})
</script>
