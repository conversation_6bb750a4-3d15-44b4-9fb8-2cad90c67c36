<template>
  <div class="auto-correct-detail-container-main" v-loading="loading">
    <!-- 状态 -->
    <div class="flex items-center justify-between px-100px">
      <div>运行状态：{{ statusMap[status] }}</div>
      <div>书写可信度：{{ reliability }}</div>
      <div>
        <n-button type="primary" @click="requestTest" :loading="testLoading"
          >测试请求</n-button
        >
      </div>
    </div>
    <n-divider />
    <!-- 数据展示 -->
    <div class="grid grid-cols-3 gap-x-[50px]" v-loading="testLoading">
      <div>
        <el-radio-group v-model="radio">
          <el-radio value="1" size="large">作答图片</el-radio>
          <el-radio value="2" size="large">批改图片</el-radio>
        </el-radio-group>
        <template v-if="radio === '1'">
          <ImgBox
            v-if="$g.tool.isTrue(correctData)"
            class="h-500px border"
            :image-url="correctData.newImageUrl"
            :boxes="correctData.ocrMsg"
          ></ImgBox>
          <g-empty v-else></g-empty>
        </template>
        <template v-if="radio === '2'">
          <ImgBox
            v-if="$g.tool.isTrue(correctData)"
            class="h-500px border"
            :image-url="correctData.imageUrl"
          ></ImgBox>
          <g-empty v-else></g-empty>
        </template>
      </div>
      <div>
        <div class="text-[20px] my-10px">发送请求</div>
        <JsonEditor v-model="requestData" />
      </div>
      <div>
        <div class="text-[20px] my-10px">接收请求</div>
        <JsonEditor
          v-if="$g.tool.isTrue(responseData)"
          v-model="responseData"
        />
        <g-empty v-else description="暂无返回JSON数据"></g-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="AutoCorrectDetail">
import JsonEditor from "@/views/audioMgt/audioInfo/detail/components/JsonEditor.vue"
import { getCorrectDetailApi, testCorrectApi } from "@/api/autoCorrect"
import { Vue3JsonEditor } from "vue3-json-editor"
import ImgBox from "@/views/resourceMgt/bookManage/autoCorrecting/components/ImgBox.vue"
const route = useRoute()
let loading = $ref(true)
let status = $ref<any>(0) //接口状态
let reliability = $ref(0) //可信度
let requestData = $ref({})
let responseData = $ref({})
let correctData = $ref<any>({}) //处理返回数据
let testLoading = $ref(false)
let statusMap = {
  2: "正常",
  3: "异常",
}
let radio = $ref("1")
initData()
/* 获取详情 */
async function initData() {
  try {
    let res = await getCorrectDetailApi({
      questionAutoCorrectId: route.query.questionAutoCorrectId,
    })
    status = res.status
    reliability = res.reliability
    requestData = res.reqJson ? JSON.parse(res.reqJson) : {}
    if ($g.tool.isTrue(res.respJson)) {
      let data = JSON.parse(res.respJson)
      responseData = $g._.cloneDeep(data)
      data.data.ocrMsg = data.data.ocr_msg.map((v) => {
        return {
          ...v,
          coord: {
            ...v.coord,
            topLeftX: v.coord.top_left_x,
            topLeftY: v.coord.top_left_y,
          },
        }
      })
      correctData = data.data
      correctData.imageUrl = res.correctImg
      correctData.newImageUrl = res.newImg
    }
    loading = false
  } catch (err) {
    loading = false
    console.log(err)
  }
}
/* 测试请求 */
async function requestTest() {
  try {
    $g.msg("测试中，请等待...")
    testLoading = true
    let res = await testCorrectApi({
      ...requestData,
    })
    testLoading = false
    $g.msg("测试完成")
    status = 2
    reliability = res.autoCorrectVO.reliability ?? 0
    correctData = res.autoCorrectVO ?? {}
    responseData = res.respJson ? JSON.parse(res.respJson) : {}
  } catch (err) {
    testLoading = false
    console.log(err)
  }
}
/* JSON 数据改变 */
function onJsonChange(val) {
  requestData = val
}
</script>

<style lang="scss" scoped>
:deep() {
  .jsoneditor-vue {
    height: calc(100vh - 290px);
  }
  .jsoneditor-contextmenu {
    display: none;
  }
}
</style>
