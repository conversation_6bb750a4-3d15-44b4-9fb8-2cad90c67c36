<template>
  <div>
    <!-- 大题部分 -->
    <div class="my-10px flex">
      <div class="flex-1 flex items-start">
        <g-mathjax :text="mainQuestion?.questionTitle" />
      </div>
      <!-- 自定义插槽 -->
      <div class="flex-shrink-0 ml-6px"><slot name="title-right"> </slot></div>
    </div>
    <!-- 子题部分 -->
    <div v-for="item in mainQuestion?.subQuestions" :key="item.subQuestionId">
      <!-- 子题题目 -->
      <g-mathjax :text="item?.subQuestionTitle" />
      <!-- 选择题 -->
      <template v-if="[1, 2, 3].includes(item?.subQuestionType)">
        <div v-for="option in item?.options" :key="option.name" class="flex">
          <div class="w-20px mr-6px">{{ option.name }}.</div>
          <div>
            <g-mathjax :text="option.title"></g-mathjax>
          </div>
        </div>
      </template>
      <!-- 解析 -->
      <div class="flex items-start">
        <div class="w-80px flex-shrink-0 text-primary">【详解】</div>
        <template
          v-for="parse in item.subQuestionParseList"
          :key="parse.subQuestionParseId"
        >
          <g-mathjax :text="parse.content" />
        </template>
      </div>
      <!-- 答案 -->
      <div class="flex items-start">
        <div class="w-80px flex-shrink-0 text-primary">【答案】</div>
        <g-mathjax :text="item.subQuestionAnswer" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
const props = defineProps({
  question: {
    type: Object as PropType<any>, //试题信息
    required: true,
  },
})
const mainQuestion = $computed(() => {
  let question = $g._.cloneDeep(props.question)
  question?.subQuestions?.forEach((v) => {
    if ([1, 2].includes(v.subQuestionType)) {
      v.options = Object.keys(v)
        .filter(
          (key) =>
            key.includes("option") &&
            v[key] &&
            key !== "optionNumbers" &&
            key != "options" &&
            key != "optionsAnswer",
        )
        .map((realKey) => {
          return {
            name: realKey.charAt(realKey.length - 1).toLocaleUpperCase(),
            title: v[realKey],
          }
        })
    } else if (v.subQuestionType == 3) {
      v.options = [
        {
          id: 1,
          name: "√",
          title: null,
        },
        {
          id: 2,
          name: "×",
          title: null,
        },
      ]
    }
  })
  return question
})
onMounted(() => {
  $g.tool.renderMathjax()
})
</script>

<style lang="scss" scoped></style>
