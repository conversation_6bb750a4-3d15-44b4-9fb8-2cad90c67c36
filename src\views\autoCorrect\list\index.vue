<template>
  <div class="auto-correct-list-container-main">
    <!-- 搜索 -->
    <g-form @search="initData" @reset="initData" :formOptions="formOptions">
    </g-form>
    <!-- 表格 -->
    <g-table :tableOptions="tableOptions" @changePage="initData">
      <template #answerPic="{ row }">
        <div>
          <n-image
            v-if="row.answerImg?.includes('https')"
            width="100"
            :src="row.answerImg"
          />
          <div v-else>-</div>
        </div>
      </template>
      <template #correctPic="{ row }">
        <div>
          <n-image
            v-if="row.correctImg?.includes('https')"
            width="100"
            :src="row.correctImg"
          />
          <div v-else>-</div>
        </div>
      </template>
      <template #questionInfo="{ row }">
        <div class="flex items-center gap-x-[10px]">
          <g-mathjax
            :text="
              row.questionInfo.questionTitle ??
              row.questionInfo.subQuestions[0].subQuestionTitle
            "
            class="line-3"
          />
          <n-button type="primary" text @click="openDialog(row)">详情</n-button>
        </div>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="primary" text @click="toDetail(row)"
            >查看详情</n-button
          >
        </n-space>
      </template>
    </g-table>
    <!-- 试题详情弹窗 -->
    <QuestionDialog :data="currentQuestionInfo" v-model:show="showDialog" />
  </div>
</template>

<script setup lang="ts" name="AutoCorrectList">
import {
  getStatusSelect,
  getSchoolList,
  getUserOpinionSelect,
  getListApi,
} from "@/api/autoCorrect"
import QuestionDialog from "./components/QuestionDialog.vue"
let currentQuestionInfo = $ref<any>(null)
let showDialog = $ref(false)
/* 筛选条件 */
const formOptions = reactive<any>({
  ref: null as any,
  filter: true,
  items: {
    status: {
      type: "select",
      label: "状态",
      options: [],
    },
    schoolId: {
      type: "select",
      label: "学校",
      options: [],
    },
    feedback: {
      type: "select",
      label: "用户意见",
      options: [],
    },
    keyword: {
      type: "text",
      label: "姓名/启鸣号/试题ID",
      showLabel: false,
      width: "400px",
      placeholder: "可输入姓名/启鸣号/试题ID 进行搜索",
    },
  },
  data: {
    status: null,
    schoolId: null,
    feedback: null,
    keyword: null,
  },
})
let feedbackMap = {
  1: "无",
  2: "点赞",
  3: "差评",
}
let statusMap = {
  2: "正常",
  3: "异常",
}
let correctModeMap = {
  1: "普通批改",
  2: "精细批改",
}
/* 表格数据 */
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      prop: "idNum",
      label: "启鸣号",
    },
    {
      prop: "userName",
      label: "姓名",
    },
    {
      prop: "schoolName",
      label: "学校",
    },
    {
      prop: "questionId",
      label: "试题ID",
    },
    {
      prop: "questionInfo",
      label: "试题内容",
      slot: true,
      width: 300,
      tooltip: false,
    },
    {
      prop: "answerPic",
      label: "作答图片",
      slot: true,
    },
    {
      prop: "correctMode",
      label: "批改方式",
      formatter: (row) => {
        return correctModeMap[row.correctMode] ?? "-"
      },
    },
    {
      prop: "correctPic",
      label: "批改图片",
      slot: true,
    },
    {
      prop: "status",
      label: "运行状态",
      formatter: (row) => {
        return statusMap[row.status] ?? "-"
      },
    },
    {
      prop: "feedback",
      label: "用户意见",
      formatter: (row) => {
        return feedbackMap[row.feedback]
      },
    },
    {
      prop: "reqTime",
      label: "请求时间",
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})

onBeforeMount(() => {
  getCondition()
  initData()
})
const router = useRouter()
/* 跳转详情 */
function toDetail(row) {
  router.push({
    name: "AutoCorrectDetail",
    query: {
      questionAutoCorrectId: row.questionAutoCorrectId,
    },
  })
}
/* 打开试题详情弹窗 */
function openDialog(row) {
  currentQuestionInfo = row.questionInfo
  showDialog = true
}
/* 获取条件 */
async function getCondition() {
  try {
    let res = await Promise.all([
      getStatusSelect(),
      getSchoolList(),
      getUserOpinionSelect(),
    ])
    formOptions.items.status.options =
      res[0]?.map((v) => {
        return {
          label: v.title,
          value: v.id,
        }
      }) ?? []
    formOptions.items.schoolId.options =
      res[1]?.map((v) => {
        return {
          label: v.schoolName,
          value: v.schoolId,
        }
      }) ?? []
    formOptions.items.feedback.options =
      res[2]?.map((v) => {
        return {
          label: v.title,
          value: v.id,
        }
      }) ?? []
  } catch (err) {
    console.log(err)
  }
}
/* 初始化数据列表 */
async function initData() {
  try {
    tableOptions.loading = true
    let res = await getListApi({
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
      ...formOptions.data,
    })
    tableOptions.data = res.list.map((v) => {
      return {
        ...v,
        resData: v.respJson ? JSON.parse(v.respJson).data : null,
      }
    })
    tableOptions.pageOptions.total = res.total
    tableOptions.loading = false
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  } catch (err) {
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
    tableOptions.loading = false
    console.log(err)
  }
}
</script>

<style lang="scss" scoped></style>
