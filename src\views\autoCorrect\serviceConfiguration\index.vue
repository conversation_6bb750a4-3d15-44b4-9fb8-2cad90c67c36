<template>
  <div class="service-configuration-container-main">
    <div class="flex items-center gap-x-[20px]">
      <div>自动批改接口BASEURL</div>
      <n-input
        v-model:value="baseUrl"
        type="text"
        class="!w-[600px]"
        placeholder="请输入接口地址"
      />
      <n-button type="primary" @click="update">修改</n-button>
      <n-button type="warning" @click="test" :loading="testLoading"
        >测试</n-button
      >
      <div v-if="showStatus">{{ status ? "正常" : "异常" }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getBaseUrl, updateBaseUrl, testBaseUrl } from "@/api/autoCorrect"
let baseUrl = $ref("")
let status = $ref<any>("")
let showStatus = $ref(false)
let testLoading = $ref(false)
fetchBaseUrl()
/* 获取baseURL */
async function fetchBaseUrl() {
  let res = await getBaseUrl()
  baseUrl = res ?? ""
}
/* 修改baseURL */
function update() {
  $g.confirm({
    content: "是否修改自动批改baseUrL?",
  })
    .then(async () => {
      await updateBaseUrl({
        baseUrl,
      })
      $g.msg("修改成功")
      await fetchBaseUrl()
    })
    .catch(() => {})
}
/* 测试接口 */
async function test() {
  testLoading = true
  status = ""
  showStatus = false
  let res = await testBaseUrl({
    baseUrl,
  })
  testLoading = false
  showStatus = true
  status = res
}
</script>

<style lang="scss" scoped></style>
