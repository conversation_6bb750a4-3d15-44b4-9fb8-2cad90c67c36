<template>
  <div class="ai-report-detail-container-main">
    <div class="h-full">
      <div class="grid grid-cols-1 gap-y-[10px]">
        <div>
          学校年级班级：{{ chatInfo.schoolName }}-{{ chatInfo.sysGradeName }}-{{
            chatInfo.className
          }}
        </div>
        <div>学生姓名：{{ chatInfo.studentName }}</div>
        <div>举报类型：{{ chatInfo.reportTypeName }}</div>
        <div>题目ID：{{ chatInfo.questionId }}</div>
        <div>举报详情：{{ chatInfo.reportReason || "-" }}</div>
        <div>举报时间：{{ chatInfo.createTime }}</div>
        <div>对话内容：</div>
      </div>
      <div
        class="h-[calc(100vh-325px)] overflow-auto w-full bg-[#f5f5f5] py-10px"
      >
        <div v-for="(item, index) in chatRecord" :key="index">
          <div v-if="chatRecord.length > 1">解法{{ index + 1 }}</div>
          <template v-if="item.list.length > 0">
            <div v-for="val in item.list" :key="val.id">
              <div class="flex" :class="{ 'justify-end': val.chatType == 1 }">
                <!-- AI老师 -->
                <div v-if="val.chatType == 2" class="max-w-[60%]">
                  <div>{{ teacherMap[chatInfo.fastGptChatRoomType] }}:</div>
                  <div class="bg-[#fff] rounded-md ml-50px px-10px pt-10px">
                    <!-- 深度思考内容 -->
                    <el-collapse
                      v-model="val.activeName"
                      v-if="val.reasoningContent"
                      accordion
                      class="!border-none"
                    >
                      <el-collapse-item
                        :title="thinkMap[val.thinkingMode]"
                        name="1"
                      >
                        <div class="px-10px bg-[#f5f5f5]">
                          <g-markdown
                            :text="val.reasoningContent"
                            mode="preview"
                          />
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                    <g-markdown :text="val.message" mode="preview" />
                  </div>
                </div>
                <!-- 学生 -->
                <div v-if="val.chatType == 1" class="max-w-[60%]">
                  <div class="text-right">{{ chatInfo.studentName }}:</div>
                  <div class="bg-[#fff] rounded-md mr-40px px-10px pt-10px">
                    <g-markdown
                      :text="val.message || '我已经准备好，请老师开始进行讲解'"
                      mode="preview"
                    />
                  </div>
                </div>
              </div>
            </div>
          </template>
          <g-empty v-else></g-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="AiReportDetail">
import { getReportDetail, handleReport } from "@/api/clientMgt"
const route = useRoute()
let chatInfo = $ref<any>({})
let chatRecord = $ref<any>([])
const teacherMap = {
  1: "小鸣老师",
  2: "小启老师",
}
const thinkMap = {
  1: "快速思考",
  2: "深度思考",
  3: "自动选择",
}

/* 获取举报详情 */
async function getReportDetailData() {
  let res = await getReportDetail({
    aiChatReportId: route.query.aiChatReportId,
  })
  chatInfo = res
}
/* 获取对话记录 */
async function getChatRecordData() {
  let res = await handleReport({
    aiChatReportId: route.query.aiChatReportId,
  })
  // 每段对话添加activeName
  chatRecord = res.map((item: any) => {
    return {
      ...item,
      list: item.list.map((val: any) => {
        return {
          ...val,
          activeName: "1",
        }
      }),
    }
  })
}
onBeforeMount(() => {
  getReportDetailData()
  getChatRecordData()
})
</script>

<style lang="scss" scoped></style>
