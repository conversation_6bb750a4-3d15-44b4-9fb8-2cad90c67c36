<template>
  <div class="ai-report-container-main">
    <g-form
      :formOptions="formOptions"
      @search="initTableData"
      @reset="initTableData"
      :tableOptions="tableOptions"
    >
    </g-form>
    <g-table
      :tableOptions="tableOptions"
      @changePage="initTableData"
      :border="false"
    >
      <template #reportReason="{ row }">
        <g-tooltip
          :content="row.reportReason || '-'"
          :ref-name="row.reportReason + 'refName'"
        ></g-tooltip>
      </template>
      <template #questionId="{ row }">
        <g-tooltip
          :content="row.questionId || '-'"
          :ref-name="row.questionId"
        ></g-tooltip>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="primary" text @click="handleDetail(row)">
            查看
          </n-button>
        </n-space>
      </template>
    </g-table>
  </div>
  ·
</template>

<script setup lang="ts" name="AiReport">
import { getReportList } from "@/api/clientMgt"
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  filter: true,
  items: {
    date: {
      type: "daterange",
      label: "时间范围",
    },
  },
  data: {
    date: null,
  },
})
const tableOptions = reactive({
  ref: null as any,
  loading: false,
  column: [
    {
      label: "序号",
      type: "index",
    },
    {
      label: "学校-年级-班级",
      prop: "schoolName",
      formatter: (row) => {
        return `${row.schoolName}-${row.sysGradeName}-${row.className}`
      },
    },
    {
      label: "学生姓名",
      prop: "studentName",
    },
    {
      label: "启鸣号",
      prop: "idNum",
    },
    {
      label: "题目ID",
      prop: "questionId",
      slot: true,
    },
    {
      label: "举报类型",
      prop: "reportTypeName",
    },
    {
      label: "举报详情",
      prop: "reportReason",
      slot: true,
    },
    {
      label: "举报时间",
      prop: "createTime",
    },
    {
      label: "操作",
      prop: "cz",
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page_size: 10,
    page: 1,
    total: 0,
  },
})

const router = useRouter()
function handleDetail(row) {
  router.push({
    name: "AiReportDetail",
    query: {
      aiChatReportId: row.aiChatReportId,
    },
  })
}
async function initTableData() {
  try {
    tableOptions.loading = true
    let res = await getReportList({
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
      beginDate: formOptions.data.date?.[0],
      endDate: formOptions.data.date?.[1],
    })
    tableOptions.data = res.list
    tableOptions.pageOptions.total = res.total
    tableOptions.loading = false
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
  }
}
onBeforeMount(() => {
  initTableData()
})
</script>

<style lang="scss" scoped>
:deep() {
  .mathjax {
    font-family: "HarmonyOS", "Arial", "sans-serif", "PingFang SC",
      "Hiragino Sans GB", "Helvetica", "Droid Sans", "思源黑体 CN",
      "Microsoft YaHei";
  }
}
</style>
