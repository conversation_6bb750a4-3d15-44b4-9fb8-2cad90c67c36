<template>
  <g-dialog
    title="添加固件"
    :formOptions="formOptions"
    v-model:show="showDialog"
    @confirm="confirm"
    width="800"
  >
    <g-form :formOptions="formOptions">
      <!-- 上传,upload-key需要正确设置 -->
      <template #apk>
        <g-upload
          v-model:fileList="formOptions.data.apk"
          type="drag"
          :max="1"
          accept=".apk"
          tips="请上传apk文件,且大小不能超过200MB"
        ></g-upload>
      </template>
    </g-form>
  </g-dialog>
</template>

<script setup lang="ts">
import { addClientVersion } from "@/api/clientMgt"
import type { PropType } from "vue"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  clientType: {
    type: [Number, String] as PropType<any>,
    required: true,
  },
})
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
let showAndroid = computed(() => props.clientType == 2 || props.clientType == 1)
const formOptions = reactive<any>({
  loading: false,
  ref: null as any,
  labelWidth: "120px",
  items: {
    versionName: {
      type: "text",
      label: "版本名称",
      maxlength: 20,
      rule: true,
      show: true,
    },
    versionNum: {
      type: "number",
      label: "版本号",
      rule: true,
      min: 0,
    },

    isMust: {
      type: "radio",
      label: "强制更新",
      rule: true,
      options: [
        { label: "否", value: 1 },
        { label: "是", value: 2 },
      ],
    },
    apk: {
      type: "upload",
      label: "上传安装包",
      rule: true,
      slot: true,
      show: showAndroid,
    },
    downloadUrl: {
      type: "text",
      label: "IOS下载地址",
      rule: {
        validator: (rule, value, callback) => {
          if (!value) {
            callback(new Error("请输入正确的下载地址"))
          }
          if (
            value &&
            !/https?:\/\/(www\.)?[a-zA-Z0-9-]+(\.[a-zA-Z]{2,})+\/?/.test(value)
          ) {
            callback(new Error("请输入正确的下载地址"))
          }
        },
        trigger: "blur",
        type: "string",
        required: true,
      },
      show: computed(() => !showAndroid.value),
    },
    updateLabel: {
      type: "textarea",
      label: "升级内容",
      showCount: true,
      maxlength: 500,
      rule: true,
    },
  },
  data: {
    versionName: null,
    versionNum: null,
    apk: [],
    updateLabel: null,
    os: 1,
    downloadUrl: "",
    isMust: 1,
  },
})

/* 添加 */
async function confirm() {
  try {
    let downloadUrl =
      props.clientType == 3
        ? formOptions.data.downloadUrl
        : formOptions.data.apk[0].fullUrl
    let data = {
      isMust: formOptions.data.isMust,
      clientType: props.clientType,
      updateLabel: formOptions.data.updateLabel,
      versionName: formOptions.data.versionName,
      versionNum: formOptions.data.versionNum,
      downloadUrl,
    }
    await addClientVersion(data)
    formOptions.loading = false
    $g.msg("添加成功")
    emit("refresh")
    emit("update:show", false)
  } catch (err) {
    console.log(err)
    formOptions.loading = false
  }
}
</script>

<style lang="scss" scoped></style>
