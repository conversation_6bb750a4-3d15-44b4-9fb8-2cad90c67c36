<template>
  <div class="hardware-container-main">
    <n-radio-group v-model:value="clientType" class="mb-15px">
      <n-radio-button v-for="end in clientList" :key="end.id" :value="end.id">
        {{ end.title }}
      </n-radio-button>
    </n-radio-group>
    <g-table
      :tableOptions="tableOptions"
      @change-page="getClientVersionListApi"
    >
      <template #header-left>
        <n-button type="primary" @click="showDialog = true">添加固件</n-button>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="primary" text @click="downLoad(row)">下载</n-button>
          <n-button type="error" text @click="deleteVersion(row)"
            >删除</n-button
          >
        </n-space>
      </template>
    </g-table>
    <!-- 添加固件 -->
    <Add
      v-model:show="showDialog"
      :clientType="clientType"
      @refresh="getClientVersionListApi"
    />
  </div>
</template>

<script setup lang="ts">
import { getClientVersionList, deleteClientVersion } from "@/api/clientMgt"
import Add from "./components/Add.vue"
// 顶部终端分类列表
let clientList = $ref<{ id: number; title: string }[]>([
  { id: 1, title: "墨水屏" },
  { id: 2, title: "金字塔APP-安卓" },
  { id: 3, title: "金字塔APP-IOS" },
])
let isMustMap = {
  1: "否",
  2: "是",
}
let clientType = $ref<number>(1)
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  pageOptions: {
    page: 1,
    page_size: 20,
    total: 0,
  },
  column: [
    { prop: "index", label: "序号" },
    { prop: "versionName", label: "版本名称" },
    { prop: "versionNum", label: "版本号" },
    { prop: "updateLabel", label: "更新文案" },
    {
      prop: "isMust",
      label: "强制更新",
      formatter: (row) => {
        return isMustMap[row.isMust]
      },
    },
    { prop: "createTime", label: "添加时间" },
    { prop: "cz", label: "操作", slot: true, width: "170" },
  ],
  data: [],
})
let showDialog = $ref(false)
/* 删除版本 */
function deleteVersion(row) {
  console.log(row)
  $g.confirm({
    content: "是否确认删除",
  })
    .then(async () => {
      await deleteClientVersion({ clientVersionId: row.clientVersionId })
      $g.msg("删除成功", "success")
      await getClientVersionListApi()
    })
    .catch(() => {})
}
/* 获取客户端版本列表 */
async function getClientVersionListApi() {
  try {
    tableOptions.loading = true
    let res = await getClientVersionList({
      clientType,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    tableOptions.data = res.list.map((v, i) => {
      return {
        ...v,
        index:
          (tableOptions.pageOptions.page - 1) *
            tableOptions.pageOptions.page_size +
          i +
          1,
      }
    })
    tableOptions.pageOptions.total = res.total
  } catch (err) {
    console.log(err)
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
    tableOptions.loading = false
  }
}
/* 下载 */
function downLoad(row) {
  $g.tool.downloadFile(row.downloadUrl, row.versionName)
}
watch(
  () => clientType,
  async () => {
    tableOptions.pageOptions.page = 1
    tableOptions.data = []
    getClientVersionListApi()
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped></style>
