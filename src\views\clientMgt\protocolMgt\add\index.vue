<template>
  <div class="protocol-add-container-main">
    <g-form :formOptions="formOptions"> </g-form>
    <div class="flex justify-center gap-10px">
      <n-button @click="handleBtn('cancel')">取消</n-button>
      <n-button
        type="primary"
        @click="handleBtn('submit')"
        :loading="btnLoading"
        >提交</n-button
      >
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  getProtocolType,
  addProtocol,
  editProtocol,
  getProtocolDetail,
  getApplyList,
} from "@/api/clientMgt"

const route = useRoute()
const router = useRouter()
let btnLoading = $ref(false)

onMounted(() => {
  getApplyListApi()
})

const formOptions = $ref<any>({
  ref: "uploadVideo",
  filter: false,
  loading: false,
  items: {
    applyId: {
      type: "select",
      label: "应用名称",
      width: "300px",
      options: [],
      labelField: "title",
      valueField: "id",
      rule: true,
    },
    protocolType: {
      type: "select",
      label: "类型",
      width: "300px",
      options: [],
      labelField: "title",
      valueField: "id",
      rule: true,
    },
    title: {
      type: "text",
      label: "标题",
      placeholder: "请输入30个字符以内的标题",
      width: "300px",
      maxlength: 30,
      rule: {
        validator: (rule, value, callback) => {
          if (!value) {
            callback(new Error("请输入标题"))
          } else {
            callback()
          }
        },
        trigger: "blur",
        type: "string",
        required: true,
      },
    },
    state: {
      type: "switch",
      label: "启用状态",
      width: "300px",
      rule: {
        type: "boolean",
        required: true,
      },
    },
    content: {
      type: "editor",
      label: "正文",
      rule: true,
      initProps: {
        max: 5000,
      },
    },
  },
  data: {
    applyId: null,
    protocolType: null,
    title: "",
    state: true,
    content: "",
  },
})

onMounted(() => {
  getTypesApi()
  getDetailApi()
})

/**
 * 获取协议接口
 */
async function getTypesApi() {
  const res = await getProtocolType()
  formOptions.items.protocolType.options = res || []
}

/**
 * 编辑获取详情
 */
async function getDetailApi() {
  if (!route.query.id) return
  const { protocolType, title, versionNum, applyId, state, content } =
    await getProtocolDetail({ clientProtocolId: route.query.id })
  formOptions.data = {
    protocolType,
    title,
    // versionNum,
    applyId,
    state: state == 2,
    content,
  }
}

function handleBtn(type: string) {
  if (type === "cancel") {
    router.back()
  } else if (type === "submit") {
    submit()
  }
}

async function submit() {
  formOptions.ref?.validate(async (errors) => {
    if (!errors) {
      btnLoading = true
      const URL = route.query.id ? editProtocol : addProtocol
      const params = {
        ...formOptions.data,
        state: formOptions.data.state ? 2 : 1,
        clientProtocolId: route.query.id,
      }
      try {
        await URL(params)
        $g.msg("操作成功", "success")
        $g.bus.emit("refreshList")
        router.back()
      } catch (error) {
        console.log(error)
      } finally {
        btnLoading = false
      }
    }
  })
}

async function getApplyListApi() {
  try {
    let res = await getApplyList()
    formOptions.items.applyId.options = res ?? []
    if (
      !route.query?.id &&
      formOptions.items.applyId.options?.length &&
      $g._.find(formOptions.items.applyId.options, (it) => it.id == 1)
    ) {
      formOptions.data.applyId = 1
    }
  } catch (error) {
    console.log(error)
  }
}
</script>
<style lang="scss" scoped></style>
