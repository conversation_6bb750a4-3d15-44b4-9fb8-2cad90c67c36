<template>
  <div class="protocol-container-main">
    <g-table :tableOptions="tableOptions" @change-page="getProtocolListApi">
      <template #header-left>
        <n-button type="primary" @click="handleBtn('add')">添加协议</n-button>
      </template>
      <template #state="{ row }">
        <n-switch :value="row.state == 2" @update:value="updateSwitch(row)" />
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="primary" text @click="handleBtn('edit', row)"
            >编辑</n-button
          >
          <n-button type="error" text @click="handleBtn('del', row)"
            >删除</n-button
          >
        </n-space>
      </template>
    </g-table>
  </div>
</template>

<script setup lang="ts" name="ProtocolMgt">
import {
  getProtocolList,
  deleteProtocol,
  updateProtocolState,
} from "@/api/clientMgt"
import { useRouterStore } from "@/stores/modules/routes"

const routerStore = useRouterStore()
const router = useRouter()
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  pageOptions: {
    page: 1,
    page_size: 20,
    total: 0,
  },
  column: [
    { prop: "applyName", label: "应用名称" },
    {
      prop: "protocolTypeName",
      label: "类型",
    },
    { prop: "title", label: "标题" },
    // { prop: "versionNum", label: "版本号" },
    { prop: "createTime", label: "发布时间" },
    {
      prop: "state",
      label: "启用状态",
      slot: true,
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
      width: "170",
    },
  ],
  data: [],
})

onBeforeMount(() => {
  getProtocolListApi()
  $g.bus.on("refreshList", () => {
    getProtocolListApi()
  })
})

/**
 * 获取协议列表
 */
async function getProtocolListApi() {
  try {
    tableOptions.loading = true
    let { list = [], total } = await getProtocolList({
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    tableOptions.data = list
    tableOptions.pageOptions.total = total
  } catch (err) {
    console.log(err)
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
    tableOptions.loading = false
  }
}

/**
 * 更新状态
 */
function updateSwitch(row) {
  updateProtocolState({
    clientProtocolId: row.clientProtocolId,
  }).finally(() => {
    getProtocolListApi()
  })
}

/**
 * 新增、编辑、删除
 */
function handleBtn(type, row?) {
  switch (type) {
    case "del":
      $g.confirm({
        content: `确认删除“${row.title}”吗？删除后无法查看`,
      })
        .then(async () => {
          await deleteProtocol({ clientProtocolId: row.clientProtocolId })
          $g.msg("删除成功", "success")
          await getProtocolListApi()
        })
        .catch(() => {})
      break
    case "add":
    case "edit":
      changeMetaTitle(type)
      router.push({
        name: "ProtocolMgtAdd",
        query: {
          type,
          id: row?.clientProtocolId,
        },
      })
      break
  }
}

/**
 * 修改面包屑title
 */
function changeMetaTitle(type) {
  routerStore.changeMenuMeta({
    name: "ProtocolMgtAdd",
    meta: {
      title: type == "add" ? "新增协议" : "编辑协议",
    },
  })
}
</script>

<style lang="scss" scoped></style>
