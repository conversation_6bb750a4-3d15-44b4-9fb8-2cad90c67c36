<template>
  <div>
    <g-dialog
      title="添加分发对象选择"
      v-model:show="showDialog"
      width="500"
      @confirm="confirm"
    >
      <el-cascader
        :teleported="false"
        :props="studentProps"
        v-model="distributeList"
        clearable
        placeholder="请选择"
        collapse-tags
        collapse-tags-tooltip
        :max-collapse-tags="3"
        style="width: 100%"
        @change="dataChange"
      />
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  getSchoolList,
  getSysGradeList,
  getClassList,
  getStudentList,
} from "@/api/clientMgt"

let props = defineProps<{
  show: boolean
  selectList: any[]
}>()

const emit = defineEmits(["update:show", "dataChange", "confirm"])

let showDialog = useVModel(props, "show", emit)
let distributeList = $ref<any>([])

const studentProps = {
  lazy: true,
  multiple: true,
  checkStrictly: true,
  async lazyLoad(node, resolve) {
    const { level, data } = node

    if (level == 4) {
      resolve(null)
      return
    }
    let res: any = []
    if (level == 0) {
      let disabledList = props?.selectList.filter((it) => it.length == 1)
      res = await getSchoolList()
      res = res
        ?.filter((item) => !disabledList.some((it) => it[0] == item.schoolId))
        .map((item) => {
          return {
            ...item,
            label: item.schoolName,
            value: item.schoolId,
            schoolId: item.schoolId,
            level: 1,
            filterable: true,
            children: [],
          }
        })
    } else if (level == 1) {
      let disabledList = props?.selectList.filter((it) => it.length == 2)
      res = await getSysGradeList({ schoolId: data.schoolId })
      res = res
        // 过滤掉已选项
        ?.filter(
          (item) =>
            !disabledList.some(
              (it) => it[0] == data.schoolId && it[1] == item.sysGradeId,
            ),
        )
        .map((item) => {
          return {
            ...item,
            schoolId: data.schoolId,
            level: 2,
            label: item?.sysGradeName,
            value: item?.sysGradeId,
            // 不再设置disabled
          }
        })
    } else if (level == 2) {
      let disabledList = props?.selectList.filter((it) => it.length == 3)
      res = await getClassList({
        schoolId: data.schoolId,
        sysGradeId: data.sysGradeId,
      })
      res = res
        ?.filter(
          (item) =>
            !disabledList.some(
              (it) =>
                it[0] == data.schoolId &&
                it[1] == data.sysGradeId &&
                it[2] == item?.schoolClassId,
            ),
        )
        ?.map((item) => {
          return {
            ...item,
            level: 3,
            schoolId: data.schoolId,
            sysGradeId: data.sysGradeId,
            label: item?.className,
            value: item?.schoolClassId,
          }
        })
    } else if (level == 3) {
      let disabledList = props?.selectList.filter((it) => it.length == 4)
      res = await getStudentList({
        schoolClassId: data.schoolClassId || null,
        schoolId: data.schoolId,
      })
      res = res
        ?.filter(
          (item) =>
            !disabledList.some(
              (it) =>
                it[0] == data.schoolId &&
                it[1] == data.sysGradeId &&
                it[2] == data?.schoolClassId &&
                it[3] == item?.schoolStudentId,
            ),
        )
        ?.map((item) => {
          return {
            ...item,
            label: item?.schoolStudentName,
            level: 4,
            value: item?.schoolStudentId,
          }
        })
    }
    let resList = res.map((item) => {
      return {
        ...item,
        leaf: level === 3,
      }
    })
    await resolve(resList)
  },
}

async function dataChange() {
  emit("dataChange")
}
async function confirm() {
  emit("confirm", distributeList)
}

watch(
  () => showDialog.value,
  (val) => {
    if (val) distributeList = []
    // distributeList = $g._.cloneDeep(props?.selectList)
  },
  { immediate: true },
)
</script>

<style scoped></style>
