<template>
  <div class="ml-120px mb-10px">
    <g-table :tableOptions="tableOptions" class="!w-[700px]">
      <template #header-distributeCount>
        <el-tooltip
          class="item"
          effect="dark"
          content="已确认分发人数/分发总人数"
          placement="top"
          trigger="click"
        >
          <div class="flex justify-center">
            <div>分发情况</div>
            <g-icon name="ri-question-line" size="14" color="" />
          </div>
        </el-tooltip>
      </template>
      <template #distributeCount="{ row }">
        <div class="flex justify-center items-center">
          <div>{{ row?.distributeCount + "/" + row?.totalCount }}</div>
          <!-- <g-icon name="ri-edit-line" size="14" color="" @click="openDialog(row)"/> -->
        </div>
      </template>
      <template #header-forbiddenStatus>
        <el-tooltip
          class="item"
          effect="dark"
          :content="`禁用分发对象后，将无法在${
            equipment.find((it) => it.value == distributeDeviceType)?.label ??
            '-'
          }中查看该第三方应用`"
          placement="top"
          trigger="click"
        >
          <div class="flex justify-center">
            <div>分发状态</div>
            <g-icon name="ri-question-line" size="14" color="" />
          </div>
        </el-tooltip>
      </template>
      <template #forbiddenStatus="{ row }">
        <div>
          <n-switch
            v-model:value="row.forbiddenStatus"
            :checked-value="2"
            :unchecked-value="1"
            @update:value="updateSchoolStatusApi(row)"
          />
        </div>
      </template>
    </g-table>
    <g-dialog
      title="分发情况"
      v-model:show="showDialog"
      @confirm="confirm"
      :maskClosable="true"
    >
      <div class="px-[10px]">
        <div class="flex justify-between">
          <div class="w-[200px]">发送对象</div>
          <div class="w-[100px]">已确认</div>
          <div class="w-[100px]">未确认</div>
        </div>
        <div class="flex justify-between h-[300px] overflow-auto">
          <div class="w-[200px]">{{ schoolDistributeInfo.schoolName }}</div>
          <div class="w-[100px]">
            <div
              v-for="it in schoolDistributeInfo.confirmedList"
              :key="it.studentId"
            >
              {{ it.studentName }}
            </div>
            <div v-if="schoolDistributeInfo?.confirmedList?.length == 0">
              无
            </div>
          </div>
          <div class="w-[100px]">
            <div
              v-for="it in schoolDistributeInfo.unconfirmedList"
              :key="it.studentId"
            >
              {{ it.studentName }}
            </div>
          </div>
        </div>
      </div>
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import { getAppInstallDetail, updateSchoolStatus } from "@/api/clientMgt"
import type { Ref } from "vue"

const route = useRoute()
let props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  distributeDeviceType: {
    type: Number,
    default: 1,
  },
})

const equipment = inject<Ref<any>>("equipment", ref([]))
const tableOptions = reactive({
  ref: null,
  loading: false,
  column: [
    {
      prop: "schoolName",
      label: "学校",
    },
    {
      prop: "distributeCount",
      label: "分发情况",
      headerSlot: true,
      slot: true,
      width: "200px",
    },
    {
      prop: "forbiddenStatus",
      label: "分发状态",
      slot: true,
      headerSlot: true,
      width: "200px",
    },
  ],
  data: props.data as any,
})

let showDialog = $ref(false)
let schoolDistributeInfo = $ref<any>({})

function confirm() {
  showDialog = false
}
async function openDialog(row) {
  showDialog = true
  let params = {
    applicationInstallId: route?.query?.applicationInstallId,
    distributeDeviceType: props.distributeDeviceType,
    schoolId: row?.schoolId,
  }
  let res = await getAppInstallDetail(params)
  schoolDistributeInfo = {
    schoolName: res?.[0]?.schoolName ?? "-",
    confirmedList: res?.filter((it) => it.distributeStatus == 2) ?? [],
    unconfirmedList: res?.filter((it) => it.distributeStatus == 1) ?? [],
  }
}
// 开启/关闭学校安装应用的分发状态
async function updateSchoolStatusApi(row) {
  let params = {
    applicationInstallId: route?.query?.applicationInstallId,
    schoolId: row?.schoolId,
    distributeDeviceType: props.distributeDeviceType,
    forbiddenStatus: row?.forbiddenStatus,
  }
  let res = await updateSchoolStatus(params)
  $g.msg("操作成功")
}
</script>

<style scoped></style>
