<template>
  <div class="relative trilateral-application-container-main">
    <g-loading v-if="pageLoading" class="h-200px"></g-loading>
    <template v-else>
      <div class="h-[calc(100vh-170px)] overflow-auto">
        <g-form :formOptions="formOptions">
          <template #equipment>
            <n-checkbox-group
              v-model:value="formOptions.data.equipment"
              :on-update:value="changeCheckBox"
            >
              <n-space item-style="display: flex;">
                <n-checkbox
                  v-for="item in formOptions.items.equipment.options"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </n-space>
            </n-checkbox-group>
          </template>
          <template #applicationPackageUrl>
            <g-upload
              class="w-[400px]"
              v-model:fileList="formOptions.data.applicationPackageUrl"
              type="drag"
              :max="1"
              accept=".apk"
              :fileSize="200 * 1024 * 1024"
              tips="请上传apk文件,且大小不能超过200MB"
              @onChange="apkChange"
            ></g-upload>
          </template>
          <template #applicationIcon>
            <g-upload
              v-model:fileList="formOptions.data.applicationIcon"
              type="image-card"
              :max="1"
              disabled
              :tips="false"
            ></g-upload>
          </template>
          <template #applicationDistributeObjVOList>
            <div>
              <div
                v-for="(it, index) in formOptions.data
                  .applicationDistributeObjVOList"
                :key="it"
              >
                {{
                  formOptions.data.applicationDistributeObjVOList[index]
                    .equipmentName
                }}
                <n-form-item
                  label="排序"
                  :path="`applicationDistributeObjVOList[${index}].sort`"
                  class="!mr-0 mt-[12px]"
                  :rule="[
                    {
                      key: 'sort' + index,
                      required: true,
                      message: '请输入排序',
                      trigger: 'blur',
                      validator: (rule, value) =>
                        value !== null && value !== undefined && value !== '',
                    },
                  ]"
                >
                  <n-input-number
                    v-model:value="
                      formOptions.data.applicationDistributeObjVOList[index]
                        .sort
                    "
                    placeholder="请输入排序"
                    class="w-[200px]"
                    :min="0"
                  />
                </n-form-item>
                <DistributedObjectTable
                  v-if="
                    route.query.applicationInstallId &&
                    formOptions.data.applicationDistributeObjVOList?.[index]
                      ?.schoolList?.length
                  "
                  :data="
                    formOptions.data.applicationDistributeObjVOList[index]
                      .schoolList
                  "
                  :distributeDeviceType="
                    formOptions.data.applicationDistributeObjVOList[index]
                      ?.distributeDeviceType
                  "
                />
                <n-form-item label="分发对象" class="!mr-0">
                  <div class="flex items-center flex-wrap gap-[5px]">
                    <div
                      v-for="(it, tagIndex) in formOptions.data
                        .applicationDistributeObjVOList[index].studentLabelList"
                      :key="it"
                    >
                      <n-tag
                        class="cursor-pointer"
                        :closable="it.isAddNew"
                        size="large"
                        round
                        @close="deleteStudent(index, tagIndex)"
                      >
                        {{ distributeObj(it) }}</n-tag
                      >
                    </div>

                    <g-icon
                      name="ri-add-circle-line"
                      size="24"
                      @click="addDistribute(index)"
                    ></g-icon>
                  </div>
                </n-form-item>
              </div>
            </div>
          </template>
        </g-form>
      </div>

      <div class="flex justify-center w-full">
        <n-button
          type="primary"
          :loading="formOptions.loading"
          @click="saveApplication"
          >确认</n-button
        >
      </div>
    </template>

    <!-- 添加分发对象 -->
    <CascaderDialog
      v-model:show="showDialog"
      v-model:selectList="selectList"
      @dataChange="dataChange"
      @confirm="confirm"
    />
  </div>
</template>

<script setup lang="ts">
import DistributedObjectTable from "./distributedObjectTable.vue"
import CascaderDialog from "./cascaderDialog.vue"

import {
  addApplication,
  editApplication,
  getDeviceTypeList,
  getSchoolStudentDetailVOList,
  getApplicationDetail,
  getApplicationInstallDistributeObjList,
} from "@/api/clientMgt"
import AliOss from "@/plugins/OSS"

let aliOss: any = new AliOss({})
const router = useRouter()
const route = useRoute()

let pageLoading = $ref<boolean>(false)
let showDialog = $ref<boolean>(false)
let selectList = $ref<any>([])

const formOptions = reactive<any>({
  loading: false,
  ref: null as any,
  labelWidth: "120px",
  items: {
    equipment: {
      type: "checkbox",
      label: "固件应用",
      options: [],
      show: true,
      width: "400px",
      rule: true,
      slot: true,
    },
    applicationPackageUrl: {
      type: "upload",
      label: "上传应用",
      rule: true,
      slot: true,
      width: "400px",
    },
    applicationName: {
      type: "text",
      label: "应用名称",
      rule: true,
      width: "400px",
      disabled: true,
    },
    applicationPackageName: {
      type: "text",
      label: "应用包名",
      rule: true,
      width: "400px",
      disabled: true,
    },
    versionName: {
      type: "text",
      label: "应用版本号",
      rule: true,
      width: "400px",
      disabled: true,
    },
    applicationBuild: {
      type: "text",
      label: "应用Build",
      rule: true,
      width: "400px",
      disabled: true,
    },
    applicationIcon: {
      type: "text",
      label: "应用图标",
      width: "400px",
      slot: true,
      rule: {
        validator: (rule, value, callback) => {
          if (!value.length) {
            callback(new Error("请上传应用图标"))
          } else {
            callback()
          }
        },
        trigger: "change",
        type: "array",
        required: true,
      },
    },
    applicationDistributeObjVOList: {
      type: "text",
      label: "",
      slot: true,
      labelWidth: "0px",
    },
  },
  data: {
    equipment: [],
    applicationPackageUrl: [],
    applicationName: null,
    applicationPackageName: null,
    applicationBuild: null,
    versionName: null,
    applicationIcon: [],
    // sort: 0,
    applicationDistributeObjVOList: [],
  },
})
let tempIndex = $ref<number>(0) //临时存储当前选中的学生
provide(
  "equipment",
  computed(() => formOptions.items.equipment.options),
)

let distributeObj = $computed(() => {
  return (it) => {
    return [it.schoolName, it.sysGradeName, it.className, it.schoolStudentName]
      .filter(Boolean)
      .join("-")
  }
})

async function dataChange() {
  if (formOptions.data.student?.length) {
    formOptions?.ref.validateField("student")
  }
}
function deleteStudent(appListIndex, tagIndex) {
  //删除标签
  formOptions.data.applicationDistributeObjVOList[
    appListIndex
  ]?.studentLabelList?.splice(tagIndex, 1)
  //删除级联选项
  formOptions.data.applicationDistributeObjVOList[appListIndex].student.splice(
    tagIndex,
    1,
  )
}
async function confirm(arr) {
  try {
    formOptions.data.applicationDistributeObjVOList[tempIndex].student.push(
      ...arr,
    )

    let res = await getStudentLabelList(
      formOptions.data.applicationDistributeObjVOList[tempIndex].student,
      true,
    )
    formOptions.data.applicationDistributeObjVOList[
      tempIndex
    ].studentLabelList = res ?? []
  } catch (error) {
    console.log(error)
  }
}

async function apkChange(file, fileList) {
  try {
    if (file?.status != "finished") return
    if (!file?.file) return
    let res = await $g.tool.getAppInfo(file.file)
    formOptions.data.applicationName = res.appName ?? "-"
    formOptions.data.applicationPackageName = res.packageName
    formOptions.data.versionName = res.versionName
    formOptions.data.applicationBuild = String(res.versionCode ?? "")
    formOptions.data.applicationIcon = [
      {
        id: $g.tool.uuid(10),
        name: "icon.png",
        status: "finished",
        percentage: 100,
        file: res.icon,
        url: await getIconUrl(res.icon),
        suffix: "png",
        size: res.icon.size,
      },
    ]
    // 数据赋值完成后单独校验上传应用，应用名称，应用包名等字段
    formOptions.ref?.validateField?.("applicationPackageUrl")
    formOptions.ref?.validateField?.("applicationName")
    formOptions.ref?.validateField?.("applicationPackageName")
    formOptions.ref?.validateField?.("versionName")
    formOptions.ref?.validateField?.("applicationBuild")
    formOptions.ref?.validateField?.("applicationIcon")
  } catch (error) {
    console.log(error)
  }
}
async function getDeviceTypeListApi() {
  try {
    let res = await getDeviceTypeList()
    formOptions.items.equipment.options = res.map((it) => ({
      label: it.title,
      value: it.id,
    }))
  } catch (error) {
    console.log(error)
  }
}
//保存应用
async function saveApplication() {
  formOptions.ref?.validate(async (errors) => {
    if (!errors) {
      try {
        formOptions.loading = true
        const {
          applicationName,
          applicationBuild,
          applicationPackageName,
          applicationPackageUrl,
          applicationIcon,
          // equipment,
          versionName,
          applicationDistributeObjVOList,
        } = formOptions.data

        let params = {
          // distributeDeviceTypeIds: equipment.join(","),
          applicationName,
          applicationBuild,
          applicationPackageName,
          applicationPackageUrl:
            applicationPackageUrl?.[0]?.resource_url?.replace(
              "https://edu-jzt.oss-cn-chengdu.aliyuncs.com",
              "https://jzt-cdn.qimingdaren.com",
            ) ?? "",
          applicationIcon: applicationIcon?.[0]?.resource_url
            ? applicationIcon[0].resource_url
            : applicationIcon?.[0]?.url || "",
          applicationVersion: versionName,
          applicationDistributeObjRequestVOList:
            applicationDistributeObjVOList?.map((it) => {
              return {
                distributeDeviceType: it?.distributeDeviceType,
                sort: it.sort,
                applicationDistributeObjSchoolRequestVOList: it.student.map(
                  (its) => {
                    return {
                      schoolId: its?.[0] ?? null,
                      sysGradeId: its?.[1] ?? null,
                      schoolClassId: its?.[2] ?? null,
                      schoolStudentId: its?.[3] ?? null,
                    }
                  },
                ),
              }
            }),
        }
        const URL = route.query.applicationInstallId
          ? editApplication
          : addApplication
        if (route.query.applicationInstallId) {
          params["applicationInstallId"] = route.query.applicationInstallId
        }
        await URL(params)
        formOptions.loading = false
        $g.msg("操作成功")
        router.push({ name: "TrilateralApplication" })
      } catch (error) {
        console.log(error)
        formOptions.loading = false
      }
    } else {
      $g.tool.scrollToErrorItem()
    }
  })
}
//获取应用详情
async function getApplicationInstallDetail() {
  try {
    pageLoading = true
    let res = await getApplicationDetail({
      applicationInstallId: route.query.applicationInstallId,
    })
    formOptions.data.equipment = res?.distributeDeviceTypeIds
      ?.split(",")
      .map((it) => Number(it))

    formOptions.data.applicationPackageUrl = [
      {
        url: res.applicationPackageUrl,
        resource_url: res.applicationPackageUrl,
        name: res.applicationPackageUrl.split("/").pop(),
      },
    ]
    formOptions.data.applicationName = res.applicationName
    formOptions.data.applicationPackageName = res.applicationPackageName
    formOptions.data.versionName = res.applicationVersion
    formOptions.data.applicationBuild = res.applicationBuild
    formOptions.data.applicationIcon = [
      {
        id: $g.tool.uuid(10),
        name: res.applicationIcon,
        status: "finished",
        percentage: 100,
        file: res.applicationIcon,
        url: res.applicationIcon,
        suffix: "png",
        size: "0",
      },
    ]
    formOptions.data.applicationDistributeObjVOList =
      res?.applicationDistributeObjResponseVOList.map((it) => {
        return {
          equipmentName:
            formOptions.items.equipment.options?.find(
              (its) => its.value == it.distributeDeviceType,
            )?.label ?? "-",
          distributeDeviceType: it?.distributeDeviceType,
          sort: it?.sort,
          student: [],
          schoolList: it?.applicationDistributeObjSchoolResponseVOList,
        }
      })
    pageLoading = false
  } catch (error) {
    console.log(error)
    pageLoading = false
  }
}
async function getIconUrl(file: any) {
  let res = await aliOss.uploadFile(
    {
      name: "applicationIcon.png",
      size: file.size,
      file: file,
      id: "applicationIcon",
    },
    undefined,
  )
  return res?.resource_url ?? ""
}

function changeCheckBox(values) {
  formOptions.data.equipment = values
  const oldIds = formOptions.data.applicationDistributeObjVOList.map(
    (item) => item.distributeDeviceType,
  )
  const added = values.filter((id) => !oldIds.includes(id))
  const removed = oldIds.filter((id) => !values.includes(id))
  // 处理新增
  added.forEach((id) => {
    formOptions.data.applicationDistributeObjVOList.push({
      equipmentName:
        formOptions.items.equipment.options?.find((its) => its.value == id)
          ?.label ?? "-",
      sort: 0,
      student: [],
      distributeDeviceType: id,
    })
  })

  // 处理移除
  formOptions.data.applicationDistributeObjVOList =
    formOptions.data.applicationDistributeObjVOList.filter(
      (item) => !removed.includes(item.distributeDeviceType),
    )
}
// 获取应用安装分发对象
async function getApplicationInstallDistributeObjListApi() {
  //级联回显
  let res = await getApplicationInstallDistributeObjList({
    applicationInstallId: route.query.applicationInstallId,
  })
  await nextTick()
  formOptions.data.applicationDistributeObjVOList.forEach(async (it) => {
    //级联选择器回显
    it.student = res
      ?.filter((item) => item.distributeDeviceType == it.distributeDeviceType)
      ?.map((its) => {
        return [
          its.schoolId,
          its.sysGradeId,
          its.schoolClassId,
          its.schoolStudentId,
        ].filter(Boolean)
      })
    it.studentLabelList = await getStudentLabelList(it.student, false)
    it.cacheStudent = it.studentLabelList
  })
}
async function getStudentLabelList(student = [], isAddNew = false) {
  const schoolStudentDetailVOList = student?.map((it) => {
    return {
      schoolId: it?.[0] ?? null,
      sysGradeId: it?.[1] ?? null,
      schoolClassId: it?.[2] ?? null,
      schoolStudentId: it?.[3] ?? null,
    }
  })
  if (schoolStudentDetailVOList.length) {
    let res = await getSchoolStudentDetailVOList({ schoolStudentDetailVOList })
    return res.map((it) => {
      if (isAddNew) {
        let cacheList =
          formOptions.data.applicationDistributeObjVOList[tempIndex]
            ?.cacheStudent ?? []
        return {
          ...it,
          isAddNew: !cacheList.some(
            ({ schoolId, sysGradeId, schoolClassId, schoolStudentId }) =>
              schoolId === it.schoolId &&
              sysGradeId === it.sysGradeId &&
              schoolClassId === it.schoolClassId &&
              schoolStudentId === it.schoolStudentId,
          ),
        }
      } else {
        return {
          ...it,
          isAddNew,
        }
      }
    })
  }
  return []
}
// 添加分发对象
function addDistribute(index) {
  showDialog = true
  tempIndex = index
  selectList =
    formOptions.data.applicationDistributeObjVOList?.[tempIndex]?.student ?? []
}
//删除apk相关应用信息删除
watch(
  () => formOptions.data.applicationPackageUrl,
  (val) => {
    if (!val?.length) {
      formOptions.data.applicationName = null
      formOptions.data.applicationPackageName = null
      formOptions.data.versionName = null
      formOptions.data.applicationBuild = null
      formOptions.data.applicationIcon = []
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
onMounted(async () => {
  await getDeviceTypeListApi()
  if (route.query.applicationInstallId) {
    await getApplicationInstallDetail()
    await getApplicationInstallDistributeObjListApi()
  }
})
</script>

<style scoped></style>
