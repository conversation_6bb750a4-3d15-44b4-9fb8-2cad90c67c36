<template>
  <div class="trilateral-application-container-main">
    <g-table :tableOptions="tableOptions" @changePage="getAppList">
      <template #header-left>
        <n-button type="primary" @click="goPage">上传应用</n-button>
      </template>
      <template #cz="{ row, index }">
        <n-button type="primary" text @click="goPage(row)">编辑</n-button>
      </template>
    </g-table>
    <!-- 上传应用apk -->
  </div>
</template>

<script setup lang="ts">
import {
  getApplicationInstallPageList,
  deleteApplication,
} from "@/api/clientMgt"
const router = useRouter()

const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
  column: [
    { prop: "applicationName", label: "应用名称", tooltip: true },
    { prop: "applicationPackageName", label: "应用包名", tooltip: true },
    { prop: "createTime", label: "上传时间" },
    { prop: "updateTime", label: "更新时间" },

    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
})
async function getAppList() {
  const res = await getApplicationInstallPageList({
    page: tableOptions.pageOptions.page,
    pageSize: tableOptions.pageOptions.page_size,
  })
  tableOptions.data = res?.list ?? []
  tableOptions.pageOptions.total = res?.total ?? 0
}
async function deleteApp({ applicationIntsallId }) {
  $g.confirm({ content: "是否删除该应用？" }).then(async () => {
    await deleteApplication({ applicationIntsallId })
    getAppList()
  })
}
function goPage(row?) {
  const query = row?.applicationInstallId
    ? { applicationInstallId: row.applicationInstallId }
    : undefined
  router.push({
    name: "AddOrEditTrilateralApp",
    ...(query ? { query } : {}),
  })
}
onMounted(() => {
  getAppList()
})
</script>

<style scoped></style>
