<template>
  <div class="ai-container-main" v-loading="showLoading">
    <div
      class="flex items-center font-bold text-16px mb-15px justify-center w-full"
    >
      <div>学生：{{ route.query.userName || "-" }}</div>
      <div class="ml-60px">年级：{{ route.query.sysGradeName || "-" }}</div>
    </div>
    <g-form :formOptions="formOptions">
      <template #sysCourseId>
        <n-select
          v-model:value="formOptions.data.sysCourseId"
          :options="formOptions.items.sysCourseId.options"
          placeholder="请选择学科"
          class="w-200px"
          clearable
          @update:value="onSubjectChange"
        />
      </template>
      <template #nowScore>
        <n-input-number
          class="w-200px"
          v-model:value="formOptions.data.nowScore"
          placeholder="请输入分数"
          :show-button="false"
        />
      </template>
      <template #goalScore>
        <n-input-number
          class="w-200px"
          v-model:value="formOptions.data.goalScore"
          placeholder="请输入分数"
          :show-button="false"
        />
      </template>
      <template #sysTextbookVersionId>
        <n-select
          v-model:value="formOptions.data.sysTextbookVersionId"
          :options="formOptions.items.sysTextbookVersionId.options"
          placeholder="请选择版本"
          class="w-200px"
          clearable
          @update:value="onVersionChange"
        />
      </template>
      <template #sysTextbookId>
        <div class="flex items-center">
          <n-select
            v-model:value="formOptions.data.sysTextbookId"
            :options="formOptions.items.sysTextbookId.options"
            placeholder="请选择教材"
            class="w-fit min-w-200px"
            :multiple="true"
            :max-tag-count="1"
            clearable
            :disabled="textBookDisabled"
            @update:value="onTextbookChange"
          />
          <div class="ml-10px text-12px text-error">
            （多选，已选择{{ formOptions.data.sysTextbookId.length }}个）
          </div>
        </div>
      </template>
      <template #chapters>
        <div
          class="w-full grid grid-cols-3 gap-15px"
          v-if="formOptions.data.sysTextbookId.length"
        >
          <div
            class="mr-40px flex flex-col items-start"
            v-for="(item, idx) in formOptions.items.chapters.options"
            :key="idx"
          >
            <div class="pl-25px font-bold mb-10px">{{ item.name }}</div>
            <n-checkbox-group v-model:value="item.select">
              <g-tree
                class="!border-none"
                :treeData="item.children"
                :treeLine="false"
                nodeKey="sysTextbookCatalogId"
                :props="{
                  label: 'sysTextbookCatalogName',
                }"
              >
                <template #body="{ data }">
                  <n-checkbox
                    :value="data.sysTextbookCatalogId"
                    :disabled="data.isLast === 1 || data.isShow === 1"
                  >
                    <div
                      class="inline-block max-w-200px truncate"
                      :title="data.sysTextbookCatalogName"
                    >
                      {{ data.sysTextbookCatalogName }}
                    </div>
                  </n-checkbox>
                </template>
              </g-tree>
            </n-checkbox-group>
          </div>
        </div>

        <div class="text-14px h-22px leading-[22px] text-[#cccccc]" v-else>
          未选择任何教材
        </div>
      </template>
      <template #weekNum>
        <n-input-number
          :min="1"
          :max="53"
          class="w-120px"
          v-model:value="formOptions.data.weekNum"
          placeholder="请输入周次"
          :show-button="false"
          @blur="onWeekCal"
        />
        <div class="ml-10px" v-if="formOptions.data.startTime">
          周（{{ formOptions.data.startTime
          }}<template v-if="formOptions.data.endTime">
            - {{ formOptions.data.endTime }}</template
          >）
        </div>
        <div
          v-if="!showPicker"
          class="text-primary select-none ml-10px active:opacity-80 cursor-pointer"
          @click="setFirstDay"
        >
          修改第一周开始时间
        </div>
        <template v-else>
          <n-date-picker
            class="ml-10px"
            v-model:formatted-value="tempDate"
            value-format="yyyy.MM.dd"
            type="date"
            clearable
          />
          <div
            class="ml-15px mr-10px text-primary select-none active:opacity-80 cursor-pointer"
            @click="rest"
          >
            重置
          </div>
          <div
            class="text-primary select-none ml-15px active:opacity-80 cursor-pointer"
            @click="confirm"
          >
            确定
          </div>
        </template>
      </template>
      <template #weekList>
        <g-table :tableOptions="tableOptions" class="!mt-0">
          <template #range="{ row }"
            >{{ row.startTime }}-{{ row.endTime }}</template
          >
          <template #middle="{ row }">
            <div
              class="w-full h-48px cursor-pointer select-none flex items-center justify-center"
              @click="setMiddle(row)"
            >
              <g-icon
                name="ri-checkbox-circle-fill"
                size="30"
                color="#18a058"
                v-if="row.isInterim"
              />
            </div>
          </template>
          <template #end="{ row }">
            <div
              class="w-full h-48px cursor-pointer select-none flex items-center justify-center"
              @click="setEnd(row)"
            >
              <g-icon
                name="ri-checkbox-circle-fill"
                size="30"
                color="#18a058"
                v-if="row.isPeriod"
              />
            </div>
          </template>
        </g-table>
      </template>
      <template #learnFrequency>
        <n-radio-group v-model:value="formOptions.data.learnFrequency">
          <n-radio-button
            v-for="item in 3"
            :key="item"
            :value="item"
            :label="item + '次'"
          />
        </n-radio-group>
      </template>
    </g-form>
    <div class="w-full flex justify-center mt-50px">
      <n-button
        type="primary"
        size="large"
        class="w-200px text-16px"
        @click="onSet"
      >
        生成计划
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/stores/modules/user"

import {
  getNewSubjectListApi,
  getNewVersionListApi,
  getBookSelectApi,
  getChapterTreeList,
  createNewProject,
} from "@/api/resourceMgt"

const userStore = useUserStore()

const FORMAT = "YYYY.MM.DD"

const route = useRoute()
const router = useRouter()

let showLoading = $ref(false)

const formOptions = reactive({
  ref: null as any,
  loading: false,
  labelWidth: "120px",
  items: {
    sysCourseId: {
      type: "select",
      label: "学科",
      rule: true,
      slot: true,
      span: 24,
      options: [] as any,
    },
    nowScore: {
      type: "number",
      label: "现有分数",
      rule: true,
      slot: true,
      span: 5,
    },
    goalScore: {
      type: "number",
      label: "目标分数",
      rule: true,
      slot: true,
      span: 19,
    },
    sysTextbookVersionId: {
      type: "select",
      label: "版本",
      rule: true,
      slot: true,
      span: 5,
      options: [],
    },
    sysTextbookId: {
      type: "select",
      label: "教材",
      slot: true,
      span: 19,
      options: [] as any,
      rule: {
        required: true,
        validator: (rule, value, cb) => {
          if (value.length) {
            cb()
          } else {
            cb(new Error("请选择教材"))
          }
        },
      },
    },
    chapters: {
      label: "教材章节",
      slot: true,
      span: 24,
      options: [
        // {
        //   name: "必修一",
        //   id: 1,
        //   select: [],
        //   children: [
        //     {
        //       label: "第一章 函数",
        //       value: 1,
        //     },
        //     {
        //       label: "第二章 导数",
        //       value: 2,
        //       disabled: true,
        //     },
        //     {
        //       label: "第三章 立体几何",
        //       value: 3,
        //     },
        //   ],
        // },
      ] as any,
      rule: {
        required: true,
        validator: (rule, value, cb) => {
          if (
            formOptions.items.chapters.options.some(
              (v) => v.select.length !== 0,
            )
          ) {
            cb()
          } else {
            cb(new Error("请选择章节"))
          }
        },
      },
    },
    weekNum: {
      type: "number",
      label: "学习时长",
      rule: true,
      slot: true,
      span: 24,
    },
    weekList: {
      label: "周次详情",
      slot: true,
      span: 24,
    },
    learnFrequency: {
      type: "radio",
      label: "每周学习频次",
      rule: true,
      slot: true,
      span: 24,
    },
  },
  data: {
    sysCourseId: null,
    nowScore: null,
    goalScore: null,
    sysTextbookVersionId: null,
    sysTextbookId: [] as any,
    chapters: [],
    weekNum: null,
    startTime: "",
    endTime: "",
    // weekList: [] as any,
    learnFrequency: null,
  },
})

let textBookDisabled = $ref(false)
let showPicker = $ref(false)
let tempDate = $ref("")

const tableOptions = reactive({
  column: [
    { prop: "weekName", label: "周次", slot: true },
    { prop: "range", label: "时间范围", slot: true, width: "200px" },
    { prop: "middle", label: "期中", slot: true },
    { prop: "end", label: "期末", slot: true },
  ],
  data: [] as any,
})

function onSet() {
  formOptions.ref?.validate((errors) => {
    if (!errors) {
      createNewProjectApi({
        schoolStudentId: route.query.schoolStudentId,
        sysCourseId: formOptions.data.sysCourseId,
        sysTextbookVersionId: formOptions.data.sysTextbookVersionId,
        planBookList: formOptions.data.sysTextbookId.map((v) => ({
          sysTextbookId: v,
          sysTextbookCatalogIdList:
            formOptions.items.chapters.options.find((vv) => vv.id === v)
              ?.select || [],
        })),
        nowScore: formOptions.data.nowScore,
        goalScore: formOptions.data.goalScore,
        weekNum: formOptions.data.weekNum,
        startTime: formOptions.data.startTime.replace(/\./g, "-"),
        endTime: formOptions.data.endTime.replace(/\./g, "-"),
        learnFrequency: formOptions.data.learnFrequency,
        weekList: tableOptions.data.map((v) => ({
          ...v,
          startTime: v.startTime.replace(/\./g, "-"),
          endTime: v.endTime.replace(/\./g, "-"),
        })),
      })
    }
  })
}

async function createNewProjectApi(params) {
  try {
    showLoading = true
    const data = await createNewProject(params)
    if (!data) return
    const { reviewPlan, studyPlan } = data
    const pageData = {
      userName: route.query.userName,
      sysGradeName: route.query.sysGradeName,
      subjectName: formOptions.items.sysCourseId.options.find(
        (v) => v.value === formOptions.data.sysCourseId,
      )?.label,
      weekNum: formOptions.data.weekNum,
      tableList: tableOptions.data.map((v) => ({
        ...v,
        studyTimes: formOptions.data.learnFrequency,
        studyPlan: studyPlan.filter((vv) => vv.weekname === v.weekName),
        reviewPlan: reviewPlan.filter((vv) => vv.weekname === v.weekName),
      })),
    }

    userStore.studyProject = pageData
    showLoading = false
    router.push({ name: "ProjectResult" })
  } catch (e) {
    showLoading = false
    console.error(e)
  }
}

function setMiddle(row) {
  tableOptions.data.forEach((v) => {
    if (v.weekName !== row.weekName) {
      v.isInterim = false
    } else {
      v.isInterim = !v.isInterim
    }
  })
}

function setEnd(row) {
  tableOptions.data.forEach((v) => {
    if (v.weekName !== row.weekName) {
      v.isPeriod = false
    } else {
      v.isPeriod = !v.isPeriod
    }
  })
}

async function getNewSubjectList() {
  const data = await getNewSubjectListApi({
    sysStageId: route.query.sysStageId,
  })
  formOptions.items.sysCourseId.options =
    data?.map((v) => ({
      label: v.sysSubjectName,
      value: v.sysCourseId,
    })) || []
}

async function onSubjectChange(val) {
  formOptions.items.sysTextbookVersionId.options = []
  formOptions.data.sysTextbookVersionId = null
  formOptions.items.sysTextbookId.options = []
  formOptions.data.sysTextbookId = []
  formOptions.items.chapters.options = []
  if (!val) {
    return
  }
  const data = await getNewVersionListApi({
    sysCourseId: val,
  })
  formOptions.items.sysTextbookVersionId.options =
    data?.map((v) => ({
      label: v.sysTextbookVersionName,
      value: v.sysTextbookVersionId,
    })) || []
}

async function onVersionChange(val) {
  formOptions.items.sysTextbookId.options = []
  formOptions.data.sysTextbookId = []
  formOptions.items.chapters.options = []
  if (!val) {
    return
  }
  const data = await getBookSelectApi({
    sysTextbookVersionId: val,
    sysCourseId: formOptions.data.sysCourseId,
  })
  formOptions.items.sysTextbookId.options =
    data?.map((v) => ({
      label: v.volume,
      value: v.sysTextbookId,
    })) || []
}

async function onTextbookChange(val) {
  try {
    textBookDisabled = true
    const chapterList = formOptions.items.chapters.options
    if (val.length > chapterList.length) {
      const newId = val.find((v) => chapterList.every((vv) => vv.id !== v))
      const data = await getChapterTreeList({
        sysTextbookId: newId,
      })
      chapterList.push({
        name: formOptions.items.sysTextbookId.options.find(
          (v) => v.value === newId,
        )?.label,
        id: newId,
        children: data || [],
      })
    } else {
      const arr = chapterList.filter((v) => val.includes(v.id))
      formOptions.items.chapters.options = arr
    }
    textBookDisabled = false
  } catch (e) {
    textBookDisabled = false
    console.error(e)
  }
}

function getToday() {
  const timeStr = $g.dayjs().format(FORMAT)
  formOptions.data.startTime = timeStr
}

function onWeekCal() {
  if (!formOptions.data.weekNum) return
  const weekLen = formOptions.data.weekNum || 0
  const weekList: any = []
  for (let i = 0; i < weekLen; i++) {
    let startTime = ""
    let endTime = ""
    if (i === 0) {
      startTime = formOptions.data.startTime
      const addDate = 7 - $g.dayjs(startTime).day()
      endTime = $g.dayjs(startTime).add(addDate, "day").format(FORMAT)
    } else {
      startTime = $g
        .dayjs(weekList[i - 1].endTime)
        .add(1, "day")
        .format(FORMAT)
      endTime = $g.dayjs(startTime).add(7, "day").format(FORMAT)
    }
    weekList.push({
      weekName: `第${i + 1}周`,
      startTime,
      endTime,
      isInterim: false,
      isPeriod: false,
    })
  }
  formOptions.data.endTime = weekList[weekList.length - 1].endTime
  tableOptions.data = weekList
}

function setFirstDay() {
  tempDate = formOptions.data.startTime
  showPicker = true
}

function rest() {
  showPicker = false
  getToday()
  onWeekCal()
}

function confirm() {
  formOptions.data.startTime = $g.dayjs(tempDate).format(FORMAT)
  showPicker = false
  onWeekCal()
}

onBeforeMount(() => {
  userStore.studyProject = null
  if (route.query.sysStageId && route.query.schoolStudentId) {
    getToday()
    getNewSubjectList()
  } else {
    router.back()
  }
})
</script>

<style lang="scss" scoped></style>
