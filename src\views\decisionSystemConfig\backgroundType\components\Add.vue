<template>
  <g-dialog
    :title="ifEdit ? '编辑账号' : '创建账号'"
    :formOptions="formOptions"
    v-model:show="showDialog"
    @confirm="confirm"
  >
    <g-form :formOptions="formOptions">
      <template #accountName>
        <div>{{ formOptions.data.accountName }}</div>
      </template>
      <!-- autocomplete="off" -->
      <template #userName>
        <n-input
          v-model:value="formOptions.data.userName"
          type="text"
          placeholder="2-10个汉字"
          class="!w-[280px]"
          :input-props="{ autocomplete: 'new-password' }"
          clearable
        />
      </template>
      <template #password>
        <n-input
          v-model:value="formOptions.data.password"
          type="password"
          placeholder="8-16位，支持字母数字及特殊字符"
          class="!w-[280px]"
          :input-props="{ autocomplete: 'new-password' }"
          clearable
          show-password-on="click"
        />
      </template>
      <template #tips>
        <div class="text-gray-default">绑定手机后支持手机登陆</div>
      </template>
    </g-form>
  </g-dialog>
</template>

<script setup lang="ts">
import {
  checkAccount,
  createAccount,
  updateAccount,
  getAccountDetail,
  getRole,
} from "@/api/userMgt"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  ifEdit: {
    type: Boolean,
    default: false,
  },
  accountAdminId: {
    type: Number,
    default: 0,
  },
})
const emit = defineEmits(["update:show", "edit", "refresh"])
watch(
  () => props.show,
  (val) => {
    if (!val) {
      emit("edit")
      formOptions.items.accountName.slot = false
    } else {
      if (props.ifEdit) {
        getAccountDetailData()
      }
    }
  },
)
//账号校验规则
const validatePass1 = async (rule, value, callback) => {
  try {
    if (formOptions.items.accountName.slot) return
    if (!value || value.length < 8 || value.length > 30) {
      callback(new Error("请输入8-30位字符"))
    } else if (!/^(?=.*[a-zA-Z])[a-zA-Z0-9]+$/.test(value)) {
      callback(new Error("请输入纯英文或者英语+数字"))
    } else {
      formOptions.items.accountName.loading = true
      let res = await checkAccount({
        accountName: formOptions.data.accountName,
      })
      formOptions.items.accountName.loading = false
      if (res.msg) callback(new Error("账号重复，请重新输入")) //true-重复 false-没有重复
    }
    callback()
  } catch (err) {
    console.log(err)
    formOptions.items.accountName.loading = false
  }
}

//姓名校验规则
const validatePass2 = (rule, value, callback) => {
  if (!value || value.length < 2 || value.length > 10) {
    callback(new Error("请输入2-10个汉字"))
  } else if (!/^[\u4E00-\u9FA5]+$/.test(value)) {
    callback(new Error("请输入2-10个汉字"))
  }
  callback()
}
// 密码校验规则
const validatePass4 = (rule, value, callback) => {
  if (props.ifEdit) callback()
  if (!value || value.length < 8 || value.length > 16) {
    callback(new Error("请输入8-16位密码"))
  }
  callback()
}
//手机号校验 非必填
const validatePass3 = (rule, value, callback) => {
  if (value && !/^1[3-9][0-9]{9}$/.test(value))
    callback(new Error("请输入正确的手机号"))
  callback()
}
const showDialog = useVModel(props, "show", emit)
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  items: {
    accountName: {
      type: "text",
      label: "账号",
      placeholder: "支持纯英文或者英语+数字,长度8-30",
      width: "280px",
      loading: false,
      rule: {
        validator: validatePass1,
        trigger: "blur",
        type: "string",
        required: true,
      },
      show: true,
      slot: false,
      // span: 12,
    },

    userName: {
      type: "text",
      label: "姓名",
      width: "280px",
      placeholder: "汉字+英文，2-10个字",
      slot: true,
      rule: {
        validator: validatePass2,
        type: "string",
        required: true,
      },
      // span: 12,
    },
    password: {
      type: "password",
      label: "密码",
      width: "280px",
      placeholder: "8-16位，支持字母数字及特殊字符",
      rule: {
        validator: validatePass4,
        type: "string",
        required: true,
      },
      slot: true,
    },
    mobile: {
      type: "text",
      label: "手机号",
      width: "280px",
      rule: {
        validator: validatePass3,
        type: "string",
        required: false,
      },
    },
    privilegeRoleId: {
      type: "select",
      label: "角色",
      width: "280px",
      rule: true,
      options: [],
      labelField: "roleName",
      valueField: "privilegeRoleId",
    },
    tips: {
      type: "text",
      label: "",
      slot: true,
    },
  },
  data: {
    accountName: null,
    userName: null,
    mobile: null,
    userId: null,
    tips: null,
    password: null,
    privilegeRoleId: null,
  },
})
/* 获取账号详情 */
async function getAccountDetailData() {
  let res = await getAccountDetail({ accountAdminId: props.accountAdminId })
  formOptions.data = res
  formOptions.items.accountName.slot = true
}
/* 创建账号 */
async function confirm() {
  try {
    if (props.ifEdit) {
      await updateAccount(formOptions.data)
      $g.msg("编辑成功")
    } else {
      await createAccount(formOptions.data)
      $g.msg("创建成功")
    }
    formOptions.loading = false
    emit("update:show", false)
    emit("refresh")
  } catch (err) {
    console.log(err)
    formOptions.loading = false
  }
}
onBeforeMount(async () => {
  let res = await getRole({
    needCount: false,
  })
  formOptions.items.privilegeRoleId.options = res
})
watch(
  () => props.ifEdit,
  (val) => {
    if (val) {
      formOptions.items.password.rule.required = false
    } else {
      formOptions.items.password.rule.required = true
    }
  },
)
</script>

<style lang="scss" scoped></style>
