<template>
  <g-dialog
    title="登录日志"
    v-model:show="showDialog"
    :show-footer="false"
    :width="750"
  >
    <g-table :tableOptions="tableOptions" @changePage="initData">
      <template #header-left>
        <div class="flex gap-x-[50px]">
          <div>账号：{{ userInfo.accountName }}</div>
          <div>姓名：{{ userInfo.userName }}</div>
        </div>
      </template>
    </g-table>
  </g-dialog>
</template>

<script setup lang="ts">
import { getAccountLogList } from "@/api/userMgt"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  userInfo: {
    type: Object,
    default: () => {},
  },
})
const emit = defineEmits(["update:show"])
const showDialog = useVModel(props, "show", emit)
const tableOptions = $ref<any>({
  ref: null as any,
  loading: false,
  column: [
    {
      prop: "accountLoginLogId",
      label: "ID",
    },
    {
      prop: "createTime",
      label: "登录时间",
      formatter: (row) => {
        return $g.dayjs(row.createTime).format("YYYY-MM-DD HH:mm:ss")
      },
    },
    {
      prop: "ip",
      label: "IP",
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})
async function initData() {
  let res = await getAccountLogList({
    accountAdminId: props.userInfo.accountAdminId,
    page: tableOptions.pageOptions.page,
    pageSize: tableOptions.pageOptions.page_size,
  })
  tableOptions.data = res.list
  tableOptions.pageOptions.total = res.total
}
watch(
  () => props.show,
  (val) => {
    if (val) {
      initData()
    }
  },
)
</script>

<style lang="scss" scoped></style>
