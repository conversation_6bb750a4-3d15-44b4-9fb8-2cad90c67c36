<template>
  <div class="background-type-container-main">
    <g-form :formOptions="formOptions" @reset="search" @search="search">
    </g-form>
    <g-table :tableOptions="tableOptions" @changePage="initData">
      <template #header-right>
        <n-button type="primary" @click="showDialog = true">
          <g-icon name="add-line" size="" color="" />
          新建账号
        </n-button>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="primary" text @click="edit(row)">修改信息</n-button>
          <n-button type="primary" text @click="openLoginDialog(row)"
            >登录日志</n-button
          >
        </n-space>
      </template>
    </g-table>
    <!-- 添加/编辑账号 -->
    <Add
      v-model:show="showDialog"
      :ifEdit="ifEdit"
      @edit="ifEdit = false"
      @refresh="search"
      :accountAdminId="accountAdminId"
    />
    <!-- 登录日志 -->
    <LoginDialog v-model:show="showLoginDialog" :userInfo="userInfo" />
  </div>
</template>

<script setup lang="ts">
import Add from "./components/Add.vue"
import LoginDialog from "./components/LoginDialog.vue"
import { getAccountList } from "@/api/userMgt"
/* 搜索表单 */
const formOptions = $ref<any>({
  ref: null as any,
  loading: false,
  filter: true,
  items: {
    keyword: {
      label: "账号/姓名检索",
      type: "text",
      showLabel: false,
    },
  },
  data: {
    keyword: "",
  },
  filterData: {
    keyword: "",
  },
})
let showDialog = $ref(false)
let showLoginDialog = $ref(false)
let ifEdit = $ref(false)
/* 账号表格 */
const tableOptions = $ref<any>({
  ref: null as any,
  loading: false,
  column: [
    {
      prop: "accountAdminId",
      label: "ID",
    },
    {
      prop: "accountName",
      label: "账号",
    },
    {
      prop: "userName",
      label: "姓名",
    },
    {
      prop: "mobile",
      label: "手机号",
    },
    {
      prop: "privilegeRoleName",
      label: "角色",
    },
    {
      prop: "createTime",
      label: "创建时间",
      formatter: (row) => {
        return $g.dayjs(row.createTime).format("YYYY-MM-DD HH:mm:ss")
      },
    },
    {
      prop: "loginCounter",
      label: "登录次数",
    },
    {
      prop: "lastLoginTime",
      label: "最后一次登录",
      formatter: (row) => {
        return row.lastLoginTime
          ? $g.dayjs(row.lastLoginTime).format("YYYY-MM-DD HH:mm:ss")
          : "-"
      },
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    total: 0,
    page: 1,
    page_size: 10,
  },
})
let accountAdminId = $ref<any>(0)
/* 修改信息 */
function edit(row) {
  accountAdminId = row.accountAdminId
  ifEdit = true
  showDialog = true
}
/* 搜索/重置 */
async function search() {
  tableOptions.pageOptions.page = 1
  await initData()
}
/* 获取账号列表 */
async function initData() {
  try {
    tableOptions.loading = true
    let res = await getAccountList({
      keyword: formOptions.data.keyword,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    tableOptions.data = res.list
    tableOptions.pageOptions.total = res.total
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
  }
}
let userInfo = $ref<any>({})
/* 登录日志 */
function openLoginDialog(row) {
  userInfo = row
  showLoginDialog = true
}
onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped></style>
