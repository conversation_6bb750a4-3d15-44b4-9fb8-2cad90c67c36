import * as XLSX from "xlsx-js-style"

/**
 * 导出数据到Excel（使用xlsx-js-style插件）
 * @param {Array} data 要导出的数据数组
 */
export const exportToExcel = (data) => {
  try {
    // 定义表头和数据映射
    const headerMap = [
      { label: "姓名", prop: "name", merge: "self" },
      { label: "性别", prop: "gender", merge: "self" },
      { label: "学号", prop: "studentId", merge: "self" },
      { label: "科目", prop: "subject", merge: "self" },
      { label: "视频总进度", prop: "videoProgress", merge: "followSubject" },
      {
        label: "是否使用错题本",
        prop: "status",
        merge: "followSubject",
      },
      { label: "章节信息", prop: "chapterTitle", merge: false },
      { label: "开始观看时间", prop: "startTime", merge: false },
      { label: "最后观看时间", prop: "endTime", merge: false },
      { label: "观看时长", prop: "duration", merge: false },
      { label: "视频观看进度", prop: "watchProgress", merge: false },
      { label: "练习题目数", prop: "exerciseCount", merge: false },
      {
        label: "练习题目进度",
        prop: "exerciseProgress",
        merge: false,
      },
    ]

    // 创建工作簿
    const workbook = XLSX.utils.book_new()

    // 准备表头数据
    const headers = headerMap.map((item) => ({
      v: item.label,
      t: "s",
      s: {
        font: { bold: true, sz: 11 },
        fill: { fgColor: { rgb: "F5F5F5" } },
        alignment: { horizontal: "center", vertical: "center", wrapText: true },
        border: {
          top: { style: "thin", color: { rgb: "E5E5E5" } },
          bottom: { style: "thin", color: { rgb: "E5E5E5" } },
          left: { style: "thin", color: { rgb: "E5E5E5" } },
          right: { style: "thin", color: { rgb: "E5E5E5" } },
        },
      },
    }))

    // 准备数据行
    const rows = data.map((item) =>
      headerMap.map((header) => ({
        v: item[header.prop],
        t: typeof item[header.prop] === "number" ? "n" : "s",
        s: {
          font: { sz: 11 },
          alignment: {
            horizontal: "center",
            vertical: "center",
            wrapText: true,
          },
          border: {
            top: { style: "thin", color: { rgb: "E5E5E5" } },
            bottom: { style: "thin", color: { rgb: "E5E5E5" } },
            left: { style: "thin", color: { rgb: "E5E5E5" } },
            right: { style: "thin", color: { rgb: "E5E5E5" } },
          },
        },
      })),
    )

    // 合并表头和数据
    const wsData = [headers, ...rows]

    // 创建工作表
    const ws = XLSX.utils.aoa_to_sheet(wsData)

    // 设置列宽
    ws["!cols"] = headerMap.map((header, index) => ({
      wch: index < 4 ? 20 : 40,
    }))

    // 处理合并单元格
    const merges = [] as any

    headerMap.forEach((header, colIndex) => {
      if (!header.merge) return

      let count = 1
      let startRow = 1

      for (let i = 1; i < data.length; i++) {
        const currentRow = data[i]
        const prevRow = data[i - 1]
        let shouldMerge = false

        if (["name", "gender", "account"].includes(header.prop)) {
          // 姓名、性别、学号根据studentId合并
          shouldMerge = currentRow.studentId === prevRow.studentId
        } else if (header.merge === "followSubject") {
          // 辅导本列根据科目且studentId相同时合并
          shouldMerge =
            currentRow.subject === prevRow.subject &&
            currentRow.studentId === prevRow.studentId
        } else if (header.merge === "self") {
          // 科目列根据自身值且studentId相同时合并
          shouldMerge =
            currentRow[header.prop] === prevRow[header.prop] &&
            currentRow.studentId === prevRow.studentId
        }

        if (shouldMerge) {
          count++
        } else {
          if (count > 1) {
            merges.push({
              s: { r: startRow, c: colIndex },
              e: { r: startRow + count - 1, c: colIndex },
            })
          }
          startRow = i + 1
          count = 1
        }
      }

      // 处理最后一组
      if (count > 1) {
        merges.push({
          s: { r: startRow, c: colIndex },
          e: { r: startRow + count - 1, c: colIndex },
        })
      }
    })

    // 添加合并单元格配置
    ws["!merges"] = merges

    // 设置行高
    ws["!rows"] = Array(data.length + 1).fill({ hpt: 30 })

    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(workbook, ws, "学生学习数据")
    const fileName = `学生学习数据${$g
      .dayjs(new Date())
      .format("YYYY-MM-DD HH:mm:ss")}.xlsx`
    // 导出文件
    XLSX.writeFile(workbook, fileName)

    return true
  } catch (error) {
    console.error("导出失败:", error)
    return false
  }
}
