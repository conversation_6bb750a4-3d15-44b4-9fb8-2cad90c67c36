<template>
  <div class="learning-statistics-container-main">
    <g-form
      :formOptions="formOptions"
      :tableOptions="tableOptions"
      @search="initData"
      @reset="initData"
    >
    </g-form>
    <g-table
      :tableOptions="tableOptions"
      @changePage="initData"
      :span-method="objectSpanMethod"
    >
      <template #header-right>
        <n-button type="primary" @click="exportExcel">导出</n-button>
      </template>
      <template #subject="{ row }">
        <div>
          <div>{{ row.subject ?? "-" }}</div>
          <div class="text-primary" v-if="row.subject">
            视频总进度{{ row.videoProgress }}
          </div>
        </div>
      </template>
      <template #chapterTitle="{ row }">
        <div
          class="text-left"
          :class="{
            'pl-20px  text-gray-default': row?.parentSysTextbooksCatalogId != 0,
          }"
        >
          {{ row.chapterTitle ?? "-" }}
        </div>
      </template>
    </g-table>
  </div>
</template>

<script setup lang="ts">
import { exportToExcel } from "./exportExcel"
import { getLearningStatistics } from "@/api/school"
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  filter: true,
  items: {
    date: {
      type: "daterange",
      label: "时间范围",
      showLabel: false,
    },
    keyword: {
      type: "text",
      label: "学生姓名",
      showLabel: false,
    },
  },
  data: {
    date: null,
    keyword: "",
  },
})
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      prop: "name",
      label: "姓名",
    },
    {
      prop: "gender",
      label: "性别",
    },
    {
      prop: "studentId",
      label: "学号",
    },
    {
      prop: "subject",
      label: "科目",
      slot: true,
    },
    {
      prop: "status",
      label: "是否使用错题本",
    },
    {
      prop: "chapterTitle",
      label: "章节信息",
      slot: true,
    },
    {
      prop: "startTime",
      label: "开始观看时间",
    },
    {
      prop: "endTime",
      label: "最后观看时间",
    },
    {
      prop: "duration",
      label: "观看时长",
    },
    {
      prop: "watchProgress",
      label: "视频观看进度",
    },
    {
      prop: "exerciseCount",
      label: "练习题目数",
    },
    {
      prop: "exerciseProgress",
      label: "练习题目进度",
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})
onBeforeMount(async () => {
  getTime()
  await initData()
})
/* 获取时间 */
function getTime() {
  // 获取当前日期
  const currentDate = new Date()
  // 计算30天前的日期
  const startDate = new Date(currentDate)
  startDate.setDate(currentDate.getDate() - 30)
  // 格式化当前日期为 YYYY-MM-DD
  const formatCurrentDate = `${currentDate.getFullYear()}-${String(
    currentDate.getMonth() + 1,
  ).padStart(2, "0")}-${String(currentDate.getDate()).padStart(2, "0")}`
  // 格式化30天前的日期为 YYYY-MM-DD
  const formatStartDate = `${startDate.getFullYear()}-${String(
    startDate.getMonth() + 1,
  ).padStart(2, "0")}-${String(startDate.getDate()).padStart(2, "0")}`
  formOptions.data.date = [formatStartDate, formatCurrentDate]
}

async function initData() {
  try {
    tableOptions.loading = true
    let res = await getLearningStatistics({
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
      startTime: formOptions.data.date[0],
      endTime: formOptions.data.date[1],
      keyword: formOptions.data.keyword,
    })
    tableOptions.pageOptions.total = res.total
    tableOptions.data = processData(res.list)
    tableOptions.loading = false
  } catch (err) {
    tableOptions.data = []
    tableOptions.loading = false
    console.log(err)
  }
}
let genderMap = {
  0: "未知",
  1: "男",
  2: "女",
}
// 处理数据，将嵌套数据转换为扁平结构
function processData(rawData) {
  return rawData.reduce((result, student) => {
    // 基础学生信息
    const baseStudentInfo = {
      name: student.studentName,
      gender: genderMap[student.gender],
      studentId: student.thirdUniqueId,
    }

    // 如果没有科目列表或为空，返回基础学生信息
    if (!student.subjectList?.length) {
      return [...result, baseStudentInfo]
    }

    const studentEntries = student.subjectList.reduce((coursesAcc, course) => {
      // 包含科目信息的基础记录
      const baseSubjectInfo = {
        ...baseStudentInfo,
        subject: course.sysSubjectName,
        sysSubjectId: course.sysSubjectId,
        videoProgress: (course.videoWatchProgress * 100).toFixed(0) + "%",
        status: course.isUsingErrorBook == 1 ? "否" : "是",
      }

      // 如果没有目录列表或为空，返回带科目信息的记录
      if (!course.catalogList?.length) {
        return [...coursesAcc, baseSubjectInfo]
      }

      // 如果有目录，处理每个目录项
      const chapterEntries = course.catalogList.map((chapter) => ({
        ...baseSubjectInfo,
        chapterTitle: chapter.sysTextbooksCatalogNameAlias,
        startTime: chapter.startTime ?? "",
        endTime: chapter.endTime ?? "",
        duration: chapter.videoWatchDuration ?? "",
        watchProgress: (chapter.videoWatchProgress * 100).toFixed(0) + "%",
        exerciseCount: chapter.exerciseQuestionNum,
        parentSysTextbooksCatalogId: chapter.parentSysTextbooksCatalogId,
        exerciseProgress:
          (chapter.exerciseQuestionProgress * 100).toFixed(0) + "%",
      }))

      return [...coursesAcc, ...chapterEntries]
    }, [])

    return [...result, ...studentEntries]
  }, [] as any)
}

// 计算合并单元格
function objectSpanMethod({ row, column, rowIndex, columnIndex }) {
  if (columnIndex < 3) {
    // 姓名、性别、账号列
    const rows = getSpanRows(rowIndex)
    if (rows > 0) {
      return {
        rowspan: rows,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }

  if (columnIndex >= 3 && columnIndex <= 4) {
    // 科目、是否使用错题本列
    const rows = getSubjectSpanRows(rowIndex)
    if (rows > 0) {
      return {
        rowspan: rows,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
}

// 计算学生信息跨行数
function getSpanRows(rowIndex) {
  let count = 0
  const studentId = tableOptions.data[rowIndex].studentId

  for (let i = rowIndex; i < tableOptions.data.length; i++) {
    if (tableOptions.data[i].studentId === studentId) {
      count++
    } else {
      break
    }
  }

  if (rowIndex > 0 && tableOptions.data[rowIndex - 1].studentId === studentId) {
    return 0
  }

  return count
}

// 计算科目信息跨行数
function getSubjectSpanRows(rowIndex) {
  let count = 0
  const sysSubjectId = tableOptions.data[rowIndex].sysSubjectId
  const studentId = tableOptions.data[rowIndex].studentId

  for (let i = rowIndex; i < tableOptions.data.length; i++) {
    if (
      tableOptions.data[i].sysSubjectId === sysSubjectId &&
      tableOptions.data[i].studentId === studentId
    ) {
      count++
    } else if (tableOptions.data[i].studentId !== studentId) {
      break
    }
  }

  if (
    rowIndex > 0 &&
    tableOptions.data[rowIndex - 1].sysSubjectId === sysSubjectId &&
    tableOptions.data[rowIndex - 1].studentId === studentId
  ) {
    return 0
  }

  return count
}

// 导出数据
async function exportExcel() {
  let res = await getLearningStatistics({
    page: 1,
    pageSize: tableOptions.pageOptions.total,
    startTime: formOptions.data.date[0],
    endTime: formOptions.data.date[1],
    keyword: formOptions.data.keyword,
  })
  $g.msg("导出数据中。。。")
  exportToExcel(processData(res.list))
}
</script>

<style lang="scss" scoped></style>
