<template>
  <div class="ai-container-main">
    <div class="text-center text-16px text-[#333]">
      Wonderful！计划生成完成，快快查阅吧！
    </div>
    <div
      class="w-full flex items-center px-15px h-40px leading-[40px] bg-[#eee] mt-15px"
    >
      <div class="w-0 whitespace-nowrap">{{ pageData?.userName }}同学</div>
      <div class="flex-grow text-center">
        {{ pageData?.sysGradeName }} {{ pageData?.subjectName }}
        {{ pageData?.weekNum }}周学习计划
      </div>
    </div>
    <div class="mt-15px w-full flex justify-center">
      <g-table :tableOptions="tableOptions">
        <template #weekName="{ row }">
          <n-popover
            trigger="manual"
            :show="row.isInterim || row.isPeriod"
            placement="left"
          >
            <template #trigger> {{ row.weekName }} </template>
            <span class="week-text">{{ row.isInterim ? "期中" : "期末" }}</span>
          </n-popover>
        </template>
        <template #studyTimes="{ row }">第{{ row.studyTimes }}次</template>
        <template #range="{ row }">
          {{ row.startTime }}-{{ row.endTime }}
        </template>
        <template #newStudy="{ row }">
          <template v-if="row.studyPlan.length">
            <div v-for="(item, idx) in row.studyPlan" :key="idx">
              {{ item.textbookName + " " + item.chapterName }}
            </div>
          </template>
          <span v-else class="text-[#cccccc]">无新学内容</span>
        </template>
        <template #review="{ row }">
          <template v-if="row.reviewPlan.length">
            <div v-for="(item, idx) in row.reviewPlan" :key="idx">
              {{ item.textbookName + " " + item.chapterName }}
            </div>
          </template>
          <span v-else class="text-[#cccccc]">无复习</span>
        </template>
      </g-table>
    </div>
    <div class="w-full flex justify-center mt-50px">
      <n-button
        type="primary"
        size="large"
        class="w-300px text-16px"
        @click="goBack"
      >
        返回上一步重新制定
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/stores/modules/user"

const userStore = useUserStore()
const router = useRouter()

let pageData = $ref<any>(null)

const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: false,
  column: [
    { prop: "weekName", label: "周次", slot: true },
    { prop: "studyTimes", label: "学习次数", slot: true, width: "100px" },
    { prop: "range", label: "时间范围", slot: true, width: "200px" },
    { prop: "newStudy", label: "新学习内容", slot: true, width: "300px" },
    { prop: "review", label: "复习的内容", slot: true, width: "300px" },
  ],
  data: [],
})

function goBack() {
  router.back()
}

onBeforeMount(() => {
  pageData = userStore.studyProject
  tableOptions.data = pageData.tableList
})

onBeforeUnmount(() => {
  userStore.studyProject = null
})
</script>

<style lang="scss" scoped></style>
<style lang="scss">
.week-text {
  color: red;
}
</style>
