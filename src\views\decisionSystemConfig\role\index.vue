<template>
  <div class="role-container-main flex">
    <div class="w-1/4 relative flex flex-col my-role">
      <g-icon
        name="ri-add-fill self-end"
        size=""
        color=""
        @click="showAddDialog = true"
      />
      <n-scrollbar style="max-height: calc(100vh - 60px - 20px * 2 - 75px)">
        <div class="flex flex-col">
          <div class="flex mt-8px">
            <span class="mr-40px text-center w-2/5">权限角色</span
            ><span class="text-gray-default w-2/5 text-center">关联账户数</span>
          </div>
          <div
            v-for="role in roleList"
            :key="role.roleName"
            class="flex mt-8px items-center"
          >
            <span
              class="w-2/5 text-center mr-40px br-[4px] cursor-pointer py-2px"
              :class="{ active: curPrivilegeRoleId === role.privilegeRoleId }"
              @click="curPrivilegeRoleId = role.privilegeRoleId"
              >{{ role.roleName }}</span
            ><span
              class="w-2/5 text-center underline text-primary cursor-pointer"
              @click="lookAccount(role)"
              >{{ role.relatedCount }}</span
            >
            <g-icon
              class="flex-grow mr-10px"
              name="ri-delete-bin-line"
              @click="deleteRole(role)"
              color="red"
              v-if="!role.superAdmin"
            ></g-icon>
          </div>
        </div>
      </n-scrollbar>
    </div>
    <div class="bg-[#efeff5] w-1px mx-20px"></div>
    <div class="flex-grow">
      <g-tree2
        :treeData="treeData"
        show-checkbox
        :multiple="true"
        :defaultProps="{ label: 'name' }"
        class="flex-1"
        node-key="privilegeFlagSn"
        v-loading="treeLoading"
        default-expand-all
        :default-checked-keys="checkedArr"
        @check-change="handleCheckChange"
      ></g-tree2>

      <n-space justify="center" class="mt-20px">
        <n-button type="primary" :loading="saveLoading" @click="saveAuth">
          保存
        </n-button>
      </n-space>
    </div>
    <g-dialog
      title="关联账号清单"
      v-model:show="showAccountDialog"
      :show-footer="false"
      width="720"
    >
      <g-table
        :tableOptions="tableOptions"
        @changePage="getAccountList"
        :autoTop="false"
      >
        <template #cz="{ row }">
          <n-button text type="primary" class="mr-6px" @click="unite(row)"
            >解绑</n-button
          >
        </template>
      </g-table>
    </g-dialog>
    <g-dialog
      title="新增角色"
      :formOptions="formOptions"
      v-model:show="showAddDialog"
      @confirm="confirm"
    >
      <g-form :formOptions="formOptions"> </g-form>
    </g-dialog>
  </div>
</template>
<script setup lang="ts">
import {
  getRole,
  addRole,
  delRole,
  getRoleAccount,
  userUntie,
  getAuthTree,
  saveAuthTree,
} from "@/api/userMgt"
interface Tree {
  name: string
  [propName: string]: any
}
let treeData = $ref<any>([])
let roleList = $ref<any>([])
let saveLoading = $ref(false)
const formOptions = reactive({
  ref: null as any,
  loading: false,
  items: {
    name: {
      type: "text",
      label: "角色名称",
      maxlength: 10,
      width: "250px",
      rule: true,
    },
    sort: {
      type: "number",
      label: "排序值",
      width: "250px",
    },
  },
  data: {
    name: null,
    sort: 0,
  },
})
let showAccountDialog = $ref(false)
let showAddDialog = $ref(false)
const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: true,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
  column: [
    { prop: "accountName", label: "账号" },
    { prop: "userName", label: "姓名" },
    { prop: "mobile", label: "手机号", width: "150px" },
    { prop: "lastLoginTime", label: "上次登录", width: "220px" },
    { prop: "cz", label: "操作", slot: true },
  ],
  data: [{ a: 1, b: 2, c: 3 }],
})

const confirm = async () => {
  try {
    await addRole(formOptions.data)
    $g.msg("添加成功")
    formOptions.loading = false
    initData()
    showAddDialog = false
  } catch {
    formOptions.loading = false
  }
}
let treeLoading = $ref(false)

let curPrivilegeRoleId = ref<any>(null)
const initData = async () => {
  let res = await getRole({
    needCount: true,
  })
  roleList = res
  if (curPrivilegeRoleId.value == null)
    curPrivilegeRoleId.value = roleList[0]?.privilegeRoleId
}
const getChecked = (data: Array<any>) => {
  if (!data.length) return
  data.forEach((item) => {
    if (item.check) checkedArr.push(item.privilegeFlagSn)
    if (item.children) {
      getChecked(item.children)
    }
  })
}
//切换角色刷新树
watch(curPrivilegeRoleId, async (val) => {
  treeLoading = true
  checkedArr = []
  let res = await getAuthTree({ privilegeRoleId: val })
  treeData = res
  getChecked(res)
  treeLoading = false
})
let checkedArr = $ref<any>([])
const handleCheckChange = (data: Tree, checked: boolean) => {
  let index = checkedArr.indexOf(data.privilegeFlagSn)
  if (checked) {
    if (index > -1) return
    checkedArr.push(data.privilegeFlagSn)
  } else {
    if (index < 0) return
    checkedArr.splice(index, 1)
  }
}
const saveAuth = async () => {
  saveLoading = true
  let params = {
    privilegeRoleId: curPrivilegeRoleId.value,
    privilegeFlagSns: checkedArr,
  }
  try {
    await saveAuthTree(params)
    $g.msg("保存成功")
    saveLoading = false
  } catch {
    saveLoading = false
  }
}
let lookPrivilegeRoleId = $ref()
const getAccountList = async () => {
  tableOptions.loading = true
  let res = await getRoleAccount({
    page: tableOptions.pageOptions.page,
    pageSize: 10,
    privilegeRoleId: lookPrivilegeRoleId,
  })
  tableOptions.loading = false
  tableOptions.pageOptions.total = res.total
  tableOptions.data = res.list
}
const lookAccount = (row) => {
  lookPrivilegeRoleId = row.privilegeRoleId
  showAccountDialog = true
  getAccountList()
}

const unite = (data) => {
  $g.confirm({
    content: "确定解绑该用户吗？",
  })

    .then(async () => {
      await userUntie({
        privilegeUserRoleId: data.privilegeUserRoleId,
      })
      getAccountList()
      initData()
      $g.msg("解绑成功")
    })
    .catch(() => {})
}
const deleteRole = (data) => {
  $g.confirm({
    content: "只能删除关联账户数为0的角色，确定要删除吗？",
  })
    .then(async () => {
      if (data.relatedCount !== 0) {
        $g.msg("只能删除关联账户数为0的角色", "info")
        return
      }
      await delRole({ id: data.privilegeRoleId })
      let index = roleList.findIndex(
        (item) => item.privilegeRoleId == data.privilegeRoleId,
      )
      if (index > -1) roleList.splice(index, 1)
      if (data.privilegeRoleId == curPrivilegeRoleId.value) {
        curPrivilegeRoleId.value = roleList[0].privilegeRoleId
      }
      $g.msg("删除成功")
    })
    .catch(() => {})
}
onBeforeMount(() => {
  initData()
})
</script>
<style scoped lang="scss">
:deep() {
  .my-role {
    .n-scrollbar-container {
      background: none;
    }
  }
}
.active {
  background: #1ea0f0;
  color: white;
}
</style>
