<template>
  <div>
    <g-dialog
      :title="$g.tool.isTrue(data) ? '编辑学校' : '创建学校'"
      :formOptions="formOptions"
      v-bind="$attrs"
      @closeX="closeX"
      @cancel="cancel"
      @confirm="confirm"
      to="#app"
      class="custom-dialog"
    >
      <g-form :formOptions="formOptions">
        <template #img>
          <g-upload
            v-model:fileList="formOptions.data.img"
            type="image-card"
            :max="1"
            :fileSize="20 * 1024 * 1024"
            accept=".jpg,.png"
            tips="仅支持.jpg .png 且大小不超过20MB"
          ></g-upload>
        </template>
        <template #provinceIds>
          <n-cascader
            class="custom-cascader"
            filterable
            placeholder="选择所属地区"
            v-model:value="formOptions.data.provinceIds"
            :options="formOptions.items.provinceIds.options"
            :max-tag-count="1"
            check-strategy="parent"
          ></n-cascader>
        </template>
      </g-form>
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  getInstitutionsApi,
  addSchoolApi,
  editSchoolApi,
  getSchoolDetailApi,
} from "@/api/school"
import { getAreaList } from "@/api/resourceMgt"
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
})
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  items: {
    group: {
      type: "select",
      label: "所属集团",
      options: [],
      labelField: "companyName",
      valueField: "companyId",
      width: "280px",
      rule: true,
    },
    schoolName: {
      type: "text",
      label: "学校名称",
      maxlength: 50,
      width: "280px",
      rule: true,
    },
    img: {
      type: "",
      label: "自习室Logo",
      labelWidth: "120px",
      slot: true,
    },
    zxsName: {
      type: "text",
      label: "自习室名称",
      maxlength: 50,
      labelWidth: "120px",
      width: "280px",
    },
    url: {
      type: "text",
      label: "来源网址",
      placeholder: "用户退出登陆后的返回地址",
      labelWidth: "120px",
      width: "280px",
    },
    hidePcNav: {
      type: "radio",
      label: "隐藏pc导航条",
      options: [
        { label: "不隐藏", value: 1 },
        { label: "隐藏", value: 2 },
      ],
      labelWidth: "120px",
      width: "280px",
    },
    provinceIds: {
      type: "select",
      label: "所属地区",
      options: [],
      span: 19,
      slot: true,
    },
  },
  data: {
    group: null,
    schoolName: null,
    img: [],
    zxsName: null,
    url: null,
    hidePcNav: null,
    provinceIds: null,
  },
})
const attrs = useAttrs()
async function getInstitutions() {
  try {
    const res = await getInstitutionsApi()
    const list = res || []
    formOptions.items.group.options =
      list?.map((item) => {
        return { ...item, disabled: !item?.canSelect }
      }) || []
  } catch (err) {
    console.log("err", err)
  }
}
async function getSchoolDetail() {
  try {
    const res = await getSchoolDetailApi({
      schoolId: props.data.schoolId,
    })
    formOptions.data.schoolName = res?.schoolName
    formOptions.data.group = res?.companies[0]?.companyId
    formOptions.data.img = res?.schoolLogo ? [{ url: res?.schoolLogo }] : []
    formOptions.data.zxsName = res?.schoolTitle
    formOptions.data.url = res?.sourceUrl
    formOptions.data.hidePcNav = res?.hidePcNav
    formOptions.data.provinceIds = res.districtId
      ? res.provinceId + "-" + res.cityId + "-" + res.districtId
      : res.cityId
      ? res.provinceId + "-" + res.cityId
      : res.provinceId
      ? res.provinceId
      : null
  } catch (err) {
    console.log("err", err)
  }
}
watch(
  () => attrs.show,
  async (val) => {
    if (val) {
      if (!formOptions.items.provinceIds.options.length) getArea()
      formOptions.items.group.disabled = $g.tool.isTrue(props.data)
      await getInstitutions()
      if ($g.tool.isTrue(props.data)) {
        getSchoolDetail()
      } else {
        formOptions.data.hidePcNav = 1
      }
    }
  },
)

//处理地域参数
function handleAreaParams() {
  const idArr = formOptions.data.provinceIds?.split("-")
  return {
    provinceId: idArr?.[0] || null,
    cityId: idArr?.[1] || null,
    districtId: idArr?.[2] || null,
  }
}

async function getArea() {
  let res = await getAreaList({
    level: 3,
  })
  formOptions.items.provinceIds.options = (res || []).map((h) => {
    if (h.areaName.includes("全国")) return
    return {
      ...h,
      label: h.shortName,
      value: h.sysAreaId, //区分省市
      children: h.children?.map((v) => {
        return {
          ...v,
          label: v.areaName,
          value: h.sysAreaId + "-" + v.sysAreaId, //区分省市,选了市要把父级放在省里,
          children: v.children?.map((vv) => {
            return {
              ...vv,
              label: vv.areaName,
              value: h.sysAreaId + "-" + v.sysAreaId + "-" + vv.sysAreaId, //选了区县要把父级放在省、市里,
            }
          }),
        }
      }),
    }
  })
  formOptions.items.provinceIds.options =
    formOptions.items.provinceIds.options.filter((item) => item)
}

async function confirm() {
  try {
    if ($g.tool.isTrue(props.data)) {
      const res = await editSchoolApi({
        schoolId: props.data.schoolId,
        schoolName: formOptions.data.schoolName,
        schoolLogo: formOptions.data.img[0]?.fullUrl,
        schoolTitle: formOptions.data.zxsName,
        sourceUrl: formOptions.data.url,
        hidePcNav: formOptions.data.hidePcNav,
        ...handleAreaParams(),
      })
      $g.msg("编辑成功")
    } else {
      const res = await addSchoolApi({
        companyId: formOptions.data.group,
        schoolName: formOptions.data.schoolName,
        schoolLogo: formOptions.data.img[0]?.fullUrl,
        schoolTitle: formOptions.data.zxsName,
        sourceUrl: formOptions.data.url,
        hidePcNav: formOptions.data.hidePcNav,
        ...handleAreaParams(),
      })
      $g.msg("创建成功")
    }
    formOptions.loading = false
    emit("update:show", false)
    emit("refresh")
  } catch (err) {
    console.log(err)
    formOptions.loading = false
  }
}
const emit = defineEmits(["update:show", "refresh"])
function closeX() {
  emit("update:show", false)
}
function cancel() {
  emit("update:show", false)
}
</script>

<style lang="scss" scoped>
:global(
    .custom-dialog
      .v-binder-follower-content
      .n-cascader-submenu:first-child
      .n-checkbox
  ) {
  display: none;
}
</style>
