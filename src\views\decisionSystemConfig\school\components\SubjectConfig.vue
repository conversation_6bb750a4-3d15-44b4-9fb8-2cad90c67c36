<template>
  <g-dialog
    :title="`学科配置-${data?.schoolName ?? ''}`"
    width="900"
    @closeX="closeX"
    @cancel="cancel"
    @confirm="confirm"
    @after-enter="afterEnter"
    @handleClose="onClose"
    :formOptions="formOptions"
  >
    <div class="text-primary pb-20px">
      提示：学校的学科配置主要应用智习室PC：自主学习（同步课堂、我的任务）、周清消缺，新版启鸣学习机pad：同步课堂、
      我的任务、试卷库-学科下拉筛选
    </div>

    <g-loading class="h-200px" v-if="loading"></g-loading>
    <div v-else class="max-h-[700px] overflow-y-auto">
      <g-form :formOptions="formOptions">
        <template v-for="(item, key) in formOptions.items" :key="key" #[key]>
          <CheckboxWithAll :prop="key" />
        </template>
      </g-form>
    </div>
  </g-dialog>
</template>

<script setup lang="ts" name="SchoolSubjectConfig">
import { NCheckbox, NCheckboxGroup, NSpace } from "naive-ui"
import { getSchoolSubjectConfig, saveSchoolSubjectConfig } from "@/api/school"

const emit = defineEmits(["update:show"])
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
})

const rule = {
  required: true,
  message: "年级下学科至少勾选1项，请检查",
  type: "array",
}

const formOptions = reactive<any>({
  ref: null as any,
  loading: true,
  items: {},
  data: {},
})

function closeX() {
  emit("update:show", false)
}

function cancel() {
  emit("update:show", false)
}

async function confirm() {
  await saveSchoolSubjectConfigApi()
}

async function afterEnter() {
  await getSchoolSubjectConfigApi()
}

const loading = ref(false)

function onClose() {
  formOptions.loading = true
  loading.value = true
  formOptions.data = {}
  formOptions.items = {}
}

async function getSchoolSubjectConfigApi() {
  loading.value = true
  formOptions.loading = true

  const data = await getSchoolSubjectConfig({
    schoolId: props.data.schoolId,
  })

  formOptions.items = data.reduce((acc, item) => {
    acc[item.sysGradeId] = {
      slot: true,
      label: item.sysGradeName,
      options: item.sysSubjectList.map((item) => ({
        label: item.sysSubjectName,
        value: item.sysCourseId,
      })),
      rule,
    }
    return acc
  }, {})

  formOptions.data = data.reduce((acc, item) => {
    acc[item.sysGradeId] = item.sysSubjectList
      .filter((item) => item.checked)
      .map((item) => item.sysCourseId)
    return acc
  }, {})

  formOptions.loading = false
  loading.value = false
}

async function saveSchoolSubjectConfigApi() {
  try {
    formOptions.loading = true

    await saveSchoolSubjectConfig({
      schoolId: props.data.schoolId,
      gradeSubjectList: Object.keys(formOptions.data).map((item) => ({
        sysGradeId: parseInt(item),
        sysCourseIdList: formOptions.data[item],
      })),
    })

    $g.msg("保存成功", "success")

    emit("update:show", false)
  } catch (error) {
    console.error(error)
  } finally {
    formOptions.loading = false
  }
}

const CheckboxWithAll = defineComponent({
  props: {
    prop: {
      type: [String, Number],
      required: true,
    },
  },
  setup(props) {
    const options = formOptions.items[props.prop].options
    const allValues = options.map((item) => item.value)

    // 计算全选状态
    const isAllChecked = computed(() => {
      return (
        formOptions.data[props.prop].length === options.length &&
        options.every((item) =>
          formOptions.data[props.prop].includes(item.value),
        )
      )
    })

    // 计算部分选择状态
    const isIndeterminate = computed(() => {
      return formOptions.data[props.prop].length > 0 && !isAllChecked.value
    })

    // 处理全选点击
    const handleCheckAllChange = (checked) => {
      formOptions.data[props.prop] = checked ? [...allValues] : []
    }

    return () =>
      h(NSpace, {}, () => [
        h(
          NCheckbox,
          {
            checked: isAllChecked.value,
            indeterminate: isIndeterminate.value,
            onUpdateChecked: handleCheckAllChange,
          },
          () => "全部",
        ),
        h(
          NCheckboxGroup,
          {
            value: formOptions.data[props.prop],
            "onUpdate:value": (val) => {
              formOptions.data[props.prop] = val
            },
          },
          () => [
            h(NSpace, {}, () =>
              options.map((item) =>
                h(NCheckbox, { value: item.value }, () => item.label),
              ),
            ),
          ],
        ),
      ])
  },
})
</script>
