<template>
  <div class="school-container-main">
    <g-form
      @search="getList"
      @reset="getList"
      :formOptions="filterFormOptions"
      :tableOptions="tableOptions"
    >
    </g-form>
    <g-table
      :tableOptions="tableOptions"
      max-height="calc(100vh - 250px)"
      @changePage="getList"
    >
      <template #header-right>
        <n-button type="primary" @click="handleAdd">
          <g-icon name="add-line" size="" color="" />
          创建学校
        </n-button>
      </template>
      <!-- isOpenSpecialService -->
      <template #isOpenService="{ row }">
        <n-switch
          :checked-value="2"
          :unchecked-value="1"
          :value="row.isOpenService"
          :loading="row.serviceLoading"
          @update:value="changeSevice(row)"
        ></n-switch>
      </template>
      <template #cz="{ row }">
        <n-button
          type="primary"
          text
          @click="handleSync(row)"
          :loading="row.syncLoading"
          >数据同步</n-button
        >
        <n-button
          class="ml-[10px]"
          type="primary"
          text
          @click="handleSubjectConfig(row)"
          >配置学科</n-button
        >
        <n-button class="ml-[10px]" type="success" text @click="handleEdit(row)"
          >编辑</n-button
        >
      </template>

      <template #isOpenFace="{ row }">
        <n-switch
          :checked-value="2"
          :unchecked-value="1"
          :value="row.isOpenFace"
          :loading="row.faceLoading"
          :disabled="row.isTianLiSchool === 2"
          @update:value="changeOpenFace(row)"
        ></n-switch>
      </template>
      <template #schoolLogo="{ row }">
        <img v-if="row?.schoolLogo" :src="row?.schoolLogo" class="w-30px" />
        <span v-else>-</span>
      </template>
    </g-table>
    <Add @refresh="getList" v-model:show="showDialog" :data="addData"></Add>
    <SubjectConfig
      v-model:show="showSubjectConfig"
      :data="subjectConfigSchool"
    ></SubjectConfig>
  </div>
</template>
<script setup lang="ts">
import {
  getSchool,
  getCompany,
  setFaceSwitch,
  setService,
  dataSync,
} from "@/api/school"
import Add from "./components/Add.vue"
import SubjectConfig from "./components/SubjectConfig.vue"
let showDialog = $ref<any>(false)
let addData = $ref<any>(null)
const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: true,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 20,
  },
  column: [
    { prop: "schoolName", label: "学校名称" },
    { prop: "companyName", label: "所属集团" },
    { prop: "allCount", label: "教师人数/学生人数/家长人数" },
    {
      prop: "isOpenService",
      label: "智习室开关",
      slot: true,
    },
    {
      prop: "isOpenFace",
      label: "人脸验证",
      slot: true,
    },
    {
      prop: "schoolLogo",
      label: "系统logo",
      slot: true,
    },
    {
      prop: "schoolTitle",
      label: "自习室名称",
    },
    { prop: "cz", label: "操作", slot: true },
  ],
  data: [],
})
const filterFormOptions = reactive<any>({
  ref: null as any,
  filter: true,
  labelWidth: "84px",
  items: {
    companyId: {
      type: "select",
      label: "集团筛选",
      showLabel: true,
      width: "200px",
      options: [],
      labelField: "shortName",
      valueField: "companyId",
    },
    schoolName: {
      type: "text",
      label: "描述",
      placeholder: "搜索学校名称",
      showLabel: false,
      width: "200px",
    },
  },
  // 列表接口使用到的筛选值,及时搜索时使用
  data: {
    companyId: "",
    schoolName: null,
  },
  // 列表接口使用到的筛选值,非及时搜索时使用
  filterData: {},
})
const getList = async () => {
  try {
    tableOptions.loading = true

    let res = await getSchool({
      ...filterFormOptions.filterData,
      ...tableOptions.pageOptions,
    })
    tableOptions.data = res.list?.map((item) => {
      return {
        ...item,
        allCount:
          item.teacherCount + "/" + item.studentCount + "/" + item.parentCount,
        realArea: getRealArea(item),
      }
    })
    tableOptions.pageOptions.total = res.total
  } catch {
    tableOptions.data = []
    tableOptions.pageOptions
  } finally {
    tableOptions.loading = false
  }
}
const getRealArea = (item) => {
  let res = [
    item.provinceName,
    item.cityName,
    item.districtName,
    item.streetName,
    item.townName,
  ]
  return res.filter(Boolean).join("/")
}
const initData = async () => {
  let res = await getCompany()
  filterFormOptions.items.companyId.options = [
    { companyId: "", shortName: "全部" },
    ...res,
  ]
  getList()
}
onBeforeMount(() => {
  initData()
})
function handleAdd() {
  showDialog = true
  addData = null
}
function handleEdit(row) {
  showDialog = true
  addData = row
}
async function changeSevice(row) {
  try {
    row.serviceLoading = true
    await setService({
      schoolId: row.schoolId,
    })
    // setTimeout(() => {

    // }, 200)
    row.isOpenService = row.isOpenService === 1 ? 2 : 1
    row.serviceLoading = false
  } catch {
    row.serviceLoading = false
  }
}

async function changeOpenFace(row) {
  try {
    row.faceLoading = true
    await setFaceSwitch({
      schoolId: row.schoolId,
    })
    // setTimeout(() => {

    // }, 200)
    row.isOpenFace = row.isOpenFace === 1 ? 2 : 1
    row.faceLoading = false
  } catch {
    row.faceLoading = false
  }
}

async function handleSync(row) {
  try {
    row.syncLoading = true
    await dataSync({
      schoolId: row.schoolId,
    })
    row.syncLoading = false
    $g.msg("已同步学校数据到自习室，预计耗时1分钟")
  } catch {
    row.syncLoading = false
  }
}

let showSubjectConfig = $ref<any>(false)
let subjectConfigSchool = $ref<Record<string, any>>({})
function handleSubjectConfig(row) {
  showSubjectConfig = true
  subjectConfigSchool = row
}
</script>
