<template>
  <div>
    <g-dialog
      title="已开通活动"
      v-model:show="showDialog"
      width="600"
      :showFooter="false"
    >
      <g-table :tableOptions="tableOptions" @changePage="getList"></g-table>
    </g-dialog>
  </div>
</template>
<script lang="ts" setup>
import { getActivityListApi } from "@/api/studentType"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentId: {
    type: Number,
  },
})
const tableOptions = reactive({
  ref: null,
  loading: false,
  column: [
    {
      prop: "activityName",
      label: "活动名称",
    },
    {
      prop: "sysStageName",
      label: "学段",
    },
    {
      prop: "sysGradeList",
      label: "年级",
      formatter(row) {
        if (!row.sysGradeList?.length) {
          return "-"
        }
        return $g._.map(row.sysGradeList, "sysGradeName").join("、")
      },
    },
    {
      prop: "createTime",
      label: "活动创建时间",
      formatter(row) {
        if (!row.createTime) {
          return "-"
        }
        return $g.dayjs(row.createTime).format("YYYY-MM-DD")
      },
    },
  ],
  data: [] as any,
  pageOptions: {
    page: 1,
    page_size: 5,
    total: 0,
  },
})

watch(
  () => props.show,
  () => {
    if (props.show) {
      tableOptions.pageOptions.page = 1
      tableOptions.pageOptions.total = 0
      getList()
    }
  },
)
async function getList() {
  try {
    tableOptions.loading = true
    const res = await getActivityListApi({
      schoolStudentId: props?.currentId,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.data = res?.list || []
    tableOptions.pageOptions.total = res?.total || 0
    tableOptions.loading = false
  } catch (err) {
    tableOptions.loading = false
  }
}
const emit = defineEmits(["update:show"])
const showDialog = useVModel(props, "show", emit)
</script>
<style scoped lang="scss">
:deep() {
  .n-input:not(.n-input--autosize) {
    width: 250px;
  }
}
</style>
