<template>
  <div>
    <g-dialog
      :title="$g.tool.isTrue(studentInfo) ? '编辑学生' : '新建学生'"
      v-model:show="showDialog"
      :formOptions="formOptions"
      @confirm="confirm"
      width="600"
    >
      <g-form :formOptions="formOptions"> </g-form>

      <div class="flex relative">
        <div class="ml-[20px] mr-[10px] mt-[2px]">家长电话：</div>
        <div>
          <n-form-item
            v-for="(item, index) in parentMobileList"
            :key="item?.id"
            ref="itemRef"
            class="mt-[-28px]"
            :rule="{
              trigger: ['change', 'blur'],
              required: false,
              message: '请输入正确的手机号格式',
              validator() {
                if (item?.value && !/^1[3-9][0-9]{9}$/.test(item?.value)) {
                  return false
                }
                return true
              },
            }"
          >
            <n-input
              placeholder="11位纯数字"
              clearable
              v-model:value="item.value"
            />
            <div
              v-show="$g.tool.isTrue(props.studentInfo)"
              class="flex ml-[10px]"
            >
              <g-icon
                v-show="
                  parentMobileList?.length < 3 &&
                  index == parentMobileList?.length - 1
                "
                name="ri-add-circle-line"
                size="25"
                color=""
                @click="onAdd(item?.id)"
              />
              <g-icon
                v-show="
                  parentMobileList?.length > 1 &&
                  index == parentMobileList?.length - 1
                "
                @click="parentMobileList.splice(index, 1)"
                class="ml-[10px]"
                name="ri-indeterminate-circle-line"
                size="25"
                color=""
              />
            </div>
          </n-form-item>
        </div>
      </div>
      <div class="flex">
        <div class="ml-[20px] mr-[10px] mt-[2px] w-[70px] text-right">
          学校：
        </div>
        <n-form-item class="mt-[-28px]">
          <div>
            <n-select
              v-model:value="formOptions.data.schoolId"
              :options="schoolIdList"
              filterable
              clearable
              :disabled="$g.tool.isTrue(props.studentInfo)"
              placeholder="请选择学校"
              class="w-[250px]"
            ></n-select>
            <div class="text-[red] mt-[6px]">
              *仅支持关联金字塔后台手动创建的学校，非阅卷系统内的学校
            </div>
          </div>
        </n-form-item>
      </div>
    </g-dialog>
  </div>
</template>
<script lang="ts" setup>
import { addApi, editApi, getStudentCreateSchoolList } from "@/api/studentType"
interface School {
  label: string
  value: number
}
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  studentInfo: {
    type: Object,
  },
  classList: {
    type: Array,
  },
})
let parentMobileList = $ref<any>([])
const formOptions = reactive<any>({
  items: {
    userName: {
      type: "text",
      label: "学生姓名",
      width: "250px",
      clearable: false,
      placeholder: "10个字符以内",
      rule: {
        validator: validateName,
        type: "string",
        required: true,
      },
      maxLength: 10,
    },
    sysGradeId: {
      type: "radio",
      options: [],
      label: "年级",
      rule: true,
    },
  },
  data: { parentMobileList: [], schoolId: null },
})
//学校列表
let schoolIdList = $ref<School[]>([])
let itemRef = $ref<any>(null)

async function getStudentCreateSchoolListApi() {
  try {
    schoolIdList = []
    let res = await getStudentCreateSchoolList()
    schoolIdList = res?.map((item) => {
      return {
        label: item?.schoolName,
        value: item?.schoolId,
      }
    })
    if (
      schoolIdList?.length &&
      schoolIdList.find((item) => item.value == formOptions.data.schoolId)
    ) {
      formOptions.data.schoolId = props.studentInfo?.schoolId
    } else {
      formOptions.data.schoolId = null
    }
  } catch (error) {
    console.log("⚡ error ==> ", error)
  }
}
//姓名校验规则
function validateName(rule, value, callback) {
  if (!value || value.length > 10) {
    callback(new Error("请输入10位字符以内"))
  } else if (!/^[\u4E00-\u9FA5A-Za-z]+$/.test(value)) {
    callback(new Error("请输入汉字或英文字符"))
  }
  callback()
}

watch(
  () => props.show,
  () => {
    if (props.show) {
      formOptions.items.sysGradeId.options = props.classList?.map(
        (item: any) => {
          return {
            label: item?.sysGradeName,
            value: item?.id,
          }
        },
      )
      formOptions.data = $g.tool.isTrue(props.studentInfo)
        ? { ...props.studentInfo }
        : {}
      parentMobileList = props.studentInfo?.parentMobileList?.length
        ? props.studentInfo?.parentMobileList?.map((item, index) => {
            return {
              value: item,
              id: index,
            }
          })
        : [{ value: "", id: 0 }]

      //获取学校列表
      getStudentCreateSchoolListApi()
    }
  },
)

function onAdd(index) {
  if (parentMobileList?.length) {
    parentMobileList?.push({
      value: "",
      id: index + 1,
    })
  }
}
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
async function add() {
  try {
    const res = await addApi({
      userName: formOptions.data.userName,
      sysGradeId: formOptions.data.sysGradeId,
      mobile: parentMobileList?.[0]?.value,
      schoolId: formOptions.data?.schoolId,
    })
    emit("refresh")
  } catch (err) {
    console.log("err", err)
  } finally {
    formOptions.loading = false
  }
}
async function edit() {
  try {
    const res = await editApi({
      accountId: formOptions.data?.accountId,
      userName: formOptions.data?.userName,
      sysGradeId: formOptions.data?.sysGradeId,
      mobileList: ($g._ as any)
        .map(parentMobileList, "value")
        .filter((item) => item),
    })
    emit("refresh")
  } catch (err) {
    console.log("err", err)
  } finally {
    formOptions.loading = false
  }
}
async function confirm() {
  let arr: any = []
  console.log("itemRef", itemRef)
  itemRef.map((ref) => {
    arr.push(ref?.validationErrored)
  })
  console.log("arr", arr)
  if (arr.some((item) => item)) {
    formOptions.loading = false
    return
  }
  if ($g.tool.isTrue(props.studentInfo)) {
    edit()
  } else {
    add()
  }
}
</script>
<style scoped lang="scss">
:deep() {
  .n-input:not(.n-input--autosize) {
    width: 250px;
  }
}
</style>
