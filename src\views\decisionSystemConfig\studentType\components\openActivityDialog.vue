<template>
  <div>
    <g-dialog
      :formOptions="formOptions"
      title="活动授权"
      v-model:show="showDialog"
      @confirm="onOpenActivity"
      :confirmName="'保存'"
      width="850"
    >
      <g-form :formOptions="formOptions">
        <template #student>
          <el-cascader
            :props="studentProps"
            :teleported="false"
            cascaderPanelRef
            clearable
            v-model="formOptions.data.student"
            filterable
            @change="dataChange"
            :options="studentOptions"
            placeholder="请选择"
          >
            <template #default="{ node }">
              <div>{{ node.label }}</div>
            </template>
          </el-cascader>
        </template>
        <template #activity>
          <g-table :tableOptions="tableOptions" @changePage="getList">
          </g-table>
        </template>
      </g-form>
    </g-dialog>
  </div>
</template>
<script lang="ts" setup>
import {
  getSchoolsApi,
  getGradesApi,
  getClassesApi,
  getStudentsApi,
  getActivitysApi,
  saveApi,
} from "@/api/studentType"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})
let studentOptions = $ref<any>([])
const tableOptions = reactive({
  ref: null,
  loading: false,
  key: "activityId",
  chooseTableIds: [],
  column: [
    {
      prop: "activityName",
      label: "活动名称",
      width: "250px",
    },
    {
      prop: "sysStageName",
      label: "学段",
      width: "100px",
    },
    {
      prop: "sysGradeList",
      label: "年级",
      width: "100px",
      formatter(row) {
        if (!row.sysGradeList?.length) {
          return "-"
        }
        return $g._.map(row.sysGradeList, "sysGradeName").join("、")
      },
    },
    {
      prop: "createTime",
      label: "活动创建时间",
      width: "150px",
      formatter(row) {
        if (!row.createTime) {
          return "-"
        }
        return $g.dayjs(row.createTime).format("YYYY-MM-DD")
      },
    },
    {
      prop: "xz",
      type: "selection",
      label: "选择",
    },
  ],
  data: [] as any,
  pageOptions: {
    page: 1,
    page_size: 5,
    total: 0,
  },
})
const studentProps = reactive({
  lazy: true,
  label: "label",
  value: "value",
  multiple: true,
  checkStrictly: true,
  emitPath: false,
  async lazyLoad(node, resolve) {
    const { level, data } = node
    if (level == 4) {
      resolve(null)
      return
    }
    let res: any = []
    if (level == 1) {
      res = await getGradesApi()
      res = res?.map((item) => {
        return {
          ...item,
          schoolId: data.schoolId,
          level: 2,
          label: item?.sysGradeName,
          value: data.schoolId + "&" + item?.sysGradeId,
        }
      })
    } else if (level == 2) {
      res = await getClassesApi({
        schoolId: data.schoolId,
        sysGradeId: data.sysGradeId,
      })
      res = res?.map((item) => {
        return {
          ...item,
          level: 3,
          schoolId: data.schoolId,
          sysGradeId: data.sysGradeId,
          label: item?.className,
          value:
            data.schoolId + "&" + data.sysGradeId + "&" + item?.schoolClassId,
        }
      })
    } else if (level == 3) {
      res = await getStudentsApi({
        schoolClassIds: data.schoolClassId ? [data.schoolClassId] : null,
      })
      res = res?.map((item) => {
        return {
          ...item,
          label: item?.studentName,
          level: 4,
          value:
            data.schoolId +
            "&" +
            data.sysGradeId +
            "&" +
            data.schoolClassId +
            "&" +
            item?.schoolStudentId,
        }
      })
    }
    let resList = res.map((item) => {
      return {
        ...item,
        leaf: level === 3,
      }
    })
    await resolve(resList)
  },
})
async function getSchools() {
  let res = await getSchoolsApi()
  let options = res.map((item) => {
    return {
      ...item,
      label: item.schoolName,
      value: item.schoolId,
      level: 1,
      filterable: true,
      children: [],
    }
  })
  studentOptions = options
}
async function dataChange(value) {
  if (formOptions.data.student?.length) {
    formOptions?.ref.validateField("student")
  }
}
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  items: {
    mode: {
      type: "radio",
      label: "模式",
      options: [
        { value: 2, label: "开通活动" },
        { value: 1, label: "关闭活动" },
      ],
      rule: true,
      slot: false,
    },
    way: {
      type: "radio",
      label: "方式",
      options: [
        { value: 1, label: "手动选择学生" },
        { value: 2, label: "批量导入（请粘贴学生启鸣号，一行一个）" },
      ],
      rule: true,
      slot: false,
    },
  },
  data: {
    mode: null,
    way: null,
    student: null,
  },
})
watch(
  () => formOptions.data.way,
  () => {
    formOptions.data.student = null
    if (formOptions.data.way == 2) {
      formOptions.items = {
        ...formOptions.items,
        student: {
          type: "textarea",
          noSideSpace: "noSideSpace",
          placeholder: "请输入学生启鸣号",
          rule: {
            required: true,
            validator: (rule, value, cb) => {
              if (!value) {
                cb(new Error("请输入学生启鸣号"))
                return
              }
              const lines = value.split("\n")
              lines.map((line) => {
                const maxLineLength = 20 // 每行的最大字符数
                if (line.length > maxLineLength) {
                  cb(new Error(`每行字符数不能超过${maxLineLength}`))
                }

                if (!/Z\d+$/.test(line) && line.charAt(0) == "Z" && line) {
                  cb(new Error("请输入Z+纯数字或者纯数字"))
                }
                if (!/^\d+$/.test(line) && line.charAt(0) != "Z" && line) {
                  cb(new Error("请输入Z+纯数字或者纯数字"))
                }
              })
            },
          },
          label: "学生",
        },
        activity: {
          type: "select",
          label: "选择活动",
          options: [],
          width: "300px",
          rule: {
            required: true,
            validator: (rule, value, cb) => {
              if (!tableOptions.chooseTableIds?.length) {
                cb(new Error("请选择活动数据"))
              } else {
                cb()
              }
            },
          },
          slot: true,
        },
      }
    } else {
      formOptions.items = {
        ...formOptions.items,
        student: {
          type: "",
          label: "学生",
          options: [],
          rule: {
            required: true,
            validator: (rule, value, cb) => {
              if (!value?.length) {
                cb(new Error("请选择学生数据"))
              } else {
                cb()
              }
            },
          },
          slot: true,
        },
        activity: {
          type: "select",
          label: "选择活动",
          options: [],
          width: "300px",
          rule: {
            required: true,
            validator: (rule, value, cb) => {
              if (!tableOptions.chooseTableIds?.length) {
                cb(new Error("请选择活动数据"))
              } else {
                cb()
              }
            },
          },
          slot: true,
        },
      }
    }
  },
)
watch(
  () => props.show,
  () => {
    if (props.show) {
      formOptions.data.way = 1
      formOptions.data.mode = 2
      tableOptions.pageOptions.page = 1
      tableOptions.pageOptions.total = 0
      tableOptions.chooseTableIds = []
      getSchools()
      getList()
    }
  },
)
watch(
  () => tableOptions.chooseTableIds?.length,
  () => {
    if (tableOptions.chooseTableIds?.length) {
      formOptions?.ref.validateField("activity")
    }
  },
)
async function onOpenActivity() {
  try {
    let schoolIds = null
    let schoolGradeIds = null
    let schoolClassIds = null
    let schoolStudentIds = null
    if (formOptions.data.way == 1) {
      schoolIds = formOptions.data.student?.filter(
        (item) => !String(item).includes("&"),
      )
      schoolGradeIds = formOptions.data.student?.filter(
        (item) =>
          String(item).includes("&") && String(item).split("&")?.length == 2,
      )
      schoolClassIds = formOptions.data.student
        ?.filter(
          (item) =>
            String(item).includes("&") && String(item).split("&")?.length == 3,
        )
        .map((item1) => {
          const arr = item1.split("&")
          return arr[arr?.length - 1]
        })
      schoolStudentIds = formOptions.data.student
        ?.filter(
          (item) =>
            String(item).includes("&") && String(item).split("&")?.length == 4,
        )
        .map((item1) => {
          const arr = item1.split("&")
          return arr[arr?.length - 1]
        })
    }
    const res = await saveApi({
      isOpen: formOptions?.data.mode,
      importType: formOptions.data.way,
      activityIds: $g._.map(tableOptions.chooseTableIds, "activityId"),
      schoolIds: schoolIds,
      schoolGradeIds: schoolGradeIds,
      schoolClassIds: schoolClassIds,
      schoolStudentIds: schoolStudentIds,
      idNums:
        formOptions.data.way == 2
          ? formOptions.data.student?.split("\n")
          : null,
    })
    if (formOptions?.data.mode == 2) {
      $g.msg("开通活动成功", "success")
    } else {
      $g.msg("关闭活动成功", "success")
    }
    emit("update:show", false)
    emit("refresh")
  } catch (err) {
    console.log("err", err)
  } finally {
    formOptions.loading = false
  }
}
async function getList() {
  try {
    tableOptions.loading = true
    const res = await getActivitysApi({
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.data = res?.list || []
    tableOptions.pageOptions.total = res?.total || 0
    tableOptions.loading = false
  } catch (err) {
    tableOptions.loading = false
  }
}
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
</script>
<style scoped lang="scss"></style>
