<template>
  <div class="student-container-main">
    <g-form
      :formOptions="filterFormOptions"
      @search="getList"
      @reset="getList"
      :tableOptions="tableOptions"
    >
    </g-form>
    <g-table :tableOptions="tableOptions" @changePage="getList">
      <template #header-right
        ><n-button type="success" @click="onOpenActivity">活动授权</n-button>
        <n-button
          v-if="tableOptions.data.some((v) => v.showAiPlan)"
          type="primary"
          @click="onAdd(null)"
          >新建学生</n-button
        >
      </template>
      <template #accountCancel="{ row }">
        {{ row.accountCancel == 2 ? "已注销" : "正常" }}
      </template>
      <template #userName="{ row }">
        <div
          v-if="row.accountSource == 4 && row.accountCancel != 2"
          @mouseenter="row.showButton = true"
          @mouseleave="row.showButton = false"
          class="w-full h-full relative min-h-[32px]"
        >
          <span :class="[!row.showButton ? 'z-[1]' : 'z-[0] opacity-0']">
            {{ row.userName }}
          </span>
          <el-button
            v-show="row.showButton"
            class="absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]"
            type="primary"
            @click="login(row)"
          >
            一键登录
          </el-button>
        </div>
        <span v-else>{{ row.userName }}</span>
      </template>
      <template #freeTalk="{ row }"
        ><el-switch v-model="row.isOpen" @change="onSwitchChange(row)"
      /></template>
      <template #activityNum="{ row }"
        ><div
          :class="row?.activityNum ? 'text-primary cursor-pointer' : ''"
          @click="onActivityList(row)"
        >
          {{ row?.activityNum || 0 }}个
        </div></template
      >
      <template #header-headPic>
        <el-tooltip
          class="item"
          effect="dark"
          content="学校开通人脸识别功能后支持上传头像"
          placement="top"
          trigger="click"
        >
          <div class="flex flex-cc">
            <div>头像</div>
            <g-icon name="ri-error-warning-line" size="14" color="" />
          </div>
        </el-tooltip>
      </template>
      <template #headPic="{ row }">
        <div v-if="row?.isOpenFace == 2">
          <div
            v-if="row.faceImage"
            class="flex relative justify-center items-center w-70px h-70px p-10px"
          >
            <g-icon
              name="ri-close-circle-fill"
              size="20"
              color="red"
              class="absolute right-0 top-0 active:opacity-80 bg-[#fff] h-20px w-20px rounded-[20px]"
              @click="delPic(row)"
            />
            <n-image
              width="50"
              hight="50"
              object-fit="contain"
              :src="row.faceImage"
            />
          </div>
          <n-button v-else type="primary" @click="onUpload(row)">上传</n-button>
        </div>
        <div v-else>
          <div>-</div>
        </div>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button text type="primary" @click="onAdd(row)">修改信息</n-button>
          <n-button
            v-if="row.showAiPlan"
            text
            type="primary"
            @click="toAIPage(row)"
            >AI计划</n-button
          >
          <n-button v-else text type="primary" @click="onBind(row)"
            >绑定学科</n-button
          >
          <n-button
            text
            type="primary"
            class="copy-btn"
            @click="handleResetPwd(row)"
            >重置密码</n-button
          >
          <n-button
            v-if="row.accountCancel == 2"
            text
            type="primary"
            @click="recover(row)"
          >
            恢复账号
          </n-button>
        </n-space>
      </template>
    </g-table>
    <AddDialog
      :classList="filterFormOptions.items.currentGrade.options"
      v-model:show="showAdd"
      @refresh="refresh"
      :studentInfo="studentInfo"
    ></AddDialog>
    <ActivityListDialog
      :currentId="currentId"
      v-model:show="showActivityList"
    ></ActivityListDialog>
    <openActivityDialog
      @refresh="getList"
      v-model:show="showOpenActivity"
    ></openActivityDialog>
    <g-dialog
      title="上传头像"
      :formOptions="imgFormOptions"
      v-model:show="showPop"
      width="420"
      @confirm="confirm"
    >
      <g-form :formOptions="imgFormOptions">
        <!-- 讲师照片 -->
        <template #imgList>
          <g-upload
            v-model:fileList="imgFormOptions.data.imgList"
            type="image-card"
            :max="1"
            :fileSize="20 * 1024 * 1024"
            accept=".jpg,.png"
            tips="仅支持.jpg .png 且大小不超过20MB"
          ></g-upload>
        </template>
      </g-form>
    </g-dialog>
    <g-dialog
      title="修改信息"
      width="400"
      :formOptions="formOptions"
      v-model:show="showEdit"
      @confirm="confirmEdit"
    >
      <g-form :formOptions="formOptions"> </g-form>
    </g-dialog>
    <g-dialog title="绑定学科" v-model:show="showBind" @confirm="bindConfirm">
      <n-checkbox-group v-model:value="selectedSubjects" name="radiogroup">
        <n-space>
          <n-checkbox
            v-for="e in subjectList"
            :key="e.value"
            :value="e.value"
            size="large"
          >
            {{ e.label }}
          </n-checkbox>
        </n-space>
      </n-checkbox-group>
      <div class="mt-10px text-14px text-error">
        *学生在【逆袭课程】处仅能查看选中学科资源
      </div>
    </g-dialog>
  </div>
</template>
<script setup lang="ts">
import AddDialog from "./components/AddDialog.vue"
import ActivityListDialog from "./components/ActivityListDialog.vue"
import openActivityDialog from "./components/openActivityDialog.vue"
import {
  getListApi,
  editState,
  getShoolListAdmin as getSchoolList,
  editStudentPic,
  editStudentInfo,
  getSubjectInfoList,
  bindSubjects,
  getLoginCode,
  resetPwd,
  recoverAccount,
} from "@/api/studentType"
import { getGradeListApi } from "@/api/common"
import ClipboardJS from "clipboard"

import router from "@/router"
let showAdd = $ref<any>(false)
let studentInfo = $ref<any>(null)
let currentId = $ref<any>(null)
let showPop = $ref(false)
let currentStudent = $ref<any>(null)
let showEdit = $ref(false)
let showActivityList = $ref<any>(false)
let showOpenActivity = $ref<any>(false)
let showBind = $ref(false)

let subjectList = $ref<any>([])

let selectedSubjects = $ref<any>([])

const formOptions = reactive({
  ref: null as any,
  loading: false,
  items: {
    studentName: {
      type: "text",
      label: "学生姓名",
      width: "200px",
      rule: true,
      span: 24,
    },
    gender: {
      type: "radio",
      label: "性别",
      span: 24,
      options: [
        {
          label: "未设置",
          value: 0,
        },
        {
          label: "男",
          value: 1,
        },
        {
          label: "女",
          value: 2,
        },
      ],
    },
  },
  data: {
    studentName: "",
    gender: 0,
  },
})
function onOpenActivity() {
  showOpenActivity = true
}
const imgFormOptions = reactive({
  ref: null as any,
  loading: false,
  items: {
    imgList: {
      type: "upload",
      label: "学生头像",
      placeholder: "请上传头像",
      width: "200px",
      rule: {
        validator: (rule, value, cb) => {
          if (value.length === 0) {
            cb(new Error("请上传头像"))
          }
        },
      },
      slot: true,
      span: 24,
    },
  },
  data: {
    imgList: [] as any,
  },
})

const tableOptions = reactive({
  ref: null,
  loading: false,
  column: [
    {
      prop: "accountName",
      label: "学生账号",
    },
    {
      prop: "thirdUniqueId",
      label: "启鸣号",
    },
    {
      prop: "accountCancel",
      label: "账号状态",
      slot: true,
      width: "90px",
    },
    {
      prop: "userName",
      label: "学生姓名",
      slot: true,
    },
    {
      prop: "parentMobileList",
      label: "家长电话",
      formatter(row) {
        return row.parentMobileList?.length
          ? row.parentMobileList.join("、")
          : "-"
      },
    },
    {
      prop: "schoolName",
      label: "学校",
    },
    {
      prop: "sysGradeName",
      label: "年级",
    },
    {
      prop: "createTime",
      label: "注册时间",
      formatter(row) {
        return row?.createTime
          ? $g.dayjs(row?.createTime).format("YYYY/MM/DD HH:mm")
          : "-"
      },
    },
    {
      prop: "loginCounter",
      label: "登录次数",
      width: "90px",
    },
    {
      prop: "lastLoginTime",
      label: "最后一次登录",
      formatter(row) {
        return row?.lastLoginTime
          ? $g.dayjs(row?.lastLoginTime).format("YYYY/MM/DD HH:mm")
          : "-"
      },
    },
    {
      prop: "freeTalk",
      label: "自由对话",
      slot: true,
      width: "90px",
    },
    {
      prop: "activityNum",
      label: "开通活动",
      slot: true,
      width: "90px",
    },
    {
      prop: "headPic",
      label: "头像",
      tooltip: false,
      width: "90px",
      slot: true,
      headerSlot: true,
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [] as any,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})
function onActivityList(item) {
  if (!item.activityNum) {
    return
  }
  currentId = item?.schoolStudentId
  showActivityList = true
}
async function onSwitchChange(row) {
  try {
    await editState({
      accountId: row.accountId,
      dialogState: row.isOpen ? 1 : 2,
    })
    getList()
  } catch (err) {
    console.log("err", err)
  }
}
const filterFormOptions = reactive<any>({
  ref: null as any,
  filter: true,
  labelWidth: "60px",
  items: {
    schoolId: {
      type: "select",
      label: "学校",
      labelField: "schoolName",
      options: [],
      valueField: "schoolId",
      width: "250px",
    },
    currentGrade: {
      type: "select",
      label: "年级",
      labelField: "sysGradeName",
      clearable: false,
      valueField: "id",
      options: [],
      width: "250px",
    },
    keyword: {
      type: "text",
      label: "学生账号/启鸣号/家长电话检索",
      placeholder: "输入学生账号/启鸣号/家长电话检索",
      width: "280px",
      showLabel: false,
    },
  },
  // 列表接口使用到的筛选值,及时搜索时使用
  data: {
    currentGrade: null,
    keyword: null,
    schoolId: null,
  },
  // 列表接口使用到的筛选值,非及时搜索时使用
  filterData: {},
})

async function getList() {
  try {
    tableOptions.loading = true
    const res = await getListApi({
      schoolId: filterFormOptions.data.schoolId,
      keyword: filterFormOptions.data.keyword,
      sysGradeId: filterFormOptions.data.currentGrade,
      pageSize: tableOptions.pageOptions.page_size,
      page: tableOptions.pageOptions.page,
    })
    tableOptions.data =
      res && res?.list?.length
        ? res?.list.map((item) => {
            return {
              ...item,
              isOpen: item.dialogState == 1 ? true : false,
            }
          })
        : []
    tableOptions.pageOptions.total = res?.total || 0
    tableOptions.loading = false
  } catch (err) {
    tableOptions.loading = false
  }
}
/* 获取学校列表 */
async function getSchoolListApi() {
  let res = await getSchoolList()
  filterFormOptions.items.schoolId.options = res
}
function onAdd(item) {
  if (!item || item?.editInfo) {
    showAdd = true
  } else {
    formOptions.data.studentName = item.userName
    formOptions.data.gender = item.gender
    showEdit = true
  }
  studentInfo = item
}
function refresh() {
  showAdd = false
  getList()
}
async function getGradeList() {
  const res = await getGradeListApi()
  filterFormOptions.items.currentGrade.options = res ? res : []
}

function toAIPage(row) {
  router.push({
    name: "AiProject",
    query: {
      sysStageId: row.sysStageId,
      userName: row.userName,
      sysGradeName: row.sysGradeName,
      schoolStudentId: row.schoolStudentId,
    },
  })
}

function onUpload(row) {
  currentStudent = row
  imgFormOptions.data.imgList = []
  showPop = true
}

async function confirm() {
  try {
    const faceImage = imgFormOptions.data.imgList[0].fullUrl
    await editStudentPicApi({
      schoolStudentId: currentStudent.schoolStudentId,
      faceImage,
    })
    currentStudent.faceImage = faceImage
    showPop = false
    imgFormOptions.loading = false
  } catch (e) {
    imgFormOptions.loading = false
  }
}

function delPic(row) {
  $g.confirm({ content: "删除后学生无法进行人脸识别，确定删除吗？" })
    .then(async () => {
      await editStudentPicApi({
        schoolStudentId: row.schoolStudentId,
        faceImage: null,
      })
      row.faceImage = null
    })
    .catch(() => {})
}

async function editStudentPicApi(params) {
  await editStudentPic(params)
  $g.msg("操作成功")
}

async function confirmEdit() {
  try {
    await editStudentInfo({
      ...formOptions.data,
      schoolStudentId: studentInfo.schoolStudentId,
    })
    studentInfo.userName = formOptions.data.studentName
    studentInfo.gender = formOptions.data.gender
    formOptions.loading = false
    showEdit = false
    $g.msg("修改成功")
  } catch (e) {
    formOptions.loading = false
  }
}

function onBind(row) {
  studentInfo = row
  getSubjectInfoListApi()
  showBind = true
}

async function getSubjectInfoListApi() {
  const data = await getSubjectInfoList({
    accountId: studentInfo.accountId,
  })
  subjectList = data.map((v) => ({
    label: v.sysSubjectName,
    value: v.sysSubjectId,
  }))
  selectedSubjects = data.filter((v) => v.bind).map((v) => v.sysSubjectId)
}

async function bindConfirm() {
  await bindSubjects({
    accountId: studentInfo.accountId,
    sysSubjectIdList: selectedSubjects,
  })
  $g.msg("绑定成功")
}

async function login(row) {
  if (row.isOpenZixishi != 2) {
    $g.msg("学生未开通AI智习室！", "warning")
    return
  }
  const { code } = await getLoginCode({
    accountId: row.accountId,
  })
  const url = `${
    import.meta.env.VITE_APP_ZHI_XI_SHI_URL
  }/#/yx/zxs/home?code=${code}&source=jztAdmin`
  window.open(url, "_blank")
}

// 重置密码
function handleResetPwd(row) {
  $g.confirm({ content: "确定重置密码吗？" })
    .then(async () => {
      try {
        const pwd = await resetPwd({
          accountId: row.accountId,
        })
        copyPwd(pwd)
      } catch (e) {
        $g.msg("重置失败", "error")
      }
    })
    .catch(() => {})
}

function copyPwd(pwd) {
  // 创建一个临时按钮用于复制
  const tempButton = document.createElement("button")
  tempButton.style.display = "none"
  tempButton.className = "temp-copy-btn"
  document.body.appendChild(tempButton)

  let clipboard = new ClipboardJS(".temp-copy-btn", {
    text: () => {
      return pwd
    },
  })
  clipboard.on("success", () => {
    ElMessageBox.alert(
      `提示：已复制新密码，请粘贴使用后提醒学生及时修改密码<br/>新密码：${pwd}`,
      "密码已重置",
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确认",
        center: true,
      },
    )
    clipboard.destroy()
    document.body.removeChild(tempButton)
  })
  clipboard.on("error", () => {
    $g.msg("复制失败", "error")
    clipboard.destroy()
    document.body.removeChild(tempButton)
  })
  // 添加：立即触发点击
  tempButton.click()
}

function recover(row) {
  $g.confirm({ content: `确认恢复已注销“${row.userName}”的账号？` })
    .then(async () => {
      try {
        await recoverAccount({ accountId: row.accountId })
        row.accountCancel = 1
        $g.msg("恢复成功！", "success")
      } catch (error) {
        console.log("⚡ error ==> ", error)
        $g.msg("恢复失败", "error")
      }
    })
    .catch(() => {})
}

onMounted(() => {
  getGradeList()
  getList()
  getSchoolListApi()
})
</script>
<style scoped lang="scss"></style>
