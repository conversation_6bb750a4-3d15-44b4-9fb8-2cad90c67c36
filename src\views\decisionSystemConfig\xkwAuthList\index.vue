<template>
  <div class="xkwAuthList-container-main">
    <g-table :tableOptions="tableOptions" @changePage="getList">
      <template #cz="{ row }">
        <n-button type="primary" text @click="onUnbind(row)"> 解绑 </n-button>
      </template>
    </g-table>
  </div>
</template>
<script lang="ts" setup>
import { getAuthList, unbindApi } from "@/api/common"

const tableOptions = reactive({
  loading: false,
  column: [
    { prop: "accountName", label: "授权账号" },
    {
      prop: "createTime",
      label: "授权时间",
    },
    {
      prop: "openId",
      label: "授权唯一ID",
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [] as any,
})
async function getList() {
  try {
    tableOptions.loading = true
    const res = await getAuthList()
    tableOptions.data = res || []
    tableOptions.loading = false
  } catch (err) {
    tableOptions.loading = false
  }
}
async function onUnbind(item) {
  try {
    const res = await unbindApi({ xkwUserAuthId: item?.xkwUserAuthId })
    $g.msg("解绑成功")
    getList()
  } catch (err) {
    console.log("err", err)
  }
}
onMounted(() => {
  getList()
})
</script>
