<template>
  <div>
    <h3>懒加载级联选择器（支持搜索第一级）</h3>
    <el-cascader
      :options="lazyOptions"
      :props="lazyProps"
      filterable
      placeholder="请选择或搜索"
      style="width: 400px"
    ></el-cascader>
  </div>
</template>

<script>
let id = 0

export default {
  data() {
    return {
      // 懒加载模式的数据（初始为空，通过方法加载第一级）
      lazyOptions: [],
      // 懒加载配置（参考实际项目的配置）
      lazyProps: {
        lazy: true,
        label: "label",
        value: "value",
        checkStrictly: false,
        lazyLoad(node, resolve) {
          const { level, data } = node
          console.log("懒加载触发 - level:", level, "data:", data)

          // 第一级(level 0)已经预加载，不需要处理
          if (level === 0) {
            resolve([])
            return
          }

          // level 1是学院，level 2是专业，level 3结束
          if (level >= 3) {
            resolve([])
            return
          }

          setTimeout(() => {
            let nodes = []
            if (level === 1) {
              // 第二级：学院
              nodes = [
                {
                  value: `${data.value}-college1`,
                  label: "计算机学院",
                  leaf: false,
                },
                {
                  value: `${data.value}-college2`,
                  label: "数学学院",
                  leaf: false,
                },
                {
                  value: `${data.value}-college3`,
                  label: "物理学院",
                  leaf: false,
                },
              ]
            } else if (level === 2) {
              // 第三级：专业
              nodes = [
                {
                  value: `${data.value}-major1`,
                  label: "软件工程",
                  leaf: true,
                },
                {
                  value: `${data.value}-major2`,
                  label: "计算机科学与技术",
                  leaf: true,
                },
                {
                  value: `${data.value}-major3`,
                  label: "人工智能",
                  leaf: true,
                },
              ]
            }
            resolve(nodes)
          }, 1000)
        },
      },
    }
  },
  methods: {
    // 初始化懒加载的第一级数据（更多数据示例）
    initLazyOptions() {
      this.lazyOptions = [
        {
          value: "school1",
          label: "北京大学",
          level: 1,
          filterable: true,
          children: [],
        },
        {
          value: "school2",
          label: "清华大学",
          level: 1,
          filterable: true,
          children: [],
        },
        {
          value: "school3",
          label: "复旦大学",
          level: 1,
          filterable: true,
          children: [],
        },
        {
          value: "school4",
          label: "上海交通大学",
          level: 1,
          filterable: true,
          children: [],
        },
        {
          value: "school5",
          label: "浙江大学",
          level: 1,
          filterable: true,
          children: [],
        },
        {
          value: "school6",
          label: "南京大学",
          level: 1,
          filterable: true,
          children: [],
        },
        {
          value: "school7",
          label: "中山大学",
          level: 1,
          filterable: true,
          children: [],
        },
        {
          value: "school8",
          label: "华中科技大学",
          level: 1,
          filterable: true,
          children: [],
        },
        {
          value: "school9",
          label: "西安交通大学",
          level: 1,
          filterable: true,
          children: [],
        },
        {
          value: "school10",
          label: "四川大学",
          level: 1,
          filterable: true,
          children: [],
        },
      ]
    },
  },
  mounted() {
    // 页面加载时初始化懒加载的第一级数据
    this.initLazyOptions()
  },
}
</script>
