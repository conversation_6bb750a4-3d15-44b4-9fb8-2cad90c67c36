<script lang="ts" setup>
import { getFrontRoute } from "@/api/user"
let time = $ref(4)
let router = useRouter()
// 5秒后返回首页
let timer = setInterval(async () => {
  time--
  if (time === 0) {
    const list = await getFrontRoute()
    clearInterval(timer)
    router.push(list.length ? "/" : "/login")
  }
}, 1000)

onUnmounted(() => {
  clearInterval(timer)
})
</script>

<template>
  <n-result
    class="mt-200px"
    status="404"
    title="404 当前页面不存在"
    description="请检查您输入的网址是否正确"
  >
    <template #icon>
      <g-empty
        :image="$g.tool.getFileUrl('status/404.png')"
        description=" "
      ></g-empty>
    </template>
    <template #footer>
      <n-button>{{ time }}s后返回首页</n-button>
    </template>
  </n-result>
</template>
