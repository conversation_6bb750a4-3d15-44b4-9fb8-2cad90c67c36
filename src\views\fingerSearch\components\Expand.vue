<template>
  <div
    class="ques-collapse"
    :class="{
      'ques-expand': expand,
      'no-expand': !showExpand,
    }"
  >
    <div
      ref="contentRef"
      class="ques-content"
      :style="{
        maxHeight: maxHeight + 'px',
      }"
    >
      <g-mathjax :text="text" class="mathjax-p"></g-mathjax>
    </div>
    <div v-if="showExpand" class="w-full h-30px flex items-end justify-center">
      <div
        class="rounded-[8px] collapse-icon flex items-center text-15px text-[#1ea0f0] cursor-pointer active:opacity-80"
        :class="{
          'expand-icon': expand,
        }"
        @click="expand = !expand"
      >
        {{ expand ? "收起" : "展开" }}识别文字
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    maxHeight?: number
    text?: string
  }>(),
  {
    maxHeight: 26,
    text: "",
  },
)

let observer: any = null
const contentRef = $ref<any>()

let expand = $ref(false)
let showExpand = $ref(false)

async function initData() {
  observer?.disconnect?.()
  observer = null
  expand = false
  await nextTick()
  $g.tool.renderMathjax()
  observer = new ResizeObserver(() => {
    showExpand = contentRef?.scrollHeight > props.maxHeight
    // if (showExpand) {
    //   observer?.disconnect?.()
    //   observer = null
    // }
  })
  observer.observe(contentRef)
}

onMounted(() => {
  initData()
})

onBeforeMount(() => {
  observer?.disconnect?.()
  observer = null
})

watch(
  () => props.text,
  (val) => {
    if (val) {
      nextTick(() => {
        initData()
      })
    }
  },
)

watch(
  () => expand,
  (val) => {
    if (!val) {
      nextTick(() => {
        contentRef?.scrollTo({ left: 0 })
      })
    }
  },
)
</script>

<style lang="scss" scoped>
.ques-collapse {
  .ques-content {
    overflow: hidden;
    position: relative;
    // &::after {
    //   display: inline-block;
    //   content: "";
    //   position: absolute;
    //   left: 0;
    //   bottom: 0;
    //   width: 100%;
    //   height: 100px;
    //   background: linear-gradient(to top, #fff, #ffffff00);
    // }
  }
  .collapse-icon {
    &::after {
      display: inline-block;
      content: "";
      width: 8px;
      height: 8px;
      border-bottom: 1px solid #1ea0f0;
      border-right: 1px solid #1ea0f0;
      transform: rotate(45deg) translateY(-2px);
      margin-left: 4px;
    }
    &.expand-icon {
      &::after {
        transform: rotate(-135deg) translateX(-6px) translateY(1px);
      }
    }
  }
  &.ques-expand {
    .ques-content {
      max-height: initial !important;
      overflow-x: auto;
      &::after {
        display: none;
      }
    }
  }
  &.no-expand {
    .ques-content {
      &::after {
        display: none !important;
      }
    }
  }
}

.mathjax-p {
  font-size: 16px !important;
}
</style>
