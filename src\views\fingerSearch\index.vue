<template>
  <div class="finger-container-main">
    <div class="w-full h-[0px]" ref="topRef"></div>
    <div
      class="w-full h-500px flex items-center justify-center"
      v-if="showLoading"
    >
      <g-loading class="h-200px"></g-loading>
    </div>
    <template v-else>
      <div
        class="w-full h-500px flex items-center justify-center"
        v-if="!searchList.length"
      >
        <g-empty description="没有识别记录"></g-empty>
      </div>
      <template v-else>
        <div
          class="w-full border border-solid border-[#1ea0f0] mb-20px p-20px rounded-[10px]"
          v-for="item in searchList"
          :key="item.fingertipSearchId"
        >
          <div class="flex items-center flex-wrap">
            <div class="text-14px mb-10px text-[#666] mr-40px">
              学校：{{ item.student?.schoolName || "-" }}
            </div>
            <div class="text-14px mb-10px text-[#666] mr-40px">
              学生：{{ item.student?.studentName || "-" }}
            </div>
            <div class="text-14px mb-10px text-[#666] mr-40px">
              启鸣号：{{ item.student?.idNum || "-" }}
            </div>
            <div class="text-14px mb-10px text-[#666]">
              拍摄时间：{{ item.createTime || "-" }}
            </div>
          </div>
          <div class="flex items-start mb-14px">
            <div class="mr-40px">
              <div class="text-16px text-[#333] mb-10px font-bold">
                拍摄原图
              </div>
              <div
                class="w-200px h-200px border border-solid border-[#eee] rounded-[10px] overflow-hidden flex items-center justify-center"
              >
                <n-image
                  v-if="item.imageUrl"
                  width="200"
                  height="200"
                  :src="item.imageUrl"
                  object-fit="contain"
                />
                <g-empty v-else description="没有内容" :size="100"></g-empty>
              </div>
            </div>
            <div class="mr-40px">
              <div class="text-16px text-[#333] mb-10px font-bold">
                指尖区域图
              </div>
              <div
                class="w-200px h-200px border border-solid border-[#eee] rounded-[10px] overflow-hidden flex items-center justify-center"
              >
                <n-image
                  v-if="item.ocrImageUrl"
                  width="200"
                  height="200"
                  :src="item.ocrImageUrl"
                  object-fit="contain"
                />
                <g-empty v-else description="没有内容" :size="100"></g-empty>
              </div>
            </div>
            <div>
              <div class="text-16px text-[#333] mb-10px font-bold">
                题目区域
              </div>
              <div
                class="w-200px h-200px border border-solid border-[#eee] rounded-[10px] overflow-hidden flex items-center justify-center"
              >
                <n-image
                  v-if="item.questionBoxImageUrl"
                  width="200"
                  height="200"
                  :src="item.questionBoxImageUrl"
                  object-fit="contain"
                />
                <g-empty v-else description="没有内容" :size="100"></g-empty>
              </div>
            </div>
          </div>
          <div class="text-16px text-[#333] mb-10px font-bold">
            识别题干文字内容
          </div>
          <div
            class="mb-14px p-20px bg-[#f8f8f8] rounded-[10px] overflow-hidden"
          >
            <Expand
              :text="item.ocrText || '没有识别到文字内容'"
              class="finger-math"
            />
          </div>
          <template v-if="item.list.length || item.moreList.length">
            <div class="flex items-center flex-wrap">
              <n-button
                v-for="(btn, index) in item.list"
                :key="btn.questionId"
                class="mr-10px mb-10px"
                :type="item.active === btn.questionId ? 'primary' : 'default'"
                @click="setActive(item, btn)"
                >题目{{ index + 1 }}</n-button
              >

              <div
                class="ml-40px h-24px mb-10px mr-20px text-16px"
                :class="{
                  '!ml-0': item.list.length === 0,
                }"
                v-if="item.moreList.length"
              >
                换一批题目:
              </div>

              <n-button
                v-for="(btn, index) in item.moreList"
                :key="btn.questionId"
                class="mr-10px mb-10px"
                :type="item.active === btn.questionId ? 'primary' : 'default'"
                @click="setActive(item, btn)"
                >题目{{ index + 1 }}</n-button
              >
            </div>
            <div
              class="mt-4px p-20px bg-[#f8f8f8] rounded-[10px] overflow-hidden"
            >
              <g-mathjax :text="getSubContent(item)" class="finger-math" />
            </div>
          </template>
        </div>
      </template>
    </template>
    <g-page :pageOptions="pageOptions" @change="onPageChange"></g-page>
  </div>
</template>

<script setup lang="ts">
import Expand from "./components/Expand.vue"
import { getSegmentSelect } from "@/api/fingerSearch"

let showLoading = $ref(true)

const topRef = $ref<any>()

const pageOptions = reactive({
  page: 1,
  page_size: 10,
  total: 0,
})

let searchList = $ref<any[]>([])

async function getSegmentSelectApi() {
  try {
    showLoading = true
    const { list, total } = await getSegmentSelect({
      page: pageOptions.page,
      pageSize: pageOptions.page_size,
    })
    searchList = list.map((v) => ({
      ...v,
      active: v.list[0]?.questionId ?? v.moreList[0]?.questionId,
    }))
    pageOptions.total = total
    nextTick(() => {
      $g.tool.renderMathjax()
    })
    showLoading = false
  } catch (e) {
    showLoading = false
    console.error(e)
  }
}

function setActive(item, btn) {
  if (item.active !== btn.questionId) {
    item.active = btn.questionId
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
}

function getSubContent(item) {
  const currentQues = item.list
    .concat(item.moreList)
    .find((v) => v.questionId === item.active)
  if (!currentQues) return "-"
  let bodyTitle = ""
  const isSingle = currentQues.subQuestions.length === 1
  currentQues.subQuestions.forEach((v, i) => {
    const title = v.subQuestionTitle || ""
    const subTitle = Object.keys(v)
      .filter((key) => key.includes("option") && v[key])
      .map((key) => {
        const option = key.replace("option", "")
        const value = v[key]
        return `<div class="ques-text-row">${option}.${value}</div>`
      })
      .join("\n")
    const numberPrefix = isSingle
      ? ""
      : `<span class="ques-num">(${i + 1})</span>`
    bodyTitle =
      bodyTitle +
      "<div class='ques-row'>" +
      "<div class='num-row'>" +
      numberPrefix +
      "<div>" +
      title +
      "</div></div>" +
      "</div>" +
      "\n" +
      subTitle
  })
  return (
    `<div class="id-title">题目ID: ${currentQues.questionId}</div>` +
    "\n" +
    (currentQues.questionTitle || "") +
    "\n" +
    bodyTitle
  )
}

async function onPageChange() {
  await getSegmentSelectApi()
  await nextTick()
  topRef?.scrollIntoView()
}

onBeforeMount(() => {
  getSegmentSelectApi()
})
</script>

<style lang="scss">
.finger-math {
  font-size: 14px !important;
  p {
    font-size: 14px !important;
    margin-bottom: 0;
  }
  & > p {
    min-height: 30px;
    margin: 10px 0;
  }

  .ques-text-row {
    font-size: 14px !important;
    display: flex;
    align-items: center;
    min-height: 30px;
    margin: 10px 0;
    & > p {
      margin: 0 !important;
    }
  }
}

.id-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 15px;
}

.ques-row {
  // display: flex;
  .num-row {
    display: flex;
    align-items: flex-start;
  }
  .ques-num {
    flex-shrink: 0;
    margin-right: 5px;
  }
}
</style>
