<template>
  <div class="resource-upload-container-main">
    <div class="flex items-center">
      <span class="flex-shrink-0 mr-10px w-[78px] text-right">项目：</span>
      <n-radio-group
        v-model:value="currentProject"
        :on-update:value="handleProjectChange"
      >
        <n-radio-button
          v-for="item in projectList"
          :key="item.name"
          :value="item.pathName"
        >
          {{ item.name }}
        </n-radio-button>
      </n-radio-group>
    </div>
    <div class="flex items-center mt-20px">
      <span class="flex-shrink-0 mr-10px w-[78px] text-right">资源类型：</span>
      <n-radio-group
        v-model:value="currentResourceType"
        :on-update:value="handleResourceTypeChange"
      >
        <n-radio-button
          v-for="item in resourceTypeList"
          :key="item.name"
          :value="item.pathName"
        >
          {{ item.name }}
        </n-radio-button>
      </n-radio-group>
    </div>
    <div class="flex items-center mt-20px">
      <span class="flex-shrink-0 mr-10px w-[78px] text-right">
        <span class="text-[red]" v-if="currentResourceType == 'img'">*</span
        >模块：
      </span>
      <el-input
        v-model="moduleName"
        placeholder="请输入模块名称"
        class="w-[400px]"
        clearable
        :maxlength="20"
        @input="handleModuleChange"
      ></el-input>
    </div>
    <div class="flex items-center mt-20px">
      <span class="flex-shrink-0 mr-10px w-[78px]">
        <span class="text-[red]">*</span>资源路径：
      </span>
      <el-input
        v-model="resourcePath"
        placeholder="请输入资源路径"
        class="w-[400px]"
        clearable
        :maxlength="50"
      ></el-input>
    </div>
    <div class="mt-20px flex">
      <span class="flex-shrink-0 mr-10px w-[78px] text-right">资源上传：</span>
      <g-upload
        class="w-[500px]"
        v-if="pathRule"
        v-model:fileList="files"
        multiple
        type="drag"
        tips="支持上传单文件不超过20M,不限制文件数量、格式"
        :fileSize="20 * 1024 * 1024"
        :showCustomFileList="false"
        :customPolicyParams="{
          api: getOssCustomPolicySignature,
          parentPath: resourcePath,
        }"
      >
        <template #fileList>
          <div class="mt-[20px]">
            <div
              class="mb-[20px]"
              v-for="(file, fileIndex) in files"
              :key="fileIndex"
            >
              <span>
                {{ file.name || file.resource_title }}
              </span>
              <span
                class="text-primary ml-[20px] cursor-pointer"
                @click="downFile(file)"
                >下载</span
              >
              <span
                class="text-primary ml-[20px] reflex cursor-pointer"
                @click="copyData(file)"
                >复制完整链接</span
              >
              <span
                class="text-primary ml-[20px] reflexPath cursor-pointer"
                @click="copyPathData(file)"
                >复制路径</span
              >
              <div class="flex" v-show="file.status == 'uploading'">
                <n-progress
                  :height="4"
                  type="line"
                  :percentage="file.percentage"
                />
                <div class="text-12px text-primary ml-10px flex-[0_0_50px]">
                  {{ file.speed }}
                </div>
              </div>
            </div>
          </div>
        </template>
      </g-upload>
      <div v-else>
        资源路径不能为空，父路径必须是英文组成且必须由/结尾（注：类型为img时需要填写模块名称）
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getOssCustomPolicySignature } from "@/api/common"
import ClipboardJS from "clipboard"
let resourcePath = $ref("test/otherType/")
let files = $ref<any>([])
const projectList = [
  { name: "测试", pathName: "test" },
  { name: "公共", pathName: "public" },
  { name: "AI智习室", pathName: "aiLearningRoom" },
  { name: "学习机", pathName: "xueXiJi" },
  { name: "智习室app", pathName: "zxsApp" },
  { name: "金字塔app", pathName: "jztApp" },
  { name: "金字塔-用户端", pathName: "jztUserEndGeFu" },
  { name: "金字塔-管理端", pathName: "geFuAdmin" },
  { name: "横屏三端适配项目", pathName: "threeLandscape" },
  { name: "新版教师用户端", pathName: "teacherUser" },
  { name: "中台", pathName: "courseMgtSystem" },
  { name: "AI英语", pathName: "aiEn" },
]
let currentProject = $ref("test")
const resourceTypeList = [
  { name: "其他", pathName: "otherType" },
  { name: "img", pathName: "img" },
  { name: "css", pathName: "css" },
  { name: "js", pathName: "js" },
  { name: "json", pathName: "json" },
]
let currentResourceType = $ref("otherType")
//模块
let moduleName = $ref("")
function downFile(file) {
  $g.tool.downloadFile(file.fullUrl, file.name)
}
/* 复制完整链接数据 */
function copyData(file) {
  let clipboard = new ClipboardJS(".reflex", {
    text: () => {
      //返回需要复制的字符串
      return file.fullUrl
    },
  })
  clipboard.on("success", () => {
    $g.msg("复制成功")
    clipboard.destroy()
  })
  clipboard.on("error", () => {
    $g.msg("复制失败", "error")
    clipboard.destroy()
  })
}
/* 复制路径数据 */
function copyPathData(file) {
  console.log("file", file)
  let clipboard = new ClipboardJS(".reflexPath", {
    text: () => {
      //返回需要复制的字符串
      return "/" + file.parentPath + file.name
    },
  })
  clipboard.on("success", () => {
    $g.msg("复制成功")
    clipboard.destroy()
  })
  clipboard.on("error", () => {
    $g.msg("复制失败", "error")
    clipboard.destroy()
  })
}

function handleProjectChange(value) {
  currentProject = value
  handleModuleChange()
}

function handleResourceTypeChange(value) {
  currentResourceType = value
  handleModuleChange()
}

function handleModuleChange() {
  resourcePath = `${currentProject}/${currentResourceType}${
    moduleName ? "/" : ""
  }${moduleName}/`
}

const pathRule = $computed(() => {
  if (!resourcePath) return false
  if (currentResourceType == "img" && !moduleName) return false
  const reg = /^[a-zA-Z/]+\/$/
  return reg.test(resourcePath)
})
</script>

<style lang="scss" scoped></style>
