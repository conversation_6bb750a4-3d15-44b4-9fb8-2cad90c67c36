<script lang="ts" setup>
import Background from "./components/Background.vue"
import { useUserStore } from "@/stores/modules/user"
import { deleteAllLock } from "@/api/bookMgt"
let { getUserInfo } = useUserStore()

const formRef: any = ref(null)

let loading = $ref(false)
const router = useRouter()

const model = reactive({
  accountName: "",
  password: "",
})

const rules = {
  accountName: [
    {
      required: true,
      message: "请输入用户名",
    },
  ],
  password: [
    {
      required: true,
      message: "请输入密码",
    },
  ],
}
async function handleSubmit() {
  await formRef.value?.validate()
  loading = true
  try {
    await getUserInfo(model)
    loading = false
    router.push("/")
    //清除用户的资源锁
    await deleteAllLock()
  } catch {
    loading = false
  }
}
</script>

<template>
  <div class="w-full h-full bg-[#dee8ff]">
    <div
      class="w-424px bg-white absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 br-[20px] z-20 p-25px"
    >
      <div class="text-28px text-primary text-center">金字塔后台管理</div>
      <div class="text-primary text-18px mt-24px">账号登录</div>
      <n-form
        class="mt-20px"
        ref="formRef"
        :model="model"
        :rules="rules"
        size="large"
        :show-label="false"
      >
        <n-form-item path="accountName">
          <n-input
            v-model:value="model.accountName"
            placeholder="请输入用户名"
          />
        </n-form-item>
        <n-form-item path="password">
          <n-input
            v-model:value="model.password"
            type="password"
            show-password-on="click"
            placeholder="请输入密码"
            @keyup.enter="handleSubmit"
          />
        </n-form-item>
        <n-space :vertical="true" :size="24" class="mt-20px">
          <n-button
            type="primary"
            size="large"
            :block="true"
            :round="true"
            :loading="loading"
            @click="handleSubmit"
          >
            确定
          </n-button>
        </n-space>
      </n-form>
    </div>
    <Background></Background>
  </div>
</template>
