<template>
  <div class="model-management-container-main">
    <div class="grid grid-cols-4 gap-13px justify-between">
      <div
        v-for="item in cardList"
        :key="item.title"
        class="border border-[#999] br-[13px] h-[100px] w-full p-13px"
        @click="handleClickCard(item)"
      >
        <!-- <img :src="item.imgSrc" :alt="item.title" /> -->
        <div class="">{{ item.title }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="ModelMain">
const cardList = [
  {
    title: "应答时间",
    imgSrc: "",
    pathName: "PromptWords",
  },
]

const router = useRouter()

function handleClickCard(item) {
  router.push({
    name: item.pathName,
  })
}
</script>

<style lang="scss" scoped></style>
