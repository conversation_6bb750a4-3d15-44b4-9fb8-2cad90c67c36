<template>
  <div class="prompt-edit-container-main">
    <div class="title-1 ml-[12px]">基本信息</div>
    <g-form :formOptions="formOptions">
      <template #标题>
        <div class="title-1">prompt</div>
      </template>
    </g-form>
    <div class="w-[800px] flex justify-center gap-40px mt-30px">
      <el-button @click="$router.back()">取消</el-button>
      <el-button type="primary" @click="saveForm">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="PromptEdit">
// 表单配置项
const formOptions = reactive({
  ref: null as any,
  loading: false,
  labelWidth: "120px",
  items: {
    prompt名称: {
      type: "text",
      label: "prompt名称",
      width: "300px",
      maxlength: 10,
      rule: true,
      span: 24,
    },
    模型选择: {
      type: "select",
      label: "模型选择",
      width: "300px",
      rule: {
        required: true,
        message: "请选择模型",
        type: "string",
      },
      span: 12,
      options: [
        {
          label: "Drive My Car",
          value: "song1",
        },
        {
          label: "Drive My Car",
          value: "song2",
        },
      ],
    },
    prompt备注: {
      type: "textarea",
      label: "prompt备注",
      width: "500px",
      labelWidth: "120px",
      maxlength: 30,
      span: 24,
    },
    标题: {
      slot: true,
      labelWidth: "0",
    },
    prompt: {
      type: "textarea",
      placeholder: "请输入prompt内容",
      label: "",
      width: "800px",
      labelWidth: "20px",
      rule: {
        required: true,
        message: "请输入prompt内容",
      },
      span: 24,
      autosize: {
        minRows: 8,
        maxRows: 40,
      },
      style: {
        height: "300px",
        resize: "none",
        overflowY: "auto",
      },
    },
  },
  data: {
    prompt名称: null,
    模型选择: "",
    prompt备注: "",
    prompt: "",
  },
})

function saveForm() {
  console.log("⚡ form ==> ", formOptions.data)
}
</script>

<style lang="scss" scoped>
// 左侧线标题
.title-1 {
  position: relative;
  padding-left: 12px;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  line-height: 1.5;
  color: #333;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 2px;
    width: 4px;
    height: calc(100% - 4px);
    background-color: #1890ff;
    border-radius: 2px;
  }
}
</style>
