<template>
  <div class="prompt-words-container-main">
    <g-table :tableOptions="tableOptions" @changePage="initData">
      <template #header-right>
        <el-button type="primary" @click="handleAddPromptWords('新增')">
          新增
        </el-button>
      </template>
      <template #check="{ row }">
        <div
          style="pointer-events: none"
          class="flex justify-center items-center check-radio"
        >
          <el-radio
            v-model="chooseRadio"
            :value="row.promptId"
            size="large"
          ></el-radio>
        </div>
      </template>
      <template #cz="{ row }">
        <div class="flex justify-center items-center">
          <div class="mr-12px flex items-center justify-end w-[56px]">
            <el-button
              v-if="!chooseRadio"
              type="primary"
              link
              @click="chooseRadio = row.promptId"
            >
              应用
            </el-button>
            <el-button
              v-else-if="chooseRadio == row.promptId"
              type="danger"
              link
              @click="chooseRadio = ''"
            >
              取消应用
            </el-button>
            <el-button v-else disabled link> 应用 </el-button>
          </div>
          <el-button type="primary" link @click="handleAddPromptWords('编辑')">
            编辑
          </el-button>
          <el-button type="primary" link @click="deletePromptWords(row)">
            删除
          </el-button>
        </div>
      </template>
    </g-table>
  </div>
</template>

<script setup lang="ts" name="PromptWords">
import { useRouterStore } from "@/stores/modules/routes"

const routerStore = useRouterStore()
const tableOptions = $ref<any>({
  ref: null as any,
  loading: false,
  column: [
    {
      prop: "check",
      label: "",
      slot: true,
      width: "64px",
    },
    {
      prop: "promptId",
      label: "ID",
    },
    {
      prop: "name",
      label: "名称",
    },
    {
      prop: "remarks",
      label: "备注",
    },
    {
      prop: "updataTime",
      label: "更新时间",
      formatter: (row) => {
        return $g.dayjs(row.updataTime).format("YYYY/MM/DD HH:mm:ss")
      },
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [
    {
      promptId: "20254141",
      name: "提示词1",
      remarks: "提示词词提示词提示词提示词提示词提示词",
      updataTime: "2025-04-11 11:11:12",
    },
    {
      promptId: "********",
      name: "提示词2",
      remarks: "提示词提示词提示词提示词提示词提示词提示词提示词提示词提示词",
      updataTime: "2025-04-11 11:11:14",
    },
  ],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})
let chooseRadio = $ref("")
const router = useRouter()

async function initData(isLoading = true) {
  try {
    if (isLoading) tableOptions.loading = true
    // const res = await getAccountLogList({
    //   accountAdminId: props.userInfo.accountAdminId,
    //   page: tableOptions.pageOptions.page,
    //   pageSize: tableOptions.pageOptions.page_size,
    // })
    // tableOptions.data = res.list
    // tableOptions.pageOptions.total = res.total
  } catch (error) {
    console.log("⚡ error ==> ", error)
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
  } finally {
    tableOptions.loading = false
  }
}

function handleAddPromptWords(title: string) {
  //修改面包屑title
  routerStore.changeMenuMeta({
    name: "PromptEdit",
    meta: {
      title: `提示词${title}`,
    },
  })
  router.push({ name: "PromptEdit", query: { title } })
}

function deletePromptWords(item) {
  $g.confirm({
    content: "是否确认删除",
  })
    .then(async () => {
      if (chooseRadio == item.id) chooseRadio = ""
      initData(false)
      $g.msg("删除成功", "success")
    })
    .catch(() => {})
}

onMounted(() => {
  initData()
})

onActivated(() => {
  initData(false)
})
</script>

<style lang="scss" scoped>
.check-radio {
  :deep() {
    .el-radio__label {
      display: none;
    }
  }
}
</style>
