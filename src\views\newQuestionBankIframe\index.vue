<template>
  <div>
    <iframe v-if="url" :src="url" frameborder="0" class="iframe"></iframe>
  </div>
</template>

<script lang="ts" setup>
import { createWebHashHistory } from "vue-router"
import config from "@/config/index"
const { baseNewQuestionOrigin } = config
let url = $ref<any>("https://zjse.xkw.com")
const route: any = useRoute()
const router: any = useRouter()
function setURL() {
  const { _openid, _m } = route.query
  const split =
    router.options.history instanceof createWebHashHistory().constructor
      ? "#"
      : ""
  const _n = `${location.origin}/${split}${route.path}?_state=1`
  // _m 当使⽤iframe嵌套题库⻚⾯时，需传⼊容器⻚⾯地址；
  // _n ⽤户试卷⽣成成功后，合作⽅接收通知地址。(跳转地址)
  if (_openid && _m && _n) {
    const newUrl = new URL(url)
    const xkw_url = $g.tool.addSearchParams(newUrl, {
      _openid,
      _m,
      _n,
    })
    url = xkw_url.toString()
  }
}
function onPostMessage(event) {
  if (event.data === "close") {
    window.close()
  }
}
watch(
  () => route.query,
  () => {
    // 在这里处理路由变化的逻辑
    const { openid, paperid } = route.query
    if (openid && paperid) {
      window.opener?.postMessage({ openid, paperid }, baseNewQuestionOrigin)
    }
  },
  {
    deep: true,
  },
)
onMounted(() => {
  window.addEventListener("message", onPostMessage)
  setURL()
})
onBeforeMount(() => {
  window.removeEventListener("message", onPostMessage)
})
</script>

<style lang="scss" scoped>
.iframe {
  width: 100vw;
  height: 100vh;
}
</style>
