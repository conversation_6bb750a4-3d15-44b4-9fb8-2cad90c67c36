<template>
  <div></div>
</template>
<script lang="ts" setup>
import {
  fetchExamXKWBind,
  fetchExamXKWOAuth,
  bindExamXKWApi,
} from "@/api/common"
import config from "@/config/index"
const route = useRoute()
let xkwOpenId = $ref<any>(null)
async function bindExamXKW() {
  const { code, state } = route.query
  if (code && state) {
    const data = await await bindExamXKWApi(
      {
        code: route.query?.code,
        state: route.query?.state,
      },
      { Authorization: route.query?.qmdrKey },
    )
    xkwOpenId = data?.openId
    if (data?.openId) {
      $g.msg("绑定成功", "success")
      getXKWBind()
    }
  }
}
async function goToXKWAuth() {
  let agreement =
    import.meta.env.VITE_APP_ENV != "development" ? "https" : "http"
  const res = await fetchExamXKWOAuth({
    redirectUri: `${agreement}://zujuan.qimingdaren.com/#/third/newQuestionBankRedirect`,
    xkwService: "COMPOSITION",
  }, { Authorization: route.query?.qmdrKey })
  if (res) {
    location.href = res
  }
}
function goToXKW() {
  const params = new URLSearchParams({
    _openid: xkwOpenId,
    _m: `${location.protocol}//zujuan.qimingdaren.com/#/third/newQuestionBankRedirect`,
  })
  const url = `${
    location.protocol
  }//zujuan.qimingdaren.com/#/newQuestionBankIframe?${params.toString()}`
  location.replace(url)
}
async function getXKWBind() {
  const data = await fetchExamXKWBind({Authorization: route.query?.qmdrKey})
  xkwOpenId = data?.openId
  if (!xkwOpenId) {
    const { code, state } = route.query
    if (code && state) {
      bindExamXKW()
      return
    }
    goToXKWAuth()
  } else {
    goToXKW()
  }
}
onMounted(() => {
  getXKWBind()
})
</script>
