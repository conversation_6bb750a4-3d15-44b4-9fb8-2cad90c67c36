<template>
  <g-dialog
    :title="typeTitle + title"
    :formOptions="formOptions"
    v-model:show="showDialog"
    @confirm="confirm"
  >
    <g-form :formOptions="formOptions"> </g-form>
  </g-dialog>
</template>

<script lang="ts" setup>
import {
  getLiteracyAdd,
  getAbilityAdd,
  getLiteracyEdit,
  getAbilityEdit,
  getKnowledgeAbilityAdd,
  getKnowledgeAbilityEdit,
} from "@/api/resourceMgt"
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  show: {
    type: Boolean,
    default: false,
  },
  params: {
    type: Object,
    default: () => {},
  },
})

const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
const formOptions = reactive<any>({
  loading: false,
  ref: null as any,
  items: {
    name: {
      type: "text",
      label: "节点名称",
      maxlength: 32,
      rule: true,
      labelWidth: "150px",
    },
    ordinal: {
      type: "number",
      label: "排序值(越小越靠前)",
      labelWidth: "150px",
      min: 0,
    },
  },
  data: {
    name: null,
    ordinal: 0,
  },
})

const titleType = {
  ability: "关键能力",
  literacy: "学科素养",
  knowledge: "知识能力",
}

const title = $computed(() => {
  return props.isEdit ? "编辑" : "新增"
})

const typeTitle = $computed(() => {
  return titleType[props.params.type]
})

/* 编辑内容 */
async function edit() {
  try {
    formOptions.loading = true
    const fun =
      props.params.type == "ability"
        ? getAbilityEdit
        : props.params.type == "literacy"
        ? getLiteracyEdit
        : getKnowledgeAbilityEdit
    let query = {
      ...formOptions.data,
      parentId: props.params.parentId ? props.params.parentId : 0,
    }
    delete query.children
    await fun(query)
    $g.msg(`${typeTitle}新增成功`)
  } catch (err) {
    console.log(err)
    formOptions.loading = false
  }
}

/* 新增内容 */
async function add() {
  try {
    formOptions.loading = true
    const fun =
      props.params.type == "ability"
        ? getAbilityAdd
        : props.params.type == "literacy"
        ? getLiteracyAdd
        : getKnowledgeAbilityAdd
    await fun({
      ...formOptions.data,
      parentId: props.params.parentId ? props.params.parentId : 0,
    })
    $g.msg(`${typeTitle}新增成功`)
  } catch (err) {
    console.log(err)
    formOptions.loading = false
  }
}

async function confirm() {
  props.isEdit ? await edit() : await add()
  await emit("refresh", props.params.type)
  await emit("update:show", false)
  formOptions.loading = false
}

watch(
  () => props.show,
  (val) => {
    if (val) {
      formOptions.data = {
        ...props.params,
        ordinal: props.params.ordinal || 0,
      }
    }
  },
)
</script>
