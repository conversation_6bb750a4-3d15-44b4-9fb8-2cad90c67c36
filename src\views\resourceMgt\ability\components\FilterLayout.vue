<template>
  <div class="resourceMgt-container-main">
    <div class="flex">
      <g-form class="!w-auto" :formOptions="filterFormOptions"> </g-form>
    </div>
    <n-grid x-gap="40" :cols="3">
      <n-gi>
        <div class="flex items-center justify-between">
          <div class="flex flex-1">
            <h3 class="mr-20px w-auto flex-1">{{ options.title1 }}</h3>
            <div class="flex items-center flex-1 justify-end">
              <slot name="left-action"> </slot>
            </div>
            <div class="flex items-center flex-1 ml-15px">
              <n-input
                v-model:value="keyword1"
                type="text"
                placeholder="输入关键能力检索"
                @input="treeSearch(1)"
              />
            </div>
          </div>
        </div>
        <slot name="left" :filterData="filterFormOptions.data"></slot>
      </n-gi>
      <n-gi>
        <div class="flex items-center justify-between">
          <div class="flex flex-1 flex-shrink-0">
            <h3 class="w-auto flex-1 flex items-center">
              <span class="flex-shrink-0">{{ options.title2 }}</span>
            </h3>

            <div class="flex items-center flex-1 justify-end">
              <slot name="right-action"> </slot>
            </div>
            <div class="flex items-center flex-1 ml-15px">
              <n-input
                v-model:value="keyword2"
                type="text"
                placeholder="输入学科素养检索"
                @input="treeSearch(2)"
              />
            </div>
          </div>
        </div>
        <slot name="right"></slot>
      </n-gi>
      <n-gi>
        <div class="flex items-center justify-between">
          <div class="flex flex-1 flex-shrink-0">
            <h3 class="w-auto flex-1 flex items-center">
              <span class="flex-shrink-0">{{ options.title3 }}</span>
            </h3>

            <div class="flex items-center flex-1 justify-end">
              <slot name="knowledge-action"> </slot>
            </div>
            <div class="flex items-center flex-1 ml-15px">
              <n-input
                v-model:value="keyword3"
                type="text"
                placeholder="输入知识能力检索"
                @input="treeSearch(3)"
              />
            </div>
          </div>
        </div>
        <slot name="knowledge"></slot>
      </n-gi>
    </n-grid>
  </div>
</template>

<script setup lang="ts">
import {
  getLiteracyList,
  getNewStageListApi,
  getNewSubjectListApi,
  getAbilityList,
  getKnowledgeAbilityList,
} from "@/api/resourceMgt"

const props = defineProps({
  // 用于控制左侧树的显示
  options: {
    type: Object,
    default() {
      return {
        title1: "关键能力",
        title2: "学科素养",
        title3: "知识能力",
      }
    },
  },
})

let keyword1 = $ref("")
let keyword2 = $ref("")
let keyword3 = $ref("")

const filterFormOptions = reactive<any>({
  ref: null as any,
  filter: true,
  labelWidth: "64px",
  showFilterButton: false,
  items: {
    stage: {
      type: "select",
      label: "学段",
      width: "150px",
      options: [],
      labelField: "name",
      valueField: "commonStagesId",
      clearable: false,
    },
    subject: {
      type: "select",
      label: "学科",
      options: [],
      labelField: "name",
      valueField: "commonSubjectsId",
      width: "150px",
      clearable: false,
    },
  },
  // 列表接口使用到的筛选值,及时搜索时使用
  data: {
    stage: null,
    subject: null,
  },
  // 列表接口使用到的筛选值,非及时搜索时使用
  filterData: {},
})

watch(
  () => filterFormOptions.data,
  (newVal) => {
    search($g._.cloneDeep(newVal))
  },
  {
    deep: true,
  },
)
const getNewSubjectList = async () => {
  const res = await getNewSubjectListApi({
    sysStageId: filterFormOptions.data.stage,
  })
  filterFormOptions.items.subject.options = res
  filterFormOptions.items.subject.labelField = "sysCourseName"
  filterFormOptions.items.subject.valueField = "sysCourseId"
  filterFormOptions.data.subject = res[0]?.sysCourseId
}
watch(
  () => filterFormOptions.data.stage,
  async (newVal) => {
    filterFormOptions.items.subject.options = []
    filterFormOptions.data.subject = null
    if (newVal) {
      await getNewSubjectList()
    }
  },
)

watch(
  () => filterFormOptions.data.subject,
  async (newVal) => {
    if (newVal) {
      await getRightTree()
    } else {
      emit("getTree2", [])
    }
  },
)

const emit = defineEmits([
  "search",
  "searchDataOption",
  "treeSearch",
  "getTree1",
  "getTree2",
  "getTree3",
])

async function search(form) {
  let flag = Object.values(form).every(function (value) {
    return value !== undefined && value !== null
  })
  // 获取筛选的数据项，传递到组件外部
  const searchOption = {}
  for (const [key, value] of Object.entries(form)) {
    let { options, valueField, labelField } = filterFormOptions.items[key]
    let option = options.find((item) => item[valueField] == value)
    searchOption[key] = { ...option, name: option?.[labelField], value }
  }
  emit("searchDataOption", searchOption)
  if (!flag) {
    emit("getTree1", [])
    emit("getTree3", [])
    return
  }
  await getLeftTree()
  await getKnowledgeTree()
}

function treeSearch(mode) {
  emit("treeSearch", {
    keyword: mode == 1 ? keyword1 : mode == 2 ? keyword2 : keyword3,
    mode,
  })
}

onMounted(() => {
  initData()
})

const getNewStageList = async () => {
  const res = await getNewStageListApi()
  filterFormOptions.items.stage.options = res
  filterFormOptions.items.stage.labelField = "title"
  filterFormOptions.items.stage.valueField = "id"
  filterFormOptions.data.stage = res.find((item) => item.title == "高中")?.id
}
async function initData() {
  await getNewStageList()
}

/* 获取左边关键能力树 */
async function getLeftTree() {
  let res = await getAbilityList({
    sysCourseId: filterFormOptions.data.subject,
  })
  emit("getTree1", res)
}

/* 获取右边学科素养树 */
async function getRightTree() {
  let res = await getLiteracyList({
    sysCourseId: filterFormOptions.data.subject,
  })
  emit("getTree2", res)
}

/* 获取知识能力树 */
async function getKnowledgeTree() {
  let res = await getKnowledgeAbilityList({
    sysCourseId: filterFormOptions.data.subject,
  })
  emit("getTree3", res)
}

defineExpose({
  refreshTree: getLeftTree,
  refreshRightTree: getRightTree,
  refreshKnowledgeTree: getKnowledgeTree,
})
</script>

<style lang="scss" scoped></style>
