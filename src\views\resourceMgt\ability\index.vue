<template>
  <div>
    <FilterLayout
      :options="{
        title1: '关键能力',
        title2: '学科素养',
        title3: '知识能力',
      }"
      @searchDataOption="searchDataOption = $event"
      @treeSearch="treeSearch"
      @getTree1="getTree1"
      @getTree2="getTree2"
      @getTree3="getTree3"
      ref="filterFormRef"
    >
      <template #left="{}">
        <g-tree
          ref="Tree1Ref"
          treeName="LeftTree"
          class="p-10px"
          :treeData="treeData1"
          nodeKey="id"
          :default-expanded-keys="[currentKey2]"
          :default-checked-keys="[currentKey2]"
          auto-expand-parent
          render-after-expand
          :expand-on-click-node="true"
          style="height: calc(100vh - 350px); overflow: auto"
          :highlight-check="false"
          :defaultProps="{
            label: 'name',
          }"
        >
          <template #body="{ data }">
            <div class="flex items-center justify-between w-full">
              <div>{{ data.name }}</div>
              <div class="flex-shrink-0 flex justify-end gap-x-[8px]">
                <g-icon
                  class="cursor-pointer"
                  name="ri-add-line"
                  color="var(--g-success)"
                  size="18"
                  @click.stop="open('ability', data)"
                />
                <g-icon
                  class="cursor-pointer"
                  name="ri-edit-line"
                  color="var(--g-primary)"
                  size="18"
                  @click.stop="edit('ability', data)"
                />
                <g-icon
                  class="cursor-pointer"
                  name="ri-delete-bin-line"
                  style="color: var(--g-danger) !important"
                  size="18"
                  @click.stop="delPointApi('ability', data)"
                />
              </div>
            </div>
          </template>
        </g-tree>
      </template>
      <template #left-action>
        <n-space justify="center">
          <n-button type="primary" text @click="open('ability')">
            新增</n-button
          >
        </n-space>
      </template>
      <template #right-action>
        <n-space justify="center">
          <n-button type="primary" text @click="open('literacy')">
            新增</n-button
          >
        </n-space>
      </template>
      <template #right>
        <g-tree
          treeName="RightTree"
          ref="Tree2Ref"
          class="p-10px"
          :treeData="treeData2"
          nodeKey="id"
          :default-expanded-keys="[currentKey]"
          :default-checked-keys="[currentKey]"
          auto-expand-parent
          render-after-expand
          :expand-on-click-node="true"
          style="height: calc(100vh - 350px); overflow: auto"
          :highlight-check="false"
          :defaultProps="{
            label: 'name',
          }"
        >
          <template #body="{ data }">
            <div class="flex items-center justify-between w-full">
              <div>{{ data.name }}</div>
              <div class="flex-shrink-0 flex justify-end gap-x-[8px]">
                <g-icon
                  class="cursor-pointer"
                  name="ri-add-line"
                  color="var(--g-success)"
                  size="18"
                  @click.stop="open('literacy', data)"
                />
                <g-icon
                  class="cursor-pointer"
                  name="ri-edit-line"
                  color="var(--g-primary)"
                  size="18"
                  @click.stop="edit('literacy', data)"
                />
                <g-icon
                  class="cursor-pointer"
                  name="ri-delete-bin-line"
                  style="color: var(--g-danger) !important"
                  size="18"
                  @click.stop="delPointApi('literacy', data)"
                />
              </div>
            </div>
          </template>
        </g-tree>
      </template>
      <template #knowledge-action>
        <n-space justify="center">
          <n-button type="primary" text @click="open('knowledge')">
            新增</n-button
          >
        </n-space>
      </template>
      <template #knowledge>
        <g-tree
          treeName="RightTree"
          ref="Tree3Ref"
          class="p-10px"
          :treeData="treeData3"
          nodeKey="id"
          :default-expanded-keys="[currentKey3]"
          :default-checked-keys="[currentKey3]"
          auto-expand-parent
          render-after-expand
          :expand-on-click-node="true"
          style="height: calc(100vh - 350px); overflow: auto"
          :highlight-check="false"
          :defaultProps="{
            label: 'name',
          }"
        >
          <template #body="{ data }">
            <div class="flex items-center justify-between w-full">
              <div>{{ data.name }}</div>
              <div class="flex-shrink-0 flex justify-end gap-x-[8px]">
                <g-icon
                  class="cursor-pointer"
                  name="ri-add-line"
                  color="var(--g-success)"
                  size="18"
                  @click.stop="open('knowledge', data)"
                />
                <g-icon
                  class="cursor-pointer"
                  name="ri-edit-line"
                  color="var(--g-primary)"
                  size="18"
                  @click.stop="edit('knowledge', data)"
                />
                <g-icon
                  class="cursor-pointer"
                  name="ri-delete-bin-line"
                  style="color: var(--g-danger) !important"
                  size="18"
                  @click.stop="delPointApi('knowledge', data)"
                />
              </div>
            </div>
          </template>
        </g-tree>
      </template>
    </FilterLayout>
    <Add
      v-model:show="showDialog"
      :params="params"
      :isEdit="isEdit"
      @refresh="refresh"
    />
  </div>
</template>

<script lang="ts" setup>
import FilterLayout from "./components/FilterLayout.vue"
import Add from "./components/Add.vue"
import {
  getLiteracyDelete,
  getAbilityDelete,
  getKnowledgeAbilityDelete,
} from "@/api/resourceMgt"
// FilterLayout组件请求数据时携带的参数信息，保存下来用于导出数据时使用
let searchDataOption: { [k: string]: any } = {}
let showDialog = $ref(false)
let params = $ref<any>({})
let filterFormRef = $ref<any>(null)
let isEdit = $ref(false)
let currentKey = $ref<any>(null)
let currentKey2 = $ref<any>(null)
let currentKey3 = $ref<any>(null)
let Tree1Ref: any = $ref(null)
let Tree2Ref: any = $ref(null)
let Tree3Ref: any = $ref(null)
function treeSearch(v) {
  let node = v.mode == 1 ? Tree1Ref : v.mode == 2 ? Tree2Ref : Tree3Ref
  node.getFilterNode(v.keyword)
}
let treeData1 = $ref([])
let treeData2 = $ref([])
let treeData3 = $ref([])

function getTree1(tree) {
  treeData1 = tree || []
}

function getTree2(tree) {
  treeData2 = transformDataStructure(tree) || []
}

function getTree3(tree) {
  treeData3 = transformDataStructure(tree) || []
}

function keySet(type: string, row) {
  type == "ability"
    ? (currentKey2 = row?.id)
    : type == "literacy"
    ? (currentKey = row?.id)
    : (currentKey3 = row?.id)
}

/* 新增节点 */
function open(type: string, row?) {
  params = {}
  isEdit = false
  keySet(type, row)
  params.type = type
  params.sysCourseId = searchDataOption.subject.sysCourseId
  params.parentId = row?.id
  showDialog = true
}
/* 编辑节点 */
function edit(type: string, row) {
  params = {}
  isEdit = true
  keySet(type, row)
  params = row
  params.type = type
  params.sysCourseId = searchDataOption.subject.sysCourseId
  showDialog = true
}
/* 刷新右侧树 */
function refreshRightTree() {
  filterFormRef?.refreshRightTree()
}
/* 刷新左右树 */
function refreshTree() {
  filterFormRef?.refreshTree()
}
/* 刷新知识能力树 */
function refreshKnowledgeTree() {
  filterFormRef?.refreshKnowledgeTree()
}

/* 删除节点 */
async function deleteNode(type, row) {
  type == "ability"
    ? await getAbilityDelete({
        id: row.id,
      })
    : type == "literacy"
    ? await getLiteracyDelete({
        id: row.id,
      })
    : await getKnowledgeAbilityDelete({
        id: row.id,
      })
}

/* 删除节点 */
function delPointApi(type, row) {
  if (row.children?.length) {
    return $g.msg("无法删除有子节点的节点合集！", "warning")
  }
  $g.confirm({ content: "是否删除当前节点" })
    .then(async () => {
      try {
        await deleteNode(type, row)
        $g.msg("删除成功")
        refresh(type)
      } catch (err) {
        console.log(err)
      }
    })
    .catch((err) => {})
}

function refresh(type) {
  type == "ability"
    ? refreshTree()
    : type == "literacy"
    ? refreshRightTree()
    : refreshKnowledgeTree()
}

/* 递归处理数据 */
function transformDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.children = newItem.children?.length
        ? transformDataStructure(newItem.children)
        : []

      return newItem
    })
  } else if (typeof data === "object" && data !== null) {
    return Object.keys(data).reduce((obj, key) => {
      obj[key] = transformDataStructure(data[key])
      return obj
    }, {})
  }
  return data
}
</script>

<style scoped lang="scss">
:deep() {
  .el-tree-node__content {
    height: auto !important;
    background-color: transparent !important;
  }
  .el-tree-node {
    white-space: break-spaces;
  }
  .custom-tree-node {
    overflow: hidden;
    .element-tree-node-label-wrapper {
      overflow: hidden;
    }
  }

  .is-checked {
    .el-tree-node__content {
      background-color: #f1f8ff !important;
    }
    .el-tree-node__children {
      .el-tree-node__content {
        background-color: transparent !important;
      }
    }
  }
}
</style>
