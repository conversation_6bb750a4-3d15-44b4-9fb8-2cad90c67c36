<template>
  <div class="add-but">
    <el-popover
      ref="popoverRef"
      placement="bottom-start"
      trigger="click"
      title="添加节点"
      :width="310"
    >
      <el-space wrap>
        <div
          v-show="exclusiveNodeVisible"
          class="node-select"
          @click="addExclusiveNode"
        >
          <el-icon size="28" color="#4cacfc"><Share /></el-icon>
          <el-text>按钮分支</el-text>
        </div>

        <div class="node-select" @click="addNotifyNode">
          <el-icon size="28" color="#4cacfc"><Comment /></el-icon>
          <el-text>消息模板</el-text>
        </div>
      </el-space>
      <template #reference>
        <el-button
          v-show="!readOnly"
          :icon="Plus"
          type="primary"
          style="z-index: 1"
          circle
        ></el-button>
      </template>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import type { PopoverInstance } from "element-plus"
import { Plus, Share, Comment } from "@element-plus/icons-vue"
import type { Ref } from "vue"

const { readOnly } = inject<{
  readOnly?: Ref<boolean>
}>("flowDesign", { readOnly: ref(false) })

const $props = defineProps({
  node: {
    type: Object,
    default: () => ({}),
  },
})

const popoverRef = $ref<PopoverInstance>()

const $emits = defineEmits(["addNode"])

//分支节点是否可见,分支节点不能出现在开始节点、按钮节点、分支节点下
const exclusiveNodeVisible = computed<boolean>(
  () =>
    !["start", "condition", "exclusive"].includes($props.node.type) &&
    $props.node.child?.type !== "exclusive",
)

// 添加一个分支节点
function addExclusiveNode() {
  $emits("addNode", "exclusive")
  popoverRef?.hide()
}

function addNotifyNode() {
  $emits("addNode", "notify")
  popoverRef?.hide()
}
</script>

<style scoped lang="scss">
.node-select {
  cursor: pointer;
  display: flex;
  padding: 8px;
  width: 135px;
  border-radius: 10px;
  position: relative;
  background-color: #f5f7fa;

  &:hover {
    background-color: #ecf5ff;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    color: #70bdfd !important;
  }
  .el-text {
    margin-left: 10px;
  }
}

.add-but {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 20px 0 32px;
  position: relative;

  &:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 1px;
    height: 100%;
    background-color: #dcdfe6;
  }
}
</style>
