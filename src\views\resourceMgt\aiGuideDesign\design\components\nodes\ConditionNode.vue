<template>
  <div class="branch-node">
    <Node v-bind="$attrs" color="lightblue" :node="node">
      <el-text :class="!node.text && 'text-[red]'">{{
        node.text || "请填写按钮文字!"
      }}</el-text>
      <slot name="append" />
    </Node>
  </div>
</template>

<script setup lang="ts">
import Node from "./Node.vue"

const props = defineProps({
  node: {
    type: Object,
    default: () => {},
  },
})

const { nodesError } = inject("flowDesign", { nodesError: ref({}) })

watchEffect(() => {
  const errors: any[] = []
  const { id, name, text } = props.node

  if (!text) {
    errors.push({ id: id, name: name, message: "请填写按钮文字" })
  }

  // 记录错误
  if (errors.length > 0) {
    nodesError.value[id] = errors
  } else {
    delete nodesError.value[id]
  }
})
</script>

<style scoped lang="scss">
.branch-node {
  :deep(.node-box) {
    margin: 60px 40px 0;
  }
}
</style>
