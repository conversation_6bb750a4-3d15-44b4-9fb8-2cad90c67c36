<template>
  <GatewayNode v-bind="$attrs" :node="node">
    <template #default="{ addNode, readOnly }">
      <el-button
        type="primary"
        :disabled="readOnly"
        plain
        round
        @click="addNode('condition', node)"
        >添加按钮</el-button
      >
    </template>
  </GatewayNode>
</template>

<script setup lang="ts">
import GatewayNode from "./GatewayNode.vue"

defineProps({
  node: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<style scoped lang="scss"></style>
