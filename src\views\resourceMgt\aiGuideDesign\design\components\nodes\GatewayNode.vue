<template>
  <div
    class="gateway-node"
    :class="node.children.length === 1 ? 'single-button' : ''"
  >
    <div class="add-branch">
      <slot :add-node="addNode" :read-only="readOnly"></slot>
    </div>
    <div v-for="(item, index) in node.children" :key="item.id" class="col-box">
      <!-- 边框显示 -->
      <template v-if="index === 0">
        <div class="top-left-border"></div>
        <div class="bottom-left-border" />
      </template>

      <template v-else-if="node.children.length === index + 1">
        <div class="top-right-border"></div>
        <div class="bottom-right-border" />
      </template>

      <TreeNode
        :node="item"
        v-bind="$attrs"
        class="col-node"
        @addNode="addNode"
        @delNode="$emits('delNode', $event)"
        @activeNode="$emits('activeNode', $event)"
      >
        <template #append>
          <div
            v-show="index !== 0 && !readOnly"
            class="move-left"
            @click.stop="moveLeft(index)"
          >
            <el-icon size="16"><ArrowLeftBold /></el-icon>
          </div>
          <div
            v-show="node.children.length !== index + 1 && !readOnly"
            class="move-right"
            @click.stop="moveRight(index)"
          >
            <el-icon size="16"><ArrowRightBold /></el-icon>
          </div>
        </template>
      </TreeNode>
    </div>
  </div>
  <Add :node="node" class="branch-but" @addNode="addNode" />
</template>

<script setup lang="ts">
import TreeNode from "./TreeNode.vue"
import { ArrowLeftBold, ArrowRightBold } from "@element-plus/icons-vue"
import Add from "./Add.vue"

const $emits = defineEmits(["addNode", "delNode", "activeNode"])

const props = defineProps({
  node: {
    type: Object,
    default: () => ({}),
  },
})

const { readOnly } = inject("flowDesign", { readOnly: ref(false) })

// 添加按钮
function addNode(type, node) {
  $emits("addNode", type, node || props.node)
}

//右移按钮
function moveRight(index: number) {
  const node = props.node.children[index]
  props.node.children.splice(index, 1)
  props.node.children.splice(index + 1, 0, node)
}

// 左移按钮
function moveLeft(index: number) {
  const node = props.node.children[index]
  props.node.children.splice(index, 1)
  props.node.children.splice(index - 1, 0, node)
}
</script>

<style scoped lang="scss">
.gateway-node {
  display: flex;
  column-gap: 10px;
  margin: 0 10px;
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  background-color: var(--designer-bg-color);
  overflow: visible;
  position: relative;

  .add-branch {
    position: absolute;
    left: 50%;
    top: -15px;
    z-index: 2;
    transform: translateX(-50%);
  }

  .col-box {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--designer-bg-color);

    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: auto;
      width: 1px;
      height: 100%;
      background-color: #dcdfe6;
    }

    .top-left-border {
      position: absolute;
      left: 0;
      top: -3px;
      height: 3px;
      width: 50%;
      background-color: var(--designer-bg-color);
    }

    .bottom-left-border {
      position: absolute;
      left: 0;
      bottom: -3px;
      height: 3px;
      width: 50%;
      background-color: var(--designer-bg-color);
    }

    .top-right-border {
      position: absolute;
      right: 0;
      top: -3px;
      height: 3px;
      width: 50%;
      background-color: var(--designer-bg-color);
    }

    .bottom-right-border {
      position: absolute;
      right: 0;
      bottom: -3px;
      height: 3px;
      width: 50%;
      background-color: var(--designer-bg-color);
    }
  }
}

.gateway-node.single-button {
  border-top: 1px solid var(--designer-bg-color);
  border-bottom: 1px solid var(--designer-bg-color);
  box-shadow: 0 0 6px 0 #4cadfc52;

  & > .col-box > .top-left-border,
  & > .col-box > .bottom-left-border {
    display: none;
  }
}

.col-node {
  &:hover {
    .move-right,
    .move-left {
      opacity: 1;
    }
  }
}

.move-right,
.move-left {
  position: absolute;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  opacity: 0;
  font-size: 20px;
  font-weight: bold;
  transition: all 0.3s ease;

  &:hover {
    background-color: #ecf5ff;
  }
}

.move-left {
  left: 0;
}

.move-right {
  right: 0;
}
</style>
