<template>
  <div class="node-box">
    <el-card
      :class="['node', { 'error-node': errorInfo?.length && !_readOnly }]"
      @click="activeNode"
    >
      <!--头部-->
      <template #header>
        <div class="head">
          <div v-if="showInput" @click.stop>
            <el-input
              ref="inputRef"
              v-model="node.name"
              v-click-outside="onClickOutside"
              maxlength="30"
              @blur="onClickOutside"
            />
          </div>
          <el-text v-else tag="b" truncated @click.stop="onShowInput">
            {{ node.name }}{{ nodeTypeName }}
          </el-text>
        </div>
        <!--删除按钮-->
        <span @click.stop>
          <el-popconfirm
            title="您确定要删除该节点吗？"
            width="200"
            :hide-after="0"
            placement="right-start"
            @confirm="delNode"
          >
            <template #reference>
              <el-button
                v-show="close && !_readOnly"
                class="node-close"
                plain
                circle
                :icon="Close"
                size="small"
                type="danger"
              />
            </template>
          </el-popconfirm>
        </span>
        <!--错误提示-->
        <el-tooltip placement="top-start">
          <template #content>
            <div v-for="err in errorInfo" :key="err.id">
              {{ err.message }}
            </div>
          </template>
          <el-icon
            v-show="errorInfo?.length && !_readOnly"
            class="warn-icon"
            size="24"
            @click.stop
            ><Warning />
          </el-icon>
        </el-tooltip>
      </template>
      <!--插槽内容-->
      <slot></slot>
    </el-card>
    <Add :node="node" @add-node="addNode" />
  </div>
</template>

<script setup lang="ts">
import { ClickOutside as vClickOutside, type InputInstance } from "element-plus"
import { Close, Warning } from "@element-plus/icons-vue"
import Add from "./Add.vue"

const _inject = inject("flowDesign", {
  readOnly: ref(false),
  nodesError: ref({}),
})

const $emits = defineEmits(["addNode", "delNode", "activeNode"])

const $props = defineProps({
  node: {
    type: Object,
    required: true,
  },
  color: {
    type: String,
    default: "#fff",
  },
  readOnly: {
    type: Boolean,
    default: false,
  },
  close: {
    type: Boolean,
    default: true,
  },
})

// 错误信息数据
const errorInfo = $computed(() => _inject.nodesError.value[$props.node.id])
// 节点是否只读
const _readOnly = $computed(() => _inject.readOnly?.value || $props.readOnly)
// 节点类型名称
const nodeTypeName = $computed(() => {
  const type = $props.node.contentType
  return type === "rich"
    ? "(富文本)"
    : type === "attach"
    ? "(附件)"
    : type === "markdown"
    ? "(markdown)"
    : ""
})

let showInput = $ref(false)
const inputRef = $ref<InputInstance>()

// 点击名字和编辑按钮打开输入框
function onShowInput() {
  if (_readOnly) return
  showInput = true
  nextTick(() => {
    inputRef?.focus()
  })
}

// 点击输入框外关闭输入框
function onClickOutside() {
  if (showInput) {
    showInput = false
  }
}

// 点击节点处理函数
function activeNode() {
  if (_readOnly) return
  $emits("activeNode", $props.node)
}

// 添加节点处理函数
function addNode(type) {
  $emits("addNode", type, $props.node)
}

// 删除节点处理函数
function delNode() {
  $emits("delNode", $props.node)
}
</script>

<style scoped lang="scss">
.node-box {
  position: relative;
  // 小三角
  &:after {
    content: "";
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translate(-50%);
    border-style: solid;
    width: 0;
    border-width: 8px 6px 4px;
    border-color: #dcdfe6 transparent transparent;
    background-color: var(--designer-bg-color);
  }

  // 错误提示
  .warn-icon {
    cursor: pointer;
    position: absolute;
    right: -30px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--el-color-error);
  }

  .error-node {
    box-shadow: 0 0 5px 0 #dd4848;
  }

  .node {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: visible;
    z-index: 10;
    margin-bottom: 0;

    .node-close {
      width: 20px;
      height: 20px;
      font-size: 14px;
      padding: 2px;
      position: absolute;
      top: -10px;
      right: -10px;
      display: none;
    }

    &:hover {
      &:not(.error-node) {
        box-shadow: 0 0 5px 0 #3398f7;
      }

      .node-close {
        display: block;
      }
    }

    :deep(.el-card__header) {
      padding: 4px 8px;
      border-radius: 7px 7px 0 0;
      background: v-bind(color);
    }

    :deep(.el-card__body) {
      position: relative;
      min-width: 198px;
      max-width: 348px;
      min-height: 60px;
      max-height: 200px;
      overflow: auto;
    }

    .head {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
}
</style>
