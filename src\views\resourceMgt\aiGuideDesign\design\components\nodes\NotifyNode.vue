<template>
  <Node v-bind="$attrs" color="#95d475" :node="node">
    <el-text v-if="nodeIsEmpty" type="danger">请填充消息内容</el-text>
    <template v-else>
      <div v-if="node.contentType === 'attach'">
        <div>共{{ nodeContent.length }}个附件</div>
        <div v-for="(item, index) in nodeContent" :key="index">
          {{ index + 1 }} : {{ item.name }}
        </div>
      </div>

      <div v-if="node.contentType === 'rich'" v-html="nodeContent"></div>

      <g-markdown
        v-else-if="node.contentType === 'markdown'"
        :modelValue="nodeContent"
        mode="preview"
      ></g-markdown>
    </template>
  </Node>
</template>

<script setup lang="ts">
import Node from "./Node.vue"

const props = defineProps({
  node: {
    type: Object,
    default: () => ({}),
  },
})

// 数据为markdown类型时的容器
const markdownContainer = $ref(null)

const { nodesError } = inject("flowDesign", { nodesError: ref({}) })

// 判断节点内容是否为空
const nodeIsEmpty = $computed(() => {
  if (props.node.contentType === "rich") {
    return !props.node.text.replace(/<br>|<\/?p>|&nbsp;/g, "").trim()
  }

  if (props.node.contentType === "markdown") {
    return !props.node.text.trim()
  }

  if (props.node.contentType === "attach") {
    return !props.node.text
  }
  return false
})

// 格式化节点内容输出
const nodeContent = $computed(() => {
  if (props.node.contentType === "attach") {
    return props.node.text ? JSON.parse(props.node.text) : []
  }
  return props.node.text
})

watchEffect(() => {
  const errors: any[] = []
  const { id, name, text } = props.node

  // 校验节点的错误信息
  if (!text) {
    errors.push({ id: id, name: name, message: "请填写消息内容" })
  }
  // 记录错误
  if (errors.length > 0) {
    nodesError.value[id] = errors
  } else {
    delete nodesError.value[id]
  }
})
</script>

<style scoped lang="scss"></style>
