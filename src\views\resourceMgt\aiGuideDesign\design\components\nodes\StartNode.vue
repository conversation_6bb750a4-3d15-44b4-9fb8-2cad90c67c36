<template>
  <div class="start-node">
    <Node v-bind="$attrs" read-only :close="false" :node="node">
      <div class="flex items-center justify-center">
        <el-text>流程开始</el-text>
      </div>
    </Node>
  </div>
</template>

<script setup lang="ts">
import Node from "./Node.vue"

defineProps({
  node: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<style scoped lang="scss">
.start-node {
  padding: 50px 0 0;

  :deep(.node-box) {
    &:after {
      display: none;
    }
    .el-card__header {
      display: none;
    }
  }
}
</style>
