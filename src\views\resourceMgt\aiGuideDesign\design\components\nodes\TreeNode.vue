<template>
  <div class="flex flex-col items-center">
    <slot />
    <component :is="nodes[node.type]" :node="node" v-bind="$attrs">
      <template v-for="(value, name) in $slots" #[name]="scope">
        <slot :name="name" v-bind="scope || {}"></slot>
      </template>
    </component>
    <TreeNode v-if="node.child" :node="node.child" v-bind="$attrs" />
  </div>
</template>

<script setup lang="ts" name="TreeNode">
import Start from "./StartNode.vue"
import End from "./EndNode.vue"
import Notify from "./NotifyNode.vue"
import Exclusive from "./ExclusiveNode.vue"
import Condition from "./ConditionNode.vue"

defineOptions({
  inheritAttrs: false,
})

defineProps({
  node: {
    type: Object,
    default: () => ({}),
  },
})

const nodes = {
  start: Start,
  notify: Notify,
  exclusive: Exclusive,
  condition: Condition,
  end: End,
}
</script>

<style scoped lang="scss"></style>
