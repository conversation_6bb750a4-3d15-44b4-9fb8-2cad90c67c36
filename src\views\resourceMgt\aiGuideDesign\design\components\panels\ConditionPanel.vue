<template>
  <g-form :formOptions="formOptions"></g-form>
</template>

<script setup lang="ts">
const props = defineProps({
  activeData: {
    type: Object,
    default: () => ({}),
  },
})

const formOptions = reactive<any>({
  ref: null,
  labelWidth: "100px",
  items: {
    text: {
      type: "text",
      label: "按钮文案",
      rule: true,
    },
  },
  data: props.activeData,
})

function validate() {
  return new Promise((resolve, reject) => {
    formOptions.ref?.validate((errors) => {
      errors ? reject(errors) : resolve(formOptions.data)
    })
  })
}

defineExpose({
  validate,
})
</script>

<style scoped lang="scss"></style>
