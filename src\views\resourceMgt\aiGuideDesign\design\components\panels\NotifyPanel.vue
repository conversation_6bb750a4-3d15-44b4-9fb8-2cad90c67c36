<template>
  <g-form :formOptions="formOptions">
    <template #contentType>
      <n-radio-group
        v-model:value="formOptions.data.contentType"
        name="radiogroup"
      >
        <n-space>
          <n-radio
            v-for="option in formOptions.items.contentType.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </n-radio>
        </n-space>
      </n-radio-group>
    </template>
    <template #text>
      <!-- 富文本 -->
      <g-editor
        v-if="formOptions.data.contentType === 'rich'"
        v-model="formOptions.data.text"
        :initProps="{
          height: 600,
        }"
      />

      <!-- 附件上传 -->
      <g-upload
        v-if="formOptions.data.contentType === 'attach'"
        :file-size="1048576000"
        :accept="'.ppt,.pptx,.pdf,.mp4,.mp3,.jpeg,.jpg,.png,.doc,.docx'"
        v-model:fileList="formOptions.data.text"
        type="drag"
        :max="10"
      >
      </g-upload>

      <!-- markdown -->
      <g-markdown
        class="w-full"
        v-if="formOptions.data.contentType === 'markdown'"
        v-model="formOptions.data.text"
      ></g-markdown>
    </template>
  </g-form>
</template>

<script lang="ts" setup>
const props = defineProps({
  activeData: {
    type: Object,
    default: () => ({}),
  },
})

const formOptions = reactive({
  ref: null as any,
  labelWidth: "60px",
  items: {
    contentType: {
      type: "radio",
      label: "类型",
      options: [
        { value: "rich", label: "富文本" },
        { value: "markdown", label: "MarkDown" },
        { value: "attach", label: "附件" },
      ],
      slot: true,
    },
    text: {
      type: "upload",
      slot: true,
      rule: {
        validator: (rule, value, cb) => {
          let contentType = formOptions.data.contentType
          if (contentType === "rich") {
            // 如果是富文本编辑器，排除<br><p></p>&nbsp;等标签后判断是否为空
            let formatStr = value.replace(/<br>|<\/?p>|&nbsp;/g, "").trim()
            return formatStr ? cb() : cb(new Error("请填充模板内容"))
          }

          if (contentType === "markdown") {
            return value.trim() ? cb() : cb(new Error("请填充MarkDown内容"))
          }

          if (contentType === "attach") {
            return value.length ? cb() : cb(new Error("请上传附件"))
          }
        },
      },
    },
  },
  data: props.activeData,
})

// 初始化的时候，根据contentType的值，来格式化数据，如果是附件，则转换成数组形式
if (props.activeData.contentType === "attach") {
  console.log("formOptions.data.text", formOptions.data.text)
  formOptions.data.text = formOptions.data.text
    ? JSON.parse(formOptions.data.text)
    : []
}

// contentType变化时，清空
watch(
  () => formOptions.data.contentType,
  (val) => {
    formOptions.data.text = val === "attach" ? [] : ""
    formOptions.ref?.restoreValidation()
  },
)

// 验证表单数据
function validate() {
  return new Promise((resolve, reject) => {
    formOptions.ref
      ?.validate((errors) => {
        if (!errors) {
          if (formOptions.data.contentType === "attach") {
            let uploadRes = formOptions.ref?.checkFileUpload()
            if (!uploadRes) reject("文件上传未完成")
          }

          // 验证通过以后返回数据
          resolve({
            ...formOptions.data,
            text:
              formOptions.data.contentType === "attach"
                ? JSON.stringify(formOptions.data.text)
                : formOptions.data.text,
          })
        }
        reject("校验失败")
      })
      .catch((err) => {
        console.log("校验失败", err)
      })
  })
}

defineExpose({
  validate,
})
</script>

<style scoped lang="scss"></style>
