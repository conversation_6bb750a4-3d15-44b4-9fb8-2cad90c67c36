<template>
  <n-drawer
    v-model:show="panelVisible"
    width="50%"
    placement="right"
    :trap-focus="false"
    destroy-on-close
  >
    <n-drawer-content closable>
      <template #header>
        <el-input
          v-show="showInput"
          v-model="activeDataCopy.name"
          v-click-outside="onClickOutside"
          maxlength="30"
          @blur="onClickOutside"
        ></el-input>
        <el-link
          v-show="!showInput"
          :underline="false"
          :icon="EditPen"
          @click="showInput = true"
        >
          {{ activeDataCopy?.name || "节点配置" }}
        </el-link>
      </template>

      <div class="pr-20px">
        <component
          :is="panels[activeData.type]"
          :active-data="activeDataCopy"
          ref="currentPanel"
        />
      </div>
      <div class="flex justify-between mt-20px pl-80px pr-40px">
        <el-button
          size="large"
          class="w-2/5"
          type="primary"
          @click="handleSaveNode"
        >
          确 定
        </el-button>
        <el-button size="large" class="w-2/5" @click="panelVisible = false">
          取 消
        </el-button>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { ClickOutside as vClickOutside } from "element-plus"
import { EditPen } from "@element-plus/icons-vue"
import Notify from "./NotifyPanel.vue"
import Condition from "./ConditionPanel.vue"

const panels = {
  notify: Notify,
  condition: Condition,
}

let showInput = $ref(false)

// 当前显示的节点面板Ref
const currentPanel: any = $ref(null)

const props = defineProps({
  activeData: {
    type: Object,
    default: () => ({}),
  },
})
const emits = defineEmits(["nodeDataChange"])
// 面板显示状态
const panelVisible = defineModel<boolean>({ required: true })

// 节点数据备份，修改时先修改备份数据
let activeDataCopy = $ref<any>({})
watch(panelVisible, (visible) => {
  if (!visible) return
  const { id, name, type, text, contentType } = props.activeData
  activeDataCopy = { id, name, type, contentType, text }
})

// 点击外部关闭名称输入框
function onClickOutside() {
  showInput && (showInput = false)
}

// 保存节点信息
function handleSaveNode() {
  currentPanel
    ?.validate()
    .then((res) => {
      props.activeData.name = activeDataCopy.name
      props.activeData.contentType = res.contentType
      props.activeData.text = res.text
      panelVisible.value = false
      emits("nodeDataChange")
    })
    .catch((err) => {
      console.log("验证失败", err)
    })
}
</script>

<style scoped lang="scss"></style>
