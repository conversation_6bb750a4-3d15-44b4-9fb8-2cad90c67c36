<template>
  <div class="designer-container" :class="fullScreen ? 'full-screen' : ''">
    <div class="action-button">
      <el-button
        :type="publishState ? 'danger' : 'primary'"
        :loading="publishLoading"
        @click="handlePublish"
      >
        {{ publishState ? "取消发布" : "发布流程" }}
      </el-button>
      <!--放大/缩小视图-->
      <el-tooltip content="放大视图" placement="bottom-start">
        <el-button
          size="small"
          :icon="Plus"
          circle
          :disabled="zoom >= 200"
          @click="zoom += 10"
        />
      </el-tooltip>
      <span>{{ zoom }}%</span>
      <el-tooltip content="缩小视图" placement="bottom-start">
        <el-button
          size="small"
          :icon="Minus"
          circle
          :disabled="zoom <= 50"
          @click="zoom -= 10"
        />
      </el-tooltip>

      <!-- 全屏编辑 -->
      <el-tooltip
        :content="fullScreen ? '退出全屏' : '全屏编辑'"
        placement="bottom-start"
      >
        <el-button
          size="small"
          :icon="FullScreen"
          circle
          @click="fullScreen = !fullScreen"
        />
      </el-tooltip>

      <el-button :loading="saveLoading" @click="handleSave(false)">
        {{ readOnly ? "编辑" : "保存" }}
      </el-button>
    </div>
    <el-scrollbar>
      <div
        class="nodes-container"
        :style="{ transform: `scale(${zoom / 100})` }"
      >
        <TreeNode
          :node="nodesData"
          @addNode="handleAddNode"
          @delNode="handleDelNode"
          @activeNode="handleActiveNode"
        ></TreeNode>
      </div>
    </el-scrollbar>

    <!-- 面板 -->
    <Panel
      v-model="panelVisible"
      :active-data="activeData"
      @nodeDataChange="startAutoSaveCountDown"
    ></Panel>
  </div>
</template>

<script setup lang="ts">
import { saveProcessTree, getProcessTree, genUniquenessId } from "@/api/bookMgt"
import { Plus, Minus, FullScreen } from "@element-plus/icons-vue"
import Panel from "./components/panels/index.vue"
import TreeNode from "./components/nodes/TreeNode.vue"
const route = useRoute()

// 面板缩放级别
let zoom = $ref(100)
// 节点设置面板的可见状态
let panelVisible = $ref(false)
// 当前设置的节点数据
let activeData = $ref({})
// 是否全屏
let fullScreen = $ref(false)
// 保存loading
let saveLoading = $ref(false)
// 发布和取消发布Loading
let publishLoading = $ref(false)
// 发布状态
let publishState = $ref(Number(route.query.flowState || null))
// 是否只读
let readOnly = ref(true)
// 节点错误信息汇总
const nodesError = ref({})
// 节点数据
let nodesData = $ref({
  id: "root",
  pid: "",
  type: "start",
  subId: "end",
  name: "流程开始",
  child: {
    id: "end",
    pid: "root",
    subId: "",
    type: "end",
    name: "结束",
    child: undefined,
  },
})

const emits = defineEmits(["dataChange"])

provide("flowDesign", {
  readOnly: readOnly,
  nodesError: nodesError,
})

// 添加分支节点
async function addExclusive(node) {
  const child = node.child
  const { id } = await genUniquenessId()
  node.subId = id
  const exclusiveNode = {
    id: id,
    subId: "",
    pid: node.id,
    type: "exclusive",
    name: "分支节点",
    child: child,
    children: [],
  }
  if (child) {
    child.pid = id
  }
  node.child = exclusiveNode
  addCondition(node.child)
  addCondition(node.child)
}

// 添加按钮节点
async function addCondition(node) {
  const { id } = await genUniquenessId()
  node.children.push({
    id,
    pid: node.id,
    subId: "",
    type: "condition",
    name: `按钮${node.children.length + 1}`,
    child: undefined,
    text: "",
  })
}

// 添加消息节点
async function addNotify(node) {
  const child = node.child
  const { id } = await genUniquenessId()
  node.subId = id
  node.child = {
    id: id,
    pid: node.id,
    subId: "",
    name: "消息模板",
    type: "notify",
    contentType: "rich",
    child: child,
    text: "",
  }

  if (child) {
    child.pid = id
  }
}

// 添加节点处理函数
function handleAddNode(type, node) {
  const addMap = {
    exclusive: addExclusive,
    condition: addCondition,
    notify: addNotify,
  }
  const fn = addMap[type]
  fn && fn(node)
  startAutoSaveCountDown()
}

// 删除节点处理函数
function handleDelNode(del) {
  delete nodesError.value[del.id]
  delNodeNext(nodesData, del)
  startAutoSaveCountDown()
}

// 递归删除
function delNodeNext(next, del) {
  delete nodesError.value[del.id]
  // 删除消息模板的时候，如果下面接着分支，则需要把连带的分支也删除
  if (del.type === "notify" && del.child?.type === "exclusive") {
    handleDelError(del.child)
    delNodeNext(del, del.child)
  }
  // 找到要删除节点的父节点
  if (next.id === del.pid) {
    // 如果是分支节点，则循环处理children
    if ("children" in next && next.child?.id !== del.id) {
      const branchNode = next
      const index = branchNode.children.findIndex((item) => item.id === del.id)
      if (index !== -1) {
        // 如果本身只有一个按钮，则删除整个分支
        if (branchNode.children.length === 1) {
          handleDelError(branchNode)
          handleDelNode(branchNode)
        } else {
          // 否则只删除那一个按钮
          handleDelError(del)
          branchNode.children.splice(index, 1)
        }
      }
    } else {
      // 如果不是分支节点，则删除此节点，替换id关系
      del.child && (del.child.pid = next.id)
      next.subId = del?.child?.id || ""
      next.child = del.child
    }
  } else {
    // 递归进行处理
    if (next.child) {
      delNodeNext(next.child, del)
    }
    // 递归分支节点的子节点
    if ("children" in next) {
      const nextBranch = next
      if (nextBranch.children?.length) {
        nextBranch.children.forEach((item) => {
          delNodeNext(item, del)
        })
      }
    }
  }
}

// 删除错误信息
function handleDelError(node) {
  delete nodesError.value[node.id]
  if (node.child) handleDelError(node.child)

  if ("children" in node) {
    const branchNode = node
    if (branchNode.children && branchNode.children.length > 0) {
      branchNode.children.forEach((item) => {
        handleDelError(item)
      })
    }
  }
}

// 激活节点的设置面板
function handleActiveNode(node) {
  console.log("node", node)
  activeData = node
  panelVisible = true
}

// 初始化流程节点数据
async function initData() {
  try {
    const data = await getProcessTree({
      questionId: route.query.questionId,
    })
    if ($g.tool.isTrue(data)) nodesData = data

    emits("dataChange", $g._.cloneDeep(nodesData))
  } catch (err) {
    console.log("初始化获取数据出错", err)
  }
}

// 验证节点是否有错误
function validateNode(autoSave = false) {
  const errors = Object.values(nodesError.value).flat()
  if (errors.length) {
    $g.msg(
      autoSave ? "节点不符合规范，自动保存失败" : "节点不符合规范，请仔细检查",
      "error",
    )
    return false
  }
  return true
}

// 保存节点数据
function handleSave(autoSave = false) {
  console.log("执行了")
  if (readOnly.value) {
    readOnly.value = false
    return
  }
  saveLoading = true

  try {
    if (!validateNode(autoSave)) return
    saveProcessTree({
      questionId: route.query.questionId,
      state: publishState,
      flowTree: nodesData,
    }).then((res) => {
      $g.msg(autoSave ? "自动保存成功" : "保存成功")
      emits("dataChange", $g._.cloneDeep(nodesData))
      !autoSave && (readOnly.value = true)
    })
  } catch (err) {
    console.log("保存时出错", err)
  } finally {
    saveLoading = false
  }
}

// 发布
function handlePublish() {
  try {
    publishLoading = true
    if (!validateNode()) return
    saveProcessTree({
      questionId: route.query.questionId,
      state: publishState ? 0 : 1,
      flowTree: nodesData,
    }).then((res) => {
      $g.msg(publishState ? "取消发布成功" : "发布成功")
      publishState = publishState ? 0 : 1
      emits("dataChange", $g._.cloneDeep(nodesData))
    })
  } catch (err) {
    console.log("发布失败")
  } finally {
    publishLoading = false
  }
}

// 自动保存节点信息，在增删节点和修改节点信息时，重置计时器
let timer: any = null
function startAutoSaveCountDown() {
  if (timer) clearTimeout(timer)
  timer = setTimeout(() => {
    handleSave(true)
    startAutoSaveCountDown()
  }, 30000)
}

onBeforeMount(initData)
onBeforeUnmount(() => {
  if (timer) clearTimeout(timer)
})
</script>

<style lang="scss" scoped>
.designer-container {
  --designer-bg-color: rgba(255, 255, 255, 8);
  position: relative;
  background: var(--designer-bg-color);
  height: calc(100vh - 57px - 40px - 40px);
  &.full-screen {
    position: fixed !important;
    width: 100vw !important;
    height: 100vh !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1005;
  }

  &.full-screen .action-button {
    top: 20px !important;
  }

  .nodes-container {
    margin: 0 auto;
    width: fit-content;
    transition: transform 0.5s;
    transform-origin: top left;
  }

  .action-button {
    user-select: none;
    position: absolute;
    z-index: 999;
    top: 40px;
    right: 20px;
    display: flex;
    align-items: center;

    span {
      margin: 0 10px;
    }
  }
}
</style>
