<template>
  <div class="aiGuideDesign-container-main relative">
    <div class="absolute right-20px top-20px z-[10]">
      <el-radio-group v-model="currentMode">
        <el-radio-button value="design">流程编辑</el-radio-button>
        <el-radio-button value="preview">预览模式</el-radio-button>
      </el-radio-group>
    </div>
    <Preview v-if="currentMode === 'preview'" :message-list="nodesData" />
    <Design
      v-show="currentMode === 'design'"
      @dataChange="nodesData = $event"
    />
  </div>
</template>
<script setup lang="ts">
import Preview from "./preview/index.vue"
import Design from "./design/index.vue"
const currentMode = $ref<"preview" | "design">("design")

let nodesData = $ref({})
</script>

<style lang="scss" scoped>
.aiGuideDesign-container-main {
  padding-right: 0 !important;
}
</style>
