<template>
  <div>
    <div v-if="item.contentType == 'markdown'">
      <g-markdown :modelValue="item.text" mode="preview"></g-markdown>
    </div>
    <div v-else-if="Array.isArray(item.text)">
      <div v-for="(vItem, vIndex) in item.text" :key="vIndex" class="mt-10px">
        <img
          v-if="
            ['.jpg', '.jpeg', '.gif', '.png', '.gif', '.webp', '.svg'].includes(
              vItem.type,
            )
          "
          :src="vItem.url"
        />
        <div
          v-else-if="
            [
              '.mp4',
              '.avi',
              '.rmvb',
              '.flv',
              '.wmv',
              '.mkv',
              '.mov',
              '.m3u8',
            ].includes(vItem.type)
          "
          class="w-400px h-225px"
        >
          <g-video
            :url="vItem.url"
            :config="{
              autoplay: false,
            }"
          ></g-video>
        </div>
        <audio
          v-else-if="['.mp3'].includes(vItem.type)"
          controls
          :src="vItem.url"
          controlsList="nodownload"
        ></audio>
        <iframe
          v-else-if="
            [
              '.doc',
              '.docx',
              '.xls',
              '.xlsx',
              '.pdf',
              '.ppt',
              '.pptx',
            ].includes(vItem.type)
          "
          id="viewIframe"
          :src="`//vip.ow365.cn/?i=28777&ssl=1&furl=${vItem.url}`"
          frameborder="1"
          width="800px"
          height="500px"
          noresize="noresize"
        ></iframe>
      </div>
    </div>
    <div v-else-if="item.text == 'init'">
      <span>同学你好，下面小鸣来带你学会这道题：</span>
      <g-mathjax :text="questionDetail.questionTitle"></g-mathjax>
      <div v-for="(vItem, vIndex) in subQuestion" :key="vIndex">
        <div class="!my-10px flex">
          <p
            v-show="subQuestion.length != 1"
            class="!mr-4px structure_number leading-[1.6rem]"
          >
            {{ vItem.structureNumber || `(${vIndex + 1})` }}
          </p>
          <g-mathjax class="flex-1" :text="vItem.subQuestionTitle"></g-mathjax>
        </div>
        <div
          v-for="option in vItem.optionArr"
          :key="option.name"
          class="flex items-start"
        >
          <div class="w-20px mr-6px">{{ option.name }}.</div>
          <div class="">
            <g-mathjax :text="option.title"></g-mathjax>
          </div>
        </div>
      </div>
    </div>
    <g-mathjax v-else :text="item.text" class="!text-[16px] !font-500" />
  </div>
</template>

<script lang="ts" setup>
import type { PropType } from "vue"
const props = defineProps({
  item: {
    type: [Object],
    default: () => {},
  },
  questionDetail: {
    type: [Object],
    default: () => {},
  },
  subQuestion: {
    type: [Array] as PropType<any[]>,
    default: () => [],
  },
})

onMounted(() => {
  $g.tool.renderMathjax()
})
</script>

<style lang="scss" scoped>
img {
  width: 100%;
}
</style>
