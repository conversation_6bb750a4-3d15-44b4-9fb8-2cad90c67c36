<template>
  <div class="inputShow">
    <g-loading v-if="showLoading" class="h-200px"></g-loading>
    <div v-else-if="aiMessage.length" class="inputBg rounded-[10px]">
      <div class="p-30px">
        <div
          v-for="(item, index) in showTextList"
          :id="item.id"
          :key="index"
          class="mb-30px flex"
          :class="item.isAi ? '' : 'justify-end'"
        >
          <div class="flex">
            <img
              v-show="item.isAi"
              src="@/assets/img/aiInput/headerLeft.png"
              class="w-40px h-40px flex-shrink-0 mr-20px"
              alt=""
            />
            <div>
              <div
                class="max-w-[900px] px-20px py-14px"
                style="width: fit-content"
                :class="
                  item.isAi
                    ? 'styleItem1 bg-white'
                    : 'styleItem2 bg-[#DEEFFF] text-[#3398F7]'
                "
              >
                <NodeContent
                  :item="item"
                  :question-detail="questionDetail"
                  :sub-question="sub_question"
                ></NodeContent>
              </div>
              <div
                v-if="item.showButton?.length"
                class="flex flex-wrap w-[900px]"
              >
                <div
                  v-for="(v, vIndex) in item.showButton"
                  :id="v.id"
                  :key="vIndex"
                  :class="[
                    v.click ? 'bg-[#3398F7] text-white' : 'bg-white',
                    v.noClick ? 'cursor-pointer' : 'cursor-pointer',
                  ]"
                  class="min-w-[150px] h-[70px] leading-[70px] rounded-[12px] text-center px-20px mt-20px mr-30px"
                  @click="buttonClick(item, v)"
                >
                  {{ v.text }}
                </div>
              </div>
              <div
                v-show="
                  item.isAi && showNodeList && index == showTextList.length - 1
                "
                class="ml-6px mt-20px"
              >
                <div class="mb-10px text-12px">其他路径：</div>
                <div
                  v-for="(v, Index) in showNodeList"
                  :key="Index"
                  class="max-w-[1200px] flex"
                  :style="{ marginLeft: `${Index as any * 12}px` }"
                >
                  <div v-for="i in v" :key="i.id" @click="nodeClick(i)">
                    <el-popover
                      :disabled="true"
                      placement="top"
                      trigger="hover"
                    >
                      <div class="max-w-[800px] max-h-[400px] overflow-auto">
                        <NodeContent
                          :item="i"
                          :question-detail="{}"
                          :sub-question="[]"
                        ></NodeContent>
                      </div>
                      <template #reference>
                        <div
                          class="max-w-[220px] text-12px border border-gray-default rounded-[8px] py-2px px-14px active:opacity-80 mb-10px mr-10px cursor-pointer truncate select-none"
                          @mouseover="onMouseOver($event, i)"
                        >
                          {{ i.text }}
                        </div>
                      </template>
                    </el-popover>
                  </div>
                </div>
              </div>
            </div>
            <img
              v-show="!item.isAi"
              src="@/assets/img/aiInput/headerRight.png"
              class="w-40px h-40px flex-shrink-0 ml-20px"
              alt=""
            />
          </div>
        </div>
        <div v-show="aiShow" class="flex aiShow">
          <img
            src="@/assets/img/aiInput/headerLeft.png"
            class="w-40px h-40px flex-shrink-0 mr-20px"
            alt=""
          />
          <div
            class="max-w-[900px] styleItem1 bg-white px-20px py-14px text-[16px] font-500"
          >
            <div ref="dzjRef"></div>
          </div>
        </div>
      </div>
    </div>
    <g-empty v-else-if="!showLoading"></g-empty>
  </div>
</template>

<script setup lang="ts">
import { getQuestionDetail } from "@/api/bookMgt"
import Typed from "typed.js"
import NodeContent from "./components/NodeContent.vue"

const route = useRoute()
const props = defineProps({
  messageList: {
    type: [Object],
    default: () => {},
  },
})
let aiShow = $ref(true)
let scrollPosition: any = $ref(document?.querySelector("#root"))
let dzjRef: any = $ref(null)
let showTextList: any = $ref([])
let aiMessage: any = $ref([])
let questionDetail: any = $ref({})
let showLoading = $ref(true)
let inprogress = $ref(false)
let typed: any = $ref(null)
let nodeList: any = $ref([])
let clickIndex = $ref(0)
let jumpButton: any = $ref({})

const sub_question = $computed(() => {
  let sub_question = questionDetail.subQuestions.map((e) => {
    let optionArr = Object.keys(e)
      .map((key) => {
        if (key.includes("option") && key.length == 7 && e[key]) {
          return {
            name: key.charAt(key.length - 1).toLocaleUpperCase(),
            title: e[key],
          }
        } else {
          return ""
        }
      })
      .filter(Boolean)

    return {
      ...e,
      optionArr,
    }
  })
  return sub_question
})

const showNodeList: any = $computed(() => {
  if (nodeList.length) {
    let data = {}
    nodeList.forEach((i) => {
      if (data[i.storey]) {
        data[i.storey].push(i)
      } else {
        data[i.storey] = [i]
      }
    })
    return data
  }
  return null
})

onMounted(() => {
  getQuestion()
})

async function getQuestion() {
  try {
    let data = await getQuestionDetail({
      questionId: route.query.questionId,
    })
    showLoading = false
    questionDetail = data
    showTextList.push({
      isAi: true,
      text: "init",
      showButton: [],
      nextId: [],
      id: $g.tool.uuid(4),
      contentType: "",
    })
    nextTick(() => {
      getData()
    })
  } catch (error) {
    showLoading = false
  }
}

function jumpButtons(node, v) {
  let flagList = node.showButton.filter((item) => {
    return !item.click
  })
  jumpButton = nodeList.find((item) => item.id == v.id)
  //存储前先删除已经走过的分支
  let buttonIndex = nodeList.findIndex((i) => i.id == jumpButton?.id)
  //有则删除，没有则添加
  if (buttonIndex != -1) {
    clickIndex = jumpButton.storey
    //截取点击按钮数据当前层级以及之上的数据
    nodeList = nodeList.filter((vv) => vv.storey <= jumpButton.storey)
    //删除点击按钮数据
    nodeList.splice(buttonIndex, 1)
  } else {
    //至少又两个未点击的按钮
    if (flagList.length > 0) {
      clickIndex += 1
      //要存储的按钮的下一步不能与已经存储的按钮的下一步相同  暂时不考虑
      flagList.forEach((item, index) => {
        nodeList.push({
          ...item,
          showTips: false,
          nodeNum: flagList.length - index - 1, //该按钮数据之后还有多少个同级按钮
          storey: clickIndex,
        })
      })
    }
  }
}

function buttonClick(node, v) {
  if (!v.noClick && !v.click) {
    v.click = true
    jumpButtons(node, v)
    showTextList.push({
      isAi: false,
      text: "已选择“" + v.text + "”",
      showButton: [],
      nextId: v.nextId,
      id: $g.tool.uuid(4),
    })
    nextTick(() => {
      scrollToBottom()
      let index = aiMessage.findIndex((item) => item.id == v.nextId)
      if (!aiMessage[index]) {
        return
      }
      if (inprogress) {
        typed.destroy()
      }
      setTimeout(() => {
        otherQues(aiMessage[index], true)
      }, 500)
    })
  }
}
function scrollToBottom() {
  if (!scrollPosition) {
    scrollPosition = document?.querySelectorAll(".n-scrollbar-content")
    scrollPosition = scrollPosition[scrollPosition.length - 1]
  }
  nextTick(() => {
    if (scrollPosition) {
      scrollPosition.scrollIntoView({
        behavior: "smooth",
        block: "end",
      })
    }
  })
}
//自定义答案打字效果
async function otherQues(
  { text, options, nextId, contentType, id },
  isAi = true,
) {
  //附件数据处理
  if (contentType == "attach") {
    let arr = JSON.parse(text)
    showTextList.push({
      isAi,
      text: arr,
      showButton: options,
      nextId,
      id,
      contentType,
    })
    loopFun(nextId)
  } else if (contentType == "markdown") {
    aiShow = false
    markdown({ text, options, nextId, contentType, id })
  } else {
    if (text) aiShow = true
    inprogress = true
    typed = new Typed(dzjRef, {
      strings: [text],
      typeSpeed: 20,
      showCursor: false,
      onComplete: async (self) => {
        showTextList.push({
          isAi,
          text,
          showButton: options,
          nextId,
          id,
          contentType,
        })
        inprogress = false
        loopFun(nextId)
        typed.destroy()
      },
    })
  }
}

//markdown类型的内容单独实现打字效果
function markdown({ text, options, nextId, contentType, id }) {
  showTextList.push({
    isAi: true,
    text: "",
    showButton: options,
    nextId,
    id,
    contentType,
  })
  let index = showTextList.length - 1
  let speed = 20 //设置定时的速度 数字越小打字越快
  let count = 1
  let timer = setInterval(() => {
    if (count != text.length + 1) {
      showTextList[index].text = text.substring(0, count) //截取字符串
      count++
    } else {
      if (timer) clearInterval(timer)
      scrollToBottom()
      loopFun(nextId)
    }
  }, speed)
}

function loopFun(nextId) {
  aiShow = false
  nextTick(() => {
    scrollToBottom()
    if (nextId) {
      let index = aiMessage.findIndex((item) => item.id == nextId)
      setTimeout(() => {
        otherQues(aiMessage[index], true)
      }, 500)
    }
  })
}

function nodeClick(i) {
  let parentNode = showTextList.find((item) => item.id == i.parentId)
  let clickNode = parentNode.showButton.find((item) => item.id == i.id)
  buttonClick(parentNode, clickNode)
}

function getData() {
  transformData(props.messageList)
  nextTick(() => {
    if (!aiMessage.length) return
    otherQues(aiMessage[0], true)
  })
}
// 数据转换
function transformData(node) {
  /**保存消息模板数据 */
  const noticeMap = {}
  /**保存按钮节点数据 */
  const buttonMap = {}
  /** 保存分叉的消息节点的id数据 */
  const isolatedNotifyNodeIds: any = []
  /**记录最开始节点的id */
  let startNodeId = ""

  const traverse = (currentNode) => {
    if (!currentNode) return
    // 如果是开始或者结束节点，处理child
    if (["start", "end"].includes(currentNode.type)) {
      currentNode.type === "start" && (startNodeId = currentNode.child?.id)
      traverse(currentNode.child)
    }

    // 如果是消息模板节点
    if (currentNode.type === "notify") {
      // 保存到消息模板数据中，其中next和按钮列表为空
      noticeMap[currentNode.id] = {
        id: currentNode.id,
        text: currentNode.text,
        nextId: null,
        options: [],
        contentType: currentNode.contentType,
      }
      // 如果上一条模板消息存在，即消息后接消息的情况，赋值next
      if (noticeMap[currentNode.pid]) {
        noticeMap[currentNode.pid].nextId = currentNode.id
      }

      // 如果上一条按钮数据在，即按钮后面接消息的情况，赋值next
      if (buttonMap[currentNode.pid]) {
        buttonMap[currentNode.pid].nextId = currentNode.id
      }

      // 如果有child，则继续处理
      if (currentNode.child) {
        traverse(currentNode.child)
      } else {
        // 如果没有child，说明是一条分支的最后一个消息节点
        noticeMap[currentNode.id].nextId =
          isolatedNotifyNodeIds[isolatedNotifyNodeIds.length - 1] || null
      }
    }

    // 如果是条件分支节点
    if (currentNode.type === "exclusive") {
      // 如果分支节点下接着消息节点，则把消息节点id添加到孤儿节点中
      if (currentNode.child?.type === "notify") {
        isolatedNotifyNodeIds.push(currentNode.child?.id)
      }
      // 如果分支节点下没有接子节点，如果孤儿节点中已经有值，则重复推入最后一个节点
      if (!currentNode?.child) {
        isolatedNotifyNodeIds.push(
          isolatedNotifyNodeIds[isolatedNotifyNodeIds.length - 1],
        )
      }

      // 先处理children字段，children中为条件分支节点下的按钮列表
      currentNode.children &&
        currentNode.children.forEach((button) => {
          // 存储按钮节点信息
          buttonMap[button.id] = {
            id: button.id,
            text: button.text,
            nextId: null,
            parentId: currentNode.pid,
          }
          // 如果按钮没有子节点，则从孤儿节点中取最后一个最为下一条消息
          if (!button.child) {
            buttonMap[button.id].nextId =
              isolatedNotifyNodeIds[isolatedNotifyNodeIds.length - 1] || null
          }

          // 如果前一条模板消息存在，即消息后面接条件分支的情况，则将按钮列表放到模板消息中
          if (noticeMap[currentNode.pid])
            noticeMap[currentNode.pid].options.push(buttonMap[button.id])

          // 处理按钮节点下的child
          if (button.child) traverse(button.child)
        })

      // 根据添加到孤儿节点中数据的规则，出栈
      if (currentNode.child?.type === "notify" || !currentNode.child) {
        isolatedNotifyNodeIds.pop()
      }
      // 处理条件分支节点下的child
      currentNode.child && traverse(currentNode.child)
    }
  }
  traverse(node)
  // 确保第一项置于顶部后进行输出
  const result: any = Object.values(noticeMap)
  const index = result.findIndex((item) => item.id === startNodeId)
  if (index !== 0 && index !== -1) result.unshift(result.splice(index, 1))
  aiMessage = $g._.cloneDeep(result)

  nextTick(() => {
    $g.tool.renderMathjax()
  })
}

function onMouseOver(e, item) {
  if (!(e.target.scrollWidth > e.target.clientWidth)) {
    item.showTips = true
  } else {
    item.showTips = false
  }
}
</script>

<style lang="scss" scoped>
.inputShow {
  padding-right: 20px;
  :deep() {
    a {
      text-decoration: underline !important;
      color: #4cacfc !important;
    }
  }
}
.inputBg {
  background: url("@/assets/img/aiInput/gptBg.png") center / 100% 100% repeat-y;
  min-height: calc(100vh - 57px - 40px - 40px);
}
.styleItem1 {
  border-radius: 0px 12px 12px 12px;
}
.styleItem2 {
  border-radius: 12px 0px 12px 12px;
}
:deep() {
  .aiShow {
    p {
      margin: 0 0 7px !important; // 和mathjax内样式一致
    }
  }
}
</style>
