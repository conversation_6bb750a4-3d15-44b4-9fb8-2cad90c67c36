<template>
  <div>
    <div class="flex items-center my-10px">
      <div class="w-[40px] flex-shrink-0">题型:</div>
      <div class="flex items-center gap-[10px] flex-wrap">
        <div
          v-for="item in questionType"
          :key="item.value"
          class="py-5px px-10px rounded-[5px]"
          :class="{
            active: mainQuestion.sysQuestionTypeId === item.value,
          }"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <!-- 大题题干 -->
    <div class="main-title w-[80px]">题干</div>
    <g-ueditor v-model="mainQuestion.questionTitle" class="my-20px">
    </g-ueditor>
    <div class="flex justify-between mt-20px mb-10px">
      <div class="sub-title w-[80px] pb-2px">小题</div>
    </div>
    <!-- 子题题干 -->
    <SubQuestion
      v-if="subQuestion.length"
      :subQuestion="subQuestion"
      :subQuestionType="subQuestionType"
    />
    <div v-else>子题数据异常！</div>
  </div>
</template>

<script setup lang="ts">
import SubQuestion from "./SubQuestion.vue"
import chooseData from "@/views/resourceMgt/paperEdit/index"
import { getSubQuestionTypeList, getQuestionTypeList } from "@/api/bookMgt"
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
})
let questionType = $ref<any>([])
let mainQuestion = $ref<any>({})
let subQuestionType = $ref<any>([])
let subQuestion = $ref<any>([
  {
    subQuestionTitle: "", // 问题描述
    subQuestionType: "",
    structureNumber: "(1)",
    score: 1, // 小题分数
    subQuestionAnswer: "", // 答案
    subQuestionParse: "", // 详细解析
    subQuestionParseList: [{ content: "" }], //解析列表
    knowledgePoints: [], // 知识点
    // 下面属于适配数据
    knowledge: [],
    optionsAnswer: [],
    options: $g._.cloneDeep(chooseData.chooseOption.slice(0, 4)),
  },
])
const route = useRoute()
const router = useRouter()
function initData(res) {
  mainQuestion.questionTitle = res.questionTitle
  mainQuestion.sysQuestionTypeId = res.sysQuestionTypeId

  res.subQuestions.forEach((v, i) => {
    v.knowledgePoints = []
    v.structureNumber = v.structureNumber ?? `(${i + 1})`
    v.score = v.score ?? 1
    if ([1, 2, 3].includes(v.subQuestionType)) {
      v.optionsAnswer = v.subQuestionAnswer?.split("") ?? []
      v.options = Object.keys(v)
        .map((vv) => {
          if (vv.indexOf("option") != -1 && !vv.includes("Answer")) {
            return {
              label: vv.replace("option", ""),
              value: v[vv],
              key: vv,
            }
          }
        })
        .filter((item) => item != null && item.value)
    }
  })
  subQuestion = res.subQuestions
}
/* 导入试题 */
function confirm() {
  let data = {
    ...mainQuestion,
    subQuestions: subQuestion,
  }
  localStorage.setItem("questionData", JSON.stringify(data))
  router.go(-1)
}
defineExpose({ confirm })
watch(
  () => props.data,
  (val) => {
    if (val) {
      initData(val)
    }
  },
)
/* 获取大题题型 */
async function getQuestionTypeListApi() {
  let res = await getQuestionTypeList({
    sysCourseId: route.query.sysCourseId,
  })
  questionType = res.map((v) => {
    return {
      label: v.sysQuestionTypeName,
      value: v.sysQuestionTypeId,
    }
  })
}

/* 获取子题类型 */
async function getSubQuestionTypeListApi() {
  let res = await getSubQuestionTypeList()
  subQuestionType = res.map((v) => {
    return {
      label: v.title,
      value: v.id,
    }
  })
  subQuestion[0].subQuestionType = res[0]?.id
}
onMounted(() => {
  getSubQuestionTypeListApi()
  getQuestionTypeListApi()
})
</script>

<style lang="scss" scoped>
.main-title {
  border-left: 4px solid #67c23a;
  padding-left: 5px;
}
.sub-title {
  border-left: 4px solid #e6a23c;
  padding-left: 5px;
}
.active {
  background: #0f99eb;
  color: #fff;
}
</style>
