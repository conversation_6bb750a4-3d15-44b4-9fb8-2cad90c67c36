<template>
  <div class="flex !h-[calc(100vh-170px)] gap-x-[20px]" v-loading="loading">
    <div class="h-full overflow-y-auto w-[500px]">
      <div class="h-40px text-[16px] leading-[40px]">识别后的JSON</div>
      <JsonEditor v-model="questionData" />
    </div>
    <div class="flex-1">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-x-[30px]">
          <div class="h-40px text-[16px] leading-[40px]">试题预览效果</div>
          <n-button
            type="primary"
            text
            @click="showDialog = true"
            :disabled="!questionData"
            >试题预览</n-button
          >
        </div>
        <n-button type="success" @click="confirm">确认导入</n-button>
      </div>
      <el-scrollbar class="flex-1 !h-[calc(100%-40px)] pr-20px">
        <Question :data="questionData" ref="questionRef" />
      </el-scrollbar>
    </div>
    <PreviewDialog v-model:show="showDialog" :question="questionData" />
  </div>
</template>

<script setup lang="ts">
import JsonEditor from "@/views/audioMgt/audioInfo/detail/components/JsonEditor.vue"
import Question from "./Question.vue"
import PreviewDialog from "./PreviewDialog.vue"
import { getQuestionAttachOcrAiRecognitionJson } from "@/api/bookMgt"
const props = defineProps({
  data: {
    type: String,
    required: true,
  },
  examNotes: {
    type: String,
    default: "",
  },
})
let questionData = $ref<any>({})
let showDialog = $ref(false)
let loading = $ref(true)
let questionRef = $ref<any>(null)
const route = useRoute()
/* ai识别数据转json */
async function getAiRecognitionJson() {
  try {
    loading = true
    const res = await getQuestionAttachOcrAiRecognitionJson({
      md: props.data,
      bookName: route.query.bookName,
      examNotes: props.examNotes,
    })
    questionData = res
    loading = false
  } catch (err) {
    console.log(err)
    loading = false
  }
}
/* json修改 */
function onJsonChange(val) {
  questionData = val
}
/* 确认导入 */
function confirm() {
  questionRef?.confirm()
}
getAiRecognitionJson()
</script>

<style lang="scss" scoped>
:deep() {
  .jsoneditor-vue {
    height: calc(100vh - 210px);
  }
  .jsoneditor-contextmenu {
    display: none;
  }
}
</style>
