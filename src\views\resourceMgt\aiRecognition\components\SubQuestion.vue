<template>
  <div>
    <div class="flex justify-between mb-8px">
      <div class="flex items-center">
        <el-radio-group v-model="current">
          <el-radio-button
            v-for="(item, index) in subQuestion"
            :key="item.paper_sub_question_id || index"
            :value="index"
          >
            小题{{ index + 1 }}
          </el-radio-button>
        </el-radio-group>
        <div class="mx-10px">
          <!-- <n-button type="success" @click="addSubQuestions">
            <g-icon name="add-line" size="" color="" />
          </n-button> -->
        </div>
      </div>
      <!-- <n-button type="error" @click="deleteSubQuestions">删除本小题</n-button> -->
    </div>
    <div class="border border-gray-light px-16px py-12px">
      <div class="flex mb-8px">
        <div class="flex">
          <div class="text-14px leading-[30px] w-70px">小题题号：</div>
          <el-input
            v-model="subQuestion[current].structureNumber"
            aria-label="小题题号"
            style="width: 66px"
          ></el-input>
        </div>
        <div class="flex ml-10px">
          <div class="text-14px leading-[30px] w-70px">小题分数：</div>
          <el-input-number
            v-model="subQuestion[current].score"
            :min="0"
            aria-label="小题分数"
            :precision="1"
          ></el-input-number>
        </div>
      </div>
      <div class="flex mb-8px">
        <div class="text-14px leading-[30px] w-70px">题型：</div>
        <div class="flex items-center gap-x-[30px]">
          <div
            v-for="item in subQuestionType"
            :key="item.value"
            class="py-5px px-10px rounded-[5px] cursor-pointer"
            :class="{
              active: subQuestion[current].subQuestionType === item.value,
              itemBox: item.value !== subQuestion[current].subQuestionType,
            }"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
      <div class="flex" style="display: none">
        <div class="text-14px leading-[30px] w-70px">知识点：</div>
        <div class="flex flex-1 items-center flex-wrap gap-[10px]">
          <n-button
            type="primary"
            @click="
              () => {
                showDialog = true
              }
            "
          >
            <g-icon name="add-line" size="" color="" />
            选择知识点
          </n-button>
          <n-tag
            v-for="(item, index) in subQuestion[current].knowledgePoints"
            :key="item.sysKnowledgePointId"
            class="cursor-pointer"
            type="primary"
            closable
            size="large"
            round
            @close="deleteKnowledge(index)"
            >{{ item.sysKnowledgePointName }}</n-tag
          >
        </div>
      </div>
      <!-- 问题描述 -->
      <div>
        <div class="flex justify-between mt-18px mb-10px">
          <div class="text-16px">问题描述</div>
          <div></div>
        </div>
        <g-ueditor v-model="subQuestion[current].subQuestionTitle"></g-ueditor>
      </div>
      <!-- 答案描述 3种情况 选择题/其他题   判断题没有答案描述只有答案  -->
      <div>
        <div class="flex justify-between mt-18px">
          <div>答案描述</div>
          <!-- <div v-if="[1, 2].includes(subQuestion[current].subQuestionType)">
            <n-button
              type="success"
              @click="increaseOrDecreaseOptions('add')"
              size="small"
            >
              <g-icon name="add-line" size="" color="" />
              添加选项
            </n-button>
            <n-button
              type="error"
              @click="increaseOrDecreaseOptions('edit')"
              class="ml-10px"
              size="small"
            >
              <g-icon name="delete-bin-line" size="" color="" />
              删除选项
            </n-button>
          </div> -->
        </div>
        <!-- 选择题 -->
        <template v-if="[1, 2].includes(subQuestion[current].subQuestionType)">
          <div v-for="item in subQuestion[current].options" :key="item.label">
            <CheckBox
              v-model:model="subQuestion[current].optionsAnswer"
              :value="item.label"
              :max="subQuestion[current].subQuestionType == 1 ? 1 : 999"
              :allow-label-click="false"
              :size="18"
            >
              <div class="flex items-center">
                <div class="mr-10px w-[20px] flex-shrink-0">
                  {{ item.label }}
                </div>
                <g-ueditor
                  class="w-[calc(100vw-80px-240px-220px-400px)]"
                  v-model="item.value"
                  :config="{ initialFrameHeight: 80 }"
                  @click.prevent
                ></g-ueditor>
              </div>
            </CheckBox>
          </div>
        </template>
        <!-- 判断题 -->
        <template v-else-if="subQuestion[current].subQuestionType == 3">
          <div class="flex items-center">
            <CheckBox
              v-for="item in [{ key: '√' }, { key: '×' }]"
              :key="item.key"
              v-model:model="subQuestion[current].optionsAnswer"
              :value="item.key"
              :max="1"
              :size="18"
              :allow-label-click="false"
            >
              <div class="mr-10px">{{ item.key }}</div>
            </CheckBox>
          </div>
        </template>
        <!-- 其他题 -->
        <template
          v-else-if="
            [4, 5, 6, 7].includes(subQuestion[current].subQuestionType)
          "
        >
          <g-ueditor
            v-model="subQuestion[current].subQuestionAnswer"
            class="ml-20px mt-10px"
            :config="{ initialFrameHeight: 120 }"
          ></g-ueditor>
        </template>
      </div>
      <!-- 详细解析 -->

      <div
        v-for="(item, index) in subQuestion[current].subQuestionParseList"
        :key="index"
      >
        <div class="mt-20px mb-10px text-16px flex justify-between">
          <div class="flex items-center gap-x-[15px]">
            <div>
              详细解析 <span v-if="index != 0">({{ index + 1 }})</span>
            </div>
          </div>
          <!-- <n-button
            type="primary"
            @click="addAnalysis"
            v-if="index == 0"
            size="small"
          >
            <g-icon name="add-line" size="" color="" />
            增加解析
          </n-button>
          <n-button
            type="error"
            @click="deleteAnalysis(index)"
            v-if="index != 0"
            size="small"
          >
            <g-icon name="delete-bin-line" size="" color="" />
            删除解析
          </n-button> -->
        </div>
        <g-ueditor
          v-model="item.content"
          :config="{ initialFrameHeight: 120 }"
          @click.prevent
        ></g-ueditor>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
import chooseData from "@/views/resourceMgt/paperEdit/index"
import CheckBox from "@/views/resourceMgt/paperEdit/components/CheckBox.vue"
const props = defineProps({
  subQuestion: {
    type: Object,
    default: () => {},
  },
  subQuestionType: {
    type: Array as PropType<any>,
    default: () => [],
  },
  deleteId: {
    type: Array,
    default: () => {
      return []
    },
  },
})
let showData = $ref(false)
let showDialog = $ref(false)
let current = $ref(0)
let initData = {
  subQuestionTitle: "", // 问题描述
  subQuestionType: "",
  structureNumber: "(1)",
  score: 1, // 小题分数
  subQuestionAnswer: "", // 答案
  subQuestionParse: "", // 详细解析
  subQuestionParseList: [{ content: "" }], //解析列表
  knowledgePoints: [], // 知识点
  // 下面属于适配数据
  knowledge: [],
  optionsAnswer: [],
  options: $g._.cloneDeep(chooseData.chooseOption.slice(0, 4)),
}
let activeBackgroundData = $ref<any>({})
/* 编辑背景资料 */
function openDataDialog(item) {
  activeBackgroundData = item
  showData = true
}
/* 新增解析 */
function addAnalysis() {
  if (props.subQuestion[current].subQuestionParseList.length == 8)
    return $g.msg("解析不能超过8个", "warning")
  props.subQuestion[current].subQuestionParseList.push({
    content: "",
  })
}
/* 删除解析 */
function deleteAnalysis(index) {
  props.subQuestion[current].subQuestionParseList.splice(index, 1)
}
/* 新增小题 */
function addSubQuestions() {
  let copyData = $g._.cloneDeep(initData)
  props.subQuestion.push({
    ...copyData,
    structureNumber: `(${props.subQuestion.length + 1})`,
    subQuestionType: props.subQuestionType[0].value,
  })
  current = props.subQuestion.length - 1
  // this.current += 1
}
/* 删除试题 */
function deleteSubQuestions() {
  if (props.subQuestion.length == 1)
    return $g.msg("只有一个小题了，不能删除！", "warning")
  $g.confirm({ content: "是否确认删除" })
    .then((res) => {
      let item = props.subQuestion.splice(current, 1)
      if (item[0].paper_sub_question_id)
        props.deleteId.push(item[0].paper_sub_question_id)
      current = 0
      $g.msg("删除成功", "success")
    })
    .catch(() => {})
}
/* 增加或删除选项 */
function increaseOrDecreaseOptions(type) {
  let subQuestionLength = props.subQuestion[current].options.length
  switch (type) {
    case "add":
      if (subQuestionLength == 10) {
        $g.msg("小题选项最多添加10个", "warning")
      } else {
        props.subQuestion[current].options.push(
          chooseData.chooseOption[subQuestionLength],
        )
      }
      break
    default:
      if (subQuestionLength == 2) {
        $g.msg("小题选项至少要有2个", "warning")
      } else {
        props.subQuestion[current].options.pop()
        $g.msg("删除小题选项成功", "success")
      }
      break
  }
}
/* 切换题型 */
function changeSubQuestionType(item) {
  $g.confirm({
    content: "您确定要切换当前题型吗？答案信息将清空！",
  }).then(() => {
    props.subQuestion[current].subQuestionType = item.value
    resetQuestionAnswer()
  })
}
/* 重置题目答案 */
function resetQuestionAnswer() {
  let currentQues = props.subQuestion[current]
  currentQues.questionAnswer = ""
  currentQues.optionsAnswer = []
  currentQues.options = $g._.cloneDeep(chooseData.chooseOption.slice(0, 4))
}
/* 删除知识点 */
function deleteKnowledge(index) {
  props.subQuestion[current].knowledgePoints.splice(index, 1)
}
/* 添加知识点 */
function addCurrentSubQuestionKnowledge(val) {
  const index = props.subQuestion[current].knowledgePoints.findIndex(
    (e: any) => {
      return e.sysKnowledgePointId == val.sysKnowledgePointId
    },
  )
  if (index == -1) {
    props.subQuestion[current].knowledgePoints.push({
      ...val,
    })
  }
}
/* 删除知识点 */
function deleteCurrentSubQuestionKnowledge(index) {
  props.subQuestion[current].knowledgePoints.slice(index, 1)
}
/* 初始化current */
function initCurrent() {
  current = 0
}
defineExpose({
  addCurrentSubQuestionKnowledge,
  deleteCurrentSubQuestionKnowledge,
  initCurrent,
})
</script>

<style lang="scss" scoped>
.active {
  background: #0f99eb;
  color: #fff;
}
</style>
