<template>
  <div class="aiRecognition-container-main">
    <div class="h-30px">
      <n-button type="primary" text v-if="finished" @click="back">
        返回</n-button
      >
    </div>
    <div class="w-[800px]" v-if="!finished" v-loading="formOptions.loading">
      <div class="my-10px text-gray-default">
        tips:鼠标移入上传组件可粘贴图片进行上传
      </div>
      <div class="min-h-[600px]">
        <g-form :formOptions="formOptions">
          <!-- 上传,upload-key需要正确设置 -->
          <template #title>
            <g-upload
              v-model:fileList="formOptions.data.title"
              type="drag"
              ref="titleRef"
              multiple
              accept=".png,.jpg,.jpeg"
            ></g-upload>
          </template>
          <template #answer>
            <g-upload
              multiple
              v-model:fileList="formOptions.data.answer"
              type="drag"
              ref="answerRef"
              accept=".png,.jpg,.jpeg"
            ></g-upload>
          </template>
        </g-form>
      </div>
      <div class="flex justify-end">
        <n-button
          type="primary"
          size="large"
          class="w-[700px]"
          @click="recognition"
          >开始识别</n-button
        >
      </div>
    </div>
    <Result v-else :data="resultData" :examNotes="formOptions.data.examNotes" />
  </div>
</template>

<script setup lang="ts">
import { getQuestionAttachOcrAiRecognition } from "@/api/bookMgt"
import Result from "./components/Result.vue"
import OSS from "@/plugins/OSS"
const route = useRoute()
const formOptions = reactive<any>({
  loading: false,
  ref: null as any,
  items: {
    bookName: {
      type: "text",
      label: "书籍名称",
      disabled: true,
    },
    title: {
      type: "upload",
      label: "题干",
      slot: true,
    },
    answer: {
      type: "upload",
      label: "答案",
      slot: true,
    },
    examNotes: {
      type: "textarea",
      label: "试题备注",
      placeholder: "请输入试题备注",
      maxlength: 1000,
      showCount: true,
    },
  },
  data: {
    bookName: route.query.bookName,
    title: [],
    answer: [],
    examNotes: "",
  },
})
let titleRef = $ref(null)
let answerRef = $ref(null)
let finished = $ref(false)
let resultData = $ref<any>("")
let accept = ".png,.jpg,.jpeg"
let refName = $ref<any>(null)
/* 识别 */
async function recognition() {
  try {
    let flag1 = formOptions.data.title.length && formOptions.data.answer.length
    let flag2 =
      formOptions.data.title.every((v) => v.status == "finished") &&
      formOptions.data.answer.every((v) => v.status == "finished")
    if (!flag1) return $g.msg("请上传题干或者答案", "warning")
    if (!flag2) return $g.msg("请等待文件上传完毕", "warning")
    formOptions.loading = true
    let params = [
      { type: 1, images: formOptions.data.title.map((v) => v.fullUrl) },
      { type: 2, images: formOptions.data.answer.map((v) => v.fullUrl) },
    ]
    let res = await getQuestionAttachOcrAiRecognition(params)
    formOptions.loading = false
    res.forEach((v) => {
      v.ocrResultList.forEach((vv) => {
        resultData += vv.md
      })
    })
    finished = true
  } catch (err) {
    console.log(err)
    formOptions.loading = false
  }
}
/* 返回 */
function back() {
  finished = false
  resultData = ""
  formOptions.data.title = []
  formOptions.data.answer = []
}
</script>

<style lang="scss" scoped></style>
