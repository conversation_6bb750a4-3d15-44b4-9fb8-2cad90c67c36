<template>
  <div>
    <g-dialog
      title="关联节点"
      v-bind="$attrs"
      v-model:show="show"
      width="1000"
      :show-footer="false"
    >
      <g-table :tableOptions="tableOptions" @change-page="getList"></g-table>
    </g-dialog>
  </div>
</template>
<script lang="ts" setup>
import { getNodeList } from "@/api/resourceMgt"
let props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentNId: {
    type: Number,
    default: null,
  },
})
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
  column: [
    { prop: "bookName", label: "书籍名称" },
    { prop: "path", label: "章节名称" },
    { prop: "questionNum", label: "题目数量" },
  ],
  data: [],
})
const emit = defineEmits(["update:show"])
const show = useVModel(props, "show", emit)
watch(
  () => props.show,
  () => {
    if (props.currentNId && props.show) {
      tableOptions.pageOptions.page = 1
      tableOptions.pageOptions.total = 0
      getList()
    }
  },
)
async function getList() {
  let res = await getNodeList({
    page: tableOptions.pageOptions.page,
    pageSize: tableOptions.pageOptions.page_size,
    sysTextbookCatalogId: props.currentNId,
  })
  tableOptions.data = res?.list || []
  tableOptions.pageOptions.total = res?.total || 0
}
</script>
<style scoped lang="scss"></style>
