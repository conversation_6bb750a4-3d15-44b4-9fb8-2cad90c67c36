<template>
  <div>
    <g-dialog
      title="关联书籍/试卷"
      v-bind="$attrs"
      v-model:show="show"
      width="600"
      :show-footer="false"
    >
      <div class="max-h-[500px] overflow-y-auto">
        <div class="mb-[16px]" v-for="(item, index) in bookList" :key="index">
          {{ item?.bookName || "-" }}
        </div>
      </div>
    </g-dialog>
  </div>
</template>
<script lang="ts" setup>
import { getRelatedBookList } from "@/api/resourceMgt"
let props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  currentAId: {
    type: Number,
    default: null,
  },
})
let bookList = $ref<any>([])

const emit = defineEmits(["update:show"])
const show = useVModel(props, "show", emit)
async function getList() {
  bookList = await getRelatedBookList({
    sysTextbookCatalogId: props.currentAId,
  })
}
watch(
  () => props.show,
  () => {
    if (props.currentAId && props.show) {
      getList()
    }
  },
)
</script>
<style scoped lang="scss"></style>
