<template>
  <div class="book-detail-container-main">
    <div class="text-[16px] mb-[20px]">{{ name }}</div>
    <g-table
      :tableOptions="currentOptions"
      row-key="id"
      :indent="40"
      :cell-style="scoreStyle"
    >
      <template #header-left>
        <div class="text-gray-default">
          知识点（已隐藏）表示该知识点未在“章节知识点”绑定功能中启用，用户端无法看到此知识点
        </div>
      </template>
      <template #num="{ row }">
        <n-button text type="primary" @click="goDetail(row)">{{
          row?.num
        }}</n-button>
      </template>
      <template
        #[slotName]="{ row, column }"
        v-for="slotName in slotList"
        :key="slotName + 'type'"
      >
        <div v-if="row.items[column.property]">
          <div v-if="row.type === '知识点'">
            {{ row.items[column.property]?.totalResourceCount || 0 }}
          </div>
          <div v-else>
            {{ row.items[column.property]?.totalResourceCount || 0 }}({{
              row.items[column.property]?.resourceCount || 0
            }})
          </div>
        </div>
        <div v-else-if="row.type !== '知识点'">0(0)</div>
        <div v-else>0</div>
      </template>
      <template #bookCount="{ row }">
        <n-button
          :disabled="!row?.bookCount"
          text
          type="primary"
          @click="onAClick(row)"
          >{{ row?.bookCount || 0 }}套</n-button
        ></template
      >
      <template #nodeCount="{ row }">
        <n-button
          :disabled="!row?.nodeCount"
          text
          type="primary"
          @click="onNoteClick(row)"
          >{{ row?.nodeCount || 0 }}</n-button
        ></template
      >
    </g-table>
    <PDialog v-model:show="showADialog" :currentAId="currentAId"></PDialog>
    <NoteDialog
      v-model:show="showNDialog"
      :currentNId="currentNId"
    ></NoteDialog>
  </div>
</template>
<script setup lang="ts">
import { getChapterListApi } from "@/api/resourceMgt"
import PDialog from "./components/PDialog.vue"
import NoteDialog from "./components/NoteDialog.vue"
import router from "@/router"
const tableOptions = reactive<any>({
  ref: null as any,
  key: "id",
  loading: false,
  column: [],
  data: [],
})
let currentAId = $ref<any>(null)
let currentNId = $ref<any>(null)
const name = computed(() => {
  let {
    sysTextbooksName,
    sysSubjectName,
    sysStagesName,
    sysTextbookVersionsName,
  } = route.query
  return [
    sysStagesName,
    sysSubjectName,
    sysTextbookVersionsName,
    sysTextbooksName,
  ]
    .filter(Boolean)
    .join("/")
})
const route = useRoute()
let columns = $ref<any>([])
let showADialog = $ref<any>(false)
let showNDialog = $ref<any>(false)
let slotList = $ref<any>([])
let initColumn = [
  { prop: "num", label: "ID", slot: true, type: "" },
  {
    prop: "name",
    label: "结构",
    minWidth: "180px",
    align: "left",
    headerAlign: "center",
  },
  { prop: "type", label: "类型" },
  { prop: "questionCount", label: "试题" },
  { prop: "bookCount", label: "关联书籍/试卷", slot: true },
  { prop: "nodeCount", label: "关联节点", slot: true },
]
let currentOptions = computed(() => {
  tableOptions.column = [...initColumn, ...columns]
  return tableOptions
})
const dealData = (item) => {
  //分为知识点和非知识点
  item.children = [
    ...(item?.children || []).map((h) => {
      return {
        ...h,
        num: h?.sysTextbookCatalogId,
        type: h?.parentSysTextbookCatalogId === 0 ? "章" : "节",
        id: item?.id + "" + h?.sysTextbookCatalogId,
        name: h?.sysTextbookCatalogName,
        items: h?.statisticList?.reduce((pre, next) => {
          return {
            ...pre,
            [next.mappingType]: {
              ...next,
            },
          }
        }, {}),
      }
    }),
    ...(item.knowledgeList || []).map((v) => {
      return {
        ...v,
        type: "知识点",
        num: v?.sysKnowledgePointId,
        id: item.id + "" + v?.sysKnowledgePointId,
        name: v?.hidden
          ? v?.sysKnowledgePointName + "(已隐藏)"
          : v?.sysKnowledgePointName,
        items: v?.statisticList?.reduce((pre, next) => {
          return {
            ...pre,
            [next.mappingType]: {
              ...next,
            },
          }
        }, {}),
      }
    }),
  ]
  if (item?.children?.length > 0) {
    item?.children.forEach((k) => {
      dealData(k)
    })
  }
}
function onAClick(item) {
  if (!item.bookCount) {
    return
  }
  currentAId = item?.sysTextbookCatalogId
  showADialog = true
}
function onNoteClick(item) {
  if (!item?.nodeCount) {
    return
  }
  currentNId = item?.sysTextbookCatalogId
  showNDialog = true
}
const initData = async () => {
  tableOptions.loading = true
  let res = await getChapterListApi({
    sysTextbookId: route.query.sysTextbookId,
  })
  columns = $g.tool.isTrue(res)
    ? res?.[0]?.statisticList?.map((item) => {
        return {
          prop: String(item.mappingType),
          label: item.resourceTypeName,
          slot: true,
        }
      })
    : []
  slotList = columns.map((item) => item.prop)
  //合并完处理数据
  if ($g.tool.isTrue(res)) {
    res.forEach((item) => {
      //第一层直接在递归外处理
      Object.assign(item, {
        type: "章",
        num: item.sysTextbookCatalogId,
        id: item.sysTextbookCatalogId,
        name: item.sysTextbookCatalogName,
        items: item?.statisticList?.reduce((pre, next) => {
          return {
            ...pre,
            [next.mappingType]: {
              ...next,
            },
          }
        }, {}),
      })
      dealData(item)
    })
  }
  tableOptions.data = res || []
  tableOptions.loading = false
}
const goDetail = (row) => {
  console.log(row)
  router.push({
    name: "ResMain",
    query: {
      id: row?.num,
      period: route.query.sysStagesId,
      discipline: route.query.sysCourseId,
      name: row?.name,
      sysKnowledgePointId: row?.sysKnowledgePointId || null,
      sysTextbookCatalogId: row?.sysTextbookCatalogId || null,
      questionCount: row?.questionCount ? row?.questionCount : 0,
      textbook: route.query.sysTextbooksId,
      bankQuestionCount: row?.bankQuestionCount ? row?.bankQuestionCount : 0,
    },
  })
}
const scoreStyle = ({ columnIndex, row, column }) => {
  if (!isNaN(column.property)) {
    if (
      row.items?.[column.property] &&
      row.items?.[column.property]?.totalResourceCount > 0
    ) {
      return {
        background: "#6aa84f",
        color: "#333",
      }
    }
  }
  if (columnIndex === 1 && row?.type === "知识点" && row?.hidden) {
    return {
      color: "#9CA3AF",
    }
  }
}
onBeforeMount(() => {
  initData()
})
</script>
