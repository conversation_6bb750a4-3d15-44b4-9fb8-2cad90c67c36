<template>
  <div class="answer-details-container-main">
    <g-table
      :tableOptions="tableOptions"
      :height="700"
      :element-loading-text="loadingText"
    >
      <template #fileUrl="{ row }">
        <n-image
          v-if="row.fileUrl?.includes('https')"
          width="100"
          :lazy="true"
          :src="row.fileUrl"
        />
        <div v-else>-</div>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="primary" text @click="openDialog(row)"
            >自动批改</n-button
          >
        </n-space>
      </template>
    </g-table>
    <g-dialog
      title="批改模式"
      :formOptions="formOptions"
      v-model:show="showDialog"
      @confirm="correct"
    >
      <g-form :formOptions="formOptions"> </g-form>
    </g-dialog>
  </div>
</template>

<script setup lang="ts" name="AnswerDetails">
import { getAnswerStudentList, handleAnswerCard } from "@/api/bookMgt"
import { autoCorrect } from "@/api/bookMgt"
let showDialog = $ref(false)
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      prop: "studentName",
      label: "姓名",
    },
    {
      prop: "finallyScore",
      label: "学生得分",
    },
    {
      prop: "scoreValue",
      label: "试题总分",
    },
    {
      prop: "fileUrl",
      label: "答题图片",
      slot: true,
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
})
const formOptions = reactive({
  loading: false,
  ref: null as any,
  items: {
    correctMode: {
      type: "radio",
      label: "批改模式",
      options: [
        {
          label: "普通批改",
          value: "GENERAL",
        },
        {
          label: "精细批改",
          value: "EXQUISITE",
        },
      ],
    },
  },
  data: {
    correctMode: "GENERAL",
  },
})
let loadingText = $ref("")
let currentData = $ref<any>({})
const route = useRoute()
onBeforeMount(() => {
  initData()
})
/* 打开弹窗 */
function openDialog(row) {
  currentData = row
  showDialog = true
}
async function initData() {
  try {
    tableOptions.loading = true
    loadingText = "正在加载中..."
    let res = await getAnswerStudentList({
      bookId: route.query.bookId,
      questionId: route.query.questionId,
    })
    tableOptions.loading = false
    tableOptions.data = res
  } catch (err) {
    tableOptions.loading = false
    console.log(err)
  }
}
/* 批改 */
async function correct() {
  try {
    showDialog = false
    $g.msg("自动批改中，请稍后...")
    loadingText = "自动批改中，请稍后..."
    tableOptions.loading = true
    let imgUrl = await handleAnswerCard({
      image: currentData.fileUrl,
    })
    if ($g.tool.isTrue(imgUrl)) {
      let res = await autoCorrect({
        correctMode: formOptions.data.correctMode,
        questionId: route.query.questionId,
        answerImg: imgUrl,
      })
      $g.msg(res.msg)
    } else {
      $g.msg("图片异常", "error")
    }
    tableOptions.loading = false
  } catch (err) {
    tableOptions.loading = false
    console.log(err)
  } finally {
    formOptions.loading = false
  }
}
</script>

<style lang="scss" scoped></style>
