<template>
  <div class="relative w-full max-w-2xl mx-auto">
    <div
      ref="containerRef"
      class="relative bg-gray-100 border-2 border-gray-300 rounded-lg w-full h-full overflow-hidden"
      :style="{ cursor: isDragging ? 'grabbing' : 'grab' }"
      @wheel.prevent="handleWheel"
      @mousedown="handleContainerMouseDown"
      @touchstart="handleContainerTouchStart"
    >
      <div
        class="absolute left-1/2 top-1/2"
        :style="{
          transform: `translate(-50%, -50%) translate(${position.x}px, ${position.y}px) scale(${scale})`,
          transition: isDragging ? 'none' : 'transform 0.2s',
          transformOrigin: 'center',
        }"
      >
        <!-- 图片容器 -->
        <div class="relative">
          <img
            ref="imageRef"
            :src="props.imageUrl"
            alt="Draggable content"
            class="select-none"
            draggable="false"
            @load="initializeImage"
          />

          <!-- 边界框层 -->
          <div
            v-for="(box, index) in newBoxes"
            :key="index"
            class="absolute border border-2 cursor-pointer transition-all duration-200 text-center"
            :class="[
              activeBoxIndex === index
                ? 'border-[#00ff00] opacity-80'
                : 'border-[red] opacity-50',
            ]"
            :style="styleLog(box)"
            @click.stop="handleBoxClick(index)"
          >
            <!-- 显示框的索引 -->
            <div
              class="absolute top-[-20px] left-[-2px] text-white bg-error text-xs px-1 w-20px h-20px"
              :class="[
                activeBoxIndex === index ? 'bg-green bg-[green]' : 'bg-error',
              ]"
            >
              {{ index + 1 }}
            </div>
          </div>
        </div>
      </div>

      <!-- 文本显示区域 -->
      <div
        v-if="activeBoxIndex !== null && props.boxes[activeBoxIndex]"
        class="absolute bottom-16 left-2 right-16 bg-black bg-opacity-75 text-white p-2 rounded shadow-lg text-sm"
      >
        <!-- <g-mathjax :text="`$${props.boxes[activeBoxIndex].text}$`" /> -->
        <g-markdown
          :modelValue="props.boxes[activeBoxIndex].text"
          mode="preview"
        ></g-markdown>
      </div>

      <!-- 缩放比例显示 -->
      <div
        class="absolute top-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm"
      >
        {{ Math.round(scale * 100) }}%
      </div>

      <!-- 控制按钮 -->
      <div class="absolute bottom-4 right-4 flex gap-2">
        <button
          @click.stop="handleZoomOut"
          class="button w-8 h-8 bg-white bg-opacity-75 hover:bg-opacity-100 border rounded-full flex justify-center items-center transition-colors cursor-pointer"
        >
          <i class="ri-subtract-line"></i>
        </button>
        <button
          class="button w-8 h-8 bg-white bg-opacity-75 hover:bg-opacity-100 border rounded-full flex justify-center items-center transition-colors cursor-pointer"
          @click.stop="handleReset"
        >
          <i class="ri-refresh-line"></i>
        </button>
        <button
          @click.stop="handleZoomIn"
          class="button w-8 h-8 bg-white bg-opacity-75 hover:bg-opacity-100 border rounded-full flex justify-center items-center transition-colors cursor-pointer"
        >
          <i class="ri-add-line"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue"

const props = defineProps({
  imageUrl: {
    type: String,
    default: "",
  },
  minScale: {
    type: Number,
    default: 0.1,
  },
  maxScale: {
    type: Number,
    default: 5,
  },
  boxes: {
    type: Array,
    default: () => [],
  },
  //坐标缩放比例
  scaling: {
    type: Number,
    default: 1,
  },
})

// 状态管理
const scale = ref(1)
const initialScale = ref(1)
const position = ref({ x: 0, y: 0 })
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const containerRef = ref(null)
const imageRef = ref(null)
const activeBoxIndex = ref(null)

//验证数据是否正确
const newBoxes = $computed(() => {
  if (!Array.isArray(props.boxes)) return []
  return props.boxes.filter((item) => item?.coord)
})

// 处理框点击
const handleBoxClick = (index) => {
  if (activeBoxIndex.value === index) {
    activeBoxIndex.value = null
  } else {
    activeBoxIndex.value = index
  }
  $g.tool.renderMathjax()
}

// 初始化图片尺寸
const initializeImage = () => {
  if (!containerRef.value || !imageRef.value) return

  const container = containerRef.value
  const image = imageRef.value

  const containerWidth = container.clientWidth
  const containerHeight = container.clientHeight
  const imageWidth = image.naturalWidth
  const imageHeight = image.naturalHeight

  const scaleX = containerWidth / imageWidth
  const scaleY = containerHeight / imageHeight

  const fitScale = Math.min(scaleX, scaleY) * 0.9

  scale.value = fitScale
  initialScale.value = fitScale
  position.value = { x: 0, y: 0 }
}

// 按钮缩放控制
const handleZoomIn = () => {
  const newScale = scale.value * 1.2
  if (newScale <= props.maxScale) {
    scale.value = newScale
    position.value = {
      x: position.value.x * 1.2,
      y: position.value.y * 1.2,
    }
  }
}

const handleZoomOut = () => {
  const newScale = scale.value / 1.2
  if (newScale >= props.minScale) {
    scale.value = newScale
    position.value = {
      x: position.value.x / 1.2,
      y: position.value.y / 1.2,
    }
  }
}

const handleReset = () => {
  scale.value = initialScale.value
  position.value = { x: 0, y: 0 }
  activeBoxIndex.value = null // 重置时清除激活状态
}

// 滚轮缩放处理
const handleWheel = (e) => {
  const wheelDelta = e.deltaY
  const ZOOM_SPEED = 0.001
  const zoomFactor = -wheelDelta * ZOOM_SPEED
  const newScale = scale.value * (1 + zoomFactor)

  if (newScale >= props.minScale && newScale <= props.maxScale) {
    const rect = containerRef.value.getBoundingClientRect()
    const mouseX = e.clientX - rect.left - rect.width / 2
    const mouseY = e.clientY - rect.top - rect.height / 2

    const scaleDiff = newScale - scale.value

    position.value = {
      x: position.value.x - (mouseX * scaleDiff) / scale.value,
      y: position.value.y - (mouseY * scaleDiff) / scale.value,
    }

    scale.value = newScale
  }
}

// 容器拖拽控制
const handleContainerMouseDown = (e) => {
  // 如果点击的是控制按钮或边界框，不执行拖拽
  if (e.target.closest(".bottom-4.right-4") || e.target.closest(".button"))
    return

  // 点击空白区域时清除激活状态
  activeBoxIndex.value = null

  e.preventDefault()
  isDragging.value = true
  dragStart.value = {
    x: e.clientX - position.value.x,
    y: e.clientY - position.value.y,
  }
}

const handleMouseMove = (e) => {
  if (!isDragging.value) return

  position.value = {
    x: e.clientX - dragStart.value.x,
    y: e.clientY - dragStart.value.y,
  }
}

const handleMouseUp = () => {
  isDragging.value = false
}

// 触摸控制
const handleContainerTouchStart = (e) => {
  // 如果点击的是控制按钮或边界框，不执行拖拽
  if (e.target.closest(".bottom-4.right-4") || e.target.closest(".button"))
    return

  if (e.touches.length === 1) {
    e.preventDefault()
    isDragging.value = true
    dragStart.value = {
      x: e.touches[0].clientX - position.value.x,
      y: e.touches[0].clientY - position.value.y,
    }
  }
}

const handleTouchMove = (e) => {
  if (!isDragging.value) return

  if (e.touches.length === 1) {
    position.value = {
      x: e.touches[0].clientX - dragStart.value.x,
      y: e.touches[0].clientY - dragStart.value.y,
    }
  }
}

const handleTouchEnd = () => {
  isDragging.value = false
}

// 监听事件
onMounted(() => {
  window.addEventListener("mousemove", handleMouseMove)
  window.addEventListener("mouseup", handleMouseUp)
  window.addEventListener("touchmove", handleTouchMove, { passive: false })
  window.addEventListener("touchend", handleTouchEnd)
})

onUnmounted(() => {
  window.removeEventListener("mousemove", handleMouseMove)
  window.removeEventListener("mouseup", handleMouseUp)
  window.removeEventListener("touchmove", handleTouchMove)
  window.removeEventListener("touchend", handleTouchEnd)
})

function styleLog(box) {
  return {
    left: `${box.coord.topLeftX / props.scaling}px`,
    top: `${box.coord.topLeftY / props.scaling}px`,
    width: `${box.coord.width / props.scaling}px`,
    height: `${box.coord.height / props.scaling}px`,
  }
}
</script>

<style scoped>
.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
</style>
