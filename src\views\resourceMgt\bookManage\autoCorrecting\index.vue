<template>
  <div class="autoCorrecting-container-main flex justify-center">
    <div class="w-[1200px] mt-20px">
      <!-- 步骤 -->
      <el-steps :active="active" finish-status="success">
        <el-step title="上传作答图片" />
        <el-step title="自动批改" />
        <el-step title="批改完成" />
      </el-steps>
      <!-- 图片上传 -->
      <div v-show="active === 0" class="mt-50px w-500px mx-auto">
        <g-upload
          v-model:fileList="list"
          type="drag"
          :max="1"
          accept=".jpg,.png"
          size
          :fileConfig="{
            img: {
              maxSize: 5 * 1024 * 1024,
            },
          }"
        ></g-upload>
        <div class="flex items-center my-50px text-18px">
          <span class="mr-40px">批改模式</span>
          <el-radio-group v-model="radioValue">
            <el-radio :value="1" size="large">普通模式</el-radio>
            <el-radio :value="2" size="large">精细模式</el-radio>
          </el-radio-group>
        </div>
        <div class="flex justify-center">
          <el-button
            v-show="active != 3"
            :disabled="!list.length"
            @click="changeActive()"
          >
            下一步
          </el-button>
        </div>
      </div>
      <div
        v-if="active == 1"
        class="m-auto w-[500px] h-[500px] text-center text-20px"
      >
        <div
          v-show="!isError"
          v-loading="showLoading"
          class="mt-100px h-100px"
        ></div>
        <img
          v-show="isError"
          :src="$g.tool.getFileUrl('error.png')"
          class="w-[400px]"
        />
        <div>
          {{
            isError
              ? "该项目处于试运行阶段，AI教师批改太累，请你谅解"
              : "自动批改中 请等待 "
          }}
        </div>
      </div>
      <!-- 批改完成 -->
      <div v-if="active == 2">
        <div class="flex">
          <div class="flex-1">
            <div class="my-20px">
              <span class="text-24px">作答图片</span>
              <span class="ml-60px">
                书写可信度：{{ correctData.reliability }}
              </span>
            </div>
            <ImgBox
              class="h-500px border"
              :image-url="correctData.newImageUrl"
              :boxes="correctData.ocrMsg"
            ></ImgBox>
          </div>
          <div class="!w-500px">
            <div class="text-24px my-20px">批改结果</div>
            <ImgBox
              class="h-500px border"
              :image-url="correctData.imageUrl"
            ></ImgBox>
          </div>
        </div>
      </div>
      <!-- 操作按钮 -->
      <div
        v-show="(active == 1 && isError == true) || active == 2"
        class="flex justify-center mt-10px"
      >
        <el-button v-show="active != 3" @click="resetCorrect">
          重新作答
        </el-button>
        <el-button v-show="active != 3" @click="correctFinish">
          完成
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="AutoCorrecting">
import { autoCorrect, autoCorrectInfo } from "@/api/autoCorrect"
import ImgBox from "./components/ImgBox.vue"

const route = useRoute()
const router = useRouter()
let active = $ref(0)
let list: any = $ref([])
let radioValue = $ref(1)
let correctData = $ref<any>({})
let showLoading = $ref(false)
let isError = $ref(false)

//下一步
async function changeActive() {
  try {
    showLoading = true
    active += 1
    let res = await autoCorrect({
      questionId: route.query.questionId,
      correctMode: radioValue == 1 ? "GENERAL" : "EXQUISITE",
      answerImg: list[0].fullUrl,
    })
    correctData = res
    active += 1
  } catch (error) {
    isError = true
    console.log("⚡ error ==> ", error)
  } finally {
    showLoading = false
  }
}

// 重新作答
function resetCorrect() {
  showLoading = false
  isError = false
  active = 0
}

//完成
function correctFinish() {
  router.back()
}

//数据回显
async function initData() {
  try {
    let res = await autoCorrectInfo({ questionId: route.query.questionId })
    if (!res || !res?.autoCorrectVO) return
    let suffix = res.answerImg.split(".").pop()
    list = [
      {
        url: res.answerImg,
        id: $g.tool.uuid(8),
        name: `img.${suffix}`,
      },
    ]
    correctData = res.autoCorrectVO
    radioValue = res.correctMode
    active = 2
  } catch (error) {
    console.log("⚡ error ==> ", error)
  }
}

onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
:deep() {
  .jsoneditor-vue {
    height: 500px;
  }
  .jsoneditor-modes {
    display: none !important;
  }
}
</style>
