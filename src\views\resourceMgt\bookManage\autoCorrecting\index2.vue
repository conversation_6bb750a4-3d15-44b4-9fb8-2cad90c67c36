<template>
  <div class="autoCorrecting-container-main flex justify-center">
    <div class="w-[1200px] mt-20px">
      <el-steps :active="active" finish-status="success">
        <el-step title="上传图片" />
        <el-step title="手写识别" />
        <el-step title="自动批改" />
        <el-step title="批改结果" />
      </el-steps>
      <div
        v-show="active === 0"
        class="flex justify-center mt-50px w-500px mx-auto"
      >
        <g-upload
          v-model:fileList="list"
          type="drag"
          :max="1"
          accept=".jpg,.png"
          size
          :fileConfig="{
            img: {
              maxSize: 5 * 1024 * 1024,
            },
          }"
        ></g-upload>
      </div>

      <div v-if="[1, 2, 3].includes(active)">
        <div class="flex">
          <div class="flex-1">
            <div class="flex">
              <div class="text-24px my-20px">作答图片</div>
              <div class="flex items-center ml-20px">
                <span class="mr-10px">坐标缩放比例</span>
                <el-select
                  v-model="scaling"
                  placeholder="Select"
                  style="width: 80px"
                >
                  <el-option
                    v-for="item in scaleList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
            <ImgBox
              class="h-500px border"
              :image-url="list[0]?.fullUrl"
              :boxes="questionData"
              :scaling="scaling"
            ></ImgBox>
          </div>
          <div class="!w-500px" v-if="[1, 2].includes(active)">
            <div class="text-24px my-20px">JSON数据</div>

            <Vue3JsonEditor
              class="overflow-auto !min-h-500px !h-500px"
              v-model="questionData"
              :show-btns="false"
              :expandedOnStart="true"
              @json-change="onJsonChange"
            />
          </div>
          <div class="!w-500px" v-else-if="active == 3">
            <div class="text-24px my-20px">批改结果</div>
            <ImgBox class="h-500px border" :image-url="endUrl"></ImgBox>
          </div>
        </div>
      </div>
      <div class="flex justify-center mt-50px">
        <div class="w-200px flex justify-center">
          <el-button
            :disabled="showLoading"
            v-show="active"
            @click="changeActive(-1)"
            >上一步</el-button
          >
          <el-button
            v-show="active != 3"
            @click="changeActive(1)"
            :loading="showLoading"
            >下一步</el-button
          >
          <el-button
            v-show="active == 3"
            @click="complete"
            :loading="showLoading"
            >完成</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  uploadImg,
  handwritingRecognition,
  getCorrectJson,
  getCorrectData,
  synthesisImg,
} from "@/api/bookMgt"
import ImgBox from "./components/ImgBox.vue"
import { Vue3JsonEditor } from "vue3-json-editor"

let active = $ref(0)

let list: any = $ref([])
const route = useRoute()
let showLoading = $ref(false)
let questionData = $ref<any>({})
const router = useRouter()
let endUrl: any = $ref(null)
let scaling = $ref(4)
const scaleList = [
  { label: "1:1", value: 1 },
  { label: "1:2", value: 2 },
  { label: "1:4", value: 4 },
  { label: "1:8", value: 8 },
]

async function changeActive(value) {
  try {
    // 上一步无需额外逻辑
    if (value === -1) {
      return (active += value)
    }
    // 第一步
    if (active === 0) {
      if (!list[0]?.fullUrl) return $g.msg("请上传图片", "error")
      showLoading = true
      await uploadImg({
        questionId: route.query.questionId,
        fileUrl: list[0].fullUrl,
      })
      await handwritingRecognition({
        questionId: route.query.questionId,
      }).then(async (res) => {
        await errorMsg(parseJSON(res))
        questionData = parseJSON(res)
      })
    }
    // 第二步
    if (active === 1) {
      showLoading = true
      await getCorrectJson({
        questionId: route.query.questionId,
        ocrJson: JSON.stringify(questionData),
      }).then(async (res) => {
        await errorMsg(parseJSON(res))
        questionData = parseJSON(res)
      })
    }
    //第三步
    if (active === 2) {
      showLoading = true
      let res = await synthesisImg({
        questionId: route.query.questionId,
        json: JSON.stringify(questionData),
      })
      endUrl = res
    }
    active += value
    showLoading = false
  } catch (error: any) {
    if (error?.message) {
      $g.notification.error({
        content: "错误",
        meta: error.message,
        duration: 3000,
      })
    }
    console.log("💊 error ==> ", error)
    showLoading = false
  }
}

/* json修改 */
function onJsonChange(val) {
  questionData = val
  debouncedPrint()
}
// 创建一个防抖函数，延迟1秒执行
const debouncedPrint = $g._.debounce((data) => {
  $g.tool.renderMathjax()
}, 500)
/**
 * 增强版JSON解析函数，可处理标准和非标准JSON
 * @param {string} source - JSON字符串
 * @returns {object|null} 解析后的JavaScript对象，解析失败返回null
 */
function parseJSON(source) {
  // 移除空白字符
  source = source.trim()

  if (!source) return null

  let jsonObj = null

  // 1. 尝试解析标准JSON
  try {
    jsonObj = JSON.parse(source)
    return jsonObj
  } catch (ex) {
    // 继续尝试其他方法
  }

  try {
    const jsonpRegex = /^([\w.]+)\(([\s\S]*)\)$/m
    const matches = jsonpRegex.exec(source)
    if (matches) {
      const jsonContent = matches[2]
      return parseJSON(jsonContent) // 递归处理JSONP中的JSON内容
    }
  } catch (ex) {
    // 继续尝试其他方法
  }

  // 3. 处理未加引号的key
  try {
    const keyRegex = /([{,]\s*)(\w+)(\s*:)/g
    const normalizedJson = source.replace(keyRegex, '$1"$2"$3')
    jsonObj = JSON.parse(normalizedJson)
    return jsonObj
  } catch (ex) {
    // 继续尝试其他方法
  }

  // 4. 尝试使用Function构造器（不安全，但可处理一些特殊情况）
  try {
    jsonObj = new Function("return " + source)()

    // 如果返回的是字符串，可能是双重编码的情况
    if (typeof jsonObj === "string") {
      try {
        return JSON.parse(jsonObj)
      } catch (e) {
        // 最后尝试将字符串作为JavaScript表达式求值
        return new Function("return " + jsonObj)()
      }
    }

    return jsonObj
  } catch (ex) {
    // 所有方法都失败了
    return null
  }
}

/* 用于判断json是否有报错信息,如果有则报错退出后面流程 */
function errorMsg(response) {
  return new Promise((resolve, reject) => {
    const jsonData =
      typeof response === "string" ? JSON.parse(response) : response
    // 递归查找 error 值
    const findError = (obj) => {
      if (!obj || typeof obj !== "object") return null
      if ("error" in obj) return obj.error

      for (let key in obj) {
        if (typeof obj[key] === "object") {
          const result = findError(obj[key])
          if (result) return result
        }
      }
      return null
    }

    const error = findError(jsonData)
    if (error) {
      reject({ message: error })
    } else {
      resolve(jsonData)
    }
  })
}

//获取步骤数据
async function getSetupData() {
  let res = await getCorrectData({ questionId: route.query.questionId })
  if (!res) return
  //第一步
  if (res?.fileUrl) {
    let suffix = res.fileUrl.split(".").pop()
    list = [
      {
        url: res.fileUrl,
        id: $g.tool.uuid(8),
        name: `img.${suffix}`,
      },
    ]
  }
  //第二步
  if (res?.ocrJson) {
    await errorMsg(parseJSON(res.ocrJson))
    questionData = parseJSON(res.ocrJson)
    active = 1
  }
  //第三步
  if (res?.correctJson) {
    await errorMsg(parseJSON(res.correctJson))
    questionData = parseJSON(res.correctJson)
    active = 2
  }
  //第四步
  if (res?.correctFileUrl) {
    endUrl = res.correctFileUrl
    active = 3
  }
}

function complete() {
  router.back()
}

onMounted(async () => {
  await getSetupData()
})
</script>

<style lang="scss" scoped>
:deep() {
  .jsoneditor-vue {
    height: 500px;
  }
  .jsoneditor-modes {
    display: none !important;
  }
}
</style>
