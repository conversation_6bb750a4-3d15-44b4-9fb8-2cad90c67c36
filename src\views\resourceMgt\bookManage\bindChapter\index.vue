<template>
  <div class="enterResource-container-main">
    <div class="flex justify-between items-end mb-10px gap-x-[10px]">
      <div class="flex-1">
        <div>书籍:{{ treeData.bookName }}</div>
      </div>
      <div class="flex-1">
        <div>关联总树章节</div>
      </div>
      <div class="flex-1 flex justify-between">
        <div>已选择</div>
        <n-space>
          <n-button
            type="primary"
            :loading="saveLoading"
            :disabled="saveOutLoading"
            @click="save(1)"
            >保存修改</n-button
          >
          <n-button
            type="error"
            :disabled="saveLoading"
            :loading="saveOutLoading"
            @click="save(2)"
            >保存后退出</n-button
          >
        </n-space>
      </div>
    </div>
    <div class="flex justify-between gap-x-[10px]">
      <!-- 当前书籍 -->
      <div class="flex-1 relative" style="border: 1px solid #ccc">
        <g-icon
          name="ri-links-fill"
          size="25"
          color="var(--g-primary)"
          style="transform: rotate(50deg)"
          class="absolute right-[-17px] z-[999]"
        />
        <g-icon
          name="ri-links-fill"
          size="25"
          color="var(--g-primary)"
          style="transform: rotate(50deg)"
          class="absolute bottom-20px right-[-17px] z-[999]"
        />
        <!-- 整书模式 -->
        <template v-if="mode == 1">
          <div class="my-10px px-10px">整书关联</div>
          <el-scrollbar class="h-[calc(100vh-230px)]">
            <div class="flex px-10px gap-x-[20px] my-10px">
              <n-button type="primary" text>{{ treeData.bookName }}</n-button>
              <n-popover trigger="hover">
                <template #trigger>
                  <n-badge :value="treeData.unionCatalogNum"> </n-badge>
                </template>
                <div
                  v-if="treeData.unionCatalogNum"
                  class="max-h-[500px] overflow-auto"
                >
                  <div
                    class="py-20px bg-[#F8F8F8] max-w-[450px] my-10px"
                    v-for="item in treeData.unionCatalog"
                    :key="item.sysTextbookId"
                  >
                    <div class="px-20px text-primary">
                      {{ item.sysTextbookVersionName }}
                      {{ item.sysTextbookName }}
                    </div>
                    <div
                      class="px-20px"
                      v-for="v in item.sysTextbookCatalogList"
                      :key="v.sysTextbookCatalogId"
                    >
                      {{ v.sysTextbookCatalogName }}
                    </div>
                  </div>
                </div>
              </n-popover>
            </div>
          </el-scrollbar>
        </template>
        <!-- 章节模式 -->
        <template v-if="mode == 2">
          <div class="my-10px px-10px">节点名称:{{ currentCatalogName }}</div>
          <el-scrollbar class="h-[calc(100vh-230px)]">
            <g-tree
              :treeData="treeData?.catalogList"
              nodeKey="bookCatalogId"
              :border="false"
              :highlightCurrent="true"
              :default-expanded-keys="[mainBookCatalogId]"
              :default-checked-keys="[mainBookCatalogId]"
              @handleNodeClick="nodeClick"
              @node-expand="
                () => {
                  $g.tool.renderMathjax()
                }
              "
            >
              <template #body="{ data }">
                <div
                  class="flex items-center !text-[12px] gap-10px w-[calc(100%)] overflow-hidden justify-between pr-20px"
                >
                  <g-mathjax :text="data?.bookCatalogName" />
                  <n-popover trigger="hover" :disabled="!data.unionCatalog">
                    <template #trigger>
                      <n-badge :value="data.unionCatalogNum"> </n-badge>
                    </template>
                    <div
                      v-if="data.unionCatalogNum"
                      class="max-h-[500px] overflow-auto"
                    >
                      <div
                        class="py-20px bg-[#F8F8F8] max-w-[450px] my-10px"
                        v-for="item in data.unionCatalog"
                        :key="item.sysTextbookId"
                      >
                        <div class="px-20px text-primary">
                          {{ item.sysTextbookVersionName }}
                          {{ item.sysTextbookName }}
                        </div>
                        <div
                          class="px-20px my-5px"
                          v-for="v in item.sysTextbookCatalogList"
                          :key="v.sysTextbookCatalogId"
                        >
                          {{ v.sysTextbookCatalogName }}
                        </div>
                      </div>
                    </div>
                  </n-popover>
                </div>
              </template>
            </g-tree>
          </el-scrollbar>
        </template>
      </div>
      <!-- 关联树 -->
      <div class="flex-1" style="border: 1px solid #ccc">
        <div class="flex justify-between items-center my-10px px-10px">
          <div class="flex-shrink-0">
            学段/学科:{{ $route.query.sysStageName }}/{{
              $route.query.sysSubjectName
            }}
          </div>
          <div class="flex-1 flex items-center ml-10px">
            <el-tree-select
              v-model="currentTextbookId"
              :data="selectOptions"
              clearable
              placeholder="请选择书籍版本"
              @change="fetchRightTreeData"
            />
          </div>
        </div>
        <el-scrollbar class="h-[calc(100vh-240px)]">
          <g-tree
            :border="false"
            ref="Tree2Ref"
            treeName="RightTree"
            :highlight-check="false"
            check-strictly
            :treeData="rightTreeData"
            @node-click="handleNodeClick"
            @node-expand="
              () => {
                $g.tool.renderMathjax()
              }
            "
          >
            <template #body="{ data }">
              <g-mathjax :text="data?.sysTextbookCatalogName" />
            </template>
          </g-tree>
        </el-scrollbar>
      </div>
      <!-- 选中关联章节 -->
      <div class="flex-1">
        <div class="flex justify-end">
          <n-button type="error" @click="clear">删除所有关联章节</n-button>
        </div>
        <el-scrollbar class="h-[calc(100vh-260px)]">
          <n-space vertical>
            <g-empty v-if="!chapterList.length"></g-empty>
            <template v-else>
              <div
                v-for="v in chapterList"
                :key="v.sysTextbookId"
                class="bg-[#F8F8F8] p-20px rounded-[8px]"
              >
                <div class="text-16px mb-10px">
                  {{ v.sysTextbookVersionName }} - {{ v.sysTextbookName }}
                </div>
                <n-space vertical>
                  <n-tag
                    v-for="item in v.sysTextbookCatalogList"
                    :key="item.sysTextbookCatalogId"
                    class="cursor-pointer"
                    type="primary"
                    closable
                    size="large"
                    round
                    @close="deleteChapter(v, item)"
                    >{{ item.sysTextbookCatalogName }}</n-tag
                  >
                </n-space>
              </div>
            </template>
          </n-space>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="BindChapter">
import { getDirectoryList, getChapterTreeList } from "@/api/resourceMgt"
import {
  saveBookCatalog,
  getVersionAndTextbook,
  getTextbookDetail,
} from "@/api/bookMgt"
const route = useRoute()
const router = useRouter()
let treeData = $ref<any>([])
let rightTreeData = $ref<any>([])
let mainBookCatalogId = $ref<any>(null)
let chapterList = $ref<any>([])
let saveLoading = $ref(false)
let saveOutLoading = $ref(false)
let selectOptions = $ref<any>([])
let currentTextbookId = $ref<any>(null)
let currentCatalogName = $ref(route.query.currentCatalogName)
let showTips = $ref(false)
let bookDetail = $ref<any>({})
//操作模式  1为整书  2为章节
const mode = $computed<any>(() => {
  return route.query.type
})

onBeforeRouteLeave((to, from, next) => {
  if (showTips) {
    const confirmationMessage =
      "当前章节还有未保存的修改内容，点击确定按钮保存并退出，点击取消按钮直接退出"
    if (window.confirm(confirmationMessage)) {
      save(2, next)
    } else {
      next()
    }
  } else {
    next()
  }
})
/* 获取当前教材信息 */
async function fetchBookDetail() {
  try {
    let res = await getTextbookDetail({
      sysTextbookId: currentTextbookId,
    })
    bookDetail = res ?? {}
  } catch (err) {
    console.log(err)
  }
}
/* 清空当前选中章节绑定关系 */
async function clear() {
  try {
    if (!mainBookCatalogId && mode == 2) return $g.msg("请选择章节", "warning")
    // if (!chapterList.length) return $g.msg("当前没有关联章节", "warning")
    $g.confirm({
      type: "warning",
      title: "提示",
      content: "否删除该节点所有关联章节",
      positiveText: "确定",
      negativeText: "取消",
    })
      .then(async () => {
        await saveBookCatalog({
          bookId: route.query.bookId,
          bookCatalogId: mode == 1 ? 0 : mainBookCatalogId,
          sysTextbookCatalogIdList: [],
        })
        $g.msg("操作成功")
        chapterList = []
        showTips = false
        await getDirectoryListApi(true)
      })
      .catch(() => {})
  } catch (err) {
    console.log(err)
  }
}
/* 保存绑定关系 */
async function save(type, next?) {
  try {
    if (!mainBookCatalogId && mode == 2) return $g.msg("请选择章节", "warning")
    // if (!chapterList.length) return $g.msg("当前没有关联章节", "warning")
    // type 1 保存并继续 2 保存并退出
    type == 1 && (saveLoading = true)
    type == 2 && (saveOutLoading = true)
    let arr = [] as any
    chapterList.forEach((item) => {
      item.sysTextbookCatalogList?.forEach((v) => {
        arr.push(v.sysTextbookCatalogId)
      })
    })
    await saveBookCatalog({
      bookId: route.query.bookId,
      bookCatalogId: mode == 1 ? 0 : mainBookCatalogId,
      sysTextbookCatalogIdList: arr,
    })
    showTips = false
    await getDirectoryListApi(true)
    $g.msg("保存成功")
    if (type == 1) {
      saveLoading = false
      saveOutLoading = false
    } else {
      if (next) {
        next()
      } else {
        router.go(-1)
      }
    }
  } catch (err) {
    saveLoading = false
    saveOutLoading = false
    console.log("error=>", err)
  }
}

/* 删除树选中章节 */
function deleteChapter(parent, val) {
  showTips = true
  let index = chapterList.findIndex(
    (item) => item.sysTextbookId == parent.sysTextbookId,
  )
  if (index != -1) {
    // 存在Book
    let num = chapterList[index].sysTextbookCatalogList.findIndex(
      (v) => v.sysTextbookCatalogId == val.sysTextbookCatalogId,
    )
    chapterList[index].sysTextbookCatalogList.splice(num, 1)
    // 删除book
    if (chapterList[index].sysTextbookCatalogList.length == 0) {
      chapterList.splice(index, 1)
    }
  }
}
function getName(node, suffix = "", id = "") {
  suffix = suffix
    ? node.data.bookCatalogName + "/" + suffix
    : node.data.bookCatalogName
  id = id
    ? String(node.data.bookCatalogId) + "/" + id
    : String(node.data.bookCatalogId)
  if (node.parent.level !== 0) {
    return getName(node.parent, suffix, id)
  }
  return {
    suffix,
    id,
  }
}
/* 左侧树选中章节 */
function nodeClick(data, node) {
  chapterList = []
  if (node.checked) {
    showTips = false
    mainBookCatalogId = data.bookCatalogId
    chapterList = $g._.cloneDeep(data.unionCatalog ?? [])
    currentCatalogName = getName(node).suffix
  } else {
    mainBookCatalogId = null
  }
}
/* 右侧树点击事件 */
function handleNodeClick(data) {
  if (!mainBookCatalogId && mode == 2)
    return $g.msg("请先选择左侧章节树", "warning")
  let index = chapterList.findIndex(
    (item) => item.sysTextbookId == bookDetail.sysTextbookId,
  )
  showTips = true
  if (index == -1) {
    chapterList.push({
      sysTextbookCatalogList: [data],
      ...bookDetail,
    })
  } else {
    // 存在Book
    let num = chapterList[index].sysTextbookCatalogList.findIndex(
      (v) => v.sysTextbookCatalogId == data.sysTextbookCatalogId,
    )
    if (num == -1) {
      chapterList[index].sysTextbookCatalogList.push(data)
    }
  }
}
onBeforeMount(() => {
  getDirectoryListApi()
  fetchVersion()
})

/* 获取书籍树 */
async function getDirectoryListApi(refreshTree = false) {
  const data = await getDirectoryList({ bookId: route.query.bookId })
  treeData = data || null

  if ((route.query.type as any) == 1) {
    // 整书
    if (data.unionCatalog && data.unionCatalog.length) {
      chapterList = $g._.cloneDeep(data.unionCatalog)
    }
  } else {
    if (refreshTree) return
    mainBookCatalogId = route.query.bookCatalogId ?? null
    chapterList =
      findInTree(treeData.catalogList, mainBookCatalogId).unionCatalog ?? []
  }

  nextTick(() => {
    $g.tool.renderMathjax()
  })
}
/* 递归查找树 */
function findInTree(tree, target) {
  for (let item of tree) {
    if (item.bookCatalogId == target) {
      return item
    }
    if (item.children && item.children.length) {
      let result = findInTree(item.children, target)
      if (result) return result
    }
  }
  return null
}
let sourceMap = {
  1: "XKW",
  2: "PLATFORM",
}
/* 获取版本信息 */
async function fetchVersion() {
  try {
    let res = await getVersionAndTextbook({
      sysCourseId: route.query.sysCourseId,
    })
    if (res.length) {
      selectOptions = transformDataStructure(res)
      currentTextbookId = selectOptions[0].children[0].value
      await fetchRightTreeData()
    }
  } catch (err) {
    console.log(err)
  }
}

/* 递归处理数据 */
function transformDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.label =
        newItem.sysTextbookVersionName ??
        (newItem.volume ? newItem.volume : newItem.sysTextbookName)
      newItem.value = newItem.textbookList?.length
        ? newItem.sysTextbookVersionId
        : newItem.sysTextbookId
      newItem.children = newItem.textbookList?.length
        ? transformDataStructure(newItem.textbookList)
        : null

      return newItem
    })
  }
  return data
}
/* 获取右侧树数据 */
async function fetchRightTreeData() {
  try {
    let res = await getChapterTreeList({
      sysTextbookId: currentTextbookId,
    })
    rightTreeData = res
    fetchBookDetail()
  } catch (err) {
    console.log(err)
  }
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-tree-node__content {
    height: auto !important;
  }
  .el-tree-node {
    white-space: break-spaces;
  }
  .custom-tree-node {
    overflow: hidden;
    .element-tree-node-label-wrapper {
      overflow: hidden;
    }
  }
}
</style>
