<template>
  <div>
    <n-popover ref="aiPopover" class="w-400px" trigger="click" placement="left">
      <template #trigger>
        <n-button type="primary" text @click="getAiProcessListApi"
          >AI预处理</n-button
        >
      </template>
      <g-table
        class="mt-18px"
        :tableOptions="tableOptions"
        size="small"
        :emptySize="100"
        :border="false"
      >
        <template #cz="{ row }">
          <el-button
            :disabled="row.state !== null"
            size="small"
            type="primary"
            text
            @click="runAI(row)"
          >
            AI预处理
          </el-button>
        </template>
      </g-table>
    </n-popover>
  </div>
</template>

<script setup lang="ts">
import { aiProcess, getAiProcessList } from "@/api/bookMgt"
/** Props类型定义 */
interface IProps {
  info: any
}

const aiPopover = $ref<any>(null)

const props = withDefaults(defineProps<IProps>(), {
  info: {},
})

const stateMap = {
  0: "不可处理",
  1: "等待",
  2: "处理中",
  3: "已处理",
  4: "失败",
}

const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: true,
  column: [
    {
      prop: "fileName",
      label: "附件名",
    },
    {
      prop: "state",
      label: "处理状态",
      formatter(row) {
        return stateMap[row.state] || "未处理"
      },
    },
    { prop: "cz", label: "操作", slot: true },
  ],
  data: [],
})

/* 获取AI预处理资源列表  测试  西昌-入学考试-高三 */
function getAiProcessListApi() {
  tableOptions.loading = false
  getAiProcessList({
    bookId: props.info.bookId,
    page: 1,
    pageSize: 999,
  }).then((res) => {
    tableOptions.data = res.list
  })
}

function runAI(row) {
  aiPopover.setShow(true)
  $g.confirm({
    title: "温馨提示",
    content: "对上传的附件进行切题识别及学科网搜题预处理，确定开始预处理？",
    positiveText: "确定",
    negativeText: "取消",
  }).then(() => {
    aiProcess({
      bookAttachId: row.bookAttachId,
    }).then((res) => {
      getAiProcessListApi()
      aiPopover.setShow(true)
      $g.msg("操作成功")
    })
  })
}
</script>

<style scoped lang="scss"></style>
