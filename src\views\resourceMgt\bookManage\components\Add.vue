<template>
  <g-dialog
    :title="title"
    :formOptions="formOptions"
    v-model:show="showDialog"
    @confirm="confirm"
    width="700"
  >
    <div class="max-h-[80vh] overflow-auto">
      <g-form :formOptions="formOptions">
        <template #sysStage>
          <n-radio-group
            v-model:value="formOptions.data.sysStage"
            name="radiogroup"
            @update:value="handleSysStage"
            :disabled="formOptions.items.sysSubjectId.disabled"
          >
            <n-space>
              <n-radio
                v-for="item in formOptions.items.sysStage.options"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </template>
        <template #bookTypeIdList>
          <n-tree-select
            class="w-[400px]"
            multiple
            v-model:value="formOptions.data.bookTypeIdList"
            :options="formOptions.items.bookTypeIdList.options"
            clearable
            :placeholder="`请选择${category == 1 ? '书籍' : '试卷'}类型`"
            :render-label="renderLabel"
            @update:value="handleUpdateValue"
          />
        </template>
        <template #cover>
          <g-upload
            v-model:fileList="formOptions.data.cover"
            type="image-card"
            :max="1"
            accept=".png,.jpg,.jpeg,.bmp,.gif,.webp"
            tips="只能上传.png,.jpg,.jpeg,.bmp,.gif,.webp文件,且不超过20MB"
          ></g-upload>
        </template>
        <template #schoolIdList>
          <n-cascader
            class="w-[400px]"
            v-model:value="formOptions.data.schoolIdList"
            multiple
            clearable
            filterable
            placeholder="选择学校"
            :options="formOptions.items.schoolIdList.options"
            max-tag-count="responsive"
            check-strategy="child"
            :on-update:value="schoolUpdate"
          />
        </template>
        <template #sysTextbookVersionIds>
          <n-cascader
            v-model:value="formOptions.data.versionAndTextbookIds"
            multiple
            clearable
            filterable
            placeholder="请选择教材/版本"
            max-tag-count="responsive"
            :options="formOptions.items.sysTextbookVersionIds.options"
            :cascade="false"
          />
        </template>
        <template #list>
          <g-upload
            class="w-[400px]"
            v-model:fileList="formOptions.data.list"
            type="drag"
            multiple
            accept=".zip,.rar,.doc,.docx,.pdf,.png,.jpg,.gif,.txt,.xls,.xlsx,.ppt,.pptx"
          ></g-upload>
        </template>
        <template #displayDate>
          <div class="flex items-center">
            <div class="flex items-center w-100px justify-end pr-12px">
              <span>展示时间</span>
              <n-tooltip placement="top" trigger="hover">
                <template #trigger>
                  <g-icon
                    name="ri-question-line"
                    size="14"
                    color=""
                    class="ml-4px"
                  />
                </template>
                <div class="text-12px">该日期之日0点起，资源可显示给学生端</div>
              </n-tooltip>
            </div>
            <n-date-picker
              type="date"
              v-model:formatted-value="formOptions.data.displayDate"
              value-format="yyyy-MM-dd"
            ></n-date-picker>
            <span class="ml-2px">起</span>
          </div>
        </template>
        <template #years>
          <div class="flex items-center w-full">
            <el-date-picker
              v-model="formOptions.data.years[0]"
              type="year"
              :editable="false"
              :clearable="false"
              :teleported="false"
              value-format="YYYY"
              @change="dateChange"
            />
            <div class="mx-6px">~</div>
            <el-date-picker
              v-model="formOptions.data.years[1]"
              type="year"
              :editable="false"
              :clearable="false"
              :teleported="false"
              value-format="YYYY"
              :disabled="true"
            />
          </div>
        </template>
        <template #provinceIds>
          <n-cascader
            multiple
            clearable
            filterable
            placeholder="选择所属地区"
            v-model:value="formOptions.data.provinceIds"
            :options="formOptions.items.provinceIds.options"
            :max-tag-count="1"
            check-strategy="child"
          ></n-cascader>
        </template>
      </g-form>
    </div>
  </g-dialog>
</template>

<script setup lang="ts">
import {
  getNewStageListApi,
  getNewSubjectListApi,
  getVersionAndTextbook,
  getAreaList,
  getTermSelect,
} from "@/api/resourceMgt"
import { getGradeListApi } from "@/api/common"
import {
  getTypeSelect,
  addBookApi,
  editBookApi,
  getSchoolList,
  copyPaperOrBook,
  getByBookName,
} from "@/api/bookMgt"
import GTooltip from "@/components/global/g-tooltip/index.vue"

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  info: {
    type: Object,
    default: () => {},
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  //类型：1书籍 ，2试卷
  category: {
    type: Number,
    default: 1,
  },
  //是否复制
  isCopy: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
let isRepeat = $ref(false) //名称是否重复

function validate(rule, value, callback) {
  if (value.length == 0) {
    callback(new Error(`请选择${props.category == 1 ? "书籍" : "试卷"}类型`))
  }
  callback()
}

//学年规则
function validate2(rule, value, callback) {
  if (value.length == 0) {
    callback(new Error(`请选择${props.category == 1 ? "书籍" : "试卷"}学年`))
  }
  callback()
}

function validate3(rule, value, callback) {
  if (isRepeat) {
    callback(new Error("名称重复，请修改！"))
  }
  if (!value) {
    callback(new Error(`请输入${props.category == 1 ? "书籍" : "试卷"}名称`))
  }
  callback()
}

const formOptions = reactive<any>({
  loading: false,
  ref: null as any,
  items: {
    sysStage: {
      type: "radio",
      label: "学段",
      options: [],
      rule: true,
      slot: true,
    },
    sysSubjectId: {
      type: "radio",
      label: "学科",
      options: [],
      rule: true,
      disabled: false,
    },
    bookTypeIdList: {
      type: "select",
      label: `${props.category == 1 ? "书籍" : "试卷"}类型`,
      options: [],
      slot: true,
      rule: {
        validator: validate,
        type: "string",
        required: true,
      },
    },
    sysGrade: {
      type: "select",
      options: [],
      label: "年级",
      span: 11,
    },
    years: {
      type: "year",
      label: `${props.category == 1 ? "书籍" : "试卷"}学年`,
      span: 11,
      slot: true,
      rule: {
        validator: validate2,
        type: "string",
        required: true,
      },
    },
    sysTermId: {
      type: "select",
      label: "学期",
      span: 11,
      clearable: false,
      options: [],
      rule: true,
    },
    year: {
      type: "select",
      label: `${props.category == 1 ? "书籍" : "试卷"}年份`,
      options: [],
      span: 11,
      clearable: false,
      rule: {
        type: "any",
        required: true,
      },
    },
    month: {
      type: "select",
      label: "月份",
      span: 11,
      options: [
        { label: "1月", value: 1 },
        { label: "2月", value: 2 },
        { label: "3月", value: 3 },
        { label: "4月", value: 4 },
        { label: "5月", value: 5 },
        { label: "6月", value: 6 },
        { label: "7月", value: 7 },
        { label: "8月", value: 8 },
        { label: "9月", value: 9 },
        { label: "10月", value: 10 },
        { label: "11月", value: 11 },
        { label: "12月", value: 12 },
      ],
    },
    schoolIdList: {
      type: "select",
      label: "归属学校",
      slot: true,
      options: [],
    },
    provinceIds: {
      type: "select",
      label: "所属地区",
      options: [],
      span: 19,
      slot: true,
    },
    bookName: {
      type: "text",
      label: `${props.category == 1 ? "书籍" : "试卷"}名称`,
      rule: {
        validator: validate3,
        type: "string",
        required: true,
      },
      width: "400px",
    },
    bookNameAlias: { type: "text", label: "别名", width: "400px" },
    sysTextbookVersionIds: {
      type: "select",
      label: "版本/教材",
      options: [],
      span: 19,
      slot: true,
    },
    cover: {
      type: "upload",
      label: "封面",
      slot: true,
    },
    nickname: {
      type: "text",
      label: "上传人",
      width: "400px",
    },
    list: {
      type: "upload",
      label: "附件",
      slot: true,
    },
    displayDate: {
      slot: true,
      showLabel: false,
    },
  },
  data: {
    sysStage: null,
    sysSubjectId: null,
    bookTypeIdList: [],
    sysGrade: null,
    years: [],
    sysTermId: null,
    year: null,
    month: null,
    schoolIdList: [],
    provinceIds: [],
    bookName: "",
    bookNameAlias: "",
    versionAndTextbookIds: [],
    cover: [],
    nickname: "",
    list: [],
    displayDate: null,
  },
})

const allSchool: any = $ref([])
let chooseSchoolName = $ref("") //选择的学校的名字'、'分隔 （省份+市+区 + 学校名字）

//类型名称总和
let typeName = $ref("")

//自动合成名称  试卷学年 + （ 【省份+市+区 + 学校名字】、 【省份+市+区 + 学校名字】） + 年级 + 学期 + 月份 + （试卷类型、试卷类型） + 科目 + 试卷
const autoName = $computed(() => {
  //年级名称
  const sysGradeName =
    formOptions.items.sysGrade.options.find(
      (item) => item.value == formOptions.data.sysGrade,
    )?.label || ""
  //学期
  const sysTermName =
    formOptions.items.sysTermId.options.find(
      (item) => item.value == formOptions.data.sysTermId,
    )?.label || ""
  //月份
  const monthName = formOptions.data.month ? formOptions.data.month + "月" : ""
  //学科
  const subjectName =
    formOptions.items.sysSubjectId.options.find(
      (item) => item.value == formOptions.data.sysSubjectId,
    )?.label || ""
  const yearRange = formOptions.data.years || []
  const yearStr =
    yearRange.length >= 2
      ? `${yearRange[0]}-${yearRange[1]}`
      : yearRange[0] || ""
  const parts = [
    yearStr,
    chooseSchoolName,
    sysGradeName,
    sysTermName,
    monthName,
    typeName,
    subjectName,
    "试卷",
  ]
  let str = parts.filter((part) => part).join("")
  //最大长度512
  if (str.length > 512) str = str.substring(0, 512)
  return str
})

const title = $computed(() => {
  if (props.isCopy) return "复制"
  return props.isEdit ? "编辑" : "新增"
})
/* 获取学段 */
async function getStage() {
  let res = await getNewStageListApi()
  formOptions.items.sysStage.options = res.map((v) => {
    return {
      ...v,
      value: v.id,
      label: v.title,
    }
  })
  formOptions.data.sysStage = sysStage ? sysStage : res[0].id
  sysSubjectId = props.info?.sysSubjectId
  await getSubject()
  await getGradeList()
  await getTypeSelectApi()
}

async function getVersionAndTextbookApi() {
  const data = await getVersionAndTextbook({
    sysCourseId: formOptions.items.sysSubjectId.options.find(
      (v) => v.value === formOptions.data.sysSubjectId,
    )?.courseId,
  })
  formOptions.items.sysTextbookVersionIds.options = data?.map((v) => ({
    label: v.sysTextbookVersionName,
    value: "version " + v.sysTextbookVersionId,
    children: v.textbookList.map((vv) => ({
      label: vv.volume ? vv.volume : vv.sysTextbookName,
      value: "textbook " + vv.sysTextbookId,
    })),
  }))
  if (props.isEdit) {
    getVersionAndTextbookIds(props.info?.sysTextbookVersionList || [])
  }
}

function getVersionAndTextbookIds(list) {
  const arr: any = []
  list.forEach((v) => {
    arr.push("version " + v.sysTextbookVersionId)
    v.sysTextbookList.forEach((vv) => {
      arr.push("textbook " + vv.sysTextbookId)
    })
  })
  formOptions.data.versionAndTextbookIds = arr
}

function renderLabel({ option }) {
  return h(GTooltip, { content: option.label, refName: String(option.key) }, {})
}
/* 获取学科 */
async function getSubject() {
  let res = await getNewSubjectListApi({
    sysStageId: formOptions.data.sysStage,
  })
  formOptions.items.sysSubjectId.options = res.map((v) => {
    return {
      value: v.sysSubjectId,
      label: v.sysCourseName,
      courseId: v.sysCourseId,
    }
  })
  formOptions.data.sysSubjectId = sysSubjectId ?? res[0].sysSubjectId
  sysSubjectId = null
}
/* 获取类型 */
async function getTypeSelectApi() {
  let res = await getTypeSelect({ sysStageId: formOptions.data.sysStage })
  formOptions.items.bookTypeIdList.options = transformDataStructure(res)
}

/* 获取年份 */
async function getyearListApi() {
  formOptions.items.year.options = formOptions.data.years.map((item) => {
    return { label: String(item), value: Number(item) }
  })
}

/* 递归处理数据 */
function transformDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.label = newItem.bookTypeName
      newItem.key = newItem.bookTypeId
      newItem.children = newItem.list.length
        ? transformDataStructure(newItem.list)
        : null
      return newItem
    })
  } else if (typeof data === "object" && data !== null) {
    return Object.keys(data).reduce((obj, key) => {
      obj[key] = transformDataStructure(data[key])
      return obj
    }, {})
  }
  return data
}

/* 获取年级 */
async function getGradeList() {
  let res = await getGradeListApi({
    sysStageId: formOptions.data.sysStage,
  })
  formOptions.items.sysGrade.options = res.map((v) => {
    return {
      label: v.title,
      value: v.id,
    }
  })
}

/* 获取学期 */
async function getTermList() {
  const res = await getTermSelect()
  formOptions.items.sysTermId.options = res.map((v) => {
    return {
      label: v.sysTermName,
      value: v.sysTermId,
    }
  })
}

/* 切换学段 */
async function handleSysStage() {
  formOptions.items.sysGrade.options = []
  formOptions.items.bookTypeIdList.options = []
  formOptions.data.sysGrade = null
  formOptions.data.sysSubjectId = null
  formOptions.data.bookTypeIdList = []
  getArea()
  await getSubject()
  await getGradeList()
  await getTypeSelectApi()
}

let sysSubjectId = $ref<any>(null)
let sysStage = $ref<any>(null)
watch(
  () => props.show,
  async (val) => {
    sysStage = null
    formOptions.data.years = []
    formOptions.items.year.options = []
    chooseSchoolName = ""
    typeName = ""
    if (val) {
      getSchoolListApi()
      getTermList()
      sysStage = props.info?.sysStageId
      if (props.isEdit) {
        formOptions.data.schoolIdList =
          props.info.isAllSchool === 2
            ? []
            : props.info.schoolList?.map((v) => v.schoolId) || []
        formOptions.items.sysSubjectId.disabled = true
        formOptions.data.displayDate = props.info?.displayDate
        formOptions.data.month = props.info?.month || null
        //特殊处理地区回填
        formOptions.data.provinceIds = props.info.bookAreaList?.length
          ? props.info.bookAreaList.map((h) => {
              return h.cityId
                ? h.cityId + "city" + h.provinceId
                : h.provinceId + "province"
            })
          : []
      } else {
        formOptions.items.sysSubjectId.disabled = false
        formOptions.data.displayDate = null
        formOptions.data.provinceIds = []
        formOptions.data.schoolIdList = []
        formOptions.data.years = [
          String(new Date().getFullYear()),
          String(new Date().getFullYear() + 1),
        ]
        formOptions.data.month = new Date().getMonth() + 1
      }
      await getStage()
      getArea()
      nextTick(() => {
        formOptions.data.cover = props.info?.cover
          ? [{ url: props.info.cover, fullUrl: props.info.cover }]
          : []
        formOptions.data.bookNameAlias = props.info?.bookNameAlias
        formOptions.data.nickname = props.info?.nickname
        formOptions.data.sysGrade = props.info?.sysGradeId
        formOptions.data.sysTermId = props.info?.sysTermId || 1
        formOptions.data.year =
          props.info?.year || formOptions.data.years[0] || null
        formOptions.data.bookTypeIdList = findContainedBookTypeIds(
          formOptions.items.bookTypeIdList.options,
          props.info?.bookTypeList?.map((v) => v.bookTypeId),
        )
        formOptions.data.bookName = props.info?.bookName
        formOptions.data.list =
          props.info?.list?.map((v) => {
            return {
              ...v,
              name: v?.fileName,
              size: v?.fileSize,
              suffix: v?.fileExtension,
              fullUrl: v?.fileAbsoluteUrl,
              time_length: v?.fileDuration || null,
              id: v?.bookAttachId,
              status: "finished",
              thumbnailUrl: $g.tool.getFileTypeIcon(v.fileExtension),
            }
          }) || []
        //通过学期和试卷年份得到试卷学年
        if (props.info?.sysTermId == 2) {
          formOptions.data.years = [
            String(formOptions.data.year - 1),
            String(formOptions.data.year),
          ]
        } else if (props.info?.sysTermId == 1) {
          formOptions.data.years = [
            String(formOptions.data.year),
            String(Number(formOptions.data.year) + 1),
          ]
        }
        getyearListApi()
      })
    }
  },
)

async function getSchoolListApi() {
  const data = await getSchoolList()
  formOptions.items.schoolIdList.options =
    data?.map((v, i) => {
      const children =
        v.schoolList?.map((vv) => ({
          label: vv.schoolName,
          value: vv.schoolId,
          ...vv,
        })) || []
      allSchool.push(...children)
      return {
        label: v.companyName,
        value: v.companyId + " " + i,
        children,
        ...v,
      }
    }) || []
}

// 递归查找包含指定 bookTypeId 的所有 bookTypeId
function findContainedBookTypeIds(data, idsToCheck) {
  if (!idsToCheck) return []
  const foundIds = new Set()
  function checkIds(item) {
    // 如果当前对象的 bookTypeId 在 idsToCheck 中，加入 foundIds
    if (idsToCheck.includes(item.bookTypeId)) {
      foundIds.add(item.bookTypeId)
    }
    // 如果当前对象有嵌套的 list，则递归检查
    if (item.list && item.list.length > 0) {
      item.list.forEach((subItem) => checkIds(subItem))
    }
  }

  data.forEach((item) => checkIds(item))

  return Array.from(foundIds)
}
//处理地域参数
function handleAreaParams() {
  if (!formOptions.data.provinceIds.length) return {}
  let provinceIds = formOptions.data.provinceIds
    .filter((h) => h.includes("province"))
    .map((h) => h.replace("province", ""))
  let cityIdsArr = formOptions.data.provinceIds
    .filter((h) => h.includes("city"))
    .map((v) => v.split("city"))
  let cityIds: any = []
  cityIdsArr.forEach(([cityId, provinceId]) => {
    cityIds.push(cityId)
    provinceIds.push(provinceId)
  })
  provinceIds = [...new Set(provinceIds)]
  return {
    provinceIds,
    cityIds,
  }
}

/* 新增资源 */
async function add() {
  try {
    formOptions.data.list = formOptions.data.list.map((v) => {
      return {
        ...v,
        fileName: v.name,
        fileAbsoluteUrl: v.fullUrl,
        fileSize: v.size,
        fileExtension: v.suffix,
        fileDuration: v.time_length || null,
      }
    })
    const [sysTextbookVersionIds, sysTextbookIds] = getIdList(
      formOptions.data.versionAndTextbookIds,
    )
    const params = {
      ...formOptions.data,
      sysTextbookVersionIds,
      sysTextbookIds,
      cover: formOptions.data.cover?.[0]?.fullUrl,
      bookSource: props.info?.xkwPaperDataId ? 1 : 2,
      xkwPaperDataId: props.info?.xkwPaperDataId
        ? props.info.xkwPaperDataId
        : null,
      ...handleAreaParams(),
    }
    delete params.versionAndTextbookIds
    delete params.years
    await addBookApi(params)
    $g.msg("新增成功")
  } catch (err) {
    console.log(err)
  }
}

function getIdList(list) {
  const versionList: any = []
  const textbookList: any = []
  list.forEach((v) => {
    const params = v.split(" ")
    if (params[0] === "version") {
      versionList.push(Number(params[1]))
    } else {
      textbookList.push(Number(params[1]))
    }
  })
  return [versionList, textbookList]
}

/* 编辑资源 */
async function edit() {
  formOptions.data.list = formOptions.data.list.map((v) => {
    return {
      ...v,
      fileName: v.name,
      fileAbsoluteUrl: v.fullUrl,
      fileSize: v.size,
      fileExtension: v.suffix,
      fileDuration: v.time_length || null,
    }
  })
  const [sysTextbookVersionIds, sysTextbookIds] = getIdList(
    formOptions.data.versionAndTextbookIds,
  )
  const params = {
    ...formOptions.data,
    sysTextbookVersionIds,
    sysTextbookIds,
    cover: formOptions.data.cover?.[0]?.fullUrl,
    bookId: props.info.bookId,
    ...handleAreaParams(),
  }
  delete params.versionAndTextbookIds
  delete params.years
  await editBookApi(params)
  $g.msg("更新成功")
}

async function copy() {
  formOptions.data.list = formOptions.data.list.map((v) => {
    return {
      ...v,
      fileName: v.name,
      fileAbsoluteUrl: v.fullUrl,
      fileSize: v.size,
      fileExtension: v.suffix,
      fileDuration: v.time_length || null,
    }
  })
  const [sysTextbookVersionIds, sysTextbookIds] = getIdList(
    formOptions.data.versionAndTextbookIds,
  )
  const params = {
    ...formOptions.data,
    sysTextbookVersionIds,
    sysTextbookIds,
    cover: formOptions.data.cover?.[0]?.fullUrl,
    bookSource: props.info?.xkwPaperDataId ? 1 : 2,
    xkwPaperDataId: props.info?.xkwPaperDataId
      ? props.info.xkwPaperDataId
      : null,
    copyId: props.info.bookId,
    ...handleAreaParams(),
  }
  delete params.versionAndTextbookIds
  await copyPaperOrBook(params)
  $g.msg("复制成功")
}

async function confirm() {
  try {
    const res = await getByBookName({
      bookName: formOptions.data.bookName,
      bookId: props.info?.bookId || null,
    })
    if (res) {
      isRepeat = true
      formOptions?.ref.validate()
      $g.confirm({
        content: `操作失败：系统内已存在相同名称的${
          props.category == 1 ? "书籍" : "试卷"
        }，请勿重复录入！`,
        negativeText: "",
      })
      isRepeat = false
      formOptions.loading = false
    } else {
      props.isCopy ? await copy() : props.isEdit ? await edit() : await add()
      await emit("refresh")
      await emit("update:show", false)
    }
  } catch (err) {
    console.log(err)
    formOptions.loading = false
  }
}

//暂时不要全国了
// function handleChildren(h, curStage) {
//   if (
//     h.areaName === "全国" &&
//     ["PRIMARY", "JUNIOR_MIDDLE"].includes(curStage.sysStageCode)
//   ) {
//     return null
//   }
//   return h.children?.map((v) => {
//     return {
//       ...v,
//       label: v.areaName,
//       value: v.sysAreaId + "city" + h.sysAreaId, //区分省市,选了市要把父级放在省里,
//     }
//   })
// }

async function getArea() {
  let res = await getAreaList({
    level: 2,
  })
  formOptions.items.provinceIds.options = (res || []).map((h) => {
    return {
      ...h,
      label: h.shortName,
      value: h.sysAreaId + "province", //区分省市
      children: h.children?.map((v) => {
        return {
          ...v,
          label: v.areaName,
          value: v.sysAreaId + "city" + h.sysAreaId, //区分省市,选了市要把父级放在省里,
        }
      }),
    }
  })
}

watch(
  () => formOptions.data.sysSubjectId,
  (val) => {
    if (val) {
      formOptions.data.versionAndTextbookIds = []
      getVersionAndTextbookApi()
    }
  },
)

//学期改变修改试卷年份
watch(
  () => formOptions.data.sysTermId,
  (val) => {
    if (val && formOptions.data.years.length && props.show) {
      formOptions.data.year =
        formOptions.data.sysTermId == 1
          ? formOptions.data.years[0]
          : formOptions.data.years[1]
    }
  },
)

//新增时书籍/试卷名称自动更新
watch(
  () => autoName,
  (val) => {
    if (val && !props.isEdit) {
      formOptions.data.bookName = val
    }
  },
)

//试卷学年修改
function dateChange() {
  formOptions.data.years[1] = Number(formOptions.data.years[0]) + 1 + ""
  getyearListApi()
  formOptions.data.year = formOptions.data.years[0]
  formOptions.data.sysTermId = 1
}

//获取学校地区字符串
function setSchoolArea(data) {
  if (data.cityId) {
    const idStr = data.cityId + "city" + data.provinceId
    return idStr
  } else if (data.provinceId) {
    const idStr = data.provinceId + "province"
    return idStr
  }
  return ""
}

//获取arr1对arr2的补集
function getComplement(arr1, arr2) {
  const set2 = new Set(arr2)
  return arr1.filter((x) => !set2.has(x))
}

//学校变化
function schoolUpdate(value, options) {
  chooseSchoolName = options
    .map((item) =>
      [item.provinceName, item.cityName, item.districtName, item.schoolName]
        .filter((part) => Boolean(part)) // 过滤掉空值或假值
        .join(""),
    )
    .join("、")
  //删除/增加学校
  const isDelete = formOptions.data.schoolIdList.length > value.length
  let changeAreaId: any = []
  //如果学校减少，则删除这些学校地址，如果学校增加则加上这些学校的地址
  if (isDelete) {
    //存储当前选择的学校的地址
    const newSchoolAreas: any = []
    //已移除的学校的id
    const deleteSchoolIds = getComplement(formOptions.data.schoolIdList, value)
    //已移除的学校
    const deleteSchool = allSchool.filter((i) => {
      return deleteSchoolIds.includes(i.schoolId)
    })
    //有效的已移除学校的地址
    deleteSchool.forEach((item) => {
      if (item.provinceId) changeAreaId.push(setSchoolArea(item))
    })
    options.forEach((item) => {
      if (item.provinceId) newSchoolAreas.push(setSchoolArea(item))
    })
    //所属地区删除已移除学校的地址
    changeAreaId.forEach((item) => {
      const index = formOptions.data.provinceIds.findIndex((i) => i == item)
      const index2 = newSchoolAreas.findIndex((i) => i == item)
      // 如果当前选则的学校的地址包含已移除学校的地址则不删除
      if (index != -1 && index2 == -1)
        formOptions.data.provinceIds.splice(index, 1)
    })
  } else {
    //新增的学校
    const addSchool = options.filter((item) => {
      return !formOptions.data.schoolIdList.includes(item.schoolId)
    })
    //有效的学校地址
    addSchool.forEach((item) => {
      if (item.provinceId) changeAreaId.push(setSchoolArea(item))
    })
    //所属地区新增新添加的学校的地址
    formOptions.data.provinceIds.push(...changeAreaId)
  }
  formOptions.data.provinceIds = [...new Set(formOptions.data.provinceIds)]
  formOptions.data.schoolIdList = value
}

//类型变化
function handleUpdateValue(value, option) {
  typeName = ""
  option.forEach((item, index) => {
    typeName = `${typeName}${index == 0 ? "" : "、"}${item.bookTypeName}`
  })
}
</script>

<style lang="scss" scoped></style>
