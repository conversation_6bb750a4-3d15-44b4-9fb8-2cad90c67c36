<template>
  <div
    class="ques-collapse"
    :class="{
      'ques-expand': expand,
      'no-expand': !showExpand,
    }"
  >
    <div
      ref="contentRef"
      class="ques-content"
      :style="{
        maxHeight: maxHeight + 'px',
      }"
    >
      <slot></slot>
    </div>
    <div
      v-if="showExpand"
      class="w-full text-center h-20px text-14px text-[#1EA0F0] active:opacity-80 select-none cursor-pointer"
      :class="{
        'expand-icon': expand,
      }"
      @click="expand = !expand"
    >
      {{ expand ? "点击收起" : "点击展开" }}
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    maxHeight?: number
  }>(),
  {
    maxHeight: 70,
  },
)

const contentRef = $ref<any>()

let expand = $ref(false)
let showExpand = $ref(false)

function initData() {
  showExpand = contentRef?.scrollHeight > props.maxHeight
}

onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.ques-collapse {
  .ques-content {
    overflow: hidden;
    position: relative;
    &::after {
      display: none;
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 50px;
      background: linear-gradient(to top, #fff, #ffffff00);
    }
  }
  &.ques-expand {
    .ques-content {
      max-height: initial !important;
      overflow-x: auto;
      &::after {
        display: none;
      }
    }
  }
  &.no-expand {
    .ques-content {
      &::after {
        display: none !important;
      }
    }
  }
}
</style>
