<template>
  <div>
    <el-dialog
      :title="title"
      v-model="showDialog"
      :close-on-click-modal="false"
      width="1400"
      top="40px"
    >
      <el-scrollbar class="max-h-[76vh] overflow-auto">
        <g-form :formOptions="formOptions" v-loading="loading">
          <template #bookCatalogArticleFormatType>
            <el-radio-group
              v-model="formOptions.data.bookCatalogArticleFormatType"
              @change="handleChange"
            >
              <el-radio
                v-for="item in formOptions.items.bookCatalogArticleFormatType
                  .options"
                :key="item.value"
                :value="item.value"
                >{{ item.label }}</el-radio
              >
            </el-radio-group>
          </template>
          <template #title>
            <g-ueditor
              v-model="formOptions.data.title"
              :config="{ initialFrameHeight: 60, maximumWords: 200 }"
              @click.prevent
            ></g-ueditor>
          </template>
          <template #content>
            <div class="w-full">
              <div v-if="currentType == 1" class="h-[500px]">
                <g-editor
                  v-model="formOptions.data.content"
                  :config="{ initialFrameHeight: 460 }"
                  :initProps="{
                    max: 99999,
                  }"
                ></g-editor>
              </div>
              <g-markdown
                class="w-full"
                height="500px"
                v-if="currentType == 2"
                v-model="formOptions.data.content"
              ></g-markdown>
              <div v-if="currentType == 3">
                <el-button
                  type="primary"
                  class="mb-20px"
                  @click="showMarkdownDialog = true"
                  >Markdown转换思维导图</el-button
                >
                <MindMap
                  v-if="showMindMap"
                  :class="{ border: currentType == 3 }"
                  ref="mindMapRef"
                  v-model:isFIB="isFIB"
                  :height="500"
                  :remoteMapData="remoteMapData"
                />
              </div>
            </div>
          </template>
        </g-form>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="confirm"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Markdown转思维导图对话框 -->
    <el-dialog
      title="Markdown转思维导图"
      v-model="showMarkdownDialog"
      width="1050px"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
      @closed="handleClosed"
      :z-index="3001"
    >
      <el-form>
        <el-form-item label="Markdown文本：" required>
          <g-markdown
            class="w-full"
            height="400px"
            v-model="markdownText"
          ></g-markdown>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showMarkdownDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="convertMarkdownToMindMap"
            :disabled="!markdownText"
            :loading="btnLoading"
          >
            确认转换
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { addArticle, editArticle, exchangeArticle } from "@/api/bookMgt"
import MindMap from "@/views/resourceMgt/components/MindMap/index.vue"
import type { PropType } from "vue"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
  activeArticle: {
    type: Object as any,
    required: true,
  },
  bookId: {
    type: [Number, String, null] as PropType<number | string | null | any>,
  },
  bookCatalogId: {
    type: [Number, String, null] as PropType<number | string | null | any>,
  },
})
let isFIB = $ref(false)
let mindMapRef = $ref<any>(null)
let loading = $ref(false)
let btnLoading = $ref(false)
const title = $computed(() => {
  return props.isEdit ? "编辑文章" : "新增文章"
})
const emit = defineEmits(["update:show", "refresh", "refreshTree"])
const showDialog = useVModel(props, "show", emit)
let currentType = $ref(1)
let isRule = ref(true)
const formOptions = $ref<any>({
  ref: null as any,
  loading: false,
  items: {
    bookCatalogArticleFormatType: {
      type: "radio",
      label: "类型",
      options: [
        { label: "富文本", value: 1 },
        { label: "Markdown", value: 2 },
        { label: "思维导图", value: 3 },
      ],

      slot: true,
    },
    title: {
      type: "textarea",
      label: "标题",
      placeholder: "请输入标题，最多输入200字",
      maxlength: 200,
      showCount: true,
      slot: true,
    },
    content: {
      type: "text",
      label: "内容",
      slot: true,
      rule: computed(() => isRule.value),
    },
  },
  data: {
    bookCatalogArticleFormatType: 1,
    content: "",
    title: "",
  },
})
let remoteMapData = $ref<any>(null)
const route = useRoute()
watch(
  () => formOptions.data.bookCatalogArticleFormatType,
  (val) => {
    if (val == 3) {
      isRule.value = false
    } else {
      isRule.value = true
    }
  },
)
/* 切换编辑器 */
function handleChange(e) {
  if ($g.tool.isTrue(formOptions.data.content) || currentType == 3) {
    formOptions.data.bookCatalogArticleFormatType = currentType
    $g.confirm({
      content: "切换编辑器会导致内容丢失，是否确认切换？",
    })
      .then(() => {
        formOptions.data.content = ""
        nextTick(() => {
          formOptions.data.bookCatalogArticleFormatType = e
          currentType = e
        })
      })
      .catch(() => {})
  } else {
    formOptions.data.bookCatalogArticleFormatType = e
    currentType = e
  }
}

let showMarkdownDialog = $ref(false)
let markdownText: any = $ref("")
let showMindMap = $ref(true)
watch(
  () => props.show,
  (val) => {
    if (val) {
      if (props.isEdit) {
        loading = true
      }
      showMindMap = true
      setTimeout(() => {
        formOptions.data = $g._.cloneDeep(props.activeArticle)
        currentType = formOptions.data.bookCatalogArticleFormatType
        remoteMapData =
          currentType == 3 ? JSON.parse(props.activeArticle.content) : null
        loading = false
      }, 500)
    } else {
      cache = []
      currentType = 1
      formOptions.data = {
        bookCatalogArticleFormatType: 1,
        content: "",
        title: "",
      }
    }
  },
)
let cache = $ref<any>([])
function replacer(key, value) {
  if (typeof value === "object" && value !== null) {
    if (cache.includes(value)) return
    cache.push(value)
  }
  return value
}
/* 新增 */
async function add() {
  try {
    let data = "" as any
    if (currentType == 3) {
      loading = true
      data = await mindMapRef.getMindMapData()
      delete data.view
    }
    await addArticle({
      bookId: route.query.bookId,
      bookCatalogId: props.chapterId,
      bookCatalogArticleFormatType:
        formOptions.data.bookCatalogArticleFormatType,
      title: formOptions.data.title,
      content:
        currentType == 3
          ? JSON.stringify(data, replacer)
          : formOptions.data.content,
    })
    $g.msg("新增成功")
    loading = false
  } catch (err) {
    console.log(err)
    formOptions.loading = false
  }
}
/* 编辑 */
async function edit() {
  try {
    let data = "" as any
    if (currentType == 3) {
      loading = true
      data = await mindMapRef.getMindMapData()
      delete data.view
    }
    await editArticle({
      bookCatalogArticleId: props.activeArticle.bookCatalogArticleId,
      bookCatalogArticleFormatType:
        formOptions.data.bookCatalogArticleFormatType,
      title: formOptions.data.title,
      content:
        currentType == 3
          ? JSON.stringify(data, replacer)
          : formOptions.data.content,
    })
    loading = false
    $g.msg("编辑成功")
  } catch (err) {
    console.log(err)
    formOptions.loading = false
  }
}

/* Markdown转思维导图 */
async function convertMarkdownToMindMap() {
  if (!markdownText) {
    $g.msg("请输入Markdown文本", "warning")
    return
  }
  btnLoading = true
  showMindMap = false
  // 这里处理Markdown转思维导图的逻辑
  loading = true
  exchangeArticle({
    content: markdownText,
    bookId: props.bookId,
    bookCatalogId: props.bookCatalogId,
  })
    .then(async (res) => {
      remoteMapData = JSON.parse(res) || null
      await nextTick()
      showMindMap = true
      setTimeout(() => {
        nextTick(async () => {
          loading = false
          showMarkdownDialog = false
          btnLoading = false
        })
      }, 500)
    })
    .catch((err) => {
      showMarkdownDialog = false
      btnLoading = false
      showMindMap = true
    })
}

function handleClosed() {
  markdownText = ""
}

/* 确认 */
async function confirm() {
  formOptions?.ref.validate(async (errors) => {
    let uploadFlag = formOptions?.ref?.checkFileUpload()
    if (!errors && uploadFlag) {
      formOptions!.loading =
        formOptions?.loading === undefined ? undefined : true
      try {
        props.isEdit ? await edit() : await add()
        await emit("update:show", false)
        await emit("refresh")
        if (!props.isEdit) emit("refreshTree")
      } catch (err) {
        console.log(err)
      } finally {
        formOptions.loading = false
      }
    } else {
      $g.msg("表单验证失败或文件上传未完成", "error")
    }
  })
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-overlay-dialog {
    overflow: hidden;
  }
}
</style>
