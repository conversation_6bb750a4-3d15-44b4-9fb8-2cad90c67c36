<template>
  <div>
    <g-dialog
      :title="title"
      :formOptions="formOptions"
      v-model:show="showDialog"
      @confirm="confirm"
    >
      <g-form :formOptions="formOptions">
        <template #file>
          <g-upload
            v-model:fileList="formOptions.data.file"
            type="drag"
            :accept="acceptType"
            multiple
          ></g-upload>
        </template>
      </g-form>
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import { uploadQuesFile, deleteQuesFile } from "@/api/resourceMgt"
import type { PropType } from "vue"
import PreviewDialog from "./PreviewDialog.vue"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "files",
  },
  questionId: {
    type: [Number, String] as PropType<any>,
    required: true,
  },
  files: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
})

const acceptType = $computed(() => {
  if (props.type == "files") {
    return ".ppt,.pptx,.png,.jpg,.jpeg,.doc,.docx,.xls,.xlsx,.pdf,.txt"
  } else {
    return ".mp3,.aac,.ogg,.wav,.mp4,.webm,.flv,.mov"
  }
})
const title = $computed(() => {
  return props.type == "files" ? "上传文档" : "上传试题讲解"
})
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
let formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  items: {
    file: {
      type: "upload",
      label: "文件上传",
      showLabel: false,
      slot: true,
      rule: true,
    },
  },
  data: {
    file: [],
  },
})

async function confirm() {
  try {
    await uploadQuesFile({
      questionId: props.questionId,
      list: formOptions.data.file.map((val) => {
        return {
          ...val,
          fileName: val.name,
          fileAbsoluteUrl: val.resource_url,
          fileSize: val.size,
          fileDuration: val.time_length || 0,
        }
      }),
    })
    $g.msg("上传成功")
    await emit("refresh")
    await emit("update:show", false)
  } catch (err) {
    console.log(err)
  } finally {
    formOptions.loading = false
  }
}
</script>

<style lang="scss" scoped></style>
