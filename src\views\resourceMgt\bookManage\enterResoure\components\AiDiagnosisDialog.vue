<template>
  <g-dialog
    title="AI诊断"
    v-model:show="showDialog"
    :show-footer="false"
    width="1600"
  >
    <div v-loading="docLoading" class="h-[75vh]">
      <iframe
        :key="key"
        :src="url"
        style="width: 100%; height: 100%"
        frameborder="0"
        allow="*"
        @load="docLoading = false"
      />
    </div>
  </g-dialog>
</template>

<script setup lang="ts">
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  url: {
    type: String,
    default: "",
  },
})
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
let docLoading = $ref(true)
let key = $ref("")

watch(
  () => props.show,
  (val) => {
    if (val) {
      docLoading = true
      key = $g.tool.uuid(4)
    } else {
      emit("refresh")
    }
  },
)
</script>

<style lang="scss" scoped></style>
