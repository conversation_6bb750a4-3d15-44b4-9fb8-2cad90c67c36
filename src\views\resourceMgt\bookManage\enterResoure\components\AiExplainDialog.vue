<template>
  <div>
    <g-dialog
      :formOptions="formOptions"
      v-model:show="showDialog"
      @confirm="confirm"
      width="1300"
      :show-footer="activeTab == 'AiExplain'"
    >
      <template #header>
        <div>
          {{ title }}
          <n-button
            text
            type="primary"
            class="ml-20px cursor-pointer"
            @click="goAiDiagnosis"
            v-if="activeTab == 'AiExplain'"
          >
            AI诊断
          </n-button>
        </div>
      </template>
      <el-tabs
        v-model="activeTab"
        class="demo-tabs"
        @tab-change="handleTabChange"
      >
        <el-tab-pane
          v-for="item in tabList"
          :key="item.value"
          :label="item.label"
          :name="item.value"
        ></el-tab-pane>
      </el-tabs>
      <div v-if="activeTab == 'AiExplain'">
        <g-form :formOptions="formOptions">
          <template #text>
            <g-markdown
              class="w-full"
              height="450px"
              v-model="formOptions.data.text"
              :mode="disabled ? 'preview' : 'edit'"
              @change="markdownChange"
            ></g-markdown>
          </template>
        </g-form>
        <n-button
          type="primary"
          :loading="getPlaybackStatus(5) == 'loading'"
          @click="playVideo"
          >{{ getPlaybackStatus(5) == "start" ? "暂停" : "播放" }}</n-button
        >
      </div>
      <div v-if="activeTab == 'Solution'" class="h-[474px]">
        <Thinking :data="thinkingData"></Thinking>
      </div>
      <div v-if="activeTab == 'DetailAnalysis'" class="h-[474px]">
        <AnalysisDetail :data="analysisDetail"></AnalysisDetail>
      </div>
      <DrawingBoard
        :boardData="boardData"
        @refresh="fetchDrawingBoard"
        v-if="activeTab == 'DrawingBoard'"
      />
      <AnimationPreview
        :boardData="boardData"
        v-if="activeTab == 'AnimationPreview'"
      />
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  getAiExplainContent,
  updateAiExplain,
  getAiAnalysis,
  getAiAnalysisDetail,
  getAiAnalysisBoard,
  saveAiAnalysisBoard,
} from "@/api/bookMgt"
import DrawingBoard from "./DrawingBoard.vue"
import Thinking from "./Thinking.vue"
import AnalysisDetail from "./AnalysisDetail.vue"
import AnimationPreview from "./AnimationPreview.vue"
import { useSpeakerStore } from "@/stores/modules/speaker"
let speakerStore = useSpeakerStore()
let { getIdByType, isPlay } = $(storeToRefs(useSpeakerStore()))
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "edit",
  },
  subQuestionParseId: {
    type: [Number, String],
  },
})
const tabList = $ref<any>([
  {
    label: "AI讲解步骤",
    value: "AiExplain",
  },
  {
    label: "解题思路",
    value: "Solution",
  },
  {
    label: "详细解析",
    value: "DetailAnalysis",
  },
  {
    label: "动态画板",
    value: "DrawingBoard",
  },
  {
    label: "动画演示",
    value: "AnimationPreview",
  },
])
let thinkingData = $ref<any>({}) //解题思路
let analysisDetail = $ref({}) //解析详情
let activeTab = $ref("AiExplain")
const emit = defineEmits(["update:show", "refresh", "goAiDiagnosis"])
const showDialog = useVModel(props, "show", emit)
let historyData = $ref("")
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  items: {
    text: {
      type: "",
      label: "",
      showLabel: false,
      slot: true,
    },
  },
  data: {
    text: "",
  },
})
let disabled = $ref(false)
let boardData = $ref<any>({})
/* 播放音频 */
function playVideo() {
  let data = {
    subQuestionParseId: props.subQuestionParseId,
  }
  speakerStore.getAudioList(5, data)
}
/* 播放状态 */
function getPlaybackStatus(type) {
  let data = props.subQuestionParseId + "S"
  if (data == getIdByType) {
    return isPlay
  } else {
    return "pause"
  }
}
/* 标题 */
const title = $computed(() => {
  return tabList.find((v) => v.value == activeTab).label
})
/* tab切换 */
function handleTabChange(val) {
  if (val != "AiExplain") speakerStore.reset()
  switch (val) {
    case "Solution":
      if (!$g.tool.isTrue(thinkingData)) fetchAiAnalysis()
      break
    case "DetailAnalysis":
      if (!$g.tool.isTrue(analysisDetail)) fetchAiAnalysisDetail()
      break
  }
}
/* 获取画板 */
async function fetchDrawingBoard() {
  let res = await getAiAnalysisBoard({
    subQuestionParseId: props.subQuestionParseId,
  })
  if (!res) saveAiAnalysisBoardApi()
  else boardData = res
}
/* 创建画板 */
async function saveAiAnalysisBoardApi() {
  let res = await saveAiAnalysisBoard({
    subQuestionParseId: props.subQuestionParseId,
  })
  boardData = res
}
/* 获取解题思路 */
async function fetchAiAnalysis() {
  try {
    let res = await getAiAnalysis({
      subQuestionParseId: props.subQuestionParseId,
    })
    thinkingData = res[0] ?? {}
  } catch (err) {
    console.log("获取解题思路失败", err)
  }
}
/* 获取详细解析 */
async function fetchAiAnalysisDetail() {
  try {
    let res = await getAiAnalysisDetail({
      subQuestionParseId: props.subQuestionParseId,
    })
    analysisDetail = res ?? {}
  } catch (err) {
    console.log("获取详细解析失败", err)
  }
}
/* 获取详情 */
async function getQuestionAiExplain() {
  let res = await getAiExplainContent({
    subQuestionParseId: props.subQuestionParseId,
  })
  formOptions.data.text = res.aiExplain || ""

  historyData = res.aiExplain
}
onMounted(() => {
  thinkingData = {}
  analysisDetail = {}
  console.log("加载了AI讲解步骤")
  getQuestionAiExplain()
  fetchDrawingBoard()
  activeTab = "AiExplain"
})
onBeforeUnmount(() => {
  speakerStore.reset()
})
// watch(
//   () => props.show,
//   (val) => {
//     if (val) {
//       getQuestionAiExplain()
//     }
//   },
// )

/* 保存 */
async function save() {
  try {
    formOptions.loading = true
    await updateAiExplain({
      aiExplain: formOptions.data.text,
      subQuestionParseId: props.subQuestionParseId,
    })
    $g.msg("保存成功", "success")
  } catch (err) {
    console.log(err)
  } finally {
    formOptions.loading = false
  }
}

async function confirm() {
  historyData != formOptions.data.text && (await save())
  emit("update:show", false)
  emit("refresh")
  historyData = ""
}

let clipboardContent = $ref<any>("")

function getContents() {
  return window.getSelection()?.toString()
}

async function getClipboardContent() {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      const text = await navigator.clipboard.readText()
      clipboardContent = text
    } else {
      clipboardContent = getContents()
    }
  } catch (err) {
    console.error("读取剪贴板内容失败：", err)
  }
}

function goAiDiagnosis() {
  getClipboardContent()
  if (clipboardContent) {
    // emit("update:show", false)
    emit("goAiDiagnosis", clipboardContent)
  } else {
    $g.msg("没有选中内容，请重新选中！", "warning")
    return
  }
}

function addPlayButtons() {
  const h3Elements = document.querySelectorAll(".github-markdown-body h3")
  h3Elements.forEach((h3, index) => {
    if (!h3.querySelector(".play-button")) {
      const playButton = document.createElement("span")
      playButton.className = "play-button"
      playButton.setAttribute("data-playing", "false")
      playButton.setAttribute("data-index", String(index))

      playButton.addEventListener("click", (e) => {
        e.stopPropagation()
        const isPlaying = playButton.getAttribute("data-playing") === "true"
        const buttonIndex = parseInt(
          playButton.getAttribute("data-index") || "0",
        )

        // 如果当前不是播放状态，先停止其他所有按钮
        if (!isPlaying) {
          const allPlayButtons = document.querySelectorAll(
            ".github-markdown-body h3 .play-button",
          )
          allPlayButtons.forEach((btn) => {
            btn.setAttribute("data-playing", "false")
          })
        }

        // 设置当前按钮状态
        playButton.setAttribute("data-playing", (!isPlaying).toString())

        if (!isPlaying) {
          console.log(
            "开始播放第",
            buttonIndex + 1,
            "个按钮, 标题:",
            h3.textContent,
          )
        } else {
          console.log(
            "暂停播放第",
            buttonIndex + 1,
            "个按钮, 标题:",
            h3.textContent,
          )
        }
      })

      h3.appendChild(playButton)
    }
  })
}

function markdownChange(text, html) {
  if (html) {
    // addPlayButtons()
  }
}
</script>

<style lang="scss" scoped>
:deep() {
  .github-markdown-body h3 {
    position: relative;
    display: flex;
    align-items: center;
  }

  .github-markdown-body h3 .play-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 8px;
    width: 24px;
    height: 24px;
    min-width: 24px;
    vertical-align: middle;
    user-select: none;
    transition: all 0.2s ease;
    position: relative;
    line-height: 1;
    padding: 0;
  }

  .github-markdown-body h3 .play-button:hover {
    transform: scale(1.1);
  }

  .github-markdown-body h3 .play-button[data-playing="true"]::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("https://qm-cloud.oss-cn-chengdu.aliyuncs.com/resource_test/question/image/202411/20241127/fdaffae1dc3922767003b790c5bde31b.png")
      no-repeat center center;
    background-size: cover;
  }

  .github-markdown-body h3 .play-button[data-playing="false"]::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("https://qm-cloud.oss-cn-chengdu.aliyuncs.com/resource_test/question/image/202411/20241127/905bc39a4e81e3a7d589f5c8dfb45df0.png")
      no-repeat center center;
    background-size: cover;
  }
}
</style>
