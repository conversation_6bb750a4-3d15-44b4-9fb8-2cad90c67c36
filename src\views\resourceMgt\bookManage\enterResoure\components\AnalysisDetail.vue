<template>
  <div class="pb-10px h-full overflow-auto">
    <template v-if="$g.tool.isTrue(data)">
      <g-markdown
        class="w-full"
        v-model="data.outline"
        mode="preview"
      ></g-markdown>
      <div class="text-20px font-600">详细解析</div>
      <div v-for="(item, index) in data.detailedExplanation" :key="index">
        <div class="flex">
          <span class="flex-shrink-0 mt-6px">{{ item.stepNumber }}.</span>
          <g-markdown
            class="w-full"
            v-model="item.stepDescription"
            mode="preview"
          ></g-markdown>
        </div>
        <g-markdown
          class="w-full"
          v-model="item.stepContent"
          mode="preview"
        ></g-markdown>
      </div>
      <div class="text-20px font-600">知识点讲解</div>
      <div v-for="(item, index) in data.knowledgeExplanation" :key="index">
        <g-markdown
          class="w-full"
          v-model="item.knowledgeName"
          mode="preview"
        ></g-markdown>
        <g-markdown
          class="w-full"
          v-model="item.knowledgeContent"
          mode="preview"
        ></g-markdown>
      </div>
      <div class="text-20px font-600">总结</div>
      <g-markdown
        class="w-full"
        v-model="data.summary"
        mode="preview"
      ></g-markdown>
    </template>
    <g-empty v-else></g-empty>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
const props = defineProps({
  data: {
    type: Object as PropType<any>,
    required: true,
  },
})
</script>

<style lang="scss" scoped></style>
