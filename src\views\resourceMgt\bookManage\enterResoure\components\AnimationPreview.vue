<template>
  <div
    :class="isFull ? '!h-full' : 'h-[600px]'"
    class="relative"
    ref="containerRef"
    v-loading="loading"
  >
    <g-empty v-if="!boardData.fileUrl" description="暂无Gegeobra文件"></g-empty>
    <template v-else>
      <!-- GGB容器 -->
      <div ref="ggbContainer" class="!w-auto !h-auto bg-white"></div>
      <!-- 全屏 -->
      <n-button
        type="primary"
        :disabled="loading"
        text
        class="absolute right-20px top-[-50px] cursor-pointer"
        @click="openScreen"
      >
        <g-icon name="ri-fullscreen-line" size="" color="" />
        全屏
      </n-button>
      <!-- 当前步骤 -->
      <div
        v-if="!loading"
        class="absolute top-10px left-10px z-[9999] transition-all duration-100 rounded-[18px] overflow-hidden px-16px py-10px text-white"
        style="background: rgba(51, 51, 51, 0.85)"
      >
        一共 {{ maxStepNum }} 步,当前是第{{ stepNum }}步
      </div>
      <!-- 步骤切换 -->
      <div
        v-if="!loading"
        class="absolute bottom-10px w-[80%] z-[9999] min-h-[58px] transition-all duration-100 left-[50%] translate-x-[-50%] flex items-center gap-x-[10px]"
      >
        <n-button
          type="primary"
          text
          :disabled="stepNum == 0"
          @click="setStep('minus')"
        >
          <img
            width="32"
            height="32"
            :src="$g.tool.getFileUrl('previous.png')"
          />
        </n-button>
        <div
          class="rounded-[8px] flex-1 overflow-hidden px-15px min-h-[50px] h-full mx-10px text-14px flex items-center"
          style="background: rgba(240, 240, 240, 0.8)"
        >
          <div class="loader" v-if="isPlay == 'loading'"></div>
          <g-icon
            v-else
            :name="
              isPlay == 'start' ? 'ri-pause-circle-line' : 'ri-play-circle-line'
            "
            size=""
            color=""
            @click="playVideo"
          />

          <g-markdown
            v-if="subtitle"
            class="w-full"
            v-model="subtitle"
            mode="stream"
          ></g-markdown>
          <div v-else class="text-[14px]">
            {{ isPlay == "loading" ? "语音加载中..." : "暂无文本" }}
          </div>
        </div>
        <n-button
          type="primary"
          text
          :disabled="stepNum == maxStepNum"
          @click="setStep('add')"
        >
          <img width="32" height="32" :src="$g.tool.getFileUrl('next.png')" />
        </n-button>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { getAiAnalysis } from "@/api/bookMgt"
import { useSpeakerStore } from "@/stores/modules/speaker"
const props = defineProps({
  boardData: {
    type: Object,
    required: true,
  },
})
let speakerStore = useSpeakerStore()
let { isPlay, subtitle } = $(storeToRefs(useSpeakerStore()))
let isFull = $ref(false) //全屏
let containerRef = $ref<any>(null) //容器
let initWidth = $ref(0) //容器宽度
let initHeight = $ref(0) //容器高度
let ggbApp = $ref<any>(null) //GeoGebra
let ggbContainer = ref<any>(null) //GeoGebra容器
let stepNum = ref(0) //步骤
let loading = $ref(true)
let stepData = $ref<any>([])
let maxStepNum = $ref(0)
let changeShowText = inject("changeShowText") as Function
let isPlaying = $ref(false)

watch(
  () => stepNum.value,
  (val) => {
    if (val) {
      playVideo("auto")
    } else {
      speakerStore.reset()
    }
  },
)

/* 播放音频 */
function playVideo(type = "handle") {
  if (stepNum.value == 0) return
  let ids = stepData[0].steps
    .filter((v) => v.bindStep == stepNum.value)
    .map((v) => v.parseFastGptSolutionStepId)
  if (!ids.length) {
    $g.msg("当前步骤暂未关联解题步骤", "warning")
    speakerStore.reset()
    return
  }
  // if (type != "auto") {
  //   isPlaying = !isPlaying
  // }
  speakerStore.getMultipleAudioList(ids, 6)
}
/* 步骤 */
function setStep(type) {
  if (!maxStepNum) return $g.msg("该GGB文件暂未绑定步骤", "warning")
  if (type == "add") {
    stepNum.value = stepNum.value + 1
  } else {
    stepNum.value = stepNum.value - 1
  }
  handleChange(stepNum.value)
}
/* 获取步骤 */
async function getAiAnalysisApi() {
  try {
    let res = await getAiAnalysis({
      subQuestionParseId: props.boardData.subQuestionParseId,
    })
    stepData = res
    res[0].steps.forEach((v) => {
      if (v.bindStep > maxStepNum) {
        maxStepNum = v.bindStep
      }
    })
  } catch (err) {
    console.log(err)
  }
}
/* 处理步进器值变化 */
function handleChange(newValue) {
  if (ggbApp && typeof ggbApp.evalCommand === "function") {
    // 使用evalCommand更新步进器的值
    ggbApp.evalCommand(`SetValue[SetIndex, ${newValue}]`)
  } else {
    console.error("GeoGebra App尚未加载或evalCommand不可用")
  }
}
onBeforeMount(async () => {
  changeShowText(false)
  speakerStore.reset()
  await getAiAnalysisApi()
  if (props.boardData.fileUrl) {
    /* 懒加载 GeoGebra */
    $g.tool
      .loadJS("https://frontend-cdn.qimingdaren.com/cdn/deployggb.js")
      .then(async () => {
        initWidth = containerRef?.clientWidth
        initHeight = containerRef?.clientHeight
        init()
      })
  } else {
    loading = false
  }
})
/* 初始化GGB */
function init() {
  ggbApp = null
  const width = containerRef.clientWidth
  const height = containerRef.clientHeight
  const targetAspectRatio = width / height
  // GeoGebra的初始化参数
  const params = {
    // appName: "geometry", // 你可以选择其他应用类型，如 "geometry", "3d", "cas" 等
    width: width,
    height: height,
    showMenuBar: false,
    showAlgebraInput: false,
    showAlgebraView: false,
    algebraInputPosition: false,
    showToolBar: false, // 隐藏工具栏
    showToolBarHelp: false,
    showResetIcon: false, // 隐藏重置图标
    enableLabelDrags: false,
    enableShiftDragZoom: true,
    enableRightClick: false,
    errorDialogsActive: false,
    useBrowserForJS: false,
    allowStyleBar: false,
    preventFocus: false,
    showZoomButtons: false, // 隐藏缩放按钮
    scale: 1,
    appName: "classic",
    perspective: "G",
    language: "zh",
    borderColor: "#FFFFFF",
    buttonRounding: 0.7,
    buttonShadows: false,
    appletOnLoad: (api) => {
      ggbApp = api
      // // 根据内容和容器计算合适的视图范围

      // 禁用自动缩放但允许手动交互
      ggbApp?.setFixed(false, false)

      // // 创建一个步进器 (Slider)，范围从 1 到 1000，初始值为 num
      ggbApp?.evalCommand(`SetIndex = Slider(0, ${maxStepNum})`)
      ggbApp?.evalCommand(`SetValue[SetIndex, ${stepNum.value}]`)
      // 监听GeoGebra中的值变化
      ggbApp.registerUpdateListener(syncSetIndexToNum)
      loading = false
    },
    fileName: props.boardData.fileUrl,
  }
  // 初始化GeoGebra

  const applet = new GGBApplet(params, true)
  // 将GeoGebra注入到指定的容器中
  applet.inject(ggbContainer.value)
  // 启动时同步 GeoGebra 的 SetIndex 和 num
  syncSetIndexToNum()
}
/* 同步 SetIndex 的值到 Vue 的 num */
function syncSetIndexToNum() {
  const setIndexValue = getSetIndex()
  if (setIndexValue && stepNum !== setIndexValue) {
    stepNum.value = Number(setIndexValue)
  } else {
    stepNum.value = 0
  }
}

/* 从 GeoGebra 获取 SetIndex 的值 */
function getSetIndex() {
  try {
    if (ggbApp && typeof ggbApp.getValue === "function") {
      const setIndexValue = ggbApp.getValue("SetIndex")
      return setIndexValue
    }
  } catch (err) {
    console.error("GeoGebra App尚未加载或getValue不可用", err)
  }
}
function resetSize() {
  if (!document.fullscreenElement) {
    ggbApp.setSize(initWidth, initHeight)
    isFull = false
  } else {
    setTimeout(() => {
      ggbApp.setSize(containerRef?.clientWidth, containerRef?.clientHeight)
      isFull = true
    }, 100)
  }
}
/* 全屏 */
function openScreen() {
  if (!isFull) {
    containerRef.requestFullscreen()
  }
}

onMounted(() => {
  // 监听全屏变化事件
  document.addEventListener("fullscreenchange", resetSize)
})
onBeforeUnmount(() => {
  changeShowText(true)
  speakerStore.reset()
  document.removeEventListener("fullscreenchange", resetSize)
})
</script>

<style lang="scss" scoped>
:deep() {
  .applet_scaler {
    transform: none !important;
  }
}
/* From Uiverse.io by Fernando-sv */
.loader {
  border: 4px solid rgba(255, 255, 255, 1);
  border-left-color: transparent;
  border-radius: 50%;
}

.loader {
  border: 4px solid rgba(255, 255, 255, 1);
  border-left-color: transparent;
  width: 20px;
  height: 20px;
}

.loader {
  border: 4px solid rgba(255, 255, 255, 1);
  border-left-color: transparent;
  width: 20px;
  height: 20px;
  animation: spin89345 1s linear infinite;
}

@keyframes spin89345 {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
