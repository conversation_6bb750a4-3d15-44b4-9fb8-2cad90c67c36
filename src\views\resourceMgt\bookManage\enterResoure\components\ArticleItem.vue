<template>
  <div>
    <div class="flex items-center justify-between px-20px">
      <!-- 头部slot -->
      <div class="flex items-center">
        <div class="flex items-center gap-x-[10px]">
          <div>序号：{{ index }}</div>
          <div>格式：{{ formatMap[article.bookCatalogArticleFormatType] }}</div>
          <div class="flex items-center">
            标题：<g-tooltip
              class="max-w-[300px]"
              :refName="article.bookCatalogArticleId + 'refName'"
              :content="article.title || '无'"
            >
            </g-tooltip>
          </div>
        </div>
        <slot name="left-header"></slot>
      </div>
      <div>
        <slot name="right-header"></slot>
      </div>
    </div>
    <div
      class="border-[2px] border-[#75C6FE] mb-16px rounded-[24px] p-10px overflow-hidden"
      style="border: 2px solid #75c6fe"
      ref="containerRef"
      v-loading="loading"
    >
      <div
        v-if="showBtn"
        class="flex items-center justify-end text-[#4EAFF7] cursor-pointer"
        @click="load"
      >
        <g-icon
          :name="
            expand ? 'ri-arrow-up-double-line' : 'ri-arrow-down-double-line'
          "
          size="14"
          class="mr-4px"
        ></g-icon>
        {{ expand ? "收起" : "展开" }}
      </div>
      <!-- 富文本/markdown -->
      <div
        v-if="!showMindMap"
        :class="['auto-height-box', expand ? 'expand' : '']"
      >
        <div ref="mainBoxRef" class="overflow-hidden relative min-h-100px">
          <template v-if="article.bookCatalogArticleFormatType == 1">
            <g-mathjax :text="text" />
          </template>
          <template v-else>
            <g-markdown :text="text" mode="preview"></g-markdown>
          </template>
        </div>
      </div>
      <!-- 思维导图 -->
      <el-scrollbar v-else max-height="400px">
        <div class="w-full" :style="{ backgroundColor: color }">
          <el-image
            v-if="showImage"
            :src="imgUrl"
            alt=""
            style=""
            class="max-w-[100%] h-auto"
            fit="cover"
            :preview-src-list="[imgUrl]"
          />
          <MindMap
            v-else
            :mode="mode"
            :height="400"
            v-model:isFIB="isFIB"
            :remoteMapData="mindMap"
          />
        </div>
      </el-scrollbar>
      <div
        v-if="showBtn"
        class="flex items-center justify-center text-[#4EAFF7] cursor-pointer"
        @click="load"
      >
        <g-icon
          :name="
            expand ? 'ri-arrow-up-double-line' : 'ri-arrow-down-double-line'
          "
          size="14"
          class="mr-4px"
        ></g-icon>
        {{ expand ? "收起" : "展开" }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { backgroundMap } from "@/views/resourceMgt/components/MindMap/config/other"
import MindMap from "@/views/resourceMgt/components/MindMap/index.vue"
const props = defineProps({
  article: {
    type: Object,
    required: true,
  },
  maxLength: {
    type: Number,
    default: 1500,
  },
  index: {
    type: Number,
    required: true,
  },
})
let formatMap = {
  1: "富文本",
  2: "Markdown",
  3: "思维导图",
}
let isFIB = $ref(false)
let mode = $ref(2)
let showImage = $ref(false)
let expand = $ref(false)
let text = $ref("") //文章数据
let containerRef = $ref<any>(null)
let mainBoxRef = $ref<any>(null)
let showBtn = $ref(false)
let loading = $ref(false)
let MindMapRef = $ref<any>(null)
let imgUrl = $ref<any>([])
let mindMap = $ref<any>(null)
const showMindMap = computed(() => {
  return props.article.bookCatalogArticleFormatType == 3
})
const color = $computed(() => {
  if (mindMap) {
    return backgroundMap[mindMap.theme.template]
  } else {
    return "#fff"
  }
})
watch(
  () => props.article,
  (val) => {
    if (val) {
      if (val.bookCatalogArticleFormatType != 3) {
        text =
          props.article?.content?.length > props.maxLength
            ? props.article.content.substring(0, props.maxLength)
            : props.article.content
        expand = false
        setTimeout(() => {
          showBtn = mainBoxRef?.scrollHeight > mainBoxRef?.clientHeight
          if (props.article.bookCatalogArticleFormatType == 1) {
            $g.tool.renderMathjax()
          }
        }, 50)
      } else {
        mindMap = JSON.parse(props.article.content)
        showImage = JSON.parse(props.article.content)?.images?.length > 0
        imgUrl = JSON.parse(props.article.content)?.images?.[0]?.url
      }
    }
  },
  {
    immediate: true,
  },
)
function imgLoad() {
  showBtn = MindMapRef.clientHeight > mainBoxRef.clientHeight
}
/* 加载所有资源 */
function load() {
  expand = !expand
  if (expand) {
    loading = true
    setTimeout(() => {
      text += props.article.content.substring(
        props.maxLength,
        props.article.content.length + 1,
      )
      loading = false
    }, 500)
  } else {
    text = props.article.content.substring(0, props.maxLength)
  }

  if (props.article.bookCatalogArticleFormatType == 1) $g.tool.renderMathjax()
}
</script>

<style lang="scss" scoped>
.auto-height-box {
  display: grid;
  grid-template-rows: 0fr;
  transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.expand {
  grid-template-rows: 1fr;
}
</style>
