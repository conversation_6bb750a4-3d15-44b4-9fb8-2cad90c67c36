<template>
  <div>
    <div class="w-full flex items-center justify-between">
      <div class="flex items-center">
        <n-input
          class="!w-230px"
          v-model:value="keyword"
          placeholder="输入文章关键词搜索"
          clearable
          @clear="onClear"
          @keydown.enter="onSearch"
        ></n-input>
        <n-button class="ml-10px" type="primary" @click="onSearch"
          >搜索</n-button
        >
      </div>
      <n-space justify="center" align="center">
        <n-button type="success" @click="$emit('openImportDialog')"
          >关联已有资源</n-button
        >
        <n-button @click="sort" :disabled="!chapterId">排序调整</n-button>
        <n-button type="primary" @click="toAdd">
          <g-icon name="ri-add-line" size="14" />
          新增文章
        </n-button>
      </n-space>
    </div>
    <div v-loading="showLoading">
      <g-empty v-if="!articleList.length"></g-empty>
      <div v-else>
        <ArticleItem
          class="my-20px"
          v-for="(item, index) in articleList"
          :key="item.bookCatalogArticleId"
          :article="item"
          :index="getNum(index)"
        >
          <template #right-header>
            <n-space justify="center">
              <n-button
                v-if="item.bookCatalogArticleFormatType == 2"
                type="success"
                text
                @click="transformingMindMap(item)"
              >
                转换思维导图</n-button
              >
              <n-button type="success" text @click="move(item)"> 移动</n-button>
              <n-button type="primary" text @click="edit(item)"> 编辑</n-button>
              <n-button type="error" text @click="delArticle(item)">
                删除</n-button
              >
            </n-space>
          </template>
        </ArticleItem>
      </div>
      <g-page :pageOptions="pageOptions" @change="initData"></g-page>
    </div>
    <!-- 新增/编辑文章 -->
    <AddArticleDialog
      v-model:show="showDialog"
      :chapterId="chapterId"
      :is-edit="isEdit"
      :activeArticle="activeArticle"
      :bookId="route.query.bookId"
      :bookCatalogId="props.chapterId"
      @refresh="initData"
      @refreshTree="refreshTree"
    />
    <!-- 排序 -->
    <SortDialog
      v-model:show="showSortDialog"
      :chapterId="chapterId"
      @refresh="initData"
    />
    <!-- 文章移动 -->
    <ChapterDialog
      v-model:show="showChapterDialog"
      :chapterId="activeChapterId"
      :bookCatalogArticleId="bookCatalogArticleId"
      @refresh="initData"
      @refreshTree="refreshTree"
    />
  </div>
</template>

<script setup lang="ts">
import ArticleItem from "./ArticleItem.vue"
import AddArticleDialog from "./AddArticleDialog.vue"
import SortDialog from "./SortDialog.vue"
import {
  deleteArticle,
  getArticleList,
  mindMapTransformation,
} from "@/api/bookMgt"
import ChapterDialog from "./ChapterDialog.vue"
import type { PropType } from "vue"
let props = defineProps({
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
})
const emit = defineEmits(["refreshTree", "openImportDialog"])
let showSortDialog = $ref(false)
let keyword = $ref<any>("")
let showDialog = $ref(false)
let articleList = $ref<any>([])
let showLoading = $ref(false)
let showChapterDialog = $ref(false)
let bookCatalogArticleId = $ref<any>(null) //文章ID
let isEdit = $ref(false)
let activeArticle = $ref<any>({
  bookCatalogArticleFormatType: "1",
  content: "",
})
let activeChapterId = $ref<any>(null)
let pageOptions = $ref({
  page: 1,
  page_size: 10,
  total: 0,
})
const route = useRoute()
function handleRefresh() {
  initData()
  emit("refreshTree")
}
defineExpose({ handleRefresh })
/* 获取试题列表 */
async function initData() {
  try {
    showLoading = true
    let res = await getArticleList({
      bookId: route.query.bookId,
      bookCatalogId: props.chapterId,
      keyword,
      page: pageOptions.page,
      pageSize: pageOptions.page_size,
    })
    setTimeout(() => {
      showLoading = false
    }, 500)
    articleList = res.list
    pageOptions.total = res.total
  } catch (err) {
    console.log(err)
    showLoading = false
  } finally {
    // showLoading = false
  }
}
/* 序号格式化 */
function getNum(index) {
  return pageOptions.page_size * (pageOptions.page - 1) + index + 1
}
function refreshTree() {
  emit("refreshTree")
}
/* 排序 */
function sort() {
  showSortDialog = true
}
/* 移动 */
function move(row) {
  bookCatalogArticleId = row.bookCatalogArticleId
  activeChapterId = row.bookCatalogId
  showChapterDialog = true
}
/* 编辑 */
function edit(row) {
  activeArticle = row
  isEdit = true
  showDialog = true
}
/* 删除 */
function delArticle(row) {
  $g.confirm({ content: "是否删除文章？" })
    .then(async () => {
      await deleteArticle({
        bookCatalogArticleId: row.bookCatalogArticleId,
      })
      $g.msg("删除成功")
      await initData()
      await refreshTree()
    })
    .catch((err) => {})
}
watch(
  () => props.chapterId,
  () => {
    initData()
  },
  {
    immediate: true,
  },
)
function onClear() {
  nextTick(() => {
    initData()
  })
}
function onSearch() {
  pageOptions.page = 1
  initData()
}
function toAdd() {
  activeArticle = {
    bookCatalogArticleFormatType: 1,
    content: "",
  }
  isEdit = false
  showDialog = true
}

function transformingMindMap(item) {
  mindMapTransformation({
    bookCatalogArticleId: item.bookCatalogArticleId,
  }).then((res) => {
    $g.msg("转换成功")
    initData()
    refreshTree()
  })
}
</script>

<style lang="scss" scoped></style>
