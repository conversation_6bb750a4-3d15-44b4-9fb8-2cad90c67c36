<template>
  <g-dialog
    title="移动到章节"
    v-model:show="showDialog"
    @confirm="confirm"
    width="1200"
  >
    <el-scrollbar class="h-500px">
      <el-input
        v-model="keyword"
        style="width: 240px"
        placeholder="输入关键词进行检索"
        @input="treeSearch"
      />
      <g-tree
        ref="TreeRef"
        treeName="RightTree"
        class="p-10px !border-none"
        :treeData="treeData"
        nodeKey="bookCatalogId"
        :default-expanded-keys="[chapterId]"
        :default-checked-keys="[chapterId ? chapterId : 0]"
        @handleNodeClick="handleClick"
        render-after-expand
        :defaultProps="{
          label: 'bookCatalogName',
        }"
        @node-expand="
          () => {
            $g.tool.renderMathjax()
          }
        "
      >
        <template #body="{ data }">
          <div>
            <g-mathjax :text="data?.bookCatalogName" class="!w-max" />
          </div>
        </template>
      </g-tree>
    </el-scrollbar>
  </g-dialog>
</template>

<script setup lang="ts">
import { getDirectoryList } from "@/api/resourceMgt"
import { moveArticle, moveQuestion, moveVideoApi } from "@/api/bookMgt"
import type { PropType } from "vue"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
  bookCatalogArticleId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
  bookCatalogQuestionId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
  bookCatalogAttachId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
})
let keyword = $ref<any>("")
let TreeRef = $ref<any>(null)
const emit = defineEmits(["update:show", "refresh", "refreshTree"])
const showDialog = useVModel(props, "show", emit)
let treeData = $ref<any>([{ bookCatalogName: "无章节", bookCatalogId: 0 }])
const route = useRoute()
let bookCatalogId = $ref<any>(null)
/* 获取树 */
async function getTreeData() {
  let res = await getDirectoryList({
    bookId: route.query.bookId,
  })
  treeData = treeData.concat(res.catalogList ?? [])
  nextTick(() => {
    $g.tool.renderMathjax()
  })
}
/* 检索 */
function treeSearch() {
  TreeRef.getFilterNode(keyword)
}
watch(
  () => props.show,
  (val) => {
    if (val) {
      getTreeData()
    } else {
      treeData = [{ bookCatalogName: "无章节", bookCatalogId: 0 }]
    }
  },
)
function handleClick(data, node) {
  if (node.checked) {
    bookCatalogId = data.bookCatalogId
  } else {
    bookCatalogId = null
  }
}
/* 确认 */
async function confirm() {
  if (
    (bookCatalogId || bookCatalogId === 0) &&
    bookCatalogId !== props.chapterId
  ) {
    if (props.bookCatalogArticleId) {
      await moveArticle({
        bookCatalogArticleId: props.bookCatalogArticleId,
        bookCatalogId: bookCatalogId || null,
      })
    } else if (props.bookCatalogQuestionId) {
      await moveQuestion({
        bookCatalogQuestionId: props.bookCatalogQuestionId,
        bookCatalogId: bookCatalogId || null,
      })
    } else if (props.bookCatalogAttachId) {
      await moveVideoApi({
        bookCatalogAttachId: props.bookCatalogAttachId,
        bookCatalogId: bookCatalogId || null,
      })
    }

    $g.msg("移动成功")
    emit("refresh")
    emit("refreshTree")
    emit("update:show", false)
  } else {
    $g.msg("请选择其他章节", "warning")
    emit("update:show", true)
  }
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-tree-node__content {
    height: auto !important;
    background-color: transparent !important;
  }
  .is-checked {
    .el-tree-node__content {
      background-color: #f1f8ff !important;
    }
    .el-tree-node__children {
      .el-tree-node__content {
        background-color: transparent !important;
      }
    }
  }
}
</style>
