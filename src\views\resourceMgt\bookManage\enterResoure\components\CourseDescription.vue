<template>
  <div>
    <g-table :tableOptions="tableOptions" @changePage="getFileList">
      <template #header-right>
        <el-button
          type="primary"
          @click="showDialog = true"
          :disabled="!props.chapterId"
          >上传</el-button
        >
      </template>
      <template #fileName="{ row }">
        <n-button type="primary" text @click="previewFile(row)">
          {{ row.fileName }}
        </n-button>
      </template>
      <template #cz="{ row }">
        <n-button type="error" @click="delFile(row)" text>删除</n-button>
      </template>
    </g-table>

    <g-dialog
      title="上传"
      :formOptions="formOptions"
      v-model:show="showDialog"
      @confirm="confirm"
    >
      <g-form :formOptions="formOptions">
        <template #fileList>
          <g-upload
            v-model:fileList="formOptions.data.fileList"
            type="drag"
            :max="1"
            accept=".ppt,.pptx,.pdf,.ggb,.js,.html"
            :fileConfig="{
              default: {
                maxSize: 50 * 1024 * 1024,
              },
            }"
            :customUpload="customUpload"
            :needCustomUpload="needCustomUpload"
            tips="支持ppt、pptx、pdf、ggb、js、html格式,且不超过50MB"
          ></g-upload>
        </template>
      </g-form>
    </g-dialog>
    <PreviewDialog
      v-model:show="showPreview"
      :fileInfo="fileInfo"
      :notPreview="false"
    />
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
import PreviewDialog from "./PreviewDialog.vue"
import {
  getFileListApi,
  saveFileApi,
  deleteFileApi,
  uploadHtmlFile,
} from "@/api/bookMgt"
let props = defineProps({
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
})
const emit = defineEmits(["refreshTree"])
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      type: "index",
      label: "序号",
    },
    {
      prop: "fileName",
      label: "资源名称",
      align: "left",
      headerAlign: "center",
      slot: true,
    },
    {
      prop: "fileSize",
      label: "大小",
      formatter: (row) => {
        return $g.tool.formatFileSize(row.fileSize)
      },
    },

    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})
const formOptions = reactive<any>({
  loading: false,
  ref: null as any,
  items: {
    fileList: {
      type: "upload",
      label: "上传文件",
      slot: true,
      rule: true,
    },
    fileName: {
      type: "text",
      label: "文件名称",
      maxlength: 50,
      rule: true,
    },
  },
  data: {
    fileList: [],
    fileName: "",
  },
})
let showDialog = $ref<boolean>(false)
const route = useRoute()
watch(
  () => formOptions.data.fileList.length,
  (val) => {
    if ($g.tool.isTrue(val)) {
      // 取前50个字符
      formOptions.data.fileName = formOptions.data.fileList[0].name.slice(0, 50)
    } else {
      formOptions.data.fileName = ""
    }
  },
)
let fileInfo = $ref<any>(null)
let showPreview = $ref<boolean>(false)
/* 预览 */
function previewFile(row) {
  let type = $g.tool.getFileType(row.fileExtension || row.suffix)
  if (["img", "video", "audio", "word", "pdf", "xlsx", "ppt"].includes(type)) {
    fileInfo = row
    showPreview = true
  } else {
    $g.msg("暂不支持该类型文件预览", "warning")
  }
}
/* 删除文件 */
async function delFile(row) {
  $g.confirm({
    title: "提示",
    content: "是否删除文件？",
  })
    .then(async () => {
      await deleteFileApi({
        bookCatalogAttachId: row.bookCatalogAttachId,
      })
      $g.msg("删除成功")
      await getFileList()
      emit("refreshTree")
    })
    .catch((err) => {})
}
/* 获取文件列表 */
async function getFileList() {
  tableOptions.loading = true
  let res = await getFileListApi({
    bookId: route.query.bookId,
    page: tableOptions.pageOptions.page,
    pageSize: tableOptions.pageOptions.page_size,
    bookCatalogId: props.chapterId,
  })
  tableOptions.data = res.list
  tableOptions.pageOptions.total = res.total
  tableOptions.loading = false
}

watch(
  () => props.chapterId,
  () => {
    getFileList()
  },
  {
    immediate: true,
  },
)

async function customUpload(file) {
  const formData = new FormData()
  formData.append("file", file.file)
  const res = await uploadHtmlFile(formData)
  file.resource_url = res.url
  file.fileResourceId = res.fileResourceId

  return {
    resource_url: res.url,
    fullUrl: res.url,
  }
}

/* 需要自定义上传 */
async function needCustomUpload(file) {
  return file.name.endsWith(".html")
}

/* 保存文件 */
async function confirm() {
  await saveFileApi({
    bookId: route.query.bookId,
    bookCatalogId: props.chapterId,
    fileName: formOptions.data.fileName,
    fileAbsoluteUrl: formOptions.data.fileList[0].resource_url,
    fileSize: formOptions.data.fileList[0].size,
    fileExtension: formOptions.data.fileList[0].suffix,
  })
  $g.msg("保存成功")
  showDialog = false
  emit("refreshTree")
  await getFileList()
}
</script>

<style lang="scss" scoped></style>
