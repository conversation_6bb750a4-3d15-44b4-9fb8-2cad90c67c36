<template>
  <div
    class="flex"
    v-loading="loading"
    ref="box"
    :class="isFull ? '!h-full' : 'h-[600px]'"
  >
    <el-scrollbar
      class="w-[280px] flex-shrink-0 px-10px !h-full bg-white"
      style="border-right: 1px solid #ccc"
    >
      <el-collapse v-model="activeNames">
        <el-collapse-item title="导入文件" name="upload">
          <div class="flex items-center justify-between">
            <div>Base64导入</div>
            <n-button
              type="primary"
              :disabled="fileList.length > 0"
              @click="setupBase64Data"
              >导入</n-button
            >
          </div>
          <el-input
            class="my-10px"
            v-model="base64Data"
            type="textarea"
            :disabled="fileList.length > 0"
            placeholder="输入base64"
          />
          <div>GGB文件导入</div>
          <g-upload
            ref="uploadRef"
            v-model:fileList="fileList"
            type="button"
            :max="1"
            accept=".ggb"
            @onChange="handleFileChange"
          >
            <n-button
              type="primary"
              :disabled="fileList.length > 0 || $g.tool.isTrue(base64Data)"
              >上传</n-button
            >
          </g-upload>
          <div>
            <n-button type="primary" @click="update" :loading="btnLoading"
              >保存GGB文件</n-button
            >
          </div>
        </el-collapse-item>
        <el-collapse-item title="属性设置" name="attribute">
          <div class="flex my-20px items-center">
            <div>步骤演示：</div>
            <!-- 绑定步进器，用户可以修改步进器值 -->
            <el-input-number
              v-model="stepNum"
              :min="0"
              :max="1000"
              @change="handleChange"
            />
          </div>
          <div class="flex my-20px items-center">
            <div>步骤关联：</div>
            <el-button type="primary" @click="showAssociation = true"
              >设置</el-button
            >
          </div>
        </el-collapse-item>
        <el-collapse-item title="批量导入" name="instructions">
          <el-input
            class="my-10px"
            v-model="instructionsData"
            :maxlength="5000"
            type="textarea"
            placeholder="输入指令"
          />
          <div class="flex justify-between">
            <el-button type="primary" @click="showHistory = true"
              >历史数据</el-button
            >
            <el-button type="primary" @click="handleInstructionsData"
              >确认插入</el-button
            >
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-scrollbar>

    <div class="flex-1 !w-auto relative flex-cc" ref="container">
      <n-button
        type="primary"
        :disabled="loading"
        text
        class="absolute right-20px top-[-50px] cursor-pointer"
        @click="openScreen"
      >
        <g-icon name="ri-fullscreen-line" size="" color="" />
        全屏
      </n-button>
      <div ref="ggbContainer" class="!w-auto !h-auto bg-white"></div>
    </div>
    <HistoryInstructions
      v-model:show="showHistory"
      :data="boardData"
      :element="box"
    />
    <StepAssociation
      v-model:show="showAssociation"
      :data="boardData"
      :element="box"
    />
  </div>
</template>

<script setup lang="ts">
import HistoryInstructions from "./HistoryInstructions.vue"
import StepAssociation from "./StepAssociation.vue"
import { saveAiAnalysisBoard, saveAiAnalysisCommand } from "@/api/bookMgt"
import {
  urlToBase64,
  fileToObjectURL,
  base64ToFile,
  handleUploadFile,
} from "./tool"
let btnLoading = $ref(false)
let activeNames = $ref<any>(["upload"]) //展开面板
let uploadRef = $ref<any>(null)
let base64Data = $ref<any>(null) //base64数据
let fileList = $ref<any>([]) //文件列表
let stepNum = ref(0) //步进器值
const ggbContainer = ref<any>(null) // GeoGebra的容器元素
let ggbApp = $ref<any>(null) // GeoGebra的实例
let instructionsData = $ref("") //批量导入指令
let showHistory = $ref<Boolean>(false)
let showAssociation = $ref(false)
let loading = $ref(true)
let firstLoad = $ref(true)
const props = defineProps({
  boardData: {
    type: Object,
    required: true,
  },
})
let box = $ref<any>(null)
let container = $ref<any>(null)
let isFull = $ref(false)
let initWidth = $ref(0)
let initHeight = $ref(0)
const emit = defineEmits(["refresh"])
onBeforeMount(() => {
  /* 懒加载 GeoGebra */
  $g.tool
    .loadJS("https://frontend-cdn.qimingdaren.com/cdn/deployggb.js")
    .then(() => {
      initWidth = container?.clientWidth
      initHeight = box?.clientHeight
      init()
    })
})

/* 全屏 */
function openScreen() {
  if (!isFull) {
    box.requestFullscreen()
  }
}
function resetSize() {
  if (!document.fullscreenElement) {
    ggbApp.setSize(initWidth, initHeight)
    isFull = false
  } else {
    setTimeout(() => {
      ggbApp.setSize(container?.clientWidth, box?.clientHeight)
      isFull = true
    }, 100)
  }
}
onMounted(() => {
  // 监听全屏变化事件
  document.addEventListener("fullscreenchange", resetSize)
})
onBeforeUnmount(() => {
  document.removeEventListener("fullscreenchange", resetSize)
})
async function initUrlData() {
  // 有默认文件URL
  if (props.boardData.fileUrl && firstLoad) {
    loading = true
    fileList = [
      {
        resource_url: props.boardData.fileUrl,
        ext: ".ggb",
        batchId: $g.tool.uuid(4),
        id: $g.tool.uuid(4),
        status: "finished",
        suffix: "ggb",
        resource_title: props.boardData.fileName ?? "默认ggb文件",
      },
    ]
    let res = (await urlToBase64(props.boardData.fileUrl)) as any
    ggbApp && ggbApp.setBase64(res.split(",")[1])
    firstLoad = false
  }
  loading = false
}

/* 输入base64数据 */
function setupBase64Data() {
  if ($g.tool.isTrue(base64Data)) {
    if (base64Data.includes(",")) {
      ggbApp && ggbApp.setBase64(base64Data.split(",")[1])
    } else {
      ggbApp && ggbApp.setBase64(base64Data)
    }
  } else {
    $g.msg("请输入base64数据", "warning")
  }
}
function init() {
  ggbApp = null
  // GeoGebra的初始化参数
  const params = {
    appName: "classic", // 你可以选择其他应用类型，如 "geometry", "3d", "cas" 等
    width: container?.clientWidth,
    height: box?.clientHeight || 600,
    showToolBar: true,
    showMenuBar: true,
    showAlgebraInput: true,
    showResetIcon: true,
    language: "zh",
    appletOnLoad: (api) => {
      ggbApp = api
      // // 创建一个步进器 (Slider)，范围从 1 到 1000，初始值为 num
      ggbApp?.evalCommand(`SetIndex = Slider(0, 1000)`)
      ggbApp?.evalCommand(`SetValue[SetIndex, ${stepNum.value}]`)
      // 监听GeoGebra中的值变化
      ggbApp.registerUpdateListener(syncSetIndexToNum)
      initUrlData()
    },
  }
  // 初始化GeoGebra

  const applet = new GGBApplet(params, true)
  // 将GeoGebra注入到指定的容器中
  applet.inject(ggbContainer.value)
  // 启动时同步 GeoGebra 的 SetIndex 和 num
  syncSetIndexToNum()
}
/* 更新GGB文件数据 */
async function update() {
  btnLoading = true
  /* 输入的是base64字符 */
  let url = ""
  let name = ""

  try {
    if ($g.tool.isTrue(base64Data)) {
      // base64导入
      let res = await base64ToFile(base64Data, "默认ggb文件")
      let fileInfo = (await handleUploadFile(res)) as any
      url = fileInfo.resource_url
      name = "默认ggb文件.ggb"
    } else if (fileList.length) {
      // GGB文件上传
      url = fileList[0].resource_url
      name = fileList[0].name
    } else {
      // 直接生成base64上传oss
      let ggbBase64 = await base64ToFile(
        "data:application/octet-stream;base64," + ggbApp.getBase64(),
        "默认ggb文件",
      )
      let fileInfo = (await handleUploadFile(ggbBase64)) as any
      url = fileInfo.resource_url
      name = "默认ggb文件.ggb"
    }
    await saveAiAnalysisBoard({
      parseDynamicArtBoardId: props.boardData.parseDynamicArtBoardId,
      subQuestionParseId: props.boardData.subQuestionParseId,
      fileUrl: url,
      fileName: name,
      stepNum: stepNum.value || null,
    })
    btnLoading = false
    $g.msg("保存成功")
    await emit("refresh")
  } catch (err) {
    console.log(err)
    btnLoading = false
  }
}
/* 保存指令 */
async function saveOrder() {
  await saveAiAnalysisCommand({
    parseDynamicArtBoardId: props.boardData.parseDynamicArtBoardId,
    order: instructionsData,
  })
  instructionsData = ""
}
/* 批量插入指令 */
async function handleInstructionsData() {
  try {
    let str = ""
    str = instructionsData.split(";").join("\n")
    ggbApp.evalCommand(str)
    await saveOrder()
    $g.msg("插入成功")
    instructionsData = ""
  } catch (err) {
    console.log("批量导入指令有误", err)
  }
}

/* 处理步进器值变化 */
function handleChange(newValue) {
  if (ggbApp && typeof ggbApp.evalCommand === "function") {
    // 使用evalCommand更新步进器的值
    ggbApp.evalCommand(`SetValue[SetIndex, ${newValue}]`)
  } else {
    console.error("GeoGebra App尚未加载或evalCommand不可用")
  }
}
/* 同步 SetIndex 的值到 Vue 的 num */
function syncSetIndexToNum() {
  const setIndexValue = getSetIndex()
  if (setIndexValue && stepNum !== setIndexValue) {
    stepNum.value = Number(setIndexValue)
  } else {
    stepNum.value = 0
  }
}
/* 从 GeoGebra 获取 SetIndex 的值 */
function getSetIndex() {
  try {
    if (ggbApp && typeof ggbApp.getValue === "function") {
      const setIndexValue = ggbApp.getValue("SetIndex")
      return setIndexValue
    }
  } catch (err) {
    console.error("GeoGebra App尚未加载或getValue不可用", err)
  }
}

/* 文件上传后的处理逻辑 */
async function handleFileChange(file, fileList) {
  if (fileList.length != 0) fileList = []
  if (
    ggbApp &&
    typeof ggbApp.setBase64 === "function" &&
    file.status == "finished"
  ) {
    let fileBase64: any = await fileToObjectURL(file.file)
    ggbApp.setBase64(fileBase64.split(",")[1])
    setTimeout(() => {
      syncSetIndexToNum()
    }, 100)
  }
}

/* 移除文件后，重新初始化ggbApp */
watch(
  () => fileList.length,
  (val) => {
    if (!val) {
      removeFile()
    }
  },
)
/* 文件移除后的处理逻辑 */
function removeFile() {
  init()
  stepNum.value = 0
}
</script>

<style lang="scss" scoped>
:deep() {
  .applet_scaler {
    transform: none !important;
  }
}
</style>
