<template>
  <g-dialog
    title="编辑"
    v-model:show="showDialog"
    :confirmName="'保存'"
    @confirm="confirm"
    :autoClose="false"
    :width="800"
  >
    <div class="flex items-center">
      <div
        class="flex items-center text-14px mr-10px whitespace-nowrap flex-shrink-0"
      >
        <span class="text-danger mr-5px">*</span>公式内容:
      </div>
      <n-input
        v-model:value="textContent"
        type="textarea"
        placeholder="请输入公式内容"
        :on-input="changeVal"
      ></n-input>
      <n-button
        type="primary"
        class="ml-10px"
        @click="
          () => {
            showText = true
            $g.tool.renderMathjax()
          }
        "
        >预览</n-button
      >
    </div>
    <div class="p-10px" v-if="showText">
      <el-scrollbar>
        <g-mathjax :text="showContent" />
      </el-scrollbar>
    </div>
  </g-dialog>
</template>

<script setup lang="ts">
import { getEditFormula } from "@/api/resourceMgt"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  formulaInfo: {
    type: Object,
    default: () => {},
  },
})
let showText = $ref(false)
let textContent = $ref<any>("")
let showContent = $computed(() => (textContent ? `$$ ${textContent} $$` : ""))
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
async function confirm() {
  if (!textContent) {
    $g.msg("公式内容不能为空", "warning")
    return
  }
  try {
    await getEditFormula({
      questionId: props.formulaInfo.questionId,
      imageUrl: props.formulaInfo.imageUrl,
      formula: textContent,
    })
    $g.msg("公式内容保存成功", "success")
    emit("update:show", false)
    emit("refresh")
  } catch (error) {
    console.log(error)
  }
}
function changeVal(val) {
  showText = false
}
watch(
  () => props.show,
  (val) => {
    if (val) {
      showText = false
      textContent = props.formulaInfo?.formula
      showText = true
      $g.tool.renderMathjax()
    }
  },
)
</script>

<style lang="scss" scoped></style>
