<template>
  <div>
    <g-dialog :title="title" v-model:show="showDialog" :show-footer="false">
      <g-table :tableOptions="tableOptions">
        <template #fileName="{ row }">
          <div class="flex gap-x-[5px] text-left items-center">
            <div class="flex-shrink-0 w-30px">
              <g-icon
                :name="$g.tool.getFileTypeIcon(row.fileExtension)"
                size="14"
                color=""
              />
            </div>
            <div>{{ row.fileName }}</div>
          </div>
        </template>
        <template #action="{ row }">
          <n-space justify="center">
            <n-button type="primary" text @click="preview(row)">预览</n-button>
            <n-button type="error" text @click="deleteQuesFileApi(row)"
              >删除</n-button
            >
          </n-space>
        </template>
      </g-table>
      <PreviewDialog v-model:show="showPreview" :fileInfo="fileInfo" />
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import { deleteQuesFile } from "@/api/resourceMgt"
import PreviewDialog from "./PreviewDialog.vue"
import type { PropType } from "vue"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "files",
  },
  data: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
})
const tableOptions = reactive<any>({
  ref: null as any,
  loading: false,
  column: [
    {
      prop: "fileName",
      label: "文件名称",
      slot: true,
    },
    {
      prop: "action",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
})
const emit = defineEmits(["update:show", "refresh"])
const title = $computed(() => {
  return props.type == "files" ? "文档列表" : "试题讲解列表"
})
const showDialog = useVModel(props, "show", emit)
let fileInfo = $ref<any>({})
let showPreview = $ref(false)
/* 预览文件 */
function preview(row) {
  fileInfo = row
  showPreview = true
}
/* 删除文件 */
async function deleteQuesFileApi(row) {
  $g.confirm({ content: "是否删除该文件" }).then(async () => {
    let num = tableOptions.data.findIndex(
      (val) => val.questionFileId == row.questionFileId,
    )
    tableOptions.data.splice(num, 1)
    await deleteQuesFile({
      questionFileId: row.questionFileId,
    })
    $g.msg("删除成功")
  })
}
watch(
  () => props.show,
  (val) => {
    if (val) {
      tableOptions.data = props.data
    }
  },
)
</script>

<style lang="scss" scoped></style>
