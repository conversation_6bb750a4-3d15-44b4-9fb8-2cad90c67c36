<template>
  <div>
    <g-dialog
      :title="title"
      v-model:show="showDialog"
      :show-footer="false"
      :width="1200"
    >
      <g-table
        :tableOptions="tableOptions"
        @change-page="changePage"
        :height="500"
        :stripe="true"
      >
        <template #originalImage="{ row }">
          <div class="flex items-center justify-center">
            <g-img
              width="150"
              object-fit="scale-down"
              :src="row?.imageUrl"
              :previewDisabled="false"
              :optimization="false"
              :previewedImgProps="{ style: { 'min-width': '200px' } }"
            />
          </div>
        </template>
        <template #renderEffect="{ row }">
          <div class="flex items-center justify-center">
            <el-scrollbar>
              <g-mathjax
                v-if="row.formula"
                :text="exchangeFormula(row.formula)"
              />
              <span v-else>-</span>
            </el-scrollbar>
          </div>
        </template>
        <template #action="{ row }">
          <n-space justify="center">
            <n-button type="primary" text @click="againIdentify(row)"
              >再次识别</n-button
            >
            <n-button type="primary" text @click="editIdentify(row)"
              >编辑识别结果</n-button
            >
            <n-button type="success" text @click="saveIdentify(row)"
              >保存</n-button
            >
          </n-space>
        </template>
      </g-table>
      <EditIdentify
        v-model:show="showEdit"
        :formulaInfo="formulaInfo"
        @refresh="
          () => {
            isEdit = true
            getFormulaListData()
          }
        "
      />
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import { getFormulaList, getFormulaOcr } from "@/api/resourceMgt"
import EditIdentify from "./EditIdentify.vue"
import { getEditFormula } from "@/api/resourceMgt"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  questionId: {
    type: Number,
    default: null,
  },
})
const tableOptions = reactive<any>({
  ref: null as any,
  loading: false,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
  column: [
    {
      type: "index",
      label: "序号",
    },
    {
      prop: "originalImage",
      label: "原图片",
      slot: true,
      width: "200px",
    },
    {
      prop: "renderEffect",
      label: "转公式后渲染效果",
      slot: true,
    },
    {
      prop: "action",
      label: "操作",
      width: "200px",
      slot: true,
    },
  ],
  data: [],
})
const emit = defineEmits(["update:show", "refresh"])
const title = $computed(() => {
  return "公式识别"
})
const showDialog = useVModel(props, "show", emit)
let formulaInfo = $ref<any>({})
let showEdit = $ref(false)
let allDataList = $ref<any[]>([])
let isEdit = $ref(false)
/* 编辑内容 */
function editIdentify(row) {
  formulaInfo = row
  showEdit = true
}

function changePage() {
  tableOptions.loading = true
  let pageStart =
    (tableOptions.pageOptions.page - 1) * tableOptions.pageOptions.page_size
  let pageEnd = pageStart + Number(tableOptions.pageOptions.page_size)
  tableOptions.data = allDataList.slice(pageStart, pageEnd)
  tableOptions.loading = false
  $g.tool.renderMathjax()
}

/* 公式识别 */
async function againIdentify(row) {
  $g.confirm({ content: "是否再次识别该图片？" }).then(async () => {
    tableOptions.loading = true
    let res = await getFormulaOcr({
      imageUrl: row.imageUrl,
    })
    row.formula = res.formula
    isEdit = true
    $g.msg("再次识别完成", "success")
    tableOptions.loading = false
    $g.tool.renderMathjax()
  })
}

function exchangeFormula(text) {
  return text ? `$$ ${text} $$` : ""
}

async function saveIdentify(data) {
  if (!data.formula) {
    $g.msg("保存的公式不能为空!", "warning")
    return
  }
  try {
    tableOptions.loading = true
    await getEditFormula({
      questionId: data.questionId,
      imageUrl: data.imageUrl,
      formula: data.formula,
    })
    isEdit = true
    $g.msg("公式内容保存成功", "success")
    await getFormulaListData()
    tableOptions.loading = false
  } catch (error) {
    console.log(error)
  }
}

async function getFormulaListData() {
  try {
    tableOptions.loading = true
    let res = await getFormulaList({ questionId: props.questionId })
    allDataList = res
    tableOptions.pageOptions.total = res.length
    let pageStart =
      (tableOptions.pageOptions.page - 1) * tableOptions.pageOptions.page_size
    let pageEnd = pageStart + Number(tableOptions.pageOptions.page_size)
    tableOptions.data = allDataList.slice(pageStart, pageEnd)
    tableOptions.loading = false
    $g.tool.renderMathjax()
  } catch (error) {
    console.log(error)
    tableOptions.loading = false
  }
}
watch(
  () => props.show,
  (val) => {
    if (val) {
      tableOptions.pageOptions.page = 1
      getFormulaListData()
      isEdit = false
    } else {
      if (isEdit) {
        emit("refresh")
      }
    }
  },
)
</script>

<style lang="scss" scoped></style>
