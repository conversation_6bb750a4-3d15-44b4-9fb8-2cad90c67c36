<template>
  <g-dialog
    title="历史记录"
    v-bind="$attrs"
    :show-footer="false"
    :on-after-enter="open"
    :width="800"
    :to="element"
    :auto-focus="false"
  >
    <n-scrollbar class="h-[500px]">
      <template v-if="list.length">
        <div v-for="item in list" :key="item.parseDynamicArtBoardOrderId">
          <n-input
            v-model:value="item.order"
            type="textarea"
            class="min-h-[80px]"
            :disabled="true"
          ></n-input>
          <div>指令发布时间:{{ item.createTime }}</div>
        </div>
      </template>
      <g-empty v-else></g-empty>
      <g-page :pageOptions="pageOptions" @change="fetchHistoryData"></g-page>
    </n-scrollbar>
  </g-dialog>
</template>

<script setup lang="ts">
import { getAiAnalysisCommandList } from "@/api/bookMgt"
import type { PropType } from "vue"
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  element: {
    type: HTMLElement as PropType<any>,
  },
})
let pageOptions = $ref({
  page: 1,
  page_size: 10,
  total: 0,
})
let list = $ref<any>([])
/* 获取指令历史 */
async function fetchHistoryData() {
  let res = await getAiAnalysisCommandList({
    parseDynamicArtBoardId: props.data.parseDynamicArtBoardId,
    page: pageOptions.page,
    pageSize: pageOptions.page_size,
  })
  list = res.list
  pageOptions.total = res.total
}
async function open() {
  pageOptions.page = 1
  pageOptions.page_size = 10
  await fetchHistoryData()
}
</script>

<style lang="scss" scoped></style>
