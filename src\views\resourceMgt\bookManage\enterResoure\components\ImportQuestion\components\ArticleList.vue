<template>
  <div>
    <div v-loading="loading" class="h-full">
      <g-empty v-if="!data.length"></g-empty>
      <div v-else>
        <ArticleItem
          class="my-20px"
          v-for="(item, index) in data"
          :key="item.bookCatalogArticleId"
          :article="item"
          :index="getNum(index)"
        >
          <template #right-header>
            <slot name="action" :item="item"></slot>
          </template>
        </ArticleItem>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ArticleItem from "@/views/resourceMgt/bookManage/enterResoure/components/ArticleItem.vue"
import type { PropType } from "vue"
const props = defineProps({
  data: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: true,
  },
  pageOptions: {
    type: Object,
    default: () => ({
      page: 1,
      page_size: 10,
    }),
  },
})
/* 序号格式化 */
function getNum(index) {
  return props.pageOptions.page_size * (props.pageOptions.page - 1) + index + 1
}
</script>

<style lang="scss" scoped></style>
