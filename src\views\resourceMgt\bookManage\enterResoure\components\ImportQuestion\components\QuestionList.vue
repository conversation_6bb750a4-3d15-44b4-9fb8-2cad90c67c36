<template>
  <div>
    <!-- title -->
    <div class="flex items-center gap-x-[10px]">
      <div>
        {{ treeData?.bookName || "-" }}
      </div>
      <div class="text-primary underline cursor-pointer" @click="$emit('back')">
        返回
      </div>
    </div>

    <!-- 内容 -->
    <div class="flex h-[700px] mt-10px">
      <!-- 章节树 -->
      <el-scrollbar class="w-[300px] h-full">
        <g-tree
          class="!border-none"
          :treeData="treeData?.catalogList"
          nodeKey="bookCatalogId"
          :highlightCurrent="true"
          @handleNodeClick="handleClick"
          @node-expand="
            () => {
              $g.tool.renderMathjax()
            }
          "
        >
          <template #body="{ data }">
            <div class="flex items-center !text-[12px]">
              <el-scrollbar x-scrollable class="w-240px">
                <div class="flex items-center gap-x-[5px]">
                  <div>
                    <g-mathjax :text="data?.bookCatalogName" class="!w-max" />
                  </div>
                  <div>
                    ({{
                      activeName == "QuestionList"
                        ? data.questionNum
                        : activeName == "ArticleList"
                        ? data.articleNum
                        : data.videoNum
                    }})
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </template>
        </g-tree>
      </el-scrollbar>

      <div
        style="height: 100%; width: 2px; background: #ccc"
        class="mx-10px"
      ></div>

      <div class="flex-1 flex-shrink-0 overflow-hidden">
        <el-tabs
          v-model="activeName"
          class="demo-tabs"
          @tab-change="handleTabChange"
        >
          <el-tab-pane
            v-for="item in tabs"
            :key="item.value"
            :label="item.label"
            :name="item.value"
          ></el-tab-pane>
        </el-tabs>

        <div>目录层级：{{ dirName || "-" }}</div>

        <div class="flex justify-end items-center">
          <div v-loading="selectAllLoading" class="mr-10px mt-[-6px] h-32px">
            <el-checkbox
              v-model="allCheck"
              @change="handleCheckAllChange"
              label="本节全选"
              size="large"
            />
          </div>
          <n-input
            class="!w-230px h-32px"
            v-model:value="keyword"
            :placeholder="placeholder"
            clearable
            @clear="onSearch"
            @keydown.enter="onSearch"
          />
          <n-button class="ml-10px" type="primary" @click="onSearch">
            搜索
          </n-button>
        </div>

        <el-scrollbar class="h-[540px] pr-10px" ref="scrollbarRef">
          <!-- 试题列表 -->
          <List
            :data="tableOptions.data"
            :loading="tableOptions.loading"
            v-if="activeName == 'QuestionList'"
          >
            <template #left-header="{ row }">
              <div class="flex items-center gap-x-[20px]">
                <div
                  class="flex text-white cursor-pointer select-none rounded-[4px]"
                >
                  <el-badge :value="row.explains?.length" type="info">
                    <div
                      class="bg-primary px-4px text-[#fff] rounded-l-[4px]"
                      @click="openFilesDialog(row, 'explains')"
                    >
                      试题讲解
                    </div>
                  </el-badge>
                </div>
                <div class="flex text-white cursor-pointer select-none">
                  <el-badge :value="row.files?.length" type="info">
                    <div
                      class="bg-[#4FADFF] px-4px rounded-l-[4px]"
                      @click="openFilesDialog(row, 'files')"
                    >
                      文档
                    </div>
                  </el-badge>
                </div>
                <n-tag
                  :bordered="false"
                  :color="
                    sourceTagStyle[row.sourceName] || sourceTagStyle.default
                  "
                >
                  {{ row.sourceName }}
                </n-tag>
                <n-button :type="row.state === 2 ? 'success' : 'error'" text>
                  {{ row.state === 2 ? "已审核" : "未审核" }}
                </n-button>
              </div>
            </template>
            <template #action="{ row }">
              <n-space justify="center" align="center" class="h-30px">
                <n-button
                  type="error"
                  v-if="bindQuestionIds.includes(row.questionId)"
                  @click="handleBindQuestion('delete', [row.questionId], row)"
                  text
                  :disabled="selectAllLoading"
                  :loading="row.bindQuestionLoading"
                  >取消选择</n-button
                >
                <n-button
                  v-else
                  type="primary"
                  @click="handleBindQuestion('add', [row.questionId], row)"
                  :disabled="selectAllLoading"
                  :loading="row.bindQuestionLoading"
                  text
                  >选择此题</n-button
                >
              </n-space>
            </template>
          </List>

          <!-- 文章列表 -->
          <ArticleList
            :data="tableOptions.data"
            :loading="tableOptions.loading"
            :pageOptions="tableOptions.pageOptions"
            v-if="activeName == 'ArticleList'"
          >
            <template #action="{ item: row }">
              <n-space justify="center" class="h-30px">
                <n-button
                  type="error"
                  v-if="bindArticleIds.includes(row.bookCatalogArticleId)"
                  @click="
                    handleBindArticle('delete', [row.bookCatalogArticleId], row)
                  "
                  text
                  :disabled="selectAllLoading"
                  :loading="row.bindArticleLoading"
                >
                  取消选择
                </n-button>
                <n-button
                  v-else
                  type="primary"
                  @click="
                    handleBindArticle('add', [row.bookCatalogArticleId], row)
                  "
                  :disabled="selectAllLoading"
                  :loading="row.bindArticleLoading"
                  text
                >
                  选择此资源
                </n-button>
              </n-space>
            </template>
          </ArticleList>

          <!-- 视频列表 -->
          <g-table
            v-if="activeName == 'VideoList'"
            :tableOptions="tableOptions"
            :height="480"
            @change="fetchVideoList"
          >
            <template #cz="{ row }">
              <n-space justify="center" class="h-30px">
                <n-button type="primary" text @click="onPlay(row)"
                  >播放</n-button
                >
                <n-button
                  type="error"
                  v-if="bindVideoIds.includes(row.bookCatalogAttachId)"
                  @click="
                    handleBindVideo('delete', [row.bookCatalogAttachId], row)
                  "
                  text
                  :disabled="selectAllLoading"
                  :loading="row.bindVideoLoading"
                >
                  取消选择
                </n-button>
                <n-button
                  v-else
                  type="primary"
                  @click="
                    handleBindVideo('add', [row.bookCatalogAttachId], row)
                  "
                  :disabled="selectAllLoading"
                  :loading="row.bindVideoLoading"
                  text
                >
                  选择此资源
                </n-button>
              </n-space>
            </template>
          </g-table>
        </el-scrollbar>

        <g-page
          v-if="activeName != 'VideoList'"
          :pageOptions="tableOptions.pageOptions"
          @change="fetchList(false, false)"
        ></g-page>
      </div>
    </div>

    <!-- 试题文件预览 -->
    <FilesDialog v-model:show="showFiles" :data="files" :type="fileType" />

    <!-- 视频播放预览 -->
    <VideoDialog :url="videoUrl" v-model:show="showVideoDialog" />
  </div>
</template>

<script setup lang="ts">
import {
  bindQuestion,
  getBindQuestions,
  bindArticle,
  getBindArticles,
  bindVideo,
  getBindVideos,
  getResourceIds,
  getArticleList,
  getStatistics,
  getVideoListApi,
} from "@/api/bookMgt"
import { getDirectoryList, getDirQuestionList } from "@/api/resourceMgt"

// 试题列表
import List from "@/views/resourceMgt/bookManage/enterResoure/components/List.vue"
// 文章列表
import ArticleList from "./ArticleList.vue"
import FilesDialog from "@/views/resourceMgt/bookManage/enterResoure/components/FilesDialog.vue"
import { sourceTagStyle } from "@/views/resourceMgt/bookManage/constant"
import type { PropType } from "vue"

const props = defineProps({
  // 进入的资源ID
  checkedBookId: {
    type: [Number, String, null] as PropType<any>,
  },
  // 需要导入的书本的章节ID
  bookCatalogId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
})

const emit = defineEmits(["back", "needRefresh", "sourceNumberChange"])

let route = useRoute()
let scrollbarRef = $ref<any>(null)
let treeData = $ref<any>([]) //章节树

// 已经导入的试题列表
let bindQuestionIds = $ref<any[]>([])
// 已经导入的文章列表
let bindArticleIds = $ref<any[]>([])
// 已经导入的视频列表
let bindVideoIds = $ref<any[]>([])
let allBookCatalogQuestionIds = $ref<any[]>([]) // 本节所有资源id
let keyword = $ref("")

// 试题文件预览相关
let files = $ref<any[]>([])
let showFiles = $ref(false)
let fileType = $ref<any>(null)

let allCheck = $ref(false)

let videoUrl = $ref("")
let showVideoDialog = $ref(false)

// 选中的目录id
let dirId = $ref<any>(null)
// 选中的路径名称
let dirName = $ref("")

// 当前激活的tab QuestionList | ArticleList | VideoList
let activeName = $ref("QuestionList")

let tabs = $ref([
  {
    label: "试题列表",
    value: "QuestionList",
  },
  {
    label: "文章列表",
    value: "ArticleList",
  },
  {
    label: "视频列表",
    value: "VideoList",
  },
])

const tableOptions = reactive({
  loading: true,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
  column: [
    {
      type: "index",
      label: "序号",
    },
    {
      prop: "fileName",
      label: "视频名称",
    },
    {
      prop: "fileSize",
      label: "大小",
      formatter(row) {
        return row?.fileSize + "MB"
      },
    },
    {
      prop: "uploadTime",
      label: "上传时间",
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [] as any[],
})

const placeholder = $computed(() => {
  switch (activeName) {
    case "QuestionList":
      return "输入试题ID/试题内容关键词搜索"
    case "ArticleList":
      return "请输入文章关键词搜索"
    case "VideoList":
      return "请输入视频关键词搜索"
  }
})

/* 获取章节树 */
async function getDirectoryListApi() {
  const data = await getDirectoryList({ bookId: props.checkedBookId })
  treeData = data || null
  getAllBookCatalogQuestionIds()
  nextTick(() => {
    $g.tool.renderMathjax()
  })
}

/** 获取章节资源统计 */
async function getStatisticsApi() {
  let res = await getStatistics({
    bookId: props.checkedBookId,
    bookCatalogId: dirId,
  })
  tabs[0].label = "试题列表" + "(" + res.questionNum + ")"
  tabs[1].label = "文章列表" + "(" + res.articleNum + ")"
  tabs[2].label = "视频列表" + "(" + res.videoNum + ")"
}

/* 获取试题列表 */
async function getDirQuestionListApi() {
  try {
    tableOptions.loading = true
    scrollbarRef.scrollTo(0, 0)
    const data = await getDirQuestionList({
      bookCatalogId: dirId,
      bookId: props.checkedBookId,
      keyword: keyword,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    if (data) {
      tableOptions.pageOptions.total = data.total
      tableOptions.data = data.list.map((v) => {
        return {
          ...v,
          explains: v.questionFiles
            .filter((vv) => vv.type === 1)
            .map((vv) => ({
              ...vv,
              name: vv.fileName,
              id: vv.questionFileId,
              status: "finished",
            })),
          files: v.questionFiles
            .filter((vv) => vv.type === 2)
            .map((vv) => ({
              ...vv,
              name: vv.fileName,
              id: vv.questionFileId,
              status: "finished",
            })),
        }
      })
    }
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
    tableOptions.data = []
  }
}

/* 获取文章列表 */
async function getArticleListApi() {
  try {
    tableOptions.loading = true
    scrollbarRef.scrollTo(0, 0)
    const data = await getArticleList({
      bookCatalogId: dirId,
      bookId: props.checkedBookId,
      keyword: keyword,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    tableOptions.data = data.list
    tableOptions.pageOptions.total = data.total
  } catch (err) {
    tableOptions.loading = false
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
    tableOptions.pageOptions.page = 1
    console.log(err)
  }
}

/* 获取视频列表 */
async function fetchVideoList() {
  tableOptions.loading = true
  let res = await getVideoListApi({
    bookId: props.checkedBookId,
    bookCatalogId: dirId,
    keyword,
    page: tableOptions.pageOptions.page,
    pageSize: tableOptions.pageOptions.page_size,
  })
  tableOptions.data = res.list
  tableOptions.pageOptions.total = res.total
  tableOptions.loading = false
}

// 请求列表数据，根据当前激活的tab 类型请求数据
async function fetchList(resetPage = false, resetKeyword = false) {
  if (resetPage) {
    tableOptions.pageOptions.page = 1
    tableOptions.pageOptions.total = 0
    tableOptions.data = []
  }

  if (resetKeyword) {
    keyword = ""
  }

  await nextTick()

  switch (activeName) {
    case "QuestionList":
      await getDirQuestionListApi()
      break
    case "ArticleList":
      await getArticleListApi()
      break
    case "VideoList":
      await fetchVideoList()
      break
  }
}

// 查询已经关联的资源的id列表,根据当前激活的tab 类型请求数据
async function fetchBindIds() {
  switch (activeName) {
    case "QuestionList":
      // 获取此章节绑定的本书的试题数量
      bindQuestionIds =
        (await getBindQuestions({
          bookId: route.query.bookId,
          bookCatalogId: props.bookCatalogId,
          otherBookId: props.checkedBookId,
        })) || []
      emit("sourceNumberChange", bindQuestionIds.length)
      break
    case "ArticleList":
      bindArticleIds =
        (await getBindArticles({
          bookId: route.query.bookId,
          bookCatalogId: props.bookCatalogId,
          otherBookId: props.checkedBookId,
        })) || []
      emit("sourceNumberChange", bindArticleIds.length)
      break
    case "VideoList":
      bindVideoIds =
        (await getBindVideos({
          bookId: route.query.bookId,
          bookCatalogId: props.bookCatalogId,
          otherBookId: props.checkedBookId,
        })) || []
      emit("sourceNumberChange", bindVideoIds.length)
      break
  }
  allCheckSet()
}

/* 章节树点击 */
async function handleClick(data, node) {
  if (node.checked) {
    dirId = data.bookCatalogId
    dirName = getName(node).suffix
    $g.tool.renderMathjax()
  } else {
    dirId = null
    dirName = "-"
    $g.tool.renderMathjax()
  }
  // 获取统计数据
  getStatisticsApi()
  // 获取章节下的资源数据
  fetchList(true, true)
  //获取本节所有资源id
  await getAllBookCatalogQuestionIds()
  allCheckSet()
}

/** tab切换 */
async function handleTabChange() {
  await getAllBookCatalogQuestionIds()
  fetchList(true, true)
  fetchBindIds()
}

/* 试题文件弹窗 */
function openFilesDialog(row, type) {
  files = row[type]
  fileType = type
  showFiles = true
}

/** 视频播放 */
function onPlay(item) {
  videoUrl = item?.fileAbsoluteUrl
  showVideoDialog = true
}

function getName(node, suffix = "", id = "") {
  suffix = suffix
    ? node.data.bookCatalogName + "/" + suffix
    : node.data.bookCatalogName
  id = id
    ? String(node.data.bookCatalogId) + "/" + id
    : String(node.data.bookCatalogId)
  if (node.parent.level !== 0) {
    return getName(node.parent, suffix, id)
  }
  return {
    suffix,
    id,
  }
}

const onSearch = $g._.debounce(() => fetchList(true, false), 500, {
  leading: true,
})

// 绑定和解绑题目的处理函数
async function handleBindQuestion(type: "add" | "delete", ids, curQuestion?) {
  let params = {
    bookId: route.query.bookId,
    bookCatalogId: props.bookCatalogId,
    bindQuestionIdList: type === "add" ? ids : [],
    cancelBindQuestionIdList: type === "delete" ? ids : [],
  }

  try {
    curQuestion && (curQuestion.bindQuestionLoading = true)
    await bindQuestion(params)
    fetchBindIds().then(() => {
      $g.msg(`已选择${bindQuestionIds.length}个资源`, "success")
    })
    emit("needRefresh")
  } catch (err) {
    console.log("绑定或解绑题目出错", err)
  } finally {
    curQuestion && (curQuestion.bindQuestionLoading = false)
    selectAllLoading = false
  }
}

// 绑定和解绑文章
async function handleBindArticle(type: "add" | "delete", ids, curArticle?) {
  let params = {
    bookId: route.query.bookId,
    bookCatalogId: props.bookCatalogId,
    bindArticleIdList: type === "add" ? ids : [],
    cancelBindArticleIdList: type === "delete" ? ids : [],
  }

  try {
    curArticle && (curArticle.bindArticleLoading = true)
    await bindArticle(params)
    fetchBindIds().then(() => {
      $g.msg(`已选择${bindArticleIds.length}个资源`, "success")
    })
    emit("needRefresh")
  } catch (err) {
    console.log("绑定或解绑文章出错", err)
  } finally {
    curArticle && (curArticle.bindArticleLoading = false)
    selectAllLoading = false
  }
}

// 绑定和解绑视频
async function handleBindVideo(type: "add" | "delete", ids, curVideo?) {
  let params = {
    bookId: route.query.bookId,
    bookCatalogId: props.bookCatalogId,
    bindBookCatalogAttachIdList: type === "add" ? ids : [],
    cancelBindBookCatalogAttachIdList: type === "delete" ? ids : [],
  }

  try {
    curVideo && (curVideo.bindVideoLoading = true)
    await bindVideo(params)
    fetchBindIds().then(() => {
      $g.msg(`已选择${bindVideoIds.length}个资源`, "success")
    })
    emit("needRefresh")
  } catch (err) {
    console.log("绑定或解绑视频出错", err)
  } finally {
    curVideo && (curVideo.bindVideoLoading = false)
    fetchBindIds()
    selectAllLoading = false
  }
}

let selectAllLoading = $ref(false)
/** 选择全部 */
async function handleBindAll() {
  try {
    selectAllLoading = true
    // 类型:1-试题,2-文章,3-视频
    let type =
      activeName === "QuestionList"
        ? 1
        : activeName === "ArticleList"
        ? 2
        : activeName === "VideoList"
        ? 3
        : undefined
    type === 1 && handleBindQuestion("add", allBookCatalogQuestionIds)
    type === 2 && handleBindArticle("add", allBookCatalogQuestionIds)
    type === 3 && handleBindVideo("add", allBookCatalogQuestionIds)
  } catch (err) {
    console.log("全选时发生错误", err)
  }
}

//全选框切换处理
function handleCheckAllChange(val) {
  if (val) {
    handleBindAll()
  } else {
    selectAllLoading = true
    const ids =
      activeName === "QuestionList"
        ? bindQuestionIds
        : activeName === "ArticleList"
        ? bindArticleIds
        : bindVideoIds
    handleBindQuestion("delete", ids)
  }
}

function intersection(arr1, arr2) {
  const set2 = new Set(arr2)
  return arr1.filter((item) => set2.has(item)).length
}

//获取本节所有资源id
async function getAllBookCatalogQuestionIds() {
  // 类型:1-试题,2-文章,3-视频
  let type =
    activeName === "QuestionList"
      ? 1
      : activeName === "ArticleList"
      ? 2
      : activeName === "VideoList"
      ? 3
      : undefined

  // 获取本节所有的资源ID
  allBookCatalogQuestionIds = await getResourceIds({
    bookId: props.checkedBookId,
    bookCatalogId: dirId || undefined,
    type,
  })
}

//全选状态设置
async function allCheckSet() {
  const ids =
    activeName === "QuestionList"
      ? bindQuestionIds
      : activeName === "ArticleList"
      ? bindArticleIds
      : bindVideoIds
  let checkNum = intersection(ids, allBookCatalogQuestionIds)
  if (checkNum && checkNum === allBookCatalogQuestionIds.length) {
    allCheck = true
  } else {
    allCheck = false
  }
}

onMounted(async () => {
  getStatisticsApi()
  await getDirectoryListApi()
  fetchList()
  fetchBindIds()
})
</script>

<style lang="scss" scoped>
:deep() {
  .n-scrollbar-container {
    background: rgba(255, 255, 255, 0) !important;
  }
  .el-tree-node__content {
    height: auto;
    background-color: transparent !important;
  }
  .el-dropdown-link {
    outline: none;
  }
  .el-tabs__item {
    font-size: 18px;
  }

  .is-checked {
    .el-tree-node__content {
      background-color: #f1f8ff !important;
    }
    .el-tree-node__children {
      .el-tree-node__content {
        background-color: transparent !important;
      }
    }
  }
}
</style>
