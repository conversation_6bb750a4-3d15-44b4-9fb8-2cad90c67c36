<template>
  <div>
    <g-form :formOptions="formOptions" @reset="search" @search="search">
      <template #bookTypeId>
        <div class="flex gap-x-[10px]">
          <n-tree-select
            v-model:value="formOptions.data.bookTypeId"
            :options="formOptions.items.bookTypeId.options"
            clearable
            :render-label="renderLabel"
            placeholder="请选择书籍类型"
            class="w-[200px]"
          />
        </div>
      </template>
    </g-form>
    <g-table :tableOptions="tableOptions" @changePage="initData" :height="600">
      <template #bookName="{ row }">
        <div class="line-3">{{ row.bookName }}</div>
      </template>
      <template #bookTypeName="{ row }">
        <div>
          {{ row.bookTypeList?.map((v) => v.bookTypeName).join("、") }}
        </div>
      </template>
      <template #sourceName="{ row }">
        <n-tag
          :bordered="false"
          :color="sourceTagStyle[row.sourceName] || sourceTagStyle.default"
        >
          {{ row.sourceName }}
        </n-tag>
      </template>
      <template #state="{ row }">
        <div :class="status[row.state].class">
          {{ status[row.state].name }}
        </div>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="primary" text @click="toResource(row)"
            >进入资源</n-button
          >
        </n-space>
      </template>
    </g-table>
  </div>
</template>

<script setup lang="ts">
import GTooltip from "@/components/global/g-tooltip/index.vue"
import { getListApi, getTypeSelect, getStatusSelect } from "@/api/bookMgt"
import { getNewStageListApi, getNewSubjectListApi } from "@/api/resourceMgt"
import { sourceTagStyle } from "../../../../constant"
const route = useRoute()
const formOptions = $ref<any>({
  ref: null as any,
  filter: true,
  loading: false,
  items: {
    sysStage: {
      type: "select",
      options: [],
      label: "学段",
      width: "120px",
    },
    sysSubjectId: {
      type: "select",
      label: "学科",
      options: [],
      width: "150px",
    },
    bookTypeId: {
      type: "select",
      label: "类型",
      options: [],
      width: "150px",
      slot: true,
    },
    state: {
      type: "select",
      label: "发布状态",
      options: [],
      width: "150px",
      showLabel: false,
    },
    keyword: {
      type: "text",
      label: "书籍名称检索",
      showLabel: false,
      width: "180px",
    },
  },
  data: {
    sysStage: null,
    sysSubjectId: null,
    bookTypeId: null,
    state: null,
    keyword: "",
  },
})
const tableOptions = $ref<any>({
  ref: null as any,
  loading: false,
  column: [
    {
      prop: "bookId",
      label: "ID",
    },
    {
      prop: "bookName",
      label: "书籍名称",
      slot: true,
      width: "150px",
    },
    {
      prop: "bookTypeName",
      label: "类型",
      slot: true,
    },
    {
      prop: "sourceName",
      label: "来源",
      slot: true,
    },
    {
      prop: "sysStageName",
      label: "学段",
    },
    {
      prop: "sysSubjectName",
      label: "学科",
    },
    {
      prop: "sysGradeName",
      label: "年级",
    },
    {
      prop: "num",
      label: "录入试题",
    },
    {
      prop: "state",
      label: "发布状态",
      slot: true,
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})
const status = {
  0: {
    name: "未发布",
    class: "text-error",
  },
  1: {
    name: "审核中",
    class: "text-primary",
  },
  2: {
    name: "已发布",
    class: "text-success",
  },
  3: {
    name: "已驳回",
    class: "text-warning",
  },
}
const emit = defineEmits(["changeBookId"])
function renderLabel({ option }) {
  return h(GTooltip, { content: option.label, refName: String(option.key) }, {})
}
watch(
  () => formOptions.data.sysStage,
  async (val) => {
    if (val) {
      formOptions.items.sysSubjectId.options = []
      formOptions.data.sysSubjectId =
        val == route.query.sysStage ? Number(route.query.sysSubjectId) : null
      formOptions.data.bookTypeId = null
      formOptions.items.bookTypeId.options = []
      await getSubject()
      await getTypeSelectApi()
    }
  },
)
/* 获取学段 */
async function getStage() {
  let res = await getNewStageListApi()
  formOptions.items.sysStage.options = res.map((v) => {
    return {
      value: v.id,
      label: v.title,
    }
  })
  formOptions.data.sysStage = Number(route.query.sysStage)
}
/* 获取学科 */
async function getSubject() {
  let res = await getNewSubjectListApi({
    sysStageId: formOptions.data.sysStage,
  })
  formOptions.items.sysSubjectId.options = res.map((v) => {
    return {
      value: v.sysSubjectId,
      label: v.sysCourseName,
    }
  })
}
/* 书籍列表 */
async function initData() {
  try {
    tableOptions.loading = true
    let params = {
      sysStage: formOptions.data.sysStage,
      sysSubjectId: formOptions.data.sysSubjectId,
      excludeBookId: route.query.bookId,
      bookTypeId: formOptions.data.bookTypeId,
      state: formOptions.data.state,
      keyword: formOptions.data.keyword,
    }
    let res = await getListApi({
      ...params,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    tableOptions.data = res.list
    tableOptions.pageOptions.total = res.total
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
  }
}

function toResource(row) {
  emit("changeBookId", row.bookId)
}
/* 获取类型 */
async function getTypeSelectApi() {
  let res = await getTypeSelect({ sysStageId: formOptions.data.sysStage })
  formOptions.items.bookTypeId.options = transformDataStructure(res)
}

/* 递归处理数据 */
function transformDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.label = newItem.bookTypeName
      newItem.key = newItem.bookTypeId
      newItem.children = newItem.list.length
        ? transformDataStructure(newItem.list)
        : null

      return newItem
    })
  } else if (typeof data === "object" && data !== null) {
    return Object.keys(data).reduce((obj, key) => {
      obj[key] = transformDataStructure(data[key])
      return obj
    }, {})
  }
  return data
}

/* 获取发布状态 */
async function getStatusSelectApi() {
  let res = await getStatusSelect()
  formOptions.items.state.options = res.map((v) => {
    return {
      label: v.stateName,
      value: v.state,
    }
  })
}

/* 搜索 */
async function search() {
  tableOptions.pageOptions.page = 1
  initData()
}
onMounted(async () => {
  await getStage()
  await getStatusSelectApi()
  await initData()
})
</script>

<style lang="scss" scoped></style>
