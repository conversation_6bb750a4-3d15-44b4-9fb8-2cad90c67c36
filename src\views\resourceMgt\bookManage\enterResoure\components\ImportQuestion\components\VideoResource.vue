<template>
  <div>
    <div class="flex items-center">
      <div>类型：</div>
      <el-radio-group v-model="videoType" @change="fetchVersionList">
        <el-radio
          :value="item.value"
          size="large"
          v-for="item in typeList"
          :key="item.value"
          >{{ item.label }}</el-radio
        >
      </el-radio-group>
    </div>

    <div class="mt-20px flex h-[670px]">
      <!-- 左侧 -->
      <div class="w-[350px] flex-shrink-0 h-full">
        <div class="cursor-pointer flex mb-10px" v-if="videoType != 3">
          <n-select
            v-model:value="currentBookId"
            :options="versionList"
            value-field="sysTextbookId"
            @update:value="fetchChapterList"
            :renderOption="renderTooltipOption"
          />
        </div>
        <!-- 章节树 -->
        <el-scrollbar class="h-[600px]">
          <g-loading class="h-200px" v-if="treeLoading"></g-loading>

          <g-tree
            v-else
            class="!border-none"
            :treeData="treeData"
            :nodeKey="nodeKeyMap[videoType]"
            :highlightCurrent="true"
            :default-expanded-keys="[currentChapterId]"
            :default-checked-keys="[currentChapterId]"
            :expand-on-click-node="true"
            @handleNodeClick="handleClick"
            @node-expand="
              () => {
                $g.tool.renderMathjax()
              }
            "
          >
            <template #body="{ data }">
              <div class="flex items-center !text-[12px]">
                <el-scrollbar x-scrollable class="w-240px">
                  <div class="flex items-center gap-x-[5px]">
                    <div>
                      <g-mathjax
                        :text="
                          videoType == 3
                            ? data?.sysKnowledgePointName
                            : data?.sysTextbookCatalogName
                        "
                        class="!w-max"
                      />
                    </div>
                    <div>({{ data.videoCount }})</div>
                  </div>
                </el-scrollbar>
              </div>
            </template>
          </g-tree>
        </el-scrollbar>
      </div>
      <div class="w-2px bg-[#ccc] mx-10px" />
      <div class="flex-1">
        <!-- 视频列表 -->
        <g-table :tableOptions="tableOptions" :height="480">
          <template #header-right>
            <div class="flex-cc">
              <n-button
                text
                type="primary"
                class="mr-16px"
                :loading="selectAllLoading"
                @click="handleBindAll"
              >
                <g-icon name="ri-add-line" color="var(--g-primary)" size="15" />
                本节全选
              </n-button>

              <n-input
                class="!w-230px"
                v-model:value="keyword"
                placeholder="请输入视频关键词搜索"
                clearable
                @clear="onSearch"
                @keydown.enter="onSearch"
              />
              <n-button class="ml-10px" type="primary" @click="onSearch">
                搜索
              </n-button>
            </div>
          </template>
          <template #cz="{ row }">
            <n-space justify="center" class="h-30px">
              <n-button type="primary" text @click="onPlay(row)">播放</n-button>
              <n-button
                type="error"
                @click="handleUnbindVideo([row.videoResourceId])"
                text
                v-if="bindIds.includes(row.videoResourceId)"
                :loading="row.bindVideoLoading"
              >
                取消选择
              </n-button>
              <n-button
                v-else
                type="primary"
                @click="handleBindVideo([row.videoResourceId])"
                :loading="row.bindVideoLoading"
                text
              >
                选择此资源
              </n-button>
            </n-space>
          </template>
        </g-table>
      </div>
    </div>
    <!-- 视频播放预览 -->
    <VideoDialog :url="videoUrl" v-model:show="showVideoDialog" :live="isXKW" />
  </div>
</template>

<script setup lang="ts">
import {
  getVersionListApi,
  getChapterListApi,
  getOnionChapterListApi,
  getChapterVideoListApi,
  getOnionChapterVideoListApi,
  getVideoPlayUrlApi,
  getKnowledgeListApi,
  getKnowledgeVideoListApi,
  bindResourceApi,
  unBindResourceApi,
} from "@/api/bookMgt"
import type { PropType } from "vue"
import { NTooltip } from "naive-ui"
const props = defineProps({
  bookCatalogId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
})
let isXKW = $ref(false)
let selectAllLoading = $ref(false)
let keyword = $ref("")
let videoType = $ref<any>("1")
let typeList = $ref<any>([
  {
    label: "四中资源",
    value: "1",
  },
  {
    label: "学科网",
    value: "2",
  },
  {
    label: "知识点视频",
    value: "3",
  },
  {
    label: "章节同步视频",
    value: "4",
  },
  {
    label: "洋葱资源",
    value: "5",
  },
])
let bindIds: any = defineModel("bind")
function renderTooltipOption({ node, option }: { node: any; option: any }) {
  return h(NTooltip, null, {
    trigger: () => node,
    default: () => option.label,
  })
}
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      type: "index",
      label: "序号",
    },
    {
      prop: "fileName",
      label: "视频名称",
    },
    {
      prop: "fileSize",
      label: "大小",
      formatter(row) {
        return Math.floor(row?.fileSize / 1024 / 1024) + "MB"
      },
    },
    {
      prop: "createTime",
      label: "上传时间",
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
})
let treeData = $ref<any>([])
let videoUrl = $ref("")
let showVideoDialog = $ref(false)
let versionList = $ref<any>([])
let currentBookId = $ref<any>(null)
let treeLoading = $ref(false)
let currentChapterId = $ref<any>(null)
let videoList = $ref<any>([]) //全部的视频数据
const route = useRoute()
let nodeKeyMap = {
  1: "sysTextbookCatalogId",
  2: "sysTextbookCatalogId",
  3: "sysKnowledgePointId",
  4: "sysTextbookCatalogId",
  5: "onionChapterSubSectionId",
}
onMounted(() => {
  fetchVersionList()
})
const emit = defineEmits(["needRefresh", "getBindVideoIds"])
/* 北京四中+学科网+系统教材章节列表 洋葱 */
async function fetchChapterList() {
  try {
    treeLoading = true
    treeData = []
    let url = "" as any
    let data = {} as any
    switch (videoType) {
      case "3":
        url = getKnowledgeListApi
        data = { sysCourseId: route.query.sysCourseId }
        break
      case "5":
        url = getOnionChapterListApi
        data = { sysTextbookId: currentBookId }
        break
      default:
        url = getChapterListApi
        data = { sysTextbookId: currentBookId, type: videoType }
        break
    }

    const res = await url(data)
    if ($g.tool.isTrue(res)) {
      const list = videoType == 5 ? processChapters(res) : res
      const attrName = nodeKeyMap[videoType]
      treeData = filterTreeData(list)
      getFirstChild(treeData, attrName)
    }
    treeLoading = false
    if (/^[0-9]*$/.test(currentChapterId)) {
      await fetchVideoList()
    }
  } catch (err) {
    treeLoading = false
    treeData = []
    console.log("获取章节列表失败", err)
  }
}
// 递归函数，用于遍历和处理 JSON 数据
function processChapters(chapters) {
  return chapters.map((chapter) => {
    // 深拷贝当前章节对象，避免修改原数据
    const newChapter = { ...chapter }
    // 如果当前章节没有 onionChapterSubSectionId，生成一个 UUID
    if (!newChapter.onionChapterSubSectionId) {
      newChapter.onionChapterSubSectionId = generateUUID(6)
    }
    // 递归处理子章节
    if (newChapter.children && newChapter.children.length > 0) {
      newChapter.children = processChapters(newChapter.children)
    }
    return newChapter
  })
}

function getFirstChild(list, attrName) {
  return (
    list?.some((v) => {
      if (!v.children?.length) {
        currentChapterId = v[attrName]
        return true
      }
      return getFirstChild(v.children, attrName)
    }) || false
  )
}

function filterTreeData(list) {
  list.forEach((v) => {
    if (v.children?.length) {
      v.children = filterTreeData(v.children)
    }
    v.videoCount = v.children?.length
      ? v.children?.reduce((p, next) => {
          return p + next.videoCount
        }, 0)
      : v.videoCount
  })
  return list.filter((v) => v.videoCount !== 0)
}

function generateUUID(length) {
  const num = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
  let str = ""
  for (let i = 0; i < length; i++) {
    str += num.charAt(Math.floor(Math.random() * num.length))
  }
  return str
}
/* 获取视频列表 */
async function fetchVideoList() {
  let url = "" as any
  let data = {} as any

  switch (videoType) {
    case "3":
      url = getKnowledgeVideoListApi
      data = { sysKnowledgePointId: currentChapterId }
      break
    case "5":
      url = getOnionChapterVideoListApi
      data = { onionChapterSubSectionId: currentChapterId }
      break
    default:
      url = getChapterVideoListApi
      data = { sysTextbookCatalogId: currentChapterId, type: videoType }
      break
  }
  try {
    let res = await url(data)
    videoList = res ?? []
    tableOptions.data = videoList.filter((v) => v.fileName.includes(keyword))
  } catch (err) {
    tableOptions.data = []
    videoList = []
    console.log("获取视频列表失败", err)
  }
}

/* 教材版本 */
async function fetchVersionList() {
  try {
    if (videoType == 2) {
      isXKW = true
    } else {
      isXKW = false
    }
    treeLoading = true
    treeData = []
    tableOptions.data = []
    videoList = []
    let res = await getVersionListApi({
      sysCourseId: route.query.sysCourseId,
      type: videoType,
    })
    versionList =
      res
        ?.map((v) => {
          return v?.textbookList?.map((vv) => {
            return {
              ...vv,
              label: `${vv?.sysTextbookVersionName}-${vv?.sysTextbookName}`,
            }
          })
        })
        .flat() ?? []
    currentBookId = versionList?.[0]?.sysTextbookId ?? null
    if (currentBookId || videoType == 3) {
      await fetchChapterList()
    }
    if (!currentBookId) treeLoading = false
  } catch (err) {
    treeLoading = false
    treeData = []
    console.log("获取教材版本失败", err)
  }
}
/* 章节点击 */
async function handleClick(data, node) {
  if (data.children?.length) {
    return
  }
  if (node.checked) {
    switch (videoType) {
      case "3":
        currentChapterId = data.sysKnowledgePointId
        break
      case "5":
        currentChapterId = data.onionChapterSubSectionId
        break
      default:
        currentChapterId = data.sysTextbookCatalogId
        break
    }

    if (/^[0-9]*$/.test(currentChapterId)) {
      await fetchVideoList()
    } else {
      tableOptions.data = []
      videoList = []
    }
  }
}
/** 视频播放 */
async function onPlay(item) {
  try {
    let res = await getVideoPlayUrlApi({
      videoResourceId: item.videoResourceId,
    })
    if (res) {
      videoUrl = res
      showVideoDialog = true
    }
  } catch (err) {
    console.log("获取视频地址失败", err)
  }
}
/* 选择资源 */
async function handleBindVideo(id) {
  try {
    await bindResourceApi({
      bookId: route.query.bookId,
      bookCatalogId: props.bookCatalogId,
      videoResourceIdList: id,
    })
    await emit("getBindVideoIds")
    emit("needRefresh")
    $g.msg("操作成功")
  } catch (err) {
    console.log(err)
  }
}
/* 取消资源绑定 */
async function handleUnbindVideo(id) {
  try {
    await unBindResourceApi({
      bookId: route.query.bookId,
      bookCatalogId: props.bookCatalogId,
      videoResourceIdList: id,
    })
    await emit("getBindVideoIds")
    emit("needRefresh")
    $g.msg("操作成功")
  } catch (err) {
    console.log(err)
  }
}

/* 全选 */
async function handleBindAll() {
  let ids = tableOptions.data
    .filter((v) => {
      return !bindIds.value.includes(v.videoResourceId)
    })
    .map((v) => v.videoResourceId)
  await handleBindVideo(ids)
}
/* 搜索 */
function onSearch() {
  tableOptions.data = videoList.filter((v) => v.fileName.includes(keyword))
}
</script>

<style lang="scss" scoped>
:deep() {
  .n-scrollbar-container {
    background: rgba(255, 255, 255, 0) !important;
  }
  .el-tree-node__content {
    height: auto;
    background-color: transparent !important;
  }
  .el-dropdown-link {
    outline: none;
  }
  .el-tabs__item {
    font-size: 18px;
  }

  .is-checked {
    .el-tree-node__content {
      background-color: #f1f8ff !important;
    }
    .el-tree-node__children {
      .el-tree-node__content {
        background-color: transparent !important;
      }
    }
  }
}
</style>
