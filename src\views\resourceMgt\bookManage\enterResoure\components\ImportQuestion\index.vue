<template>
  <g-dialog
    :title="title"
    v-bind="$attrs"
    :show-footer="false"
    width="1400"
    :on-after-enter="handleModalOpen"
    @close-x="handleClose"
  >
    <el-tabs
      v-model="activeName"
      class="demo-tabs"
      @tab-change="handleTabChange"
    >
      <el-tab-pane
        v-for="item in tabs"
        :key="item.value"
        :label="item.label"
        :name="item.value"
      ></el-tab-pane>
    </el-tabs>
    <template v-if="activeName == 'PaperSource'">
      <div class="h-[720px]">
        <!-- 资源列表 -->
        <ResourceList
          v-show="activeType == 'ResourceList'"
          @changeBookId="changeBookId"
        />
        <!-- 试题列表 -->
        <QuestionList
          v-if="activeType == 'QuestionList'"
          :checkedBookId="checkedBookId"
          :bookCatalogId="bookCatalogId"
          @sourceNumberChange="sourceNumber = $event"
          @needRefresh="refreshFlag = true"
          @back="handleBack"
        />
      </div>
    </template>
    <template v-else-if="activeName == 'VideoResource'">
      <div class="h-[720px]">
        <VideoResource
          :bookCatalogId="bookCatalogId"
          @needRefresh="refreshFlag = true"
          @getBindVideoIds="getBindVideoIds"
          v-model:bind="bindIds"
        />
      </div>
    </template>
  </g-dialog>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
import QuestionList from "./components/QuestionList.vue"
import ResourceList from "./components/ResourceList.vue"
import VideoResource from "./components/VideoResource.vue"
import { getBindResourceApi } from "@/api/bookMgt"

const props = defineProps({
  bookCatalogId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
})
let bindIds = $ref<any>([])
let activeName = $ref<any>("PaperSource")
const tabs = $ref<any>([
  {
    label: "书籍/试卷资源",
    value: "PaperSource",
  },
  {
    label: "视频资源",
    value: "VideoResource",
  },
])
const emits = defineEmits(["refreshQuestionList"])
const route = useRoute()
let activeType = $ref("ResourceList")
// 当前进入的资源ID
let checkedBookId = $ref(null)
// 标志是否需要刷新外部试题列表，如果进行过编辑操作， 则需要刷新列表
let refreshFlag = $ref(false)
//已选择资源个数
let sourceNumber = $ref<number>(0)

const title = $computed(() => {
  return activeType === "ResourceList"
    ? "选择资源"
    : `选择资源（${sourceNumber}个）`
})
watch(
  () => bindIds,
  () => {
    tabs[1].label = `视频资源(${bindIds.length})`
  },
)
/* 获取当前章节绑定的资源ID列表 */
async function getBindVideoIds() {
  try {
    let res = await getBindResourceApi({
      bookId: route.query.bookId,
      bookCatalogId: props.bookCatalogId,
    })
    bindIds = res
  } catch (err) {
    console.log("获取绑定的资源失败", err)
  }
}
/* 选中书籍进入资源 */
function changeBookId(val) {
  checkedBookId = val
  activeType = "QuestionList"
}
/* 切换tab */
function handleTabChange() {}
/** 书籍返回到资源列表 */
function handleBack() {
  activeType = "ResourceList"
  checkedBookId = null
}

// 弹窗打开后的回调
function handleModalOpen() {
  refreshFlag = false
  getBindVideoIds()
}

// 弹窗关闭处理函数，如果编辑过数据，需要外部的刷新试题列表
function handleClose() {
  refreshFlag && emits("refreshQuestionList")
}
</script>

<style lang="scss" scoped></style>
