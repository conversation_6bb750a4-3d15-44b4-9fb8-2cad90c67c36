<template>
  <div v-loading="loading" ref="listRef">
    <template
      v-if="data.length"
      v-for="item in data"
      :key="item.bookCatalogQuestionId || item.questionId"
    >
      <QuestionExhibition
        :question="item"
        @fast-gpt="fastGpt"
        @open-ai-explain="openAiExplain"
        @get-list="() => emit('changePage')"
        :url-list="urlList"
        :show-audio="showAudioBtn"
        :show-add-knowledge="true"
        :id="'id' + item.questionId"
        :disabled="disabled"
      >
        <template #start-header>
          <slot name="start-header" :row="item"></slot>
        </template>
        <template #left-header>
          <slot name="left-header" :row="item"></slot>
        </template>
        <template #action>
          <slot name="action" :row="item"></slot>
        </template>
        <template #custom>
          <slot name="custom" :row="item"></slot>
        </template>
        <template #title-right>
          <slot name="title-right" :row="item"></slot>
        </template>
      </QuestionExhibition>
    </template>
    <g-empty v-else></g-empty>
    <g-page
      v-if="$g.tool.isTrue(pageOptions)"
      :pageOptions="pageOptions"
      @change="changePage"
    ></g-page>
  </div>
  <!-- 播放音频 -->
  <Speaker
    v-model:show="showAudio"
    :showText="showText"
    :width="width"
    ref="speakerRef"
  />
</template>

<script setup lang="ts">
import Speaker from "./SubtitleSpeaker.vue"
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  pageOptions: {
    type: Object,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  urlList: {
    type: Array,
    default: () => [],
  },
  expandTree: {
    type: Boolean,
    default: false,
  },
  showAudioBtn: {
    type: Boolean,
    default: false,
  },
  showText: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})
let listRef = $ref<any>(null)
let showAudio = $ref(false)
let speakerRef = $ref<any>(null)
const emit = defineEmits(["changePage", "fastGpt", "openAiExplain"])
function changePage() {
  emit("changePage")
}
function fastGpt(id, url, item) {
  emit("fastGpt", id, url, item)
}

function openAiExplain(currentSubParse) {
  emit("openAiExplain", currentSubParse)
}

let width = $ref(0)

onMounted(() => {
  watch(
    () => props.expandTree,
    (val) => {
      setTimeout(() => {
        if (listRef) {
          width = listRef.clientWidth
        }
      }, 120)
    },
    {
      immediate: true,
    },
  )
})
</script>

<style lang="scss" scoped></style>
