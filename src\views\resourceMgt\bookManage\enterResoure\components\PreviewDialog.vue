<template>
  <g-dialog
    title="预览"
    v-model:show="showDialog"
    :show-footer="false"
    :width="800"
  >
    <div class="flex items-center justify-center w-full">
      <template v-if="fileType == 'video'">
        <g-video
          :url="fileInfo.fileAbsoluteUrl || fileInfo.fullUrl"
          :config="{
            autoplay: false,
          }"
        ></g-video>
      </template>
      <template v-else-if="fileType == 'img'">
        <n-image
          width="400"
          :preview-disabled="notPreview"
          :src="fileInfo.fileAbsoluteUrl || fileInfo.fullUrl"
        />
      </template>

      <template v-else-if="fileType == 'audio'">
        <div class="flex justify-center">
          <audio :src="fileInfo.fileAbsoluteUrl || fileInfo.fullUrl" controls />
        </div>
      </template>
      <template v-else>
        <iframe
          class="w-full h-[700px]"
          :src="
            $g.tool.getWeb365Url(fileInfo.fileAbsoluteUrl || fileInfo.fullUrl)
          "
          frameborder="0"
        >
        </iframe>
      </template>
    </div>
  </g-dialog>
</template>

<script setup lang="ts">
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  fileInfo: {
    type: Object,
    default: () => {},
  },
  notPreview: {
    type: Boolean,
    default: true,
  },
})
const emit = defineEmits(["update:show"])
const showDialog = useVModel(props, "show", emit)
const fileType = $computed(() => {
  return $g.tool.getFileType(
    props.fileInfo.fileExtension || props.fileInfo.suffix,
  )
})
</script>

<style lang="scss" scoped></style>
