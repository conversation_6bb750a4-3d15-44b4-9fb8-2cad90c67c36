<template>
  <g-dialog
    title="试题查看"
    v-bind="$attrs"
    :show-footer="false"
    width="1000"
    :on-after-enter="open"
    :on-before-leave="leave"
  >
    <el-scrollbar class="h-[600px]" v-loading="loading">
      <template v-if="!loading">
        <QuestionExhibition :question="data">
          <template #title-right>
            <span v-if="data.score" class="text-[#d9001b]"
              >{{ data.score }}分
            </span>
          </template>
        </QuestionExhibition>
      </template>
    </el-scrollbar>
  </g-dialog>
</template>

<script setup lang="ts">
import { getQuestionDetail } from "@/api/bookMgt"
const props = defineProps({
  questionInfo: {
    type: Object,
    default: () => {},
  },
})
let loading = $ref(true)
let data = $ref<any>({})
async function open() {
  try {
    loading = true
    let res = await getQuestionDetail({
      questionId: props.questionInfo.questionId,
      bookCatalogQuestionId: props.questionInfo.bookCatalogQuestionId,
    })
    data = res
    loading = false
  } catch (err) {
    loading = false
    console.log("获取试题详情失败", err)
  }
}
function leave() {
  loading = true
  data = {}
}
</script>

<style lang="scss" scoped></style>
