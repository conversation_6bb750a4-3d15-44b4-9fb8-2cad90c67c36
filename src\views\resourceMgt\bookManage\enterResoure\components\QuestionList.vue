<template>
  <div>
    <div class="w-full flex items-center justify-between">
      <div class="flex items-center">
        <n-input
          class="!w-230px"
          v-model:value="keyword"
          placeholder="输入试题ID/试题内容关键词搜索"
          clearable
          @clear="onClear"
          @keydown.enter="onSearch"
        ></n-input>
        <n-button class="ml-10px" type="primary" @click="onSearch"
          >搜索</n-button
        >
      </div>
      <n-space justify="center" align="center">
        <n-checkbox
          v-model:checked="isFilterNoKnowledge"
          @update:checked="onFilterNoKnowledge"
        >
          筛选无知识标签试题
        </n-checkbox>
        <n-button
          type="success"
          :disabled="resourceMgt.getDisabled"
          @click="$emit('openImportDialog')"
          >关联已有资源</n-button
        >
        <n-button
          v-if="route.query.bookSource == '4' || route.query.bookSource == '5'"
          class="mr-10px"
          @click="syncQuestion"
          type="primary"
          text
          :disabled="resourceMgt.getDisabled"
          >同步试题</n-button
        >
        <n-button @click="onSetOrder" :disabled="resourceMgt.getDisabled"
          >排序调整(总分{{ totalScore }}分)</n-button
        >
        <n-button
          :disabled="resourceMgt.getDisabled"
          type="primary"
          @click="showXKWDialog = true"
        >
          学科网搜索
        </n-button>
        <n-button
          type="primary"
          @click="toAdd"
          :disabled="resourceMgt.getDisabled"
        >
          <g-icon name="ri-add-line" size="14" />
          新增试题
        </n-button>
      </n-space>
    </div>
    <div class="flex justify-between h-[20px] items-center mt-16px">
      <el-checkbox
        v-model="allCheck"
        @change="handleCheckAllChange"
        label="全选"
        size="large"
      />
      <n-button type="primary" text @click="batchDelete" :disabled="isDisabled">
        批量删除
      </n-button>
    </div>

    <List
      :data="tableOptions.data"
      :expandTree="expandTree"
      :loading="tableOptions.loading"
      :page-options="tableOptions.pageOptions"
      :show-audio-btn="true"
      :showText="showText"
      @change-page="getDirQuestionListApi(false)"
      :url-list="urlList"
      @fast-gpt="fastGpt"
      @open-ai-explain="openAiExplain"
      :disabled="resourceMgt.getDisabled"
    >
      <template #start-header="{ row }">
        <div class="h-24px overflow-hidden">
          <el-checkbox
            v-model="row.isChecked"
            :value="row.questionId"
            class="mt-[-8px]"
            size="large"
            @change="handleCheckedCitiesChange($event, row.questionId)"
          />
        </div>
      </template>
      <template #left-header="{ row }">
        <div class="flex items-center gap-x-[10px]">
          <div class="flex text-white cursor-pointer select-none rounded-[4px]">
            <el-badge :value="row.explains?.length" type="info">
              <div
                :class="
                  resourceMgt.getDisabled
                    ? '!cursor-not-allowed !bg-[#a7a9aa]'
                    : ''
                "
                class="bg-primary px-4px text-[#fff] rounded-l-[4px]"
                @click="openFilesDialog(row, 'explains')"
              >
                试题讲解
              </div>
            </el-badge>

            <div
              :class="
                resourceMgt.getDisabled
                  ? '!cursor-not-allowed !bg-[#a7a9aa]'
                  : ''
              "
              class="bg-success px-4px rounded-r-[4px]"
              @click="openAddFileDialog(row, 'explains')"
            >
              上传
            </div>
          </div>
          <div class="flex text-white cursor-pointer select-none">
            <el-badge :value="row.files?.length" type="info">
              <div
                :class="
                  resourceMgt.getDisabled
                    ? '!cursor-not-allowed !bg-[#a7a9aa]'
                    : ''
                "
                class="bg-[#4FADFF] px-4px rounded-l-[4px]"
                @click="openFilesDialog(row, 'files')"
              >
                文档
              </div>
            </el-badge>

            <div
              class="bg-success px-4px rounded-r-[4px]"
              :class="
                resourceMgt.getDisabled
                  ? '!cursor-not-allowed !bg-[#a7a9aa]'
                  : ''
              "
              @click="openAddFileDialog(row, 'files')"
            >
              上传
            </div>
          </div>
          <n-button
            type="primary"
            :disabled="resourceMgt.getDisabled"
            text
            @click="toDesc(row)"
            >共{{ row.describeCount }}份描述
            <g-icon name="arrow-right-s-line" size="" color="" />
          </n-button>
          <n-tag
            :bordered="false"
            :color="sourceTagStyle[row.sourceName] || sourceTagStyle.default"
          >
            {{ row.sourceName }}
          </n-tag>
          <div class="text-[#FF7D29]" v-if="row.sysQuestionDifficultyId">
            题目难度：{{ difficultyMap[row.sysQuestionDifficultyId] }}
          </div>
        </div>
      </template>
      <template #action="{ row }">
        <n-space justify="center" align="center">
          <div class="flex items-center">
            <div class="flex-shrink-0">难度：</div>
            <n-select
              v-model:value="row.sysQuestionDifficultyId"
              :options="questionDifficultyList"
              placeholder="请选择难度"
              label-field="sysQuestionDifficultyName"
              value-field="sysQuestionDifficultyId"
              class="w-[120px]"
              @update:value="updateQuestionDifficultyApi(row)"
              :disabled="resourceMgt.getDisabled"
            />
          </div>
          <n-button
            type="primary"
            :disabled="resourceMgt.getDisabled"
            text
            @click="jumpAutoCorrecting(row)"
          >
            自动批改
          </n-button>

          <n-popover
            trigger="hover"
            placement="bottom"
            :disabled="resourceMgt.getDisabled"
          >
            <template #trigger>
              <!-- 试题审核状态 0-未发布,2-已发布  -->
              <n-button
                :disabled="resourceMgt.getDisabled"
                :type="row.state === 2 ? 'success' : 'error'"
                text
              >
                {{ row.state === 2 ? "已审核" : "未审核" }}
                <g-icon name="arrow-down-s-line" size="" color="" />
              </n-button>
            </template>
            <template #default>
              <n-button
                :type="row.state === 2 ? 'error' : 'success'"
                text
                :loading="row.auditLoading"
                @click="handleQuestionAudit(row)"
              >
                {{ row.state === 2 ? "未审核" : "已审核" }}
              </n-button>
            </template>
          </n-popover>

          <n-button
            type="primary"
            text
            @click="editQues(row)"
            :disabled="resourceMgt.getDisabled"
            >编辑</n-button
          >

          <n-popover
            trigger="hover"
            placement="bottom"
            :disabled="resourceMgt.getDisabled"
            v-if="route.name == 'EnterResoure'"
          >
            <template #trigger>
              <!-- 试题审核状态 0-未发布,2-已发布  -->
              <n-button type="primary" :disabled="resourceMgt.getDisabled" text>
                更多
                <g-icon name="arrow-down-s-line" size="" color="" />
              </n-button>
            </template>
            <template #default>
              <div class="mb-5px">
                <n-button
                  :type="row.formulaImageScanState == 2 ? 'success' : 'primary'"
                  text
                  @click="move(row)"
                >
                  移动
                </n-button>
              </div>
              <div>
                <n-button
                  :type="row.formulaImageScanState == 2 ? 'success' : 'primary'"
                  text
                  @click="editIdentify(row)"
                >
                  公式识别({{
                    row.formulaImageScanState == 2 ? "已识别" : "未识别"
                  }})
                </n-button>
              </div>
              <div class="my-5px">
                <n-button type="primary" text @click="editAiGuide(row)">
                  {{ Number(row.flowState) ? "(已发布)" : "" }}引导式讲解
                </n-button>
              </div>
              <div
                class="my-5px"
                v-if="['4'].includes(route.query.bookSource as any)"
              >
                <n-button type="primary" text @click="toAnswerDetail(row)"
                  >作答详情</n-button
                >
              </div>
              <div>
                <n-button type="error" text @click="delQues(row, 3)"
                  >删除</n-button
                >
              </div>
            </template>
          </n-popover>
        </n-space>
      </template>
      <template #custom="{ row }">
        <div class="mt-10px flex items-center gap-[40px] flex-wrap">
          <div class="flex items-center gap-x-[10px]">
            <span class="flex-shrink-0">关键能力:</span>
            <n-tree-select
              multiple
              v-model:value="row.sysCourseAbilityIdList"
              :options="abilityList"
              :render-label="renderLabel"
              placeholder="请选择关键能力"
              class="w-[220px]"
              :disabled="resourceMgt.getDisabled"
              @update:value="saveAbility(row)"
            />
          </div>
          <div class="flex items-center gap-x-[10px]">
            <span class="flex-shrink-0">学科素养:</span>
            <n-tree-select
              multiple
              v-model:value="row.sysCourseLiteracyIdList"
              :options="literacyList"
              :render-label="renderLabel"
              placeholder="请选择学科素养"
              class="w-[220px]"
              :disabled="resourceMgt.getDisabled"
              @update:value="saveLiteracy(row)"
            />
          </div>
          <div class="flex items-center gap-x-[10px]">
            <span class="flex-shrink-0">知识能力:</span>
            <n-tree-select
              multiple
              v-model:value="row.sysCourseKnowAbilityIdList"
              :options="knowledgeAbilityList"
              :render-label="renderLabel"
              placeholder="请选择知识能力"
              class="w-[220px]"
              :disabled="resourceMgt.getDisabled"
              @update:value="saveKnowledge(row)"
            />
          </div>
          <div
            v-show="
              row?.subQuestions?.[row.currentClickSubIdx]?.reasonableMinTime
            "
            class="flex items-center gap-x-[10px]"
          >
            <span class="flex-shrink-0">常规应答时间:</span>
            <span
              >{{
                row?.subQuestions?.[row.currentClickSubIdx]?.reasonableMinTime
              }}-{{
                row?.subQuestions?.[row.currentClickSubIdx]?.reasonableMaxTime
              }}秒</span
            >
          </div>
        </div>
      </template>
      <template #title-right="{ row }">
        <span v-if="row.score" class="text-[#d9001b]">{{ row.score }}分 </span>
      </template>
    </List>
    <IframeDialog
      v-model:show="dialogVisible2"
      :url="currentUrl"
      @refresh="getDirQuestionListApi(false)"
    />
    <AddFileDialog
      v-model:show="showAddFile"
      @refresh="getDirQuestionListApi(false)"
      :questionId="questionId"
      :type="fileType"
    />
    <FilesDialog
      v-model:show="showFiles"
      :data="files"
      :type="fileType"
      @refresh="getDirQuestionListApi(false)"
    />
    <template v-if="explainDialogVisible">
      <AiExplainDialog
        v-model:show="explainDialogVisible"
        :subQuestionParseId="currentSubParse?.subQuestionParseId"
        @refresh="getDirQuestionListApi(false)"
        @goAiDiagnosis="goAiDiagnosis"
      />
    </template>
    <AiDiagnosisDialog v-model:show="dialogVisible3" :url="diagnosisUrl" />
    <FormulaIdentifyDialog
      v-model:show="identifyDialogVisible"
      :question-id="identifyQuestionId"
      @refresh="getDirQuestionListApi(false)"
    />
    <g-dialog
      :title="`排序调整(总分${dynamicsScore}分)`"
      v-model:show="showOrder"
      width="800"
      @confirm="confirmOrder"
    >
      <div class="flex flex-col items-center">
        <div
          class="flex items-center font-bold h-30px leading-[30px] gap-x-[20px] gap-y-[10px] w-full pl-50px pr-20px"
        >
          <div class="w-20px"></div>
          <div class="w-100px text-center">序号</div>
          <div class="w-100px text-center">原有序号</div>
          <div class="flex-1 text-center px-10px">题型</div>
          <div class="w-200px text-center">分值</div>
        </div>
        <el-scrollbar height="500px" class="w-full pl-50px pr-20px">
          <Draggable
            v-model="orderList"
            group="people"
            @start="drag = true"
            @end="drag = false"
            item-key="bookCatalogQuestionId"
            :force-fallback="true"
            :scroll-sensitivity="60"
            @change="onMoveCallback"
          >
            <template #item="{ element, index }">
              <div
                class="flex items-center font-bold leading-[32px] gap-x-[20px] mt-10px select-none pb-5px"
                style="border-bottom: 1px solid #ccc"
              >
                <g-icon name="draggable" size="" color="" />
                <div class="w-100px text-center" style="border: 1px solid #ccc">
                  <n-button
                    type="primary"
                    text
                    @click="openPreviewQuestionDialog(element)"
                    >{{ index + 1 }}</n-button
                  >
                </div>
                <div class="w-100px text-center" style="border: 1px solid #ccc">
                  {{ element.ordinal }}
                </div>
                <div
                  class="flex-1 text-center px-10px"
                  style="border: 1px solid #ccc"
                >
                  {{ element.sysQuestionTypeName ?? "-" }}
                </div>
                <div class="w-200px text-center">
                  <div
                    v-for="(item, index) in element.subQuestionList"
                    :key="item.subQuestionId"
                    class="flex items-center"
                    :class="element.subQuestionList.length > 1 ? 'mb-10px' : ''"
                  >
                    <div class="w-60px flex-shrink-0 text-[#333] font-normal">
                      ({{ index + 1 }})
                    </div>
                    <n-input-number
                      v-model:value="item.score"
                      clearable
                      :min="1"
                      :show-button="false"
                    />
                  </div>
                </div>
              </div>
            </template>
          </Draggable>
        </el-scrollbar>
      </div>
    </g-dialog>
    <!-- 关联已有试题 -->
    <!-- <ImportQuestion
      v-model:show="showImportQuestion"
      :bookCatalogId="chapterId"
      @refreshQuestionList="handleRefresh"
    /> -->

    <div
      class="fixed z-[99999] left-0 right-0 top-0 bottom-0"
      element-loading-text="同步中..."
      element-loading-background="rgba(0, 0, 0, 0.5)"
      v-show="testLoading"
      v-loading="testLoading"
    ></div>
    <!-- 文章移动 -->
    <ChapterDialog
      v-model:show="showChapterDialog"
      :chapter-id="activeChapterId"
      :book-catalog-questionId="bookCatalogQuestionId"
      move-type="question"
      @refresh="getDirQuestionListApi"
      @refreshTree="refreshTree"
    />
    <PreviewQuestion
      v-model:show="showQuestionPreview"
      :question-info="previewQuestionInfo"
    />
    <XKWDialog
      v-model:show="showXKWDialog"
      @importQuestion="importQuestion"
      :chapterId="chapterId"
      from="questionList"
    />
  </div>
</template>

<script setup lang="ts">
import { useResourceMgtStore } from "@/stores/modules/resourceMgt"
import GTooltip from "@/components/global/g-tooltip/index.vue"
import type { PropType } from "vue"
import {
  getDirQuestionList,
  setOrdinal,
  deleteQuestion,
  getQuestionOrdinal,
  getBookQuestionScore,
} from "@/api/resourceMgt"
import {
  getIframeConfig,
  getSyncAdmin,
  auditQuestion,
  getSubjectLiteracyList,
  getSubjectAbilityList,
  updateQuestionSubjectAbility,
  updateQuestionSubjectLiteracy,
  updateQuestionKnowledgeAbility,
  getQuestionDifficultyListApi,
  updateQuestionDifficulty,
  getPage,
  getQuestionIdList,
  bindQuestion,
  deleteQuestionList,
} from "@/api/bookMgt"
import { getKnowledgeAbilityList } from "@/api/resourceMgt"
import IframeDialog from "./IframeDialog.vue"
import FilesDialog from "./FilesDialog.vue"
import AddFileDialog from "./AddFileDialog.vue"
import PreviewQuestion from "./PreviewQuestion.vue"
import List from "./List.vue"
import Draggable from "vuedraggable"
import AiExplainDialog from "./AiExplainDialog.vue"
import AiDiagnosisDialog from "./AiDiagnosisDialog.vue"
import ImportQuestion from "./ImportQuestion/index.vue"
import FormulaIdentifyDialog from "./FormulaIdentifyDialog.vue"
import XKWDialog from "@/views/resourceMgt/paperEdit/components/XKWDialog.vue"
import { sourceTagStyle } from "../../constant"
import ChapterDialog from "./ChapterDialog.vue"
import { useSpeakerStore } from "@/stores/modules/speaker"

const resourceMgt = useResourceMgtStore()
let showQuestionPreview = $ref(false)
let speakerStore = useSpeakerStore()
const props = defineProps({
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
  expandTree: {
    type: Boolean,
    default: true,
  },
  treeData: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
  dirName: {
    type: String,
    default: "",
  },
})
let isFilterNoKnowledge = $ref(false) // 筛选无知识标签试题
let testLoading = $ref(false)
let urlList: any = $ref([])
let dialogVisible2 = $ref(false)
let showText = $ref(true) //是否显示字幕
const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: true,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },

  data: [] as any[],
})
let drag = $ref(false)
let keyword = $ref("")
const route = useRoute()
const router = useRouter()
const difficultyMap = $ref({
  17: "C-",
  18: "C",
  19: "B",
  20: "A",
  21: "A+",
})
let showOrder = $ref(false)
let orderList = $ref<any>([])
let explainDialogVisible = $ref(false)
let currentUrl = $ref("")
let files = $ref<any[]>([])
let showFiles = $ref(false)
let fileType = $ref<any>(null)
let showAddFile = $ref(false)
let questionId = $ref<any>("")
const emit = defineEmits(["refreshTree", "openImportDialog"])
let showImportQuestion = $ref(false) // 关联已有试题弹窗
function renderLabel({ option }) {
  return h(GTooltip, { content: option.label, refName: String(option.key) }, {})
}
let literacyList = $ref<any>([]) //学科素养
let abilityList = $ref<any>([]) //学科能力
let knowledgeAbilityList = $ref<any>([]) //知识能力
let questionDifficultyList = $ref<any>([]) //题目难度列表

let showChapterDialog = $ref(false)
let activeChapterId = $ref<any>(null)
let bookCatalogQuestionId = $ref<any>(null) //题目ID
let sysCourseId = $ref(route.query.sysCourseId)
let totalScore = $ref(0) //总分
let previewQuestionInfo = $ref<any>({}) //预览试题信息
let showXKWDialog = $ref(false) // 学科网搜索
let dynamicsScore = $computed(() => {
  let score = 0
  if (orderList.length) {
    orderList.forEach((item) => {
      item.subQuestionList.forEach((subItem) => {
        score += subItem.score
      })
    })
  }
  return score
})
provide("changeShowText", changeShowText)

function changeShowText(show) {
  showText = show
}
getQuestionDifficultyListApiApi()
/* 获取题目难度列表 */
async function getQuestionDifficultyListApiApi() {
  let res = await getQuestionDifficultyListApi()
  questionDifficultyList = res
}
/* 更新题目难度 */
async function updateQuestionDifficultyApi(row) {
  await updateQuestionDifficulty({
    questionId: row.questionId,
    sysQuestionDifficultyId: row.sysQuestionDifficultyId,
  })
  $g.msg("更新成功")
}
/* 筛选无知识标签试题 */
async function onFilterNoKnowledge() {
  tableOptions.pageOptions.page = 1
  await getDirQuestionListApi()
}
onMounted(() => {
  getSubjectLiteracyListApi()
  getSubjectAbilityListApi()
  getKnowledgeAbilityListApi()
})
/* 跳转作答详情 */
function toAnswerDetail(row) {
  router.push({
    name: "AnswerDetails",
    query: {
      bookId: route.query.bookId,
      questionId: row.questionId,
      activeName:
        (route.query.category as any) == 2 ? "PaperManage" : "BookManage",
    },
  })
}
/* 打开试题查看弹窗 */
function openPreviewQuestionDialog(item) {
  previewQuestionInfo = item
  showQuestionPreview = true
}
/* 获取当前目录分数 */
async function getBookQuestionScoreApi() {
  try {
    let res = await getBookQuestionScore({
      bookCatalogId: props.chapterId,
      bookId: route.query.bookId,
    })
    totalScore = res || 0
  } catch (err) {
    console.log("获取当前目录分数失败", err)
  }
}
/* 移动 */
function move(row) {
  bookCatalogQuestionId = row.bookCatalogQuestionId
  activeChapterId = row.bookCatalogId
  showChapterDialog = true
}

/* 更新树 */
function refreshTree() {
  emit("refreshTree")
}

/* 保存学科素养 */
async function saveLiteracy(row) {
  await updateQuestionSubjectLiteracy({
    questionId: row.questionId,
    sysCourseLiteracyIdList: row.sysCourseLiteracyIdList,
  })
  $g.msg("更新成功")
}
/* 保存学科能力 */
async function saveAbility(row) {
  await updateQuestionSubjectAbility({
    questionId: row.questionId,
    sysCourseAbilityIdList: row.sysCourseAbilityIdList,
  })
  $g.msg("更新成功")
}
/* 保存知识能力 */
async function saveKnowledge(row) {
  await updateQuestionKnowledgeAbility({
    questionId: row.questionId,
    sysCourseKnowAbilityIdList: row.sysCourseKnowAbilityIdList,
  })
  $g.msg("更新成功")
}
/* 获取学科素养列表 */
async function getSubjectLiteracyListApi() {
  let res = await getSubjectLiteracyList({
    sysCourseId: sysCourseId,
  })
  literacyList = res ? transformDataStructure(res) : []
}
/* 获取学科能力列表 */
async function getSubjectAbilityListApi() {
  let res = await getSubjectAbilityList({
    sysCourseId: sysCourseId,
  })
  abilityList = res ? transformDataStructure(res) : []
}
/* 获取知识能力列表 */
async function getKnowledgeAbilityListApi() {
  let res = await getKnowledgeAbilityList({
    sysCourseId: sysCourseId,
  })
  knowledgeAbilityList = res ? transformDataStructure(res) : []
}

/* 递归处理数据 */
function transformDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.label = newItem.name
      newItem.key = newItem.id
      newItem.children = newItem.children?.length
        ? transformDataStructure(newItem.children)
        : null

      return newItem
    })
  } else if (typeof data === "object" && data !== null) {
    return Object.keys(data).reduce((obj, key) => {
      obj[key] = transformDataStructure(data[key])
      return obj
    }, {})
  }
  return data
}
/* 列表弹窗 */
function openFilesDialog(row, type) {
  if (resourceMgt.getDisabled) return
  files = row[type]
  fileType = type
  showFiles = true
}
/* 上传弹窗 */
function openAddFileDialog(row, type) {
  if (resourceMgt.getDisabled) return
  questionId = row.questionId
  fileType = type
  showAddFile = true
}
/* 试题列表 */
async function getDirQuestionListApi(isInitState = true) {
  try {
    tableOptions.loading = true
    //获取所有id
    getAllBookCatalogQuestionIds({
      bookCatalogId: props.chapterId,
      bookId: route.query.bookId,
      keyword: keyword,
      isFilter: isFilterNoKnowledge ? 2 : 1,
    })
    //重置批量删除数据
    if (isInitState) {
      allCheck = false
      deleteList = []
    }
    const data = await getDirQuestionList({
      bookCatalogId: props.chapterId,
      bookId: route.query.bookId,
      keyword: keyword,
      isFilter: isFilterNoKnowledge ? 2 : 1,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    if (data) {
      tableOptions.pageOptions.total = data.total
      tableOptions.data = data.list.map((v) => ({
        ...v,
        explains: v.questionFiles
          .filter((vv) => vv.type === 1)
          .map((vv) => ({
            ...vv,
            name: vv.fileName,
            id: vv.questionFileId,
            status: "finished",
          })),
        files: v.questionFiles
          .filter((vv) => vv.type === 2)
          .map((vv) => ({
            ...vv,
            name: vv.fileName,
            id: vv.questionFileId,
            status: "finished",
          })),
        currentClickSubIdx: 0, // 记录下当前点击的子题索引
        isChecked: allCheck || deleteList.includes(v.questionId),
      }))
      await getBookQuestionScoreApi()
      nextTick(() => {
        $g.tool.renderMathjax()
      })
    }
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
    tableOptions.data = []
  }
}
/* 跳转描述 */
function toDesc(row) {
  router.push({
    name: "PaperDescription",
    query: {
      bookId: route.query.bookId,
      questionId: row.questionId,
      activeName:
        (route.query.category as any) == 2 ? "PaperManage" : "BookManage",
    },
  })
}
// 跳转流程编辑页面
function editAiGuide(row) {
  router.push({
    name: "AiGuideDesign",
    query: {
      bookId: route.query.bookId,
      questionId: row.questionId,
      flowState: row.flowState,
      activeName:
        (route.query.category as any) == 2 ? "PaperManage" : "BookManage",
    },
  })
}

// 试题审核
async function handleQuestionAudit(question) {
  try {
    question.auditLoading = true
    await auditQuestion({
      questionId: question.questionId,
    })
    question.state = question.state === 2 ? 0 : 2
    question.showPopover = false
  } catch (err) {
    console.log("更改审核状态出错", err)
  } finally {
    question.auditLoading = false
  }
}

/* 跳转新增试题 */
function toAdd() {
  router.push({
    name: "PaperEdit",
    query: {
      bookCatalogId: props.chapterId,
      sysCourseId: route.query.sysCourseId,
      bookId: route.query.bookId,
      bookName: route.query.bookName,
      category: route.query.category,
      activeName:
        (route.query.category as any) == 2 ? "PaperManage" : "BookManage",
    },
  })
}
/* 编辑试题 */
function editQues(row) {
  router.push({
    name: "PaperEdit",
    query: {
      bookCatalogId: props.chapterId,
      questionId: row.questionId,
      sysCourseId: route.query.sysCourseId,
      bookId: route.query.bookId,
      bookCatalogQuestionId: row.bookCatalogQuestionId,
      category: route.query.category,
      activeName:
        (route.query.category as any) == 2 ? "PaperManage" : "BookManage",
    },
  })
}

function delQues(row, type) {
  let deleteStr = ""
  if (!props.chapterId) {
    deleteStr = `《${props.treeData.bookName}》，1个试题资源？`
  } else {
    const arr = props.dirName.split("/")
    const catalogName = arr[arr.length - 1]
    deleteStr = `是否要删除《${props.treeData.bookName}》${catalogName}，1个试题资源？`
  }
  const delType = type === 1 ? "试题讲解？" : type === 2 ? "文档？" : deleteStr
  $g.confirm({
    type: "warning",
    title: "提示",
    content: "是否要删除" + delType,
    positiveText: "确定",
    negativeText: "取消",
  })
    .then(async () => {
      if (type == 3) {
        await deleteQues(row)
      }
    })
    .catch(() => {})
}
/* 删除试题 */
async function deleteQues(row) {
  await deleteQuestion({
    questionId: row.questionId,
    bookCatalogId: row.bookCatalogId,
    bookId: route.query.bookId,
  })

  const { page, page_size, total } = tableOptions.pageOptions
  // 如果当前页是最后一页（非第一页），且最后一页只有一条数据被删除，则往前翻一页
  if (
    page > 1 &&
    page === Math.ceil(total / page_size) &&
    total % page_size === 1
  ) {
    tableOptions.pageOptions.page = page - 1
  }

  $g.msg("删除成功")
  await getDirQuestionListApi(false)
  emit("refreshTree")
  // await getDirectoryListApi()
}
/* 打开排序弹窗 */
async function onSetOrder() {
  if (!tableOptions.data.length) {
    $g.msg("没有数据", "error")
  } else {
    let res = await getQuestionOrdinal({
      bookCatalogId: props.chapterId,
      bookId: route.query.bookId,
    })
    orderList = res.map((v) => {
      return {
        ...v,
        newOrdinal: v.ordinal,
      }
    })

    showOrder = true
  }
}
function onMoveCallback(val) {
  orderList.forEach((v, i) => {
    v.newOrdinal = i + 1
  })
}
/* 排序调整 */

async function confirmOrder() {
  let list = orderList.map((v) => {
    return {
      bookCatalogQuestionId: v.bookCatalogQuestionId,
      questionId: v.questionId,
      subQuestionList: v.subQuestionList.map((vv) => {
        return {
          subQuestionId: vv.subQuestionId,
          score: vv.score,
        }
      }),
    }
  })
  await setOrdinal({
    bookCatalogId: props.chapterId,
    bookId: route.query.bookId,
    questionList: list,
  })
  await getDirQuestionListApi()
  $g.msg("调整成功", "success")
}
/* 复制并打开弹框 */
function fastGpt(id, url = "", item: any = "") {
  if (url) {
    if (item) {
      const { subQuestionId, subQuestionParseId } = item
      currentUrl = `${url}&questionID=${id}&subQuestionID=${subQuestionId}&solutionID=${subQuestionParseId}`
    } else {
      currentUrl = url
    }
  }
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard
      //题目数据未同步，暂时使用测试数据
      .writeText(`请回答试题ID为${id}的试题`)
      // .writeText(`请回答试题ID为1097210的试题`)
      .then(() => {
        $g.msg("试题ID复制成功")
        if (url) {
          dialogVisible2 = true
        }
      })
      .catch((err) => {
        $g.msg("试题ID复制失败！请手动复制", "error")
        console.error(err)
      })
  } else {
    fallbackCopyText(id, url)
  }
}
/* 复制兼容 */
function fallbackCopyText(text, url) {
  const textArea = document.createElement("textarea")
  textArea.value = `请回答试题ID为${text}的试题`
  // textArea.value = `请回答试题ID为1097210的试题`
  document.body.appendChild(textArea)
  textArea.select()
  try {
    document.execCommand("copy")
    $g.msg("试题ID复制成功")
    if (url) {
      dialogVisible2 = true
    }
  } catch (err) {
    $g.msg("试题ID复制失败！请手动复制", "error")
    console.error(err)
  }
  document.body.removeChild(textArea)
}

// ai讲解步骤的Id
let currentSubParse = $ref<any>()

function openAiExplain(data) {
  currentSubParse = data
  explainDialogVisible = true
}

// 测试AI诊断
let dialogVisible3 = $ref<boolean>(false)
let diagnosisUrl = $ref<any>("")
function goAiDiagnosis(text) {
  diagnosisUrl = `https://gpt.qimingdaren.com/chat/share?shareId=uf578n194wpt61fxg5f9hbzx&text=${text}`
  dialogVisible3 = true
}

function editIdentify(row) {
  identifyQuestionId = row.questionId
  identifyDialogVisible = true
}

let identifyDialogVisible = $ref<boolean>(false)
let identifyQuestionId = $ref<any>(null)
const onSearch = $g._.debounce(
  () => {
    tableOptions.pageOptions.page = 1
    getDirQuestionListApi()
  },
  500,
  {
    leading: true,
  },
)
watch(
  () => props.chapterId,
  async () => {
    tableOptions.pageOptions.page = 1
    questionPosition()
  },
  {
    immediate: true,
  },
)
function onClear() {
  nextTick(() => {
    onSearch()
  })
}

async function getIframeConfigList() {
  let res = await getIframeConfig()
  let newList: any = res.reverse()
  urlList = newList.map((item) => {
    let obj: any = {}
    switch (item.uniqueFlag) {
      case "XIAO_QI":
        obj = {
          url: item.iframe,
          name: "小启老师",
        }
        break
      case "XIAO_ZHI":
        obj = {
          url: item.iframe,
          name: "小智老师",
        }
        break
      case "XIAOMING":
        obj = {
          url: item.iframe,
          name: "小鸣老师",
        }
        break
      default:
        obj = null
        break
    }
    return obj
  })
  urlList = urlList.filter((item) => item || item === 0)
}
onBeforeMount(() => {
  getIframeConfigList()
})

function syncQuestion() {
  $g.confirm({
    content: "确定同步试题？",
  })
    .then(async () => {
      try {
        testLoading = true
        await getSyncAdmin({
          bookThirdUniqueId: route.query.thirdUniqueId,
          bookSource: route.query.bookSource,
        })
        testLoading = false
        $g.msg("同步试题完成", "success")
        await getDirQuestionListApi()
      } catch (e) {
        console.log(e)
        $g.msg("同步试题失败", "error")
        testLoading = false
      }
    })
    .catch(() => {})
}

// 刷新试题列表数据和树组件数据
async function handleRefresh() {
  emit("refreshTree")
  questionPosition()
}

//更新题目列表（根据情况进行题目定位）
async function questionPosition() {
  await setPage()
  await getDirQuestionListApi()
  //导入试题后定位到新试题
  nextTick(() => {
    if (resourceMgt.getQuestionId) {
      //滚到底部
      const dom: any = document.querySelector(`#id${resourceMgt.getQuestionId}`)
      dom?.scrollIntoView({
        behavior: "smooth",
        block: "center",
      })
    }
    resourceMgt.setQuestionId("")
  })
}

//设置页码为最后一页
async function setPage() {
  try {
    if (!resourceMgt.getQuestionId) return
    const res = await getPage({
      questionId: resourceMgt.getQuestionId,
      bookCatalogId: props.chapterId,
      bookId: route.query.bookId,
      keyword: keyword,
      isFilter: isFilterNoKnowledge ? 2 : 1,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    if (res.page) tableOptions.pageOptions.page = res.page
  } catch (error) {
    console.log("⚡ error ==> ", error)
  }
}

function importQuestion(res, type) {
  resourceMgt.setQuestionId(res.questionId)
  if (type == "import") {
    handleRefresh()
  } else {
    resourceMgt.setQuestion(res)
    toAdd()
  }
}

// 进入试题编辑的时候，进行了缓存
onActivated(() => {
  handleRefresh()
  speakerStore.reset()
})

function jumpAutoCorrecting(row) {
  router.push({
    name: "AutoCorrecting",
    query: {
      bookId: route.query.bookId,
      questionId: row.questionId,
      activeName:
        (route.query.category as any) == 2 ? "PaperManage" : "BookManage",
    },
  })
}

/*批量删除功能开始*/
let allCheck = $ref(false)
let deleteList = $ref<any[]>([])
let allBookCatalogQuestionIds = $ref<any[]>([]) //当前目录所有资源id
const isDisabled = $computed(() => {
  return !deleteList.length
})

//获取当前目录所有资源id
async function getAllBookCatalogQuestionIds(params) {
  allBookCatalogQuestionIds = await getQuestionIdList(params)
}

//全选
function handleCheckAllChange(val) {
  // 全选之后需将所有题目id放入deleteList（请求接口获取）
  allCheck = val
  if (val) {
    deleteList = allBookCatalogQuestionIds
    $g.msg(`已选择${allBookCatalogQuestionIds.length}个资源`, "info")
  } else {
    deleteList = []
  }
  tableOptions.data.forEach((item) => {
    item.isChecked = val
  })
}

//单个试题选中
function handleCheckedCitiesChange(e, val) {
  if (e) {
    const index = deleteList.indexOf(val)
    if (index === -1) deleteList.push(val)
  } else {
    deleteList = deleteList.filter((item) => item !== val)
  }
  const checkedCount = deleteList.length
  allCheck = checkedCount === tableOptions.pageOptions.total
}

function batchDelete() {
  const total = allCheck ? tableOptions.pageOptions.total : deleteList.length
  let deleteStr = ""
  if (!props.chapterId) {
    deleteStr = `是否要删除《${props.treeData.bookName}》，${total}个试题资源？`
  } else {
    const arr = props.dirName.split("/")
    const catalogName = arr[arr.length - 1]
    deleteStr = `是否要删除《${props.treeData.bookName}》${catalogName}，${total}个试题资源？`
  }
  $g.confirm({
    type: "info",
    content: deleteStr,
    positiveText: "确定",
    negativeText: "取消",
  }).then(async () => {
    await deleteQuestionList({
      bookId: route.query.bookId,
      bookCatalogId: props.chapterId,
      questionIdList: deleteList,
    })
    //计算删除后的最大页码，如果删除后最大页码小于当前页码，则将页码设置为最后一页页码
    const page = Math.ceil(
      (allBookCatalogQuestionIds.length - deleteList.length) / 10,
    )
    if (page < tableOptions.pageOptions.page) {
      tableOptions.pageOptions.page = page || 1
    }
    $g.msg("删除成功")
    await getDirQuestionListApi()
    emit("refreshTree")
  })
}
/*批量删除功能结束*/

defineExpose({ handleRefresh, getDirQuestionListApi })
</script>

<style lang="scss" scoped></style>
