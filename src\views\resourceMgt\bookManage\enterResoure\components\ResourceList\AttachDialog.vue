<template>
  <div>
    <g-dialog
      title="查看识别结果"
      v-model:show="showDialog"
      @confirm="confirm"
      width="1600"
      :show-footer="false"
      :on-after-enter="getDetail"
      @close-x="closeData"
    >
      <div class="h-[700px]" v-loading="loading">
        <OcrRecognitionResult
          v-if="!loading"
          :file-url="fileData.fileAbsoluteUrl"
          :mdList="fileData.list"
        />
      </div>
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import OcrRecognitionResult from "@/views/resourceMgt/components/OcrRecognitionResult/index.vue"
import { getQuestionAttachOcr } from "@/api/bookMgt"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  bookAttachId: {
    type: Number,
    default: null,
  },
})
let fileData = $ref<any>({})
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
let loading = $ref(true)
/* 获取文件数据 */
async function getDetail() {
  try {
    loading = true
    let res = await getQuestionAttachOcr({
      bookAttachId: props.bookAttachId,
    })
    loading = false
    fileData = res
  } catch (err) {
    loading = false
    console.log(err)
  }
}

async function confirm() {
  emit("update:show", false)
}

function closeData() {
  loading = true
  fileData = {}
}
</script>

<style lang="scss" scoped></style>
