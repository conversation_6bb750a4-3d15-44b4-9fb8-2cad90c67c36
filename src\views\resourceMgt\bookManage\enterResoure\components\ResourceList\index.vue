<template>
  <div>
    <g-form :formOptions="formOptions" @search="searchData" @reset="getList" />
    <g-table :tableOptions="tableOptions" @changePage="getList">
      <template #header-left>
        目前只支持pdf/jpeg/png/jpg文件的识别，单文件大小不超过300M
      </template>
      <template #fileName="{ row }">
        <div class="cursor-pointer flex items-center overflow-hidden">
          <div
            class="text-primary underline flex-1"
            style="word-break: break-all"
            @click="previewFile(row)"
          >
            {{ row.fileName }}
          </div>
          <div @click="onDownLoad(row)" class="ml-10px flex-shrink-0 w-fit">
            <g-icon name="ri-download-line" size="20" color="#333639" />
          </div>
        </div>
      </template>
      <template #taskState="{ row }">
        <div :class="`text-${showClass(row.taskState)}`">
          {{ showState(row.taskState) }}
        </div>
      </template>
      <template #cz="{ row }">
        <n-space
          justify="center"
          v-if="['pdf', 'jpeg', 'jpg', 'png'].includes(row.fileExtension)"
        >
          <n-button
            v-if="row.taskState == 2"
            type="success"
            text
            @click="goDetail(row)"
            >查询识别结果</n-button
          >
          <n-button
            v-if="row.taskState == 0"
            type="primary"
            text
            @click="addOcrTask(row)"
            >开始识别</n-button
          >
          <n-button
            v-if="row.taskState == 2 || row.taskState == 3"
            type="primary"
            text
            @click="addOcrTask(row)"
            >重新识别</n-button
          >
          <span v-if="row.taskState == 1">-</span>
        </n-space>
        <div v-else>-</div>
      </template>
    </g-table>
    <AttachDialog
      v-model:show="detailDialogVisible"
      :bookAttachId="editBookAttachId"
    />
    <PreviewDialog
      v-model:show="showPreview"
      :fileInfo="fileInfo"
      :notPreview="false"
    />
  </div>
</template>

<script lang="ts" setup>
import AttachDialog from "./AttachDialog.vue"
import PreviewDialog from "../PreviewDialog.vue"
import {
  getBookAttachList,
  getOcrTaskStateSelect,
  getAddOcrTask,
  getAgainOcrTask,
} from "@/api/resourceMgt"
const route = useRoute()
const formOptions = $ref<any>({
  ref: null as any,
  filter: true,
  loading: false,
  items: {
    taskState: {
      type: "select",
      label: "识别状态",
      options: [],
      width: "150px",
      labelField: "title",
      valueField: "id",
    },
    keyword: {
      type: "text",
      label: "附件名称",
      showLabel: false,
      width: "180px",
    },
  },
  data: {
    taskState: null,
    keyword: "",
  },
})
let showPreview = $ref(false)
let fileInfo = $ref<any>({})
const tableOptions = $ref<any>({
  ref: null as any,
  loading: false,
  column: [
    {
      type: "index",
      label: "序号",
    },
    {
      prop: "fileName",
      label: "资源名称",
      align: "left",
      headerAlign: "center",
      slot: true,
    },
    {
      prop: "fileSize",
      label: "大小",
      formatter: (row) => {
        return $g.tool.formatFileSize(row.fileSize)
      },
    },
    {
      prop: "taskState",
      label: "识别状态",
      slot: true,
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})

let detailDialogVisible = $ref<boolean>(false)
let editBookAttachId = $ref<any>(null)
/* 预览 */
function previewFile(row) {
  let type = $g.tool.getFileType(row.fileExtension || row.suffix)
  if (["img", "video", "audio", "word", "pdf", "xlsx", "ppt"].includes(type)) {
    fileInfo = row
    showPreview = true
  } else {
    $g.msg("暂不支持该类型文件预览", "warning")
  }
}
// 有正在识别中的任务
const taskStateIng = $computed(() => {
  let flag = tableOptions.data.find((item) => item.taskState == 1)
  return flag ? true : false
})

async function getList() {
  try {
    tableOptions.loading = true
    let params = {
      bookId: route.query.bookId,
      ...formOptions.data,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    }
    let res = await getBookAttachList(params)
    tableOptions.data = res.list
    tableOptions.pageOptions.total = res.total
  } catch (error) {
    console.log(error)
    tableOptions.data = []
  } finally {
    tableOptions.loading = false
  }
}

async function getSetList() {
  try {
    let params = {
      bookId: route.query.bookId,
      ...formOptions.data,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    }
    let res = await getBookAttachList(params)
    tableOptions.data = res.list
    tableOptions.pageOptions.total = res.total
  } catch (error) {
    console.log(error)
    tableOptions.data = []
  }
}

/* ocr任务状态下拉选择列表 */
async function getStateSelect() {
  let res = await getOcrTaskStateSelect()
  formOptions.items.taskState.options = res.map((item) => {
    return {
      ...item,
      class: exchangeState(item.id),
    }
  })
}

function exchangeState(state) {
  let type = ""
  switch (state) {
    case 0:
      type = ""
      break
    case 1:
      type = "warning"
      break
    case 2:
      type = "success"
      break
    case 3:
      type = "error"
      break
    default:
      type = ""
      break
  }
  return type
}

function showState(id) {
  let flag = formOptions.items.taskState.options.find((item) => item.id == id)
  return flag ? flag.title : ""
}

function showClass(id) {
  let flag = formOptions.items.taskState.options.find((item) => item.id == id)
  return flag ? flag.class : ""
}

function searchData() {
  tableOptions.pageOptions.page = 1
  getList()
}

function goDetail(data) {
  editBookAttachId = data.bookAttachId
  detailDialogVisible = true
}

function addOcrTask(row) {
  $g.confirm({
    content: `${
      row.taskState == 0
        ? "是否添加该附件的OCR识别任务？"
        : "是否再次识别该附件？"
    }`,
  })
    .then(async () => {
      row.taskState == 0
        ? await getAddOcrTask({
            bookAttachId: row.bookAttachId,
          })
        : await getAgainOcrTask({ bookAttachId: row.bookAttachId })
      $g.msg("添加OCR识别任务成功！", "success")
      await getList()
    })
    .catch((err) => {})
}

let timer = $ref<any>(null)

// 如果任务有识别中，每隔两三秒去请求一次列表
function setTimeGetList() {
  timer = setInterval(() => {
    getSetList()
  }, 3000)
}

watch(
  () => taskStateIng,
  (val) => {
    if (val) {
      setTimeGetList()
    } else {
      clearInterval(timer)
      timer = null
    }
  },
  { immediate: true },
)

const onDownLoad = (row) => {
  $g.tool.downloadFile(row.fileAbsoluteUrl, row.fileName)
}

onMounted(() => {
  getStateSelect()
  getList()
})

onBeforeUnmount(() => {
  clearInterval(timer)
  timer = null
})
</script>

<style lang="scss" scoped></style>
