<template>
  <g-dialog
    title="资源排序"
    v-model:show="showDialog"
    :width="1200"
    :on-after-enter="fetchTree"
    @handleClose="afterClose"
    @closeX="beforeClose"
    @cancel="beforeClose"
    @confirm="sort"
  >
    <el-scrollbar class="h-[600px]">
      <g-loading class="h-200px" v-if="treeLoading"></g-loading>
      <g-tree
        v-else
        ref="treeRef"
        class="!border-none w-full"
        :tree-line="false"
        :treeData="treeData"
        nodeKey="bookCatalogId"
        :highlight-check="false"
        :highlight-current="false"
        :default-expand-all="true"
        @node-expand="
          () => {
            $g.tool.renderMathjax()
          }
        "
      >
        <template #body="{ data }">
          <div class="w-[calc(100%)] overflow-hidden my-5px">
            <!-- 章节 -->
            <div
              class="flex items-center !text-[12px] gap-10px w-[calc(100%)] overflow-hidden justify-between"
            >
              <g-mathjax :text="data.nameAlias ?? data.originalName" />

              <div class="flex justify-between gap-x-[10px] flex-shrink-0">
                <!-- <n-button
                  v-if="data.parentBookCatalogId"
                  :type="data.isContent == 2 ? 'success' : 'primary'"
                  text
                  @click.stop="handleChangeIsDelete(data)"
                >
                  <g-icon name="ri-settings-6-line" size="14" color="" />
                  {{ data.isContent == 2 ? "已设置为内容" : "设为内容" }}
                </n-button> -->
                <n-button
                  type="primary"
                  text
                  @click.stop="handleChangeName(data)"
                >
                  <g-icon name="ri-edit-line" size="" color="" />
                  改名
                </n-button>
                <!-- <n-button
                  :type="data.isVisible == 1 ? 'error' : 'primary'"
                  text
                  @click.stop="handleChangeIsHidden(data)"
                >
                  <g-icon
                    :name="
                      data.isVisible == 1 ? 'ri-eye-off-line' : 'ri-eye-line'
                    "
                    size=""
                    color=""
                  />
                  {{ data.isVisible == 1 ? "隐藏" : "显示" }}
                </n-button> -->

                <n-button type="primary" text @click.stop="resetName(data)">
                  <g-icon name="ri-refresh-line" size="" color="" />
                  重置名字
                </n-button>
              </div>
            </div>
            <!-- 资源 -->
            <div>
              <Draggable
                v-model="data.resourceList"
                :group="data.bookCatalogId"
                @start="draggable = true"
                @end="draggable = false"
                item-key="bookCatalogResourceFromId"
              >
                <template #item="{ element }">
                  <div
                    class="flex items-center !text-[12px] gap-10px w-[calc(100%)] overflow-hidden justify-between my-10px bg-[rgba(238,238,238,0.3)]"
                  >
                    <div class="flex items-center">
                      <g-icon name="ri-draggable" size="" color="" />
                      <div class="flex-shrink-0 mr-10px">
                        {{ sourceType[element.bookCatalogResourceType] }}
                      </div>
                      <g-mathjax
                        :text="element.nameAlias ?? element.originalName"
                        class="mt-5px"
                      />
                      <div
                        v-if="element.bookCatalogResourceType == 1"
                        class="flex-shrink-0"
                      >
                        ({{ element.questionCount }}题)
                      </div>
                    </div>
                    <!-- 操作 -->
                    <div class="flex justify-between gap-x-[10px]">
                      <n-button
                        type="primary"
                        text
                        @click.stop="handleChangeName(element)"
                      >
                        <g-icon name="ri-edit-line" size="" color="" />
                        改名
                      </n-button>
                      <!-- <n-button
                        :type="element.isVisible == 1 ? 'error' : 'primary'"
                        text
                        @click.stop="handleChangeIsHidden(element)"
                      >
                        <g-icon
                          :name="
                            element.isVisible == 1
                              ? 'ri-eye-off-line'
                              : 'ri-eye-line'
                          "
                          size=""
                          color=""
                        />
                        {{ element.isVisible == 1 ? "隐藏" : "显示" }}
                      </n-button> -->
                      <n-button
                        type="primary"
                        text
                        @click.stop="resetName(element)"
                      >
                        <g-icon name="ri-refresh-line" size="" color="" />
                        重置名字
                      </n-button>
                    </div>
                  </div>
                </template>
              </Draggable>
            </div>
          </div>
        </template>
      </g-tree>
    </el-scrollbar>
    <!-- 改名弹窗 -->
    <g-dialog
      title="新名称"
      v-model:show="showEditDialog"
      :formOptions="formOptions"
      @confirm="confirm"
    >
      <g-form :formOptions="formOptions"> </g-form>
    </g-dialog>
  </g-dialog>
</template>

<script setup lang="ts">
import { getChapterResourceTreeApi, resourceSortApi } from "@/api/bookMgt"
import Draggable from "vuedraggable"
let showDialog = defineModel<boolean>("show")
const route = useRoute()
let treeLoading = $ref(true)
let treeData = $ref<any>([])
let draggable = $ref(false)
let showEditDialog = $ref(false)
let currentData = $ref<any>({}) //当前点击更名的数据
const formOptions = reactive<any>({
  loading: false,
  ref: null as any,
  items: {
    nameAlias: {
      type: "text",
      label: "新名称",
      placeholder: "最多50个字符",
      rule: true,
    },
  },
  data: {
    nameAlias: "",
  },
})
const sourceType = {
  1: "试题:",
  2: "知识卡片:",
  3: "视频:",
}
async function fetchTree() {
  try {
    treeLoading = true
    let res = await getChapterResourceTreeApi({
      bookId: route.query.bookId,
    })
    treeData = res ?? []
    treeLoading = false
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  } catch (err) {
    console.log(err)
  }
}

function handleChangeName(data) {
  formOptions.data.nameAlias = data.nameAlias || data.originalName
  currentData = data
  showEditDialog = true
}

function beforeClose() {
  treeData = []
  currentData = {}
}
function afterClose() {
  treeLoading = true
}
// function handleChangeIsDelete(data) {
//   if (data.isContent == 2) {
//     data.isContent = 1
//     resetChildrenIsContent(data, 1)
//   } else {
//     data.isContent = 2
//     resetChildrenIsContent(data, 2)
//   }
// }
// function handleChangeIsHidden(data) {
//   if (data.isVisible == 2) {
//     data.isVisible = 1
//   } else {
//     data.isVisible = 2
//   }
// }
/* 重置data.children每一项的isContent,接受参数type为1，2，给data.children每一项的isContent赋值，每一项都可能存在children */
// function resetChildrenIsContent(data, type) {
//   data.children.forEach((item) => {
//     item.isContent = type
//     if (item.children) {
//       resetChildrenIsContent(item, type)
//     }
//   })
// }
function resetName(data) {
  data.nameAlias = null
  nextTick(() => {
    $g.tool.renderMathjax()
  })
  $g.msg("操作成功")
}

/* 点击更名 */
function confirm() {
  try {
    currentData.nameAlias = formOptions.data.nameAlias
    showEditDialog = false
    $g.msg("操作成功")
  } catch (e) {
    console.log(e)
  } finally {
    formOptions.loading = false
  }
}
async function sort() {
  try {
    await resourceSortApi({
      bookId: route.query.bookId,
      bookCatalogList: treeData,
    })
    $g.msg("资源排序成功")
  } catch (err) {
    console.log(err)
  }
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-tree-node__content {
    height: auto !important;
    background-color: transparent !important;
    align-items: start;
  }
  .el-tree-node {
    white-space: break-spaces;
    .el-icon {
      padding: 0 6px;
      margin-top: 6px;
    }
  }
  .custom-tree-node {
    overflow: hidden;
    .element-tree-node-label-wrapper {
      overflow: hidden;
    }
  }

  .is-checked {
    .el-tree-node__content {
      background-color: #fff !important;
    }
    .el-tree-node__children {
      .el-tree-node__content {
        background-color: transparent !important;
      }
    }
  }
}
</style>
