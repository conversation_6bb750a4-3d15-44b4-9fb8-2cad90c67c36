<template>
  <g-dialog
    title="步骤关联"
    v-model:show="showDialog"
    width="1200"
    :on-after-enter="initData"
    @confirm="confirm"
    :to="element"
    :auto-focus="false"
  >
    <g-table :tableOptions="tableOptions" height="600">
      <template #thoughtProcess="{ row }">
        <g-markdown
          class="w-full overflow-x-auto"
          mode="preview"
          v-model="row.thoughtProcess"
        />
      </template>
      <template #detailedExplanation="{ row }">
        <g-markdown
          class="w-full overflow-x-auto"
          mode="preview"
          v-model="row.detailedExplanation"
        />
      </template>
      <template #knowledgePoint="{ row }">
        <g-markdown
          class="w-full overflow-x-auto"
          mode="preview"
          v-model="row.knowledgePoint"
        />
      </template>
      <template #knowledgeExplanation="{ row }">
        <g-markdown
          class="w-full overflow-x-auto"
          mode="preview"
          v-model="row.knowledgeExplanation"
        />
      </template>
      <template #bindStep="{ row }">
        <el-input-number
          v-model="row.bindStep"
          :controls="false"
          class="w-[60px]"
        />
      </template>
    </g-table>
  </g-dialog>
</template>

<script setup lang="ts">
import { getAiAnalysis, updateAiAnalysisStepBind } from "@/api/bookMgt"
import type { PropType } from "vue"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    required: true,
  },
  element: {
    type: HTMLElement as PropType<any>,
  },
})
const emit = defineEmits(["update:show"])
const showDialog = useVModel(props, "show", emit)
const tableOptions = reactive<any>({
  ref: null as any,
  loading: true,
  column: [
    {
      prop: "stepNumber",
      label: "解题步骤",
      width: 100,
    },
    {
      prop: "thoughtProcess",
      label: "解题思路",
      slot: true,
      tooltip: false,
    },
    {
      prop: "detailedExplanation",
      label: "详细解析",
      slot: true,
      tooltip: false,
    },
    {
      prop: "knowledgePoint",
      label: "知识点",
      slot: true,
      tooltip: false,
    },
    {
      prop: "knowledgeExplanation",
      label: "知识点讲解",
      slot: true,
      tooltip: false,
    },
    {
      prop: "bindStep",
      label: "步骤关联",
      slot: true,
      width: 100,
      tooltip: false,
    },
  ],
  data: [],
  outline: "",
})
async function initData() {
  try {
    tableOptions.loading = true
    let res = await getAiAnalysis({
      subQuestionParseId: props.data.subQuestionParseId,
    })
    if (res.length) {
      tableOptions.data = res[0].steps
      tableOptions.outline = res[0].outline
      nextTick(() => {
        $g.tool.renderMathjax()
      })
    }

    setTimeout(() => {
      tableOptions.loading = false
    }, 200)
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
  }
}
async function confirm() {
  await updateAiAnalysisStepBind({
    subQuestionParseId: props.data.subQuestionParseId,
    detailList: [
      {
        outline: tableOptions.outline,
        steps: tableOptions.data,
      },
    ],
  })
  $g.msg("关联成功")
}
</script>

<style lang="scss" scoped></style>
