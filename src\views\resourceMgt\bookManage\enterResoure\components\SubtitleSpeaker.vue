<template v-if="showText">
  <div
    class="fixed bottom-10px z-[9999] transition-all duration-100 w-fit left-[50%] translate-x-[-50%] max-w-[1280px]"
    :class="show && showText ? 'block' : 'hidden'"
  >
    <div
      class="rounded-[8px] overflow-hidden px-16px py-10px"
      style="background: rgba(51, 51, 51, 0.85)"
    >
      <div class="flex items-start text-white">
        <g-icon
          :name="
            !speakerStore.isPlay ? 'ri-play-circle-line' : 'pause-circle-line'
          "
          size=""
          color=""
          class="mt-[-6px] mr-6px"
          @click="toPlay"
        />
        <g-markdown
          class="w-full"
          v-model="subtitle"
          mode="stream"
        ></g-markdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSpeakerStore } from "@/stores/modules/speaker"
let speakerStore = useSpeakerStore()
let { showSubTitle, subtitle } = $(storeToRefs(useSpeakerStore()))
const props = defineProps({
  width: {
    type: Number,
    default: 0,
  },
  showText: {
    type: Boolean,
    default: true,
  },
})

let show = defineModel<Boolean>("show")
const clientWidth = $computed(() => {
  return props.width
})
/* 播放 */
function toPlay() {
  if (speakerStore.isPlay) {
    speakerStore.pause()
  } else {
    if (speakerStore.audio) {
      speakerStore.play()
    } else {
      speakerStore.init()
      speakerStore.play()
    }
  }
}
function reset() {
  speakerStore.reset()
}
watch(
  () => subtitle,
  () => {
    $g.tool.renderMathjax()
  },
  {
    immediate: true,
  },
)
watch(
  () => showSubTitle,
  (val) => {
    if (val) {
      show.value = true
    } else {
      show.value = false
    }
  },
)

onBeforeUnmount(() => {
  speakerStore.audio && reset()
})

defineExpose({ toPlay, reset })
</script>

<style lang="scss" scoped></style>
