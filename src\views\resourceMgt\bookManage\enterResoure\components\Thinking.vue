<template>
  <div class="pb-10px h-full overflow-auto">
    <template v-if="$g.tool.isTrue(data)">
      <g-markdown
        class="w-full"
        v-model="data.outline"
        mode="preview"
      ></g-markdown>
      <g-table :tableOptions="tableOptions">
        <template #thoughtProcess="{ row }">
          <g-markdown
            class="w-full"
            v-model="row.thoughtProcess"
            mode="preview"
          ></g-markdown>
        </template>
        <template #detailedExplanation="{ row }">
          <g-markdown
            class="w-full"
            v-model="row.detailedExplanation"
            mode="preview"
          ></g-markdown>
        </template>
        <template #knowledgePoint="{ row }">
          <g-markdown
            class="w-full"
            v-model="row.knowledgePoint"
            mode="preview"
          ></g-markdown>
        </template>
        <template #knowledgeExplanation="{ row }">
          <g-markdown
            class="w-full"
            v-model="row.knowledgeExplanation"
            mode="preview"
          ></g-markdown>
        </template>
      </g-table>
    </template>
    <g-empty v-else></g-empty>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
const props = defineProps({
  data: {
    type: Object as PropType<any>,
    required: true,
  },
})

const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: false,
  column: [
    { prop: "stepNumber", label: "步骤" },
    { prop: "thoughtProcess", label: "解题思路", slot: true },
    { prop: "detailedExplanation", label: "详细解析", slot: true },
    { prop: "knowledgePoint", label: "知识点", slot: true },
    { prop: "knowledgeExplanation", label: "知识点讲解", slot: true },
  ],
  data: [],
})

watch(
  () => props.data,
  () => {
    tableOptions.data = props.data?.steps
  },
)

onMounted(() => {
  tableOptions.data = props.data?.steps
})
</script>

<style lang="scss" scoped></style>
