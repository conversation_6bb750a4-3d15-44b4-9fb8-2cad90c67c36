<template>
  <n-modal
    v-model:show="showModal"
    preset="card"
    transform-origin="center"
    :style="{
      width: '600px',
    }"
    size="huge"
    :bordered="false"
  >
    <template #header>
      <span>章节排序</span>
      <span class="text-14px ml-10px text-[#666]"
        >(鼠标按住拖拽即可调整排序)</span
      >
    </template>
    <div class="text-16px text-[#333]">目录树</div>
    <div class="w-full h-[400px] mt-10px overflow-y-auto">
      <g-tree
        class="!border-none"
        :treeData="list"
        nodeKey="bookCatalogId"
        :highlightCurrent="true"
        draggable
      >
        <template #body="{ data }">
          <div
            class="flex items-center !text-[12px] gap-10px w-[calc(100%)] overflow-hidden justify-between"
          >
            <g-mathjax :text="data?.bookCatalogName" />
          </div>
        </template>
      </g-tree>
    </div>
    <template #footer>
      <div class="w-full flex items-center justify-center">
        <n-button class="mr-30px" size="large" @click="cancel">取消</n-button>
        <n-button size="large" type="primary" @click="confirm"
          >保存并关闭</n-button
        >
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
const props = defineProps<{
  list: any[]
}>()
const showModal = defineModel<boolean>("show")
const emit = defineEmits<{
  change: []
}>()

function cancel() {
  showModal.value = false
}

function confirm() {
  showModal.value = false
  emit("change")
}

watch(
  () => showModal.value,
  () => {
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  },
)
</script>

<style lang="scss" scoped></style>
