<template>
  <div>
    <g-dialog
      @closeX="closeX"
      @cancel="cancel"
      :width="800"
      title="上传视频"
      @confirm="confirm"
      v-bind="$attrs"
    >
      <g-upload
        accept=".mp4"
        ref="videoUpload"
        tips="支持mp4格式，最大2G，分辨率： 1920×1080  1280×720"
        v-model:fileList="files"
        :fileConfig="{
          video: {
            maxSize: 2 * 1024 * 1024 * 1024,
          },
        }"
        :videoResolution="['1920×1080', '1280×720']"
        type="drag"
        :max="1"
      >
      </g-upload>
      <div class="flex mt-[16px]">
        <span class="mr-[10px] flex-shrink-0 mt-[5px]">字幕文件</span>
        <g-upload
          accept=".srt"
          ref="videoUpload"
          v-model:fileList="subtitleFiles"
          type="button"
          :max="1"
        >
          <n-button type="primary">上传字幕文件</n-button>
        </g-upload>
      </div>
    </g-dialog>
  </div>
</template>
<script lang="ts" setup>
import { uploadVideoApi } from "@/api/bookMgt"
import type { PropType } from "vue"
let props = defineProps({
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
})
const route = useRoute()
const attrs = useAttrs()
let subtitleFiles = $ref<any>([])
const emit = defineEmits(["update:show", "refresh"])
let files = $ref<any>([])
async function confirm() {
  if (!files?.[0]?.name) {
    $g.msg("请先上传文件", "warning")
    return
  }
  if (files[0]?.status == "uploading") {
    $g.msg("请等文件上传完毕", "warning")
    return
  }
  try {
    const res = await uploadVideoApi({
      bookId: route.query.bookId,
      bookCatalogId: props.chapterId,
      video: {
        fileName: files[0]?.name,
        fileAbsoluteUrl: files[0]?.fullUrl,
        fileExtension: files[0]?.suffix,
        fileSize: files[0]?.file.size,
        hashCode: files[0]?.hash,
        resolutionWidth: files[0]?.videoInfo?.width,
        resolutionHeight: files[0]?.videoInfo?.height,
        fileDuration: files[0]?.time_length,
        subtitleUrl: subtitleFiles[0]?.fullUrl,
      },
    })
    $g.msg("上传成功", "success")
    emit("update:show", false)
    emit("refresh")
  } catch (error) {
    console.log("error", error)
  }
}
function closeX() {
  emit("update:show", false)
}

function cancel() {
  emit("update:show", false)
}
</script>
<style scoped lang="scss"></style>
