<template>
  <div>
    <div class="w-full flex items-center justify-between">
      <div class="flex items-center">
        <n-input
          class="!w-230px"
          v-model:value="keyword"
          placeholder="输入视频关键词搜索"
          clearable
          @keydown.enter="onSearch"
        ></n-input>
        <n-button class="ml-10px" type="primary" @click="onSearch"
          >搜索</n-button
        >
      </div>
      <n-space justify="center" align="center">
        <n-button type="success" @click="$emit('openImportDialog')">
          关联已有资源
        </n-button>
        <n-button
          @click="sort"
          :disabled="!chapterId || !tableOptions?.data?.length"
          >排序调整
        </n-button>
        <n-button type="primary" @click="toAdd">
          <g-icon name="ri-add-line" size="14" />
          上传
        </n-button>
      </n-space>
    </div>
    <g-table :tableOptions="tableOptions" @changePage="getList">
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="primary" text @click="onPlay(row)">播放</n-button>
          <n-button type="error" text @click="onDelete(row)">删除</n-button>
          <n-button text type="warning" @click="onMove(row)">移动</n-button>
          <n-button
            v-if="row.videoResourceSourceName == '学科网'"
            text
            type="primary"
            @click="onDownLoad(row)"
            :disabled="row.downloadLoading"
            >{{ row.downloadLoading ? row.downloadPercent : "下载" }}</n-button
          >
        </n-space>
      </template>
    </g-table>
    <VideoAddDialog
      :chapterId="props.chapterId"
      v-model:show="showAddDialog"
      v-if="showAddDialog"
      @refresh="refresh"
    ></VideoAddDialog>
    <!-- 排序 -->
    <VideoSortDialog
      v-model:show="showSortDialog"
      :chapterId="chapterId"
      @refresh="refresh"
    />
    <VideoDialog :url="videoUrl" v-model:show="showVideoDialog"></VideoDialog>

    <!-- 视频移动 -->
    <ChapterDialog
      v-model:show="showChapterDialog"
      :chapterId="activeChapterId"
      :bookCatalogAttachId="bookCatalogAttachId"
      @refresh="refresh"
    />
  </div>
</template>
<script lang="ts" setup>
import type { PropType } from "vue"
import VideoAddDialog from "./VideoAddDialog.vue"
import {
  getVideoListApi,
  deleteVidieoApi,
  getVideoUrlApi,
  getDownloadVideo,
} from "@/api/bookMgt"
import VideoSortDialog from "./VideoSortDialog.vue"
import ChapterDialog from "./ChapterDialog.vue"
let props = defineProps({
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
})
let keyword = $ref<any>("")
const route = useRoute()
let videoUrl = $ref<any>("")
let showVideoDialog = $ref<any>(false)
let showAddDialog = $ref<any>(false)
let showChapterDialog = $ref(false)
let activeChapterId = $ref<any>(null)
let bookCatalogAttachId = $ref<any>(null)
const emit = defineEmits(["refreshTree", "openImportDialog"])
let showSortDialog = $ref(false)

function handleRefresh() {
  getList()
  emit("refreshTree")
}
defineExpose({
  handleRefresh,
})
function onSearch() {
  tableOptions.pageOptions.page = 1
  tableOptions.pageOptions.total = 0
  getList()
}
/* 排序 */
function sort() {
  showSortDialog = true
}
async function onPlay(item) {
  try {
    videoUrl = item?.fileAbsoluteUrl
    if (!item.videoResourceId) {
      $g.msg("视频地址错误，无法播放")
      return
    }
    videoUrl = await getVideoUrlApi({ videoResourceId: item.videoResourceId })
    showVideoDialog = true
  } catch (err) {
    console.log("获取视频播放地址出错", err)
  }
}
async function onDelete(item) {
  $g.confirm({
    type: "info",
    title: "温馨提示",
    content: "确定删除该视频吗？",
    positiveText: "确定",
    negativeText: "取消",
  })
    .then(async () => {
      const res = await deleteVidieoApi({
        bookCatalogAttachId: item?.bookCatalogAttachId,
      })
      $g.msg("删除成功")
      refresh()
    })
    .catch((err) => {})
}
async function getList() {
  try {
    const res = await getVideoListApi({
      bookId: route.query.bookId,
      bookCatalogId: props.chapterId,
      keyword,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.data = res?.list || []
    tableOptions.pageOptions.total = res?.total || 0
  } catch (error) {
    console.log("error", error)
  }
}
const tableOptions = $ref<any>({
  ref: null as any,
  loading: false,
  column: [
    {
      type: "index",
      label: "序号",
    },
    {
      prop: "videoResourceSourceName",
      label: "类型",
    },
    {
      prop: "fileName",
      label: "视频名称",
    },
    {
      prop: "fileSize",
      label: "大小",
      formatter(row) {
        return row?.fileSize + "MB"
      },
    },
    {
      prop: "uploadTime",
      label: "上传时间",
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})
watch(
  () => props.chapterId,
  () => {
    tableOptions.pageOptions.page = 1
    tableOptions.pageOptions.total = 1
    getList()
  },
  {
    immediate: true,
  },
)
function toAdd() {
  showAddDialog = true
}
function refresh() {
  emit("refreshTree")
  tableOptions.pageOptions.page = 1
  tableOptions.pageOptions.total = 1
  getList()
}

function onMove(item) {
  bookCatalogAttachId = item.bookCatalogAttachId
  activeChapterId = item.bookCatalogId
  showChapterDialog = true
}

//下载
async function onDownLoad(row) {
  try {
    row.downloadPercent = ""
    row.downloadLoading = true
    const { analyseState, downloadUrl } = await getDownloadVideo({
      videoResourceId: row.videoResourceId,
    })
    row.analyseState = analyseState
    if (analyseState == 1) {
      $g.msg("请稍后再试")
    } else if (analyseState == 2) {
      await $g.tool.downloadFile(downloadUrl, row.fileName, (percent) => {
        row.downloadPercent = percent + "%"
      })
    } else {
      $g.msg("视频下载失败！")
    }
    setTimeout(() => {
      row.downloadLoading = false
    }, 1000)
  } catch (error) {
    console.log("error", error)
    row.downloadLoading = false
  }
}
</script>
<style scoped lang="scss"></style>
