<template>
  <g-dialog
    title="排序调整"
    v-model:show="showDialog"
    width="500"
    @confirm="confirm"
  >
    <div class="flex flex-col items-center">
      <div
        class="flex items-center font-bold h-30px leading-[30px] gap-x-[20px] gap-y-[10px] w-full pl-50px"
      >
        <div class="w-20px"></div>
        <div class="w-100px text-center">序号</div>
        <div class="w-100px text-center">原有序号</div>
      </div>
      <el-scrollbar height="400px" class="w-full pl-50px">
        <Draggable
          v-model="list"
          group="people"
          @start="drag = true"
          @end="drag = false"
          item-key="bookCatalogQuestionId"
          @change="onMoveCallback"
        >
          <template #item="{ element, index }">
            <div
              class="flex items-center font-bold h-32px leading-[32px] gap-x-[20px] mt-10px"
            >
              <g-icon name="draggable" size="" color="" />
              <div class="w-100px text-center" style="border: 1px solid #ccc">
                {{ element.newOrdinal }}
              </div>
              <div class="w-100px text-center" style="border: 1px solid #ccc">
                {{ element.sort }}
              </div>
            </div>
          </template>
        </Draggable>
      </el-scrollbar>
    </div>
  </g-dialog>
</template>

<script setup lang="ts">
import Draggable from "vuedraggable"
import type { PropType } from "vue"
import { getVideoSort, saveVideoSort } from "@/api/bookMgt"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  orderList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
})
const emit = defineEmits(["update:show", "refresh"])
const route = useRoute()
const showDialog = useVModel(props, "show", emit)
let drag = $ref(false)
let list = $ref<any>([])
function onMoveCallback(val) {
  list.forEach((v, i) => {
    v.newOrdinal = i + 1
  })
}
/* 确认 */
async function confirm() {
  try {
    await saveVideoSort({
      bookId: route.query.bookId,
      bookCatalogId: props.chapterId,
      bookCatalogAttachIdList: list.map((v) => v.bookCatalogAttachId),
    })
    $g.msg("排序成功")
    emit("refresh")
    emit("update:show", false)
  } catch (err) {
    console.log(err)
  }
}
/* 获取排序 */
async function initData() {
  let res = await getVideoSort({
    bookId: route.query.bookId,
    bookCatalogId: props.chapterId,
  })
  list = res.map((v) => {
    return {
      ...v,
      newOrdinal: v.sort,
    }
  })
}
watch(
  () => props.show,
  (val) => {
    if (val) initData()
  },
)
</script>

<style lang="scss" scoped></style>
