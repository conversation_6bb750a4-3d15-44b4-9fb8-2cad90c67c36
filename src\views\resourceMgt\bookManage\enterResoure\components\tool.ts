import OSS from "@/plugins/OSS"
/* URL转换为Base64 */
export async function urlToBase64(url) {
  try {
    // 通过 fetch 获取文件数据
    const response = await fetch(url)
    // 获取文件的 Blob 对象
    const blob = await response.blob()
    // 使用 FileReader 将 Blob 转换为 Base64
    return await new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onloadend = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
      // 读取 Blob，并将结果转换为 Base64 字符串
      reader.readAsDataURL(blob)
    })
  } catch (error) {
    console.error("转换失败: ", error)
    return null
  }
}
/* 文件转base64 */
export function fileToObjectURL(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result)
    }
    reader.onerror = (error) => {
      reject(error)
    }
    reader.readAsDataURL(file)
  })
}
/* base64转文件对象 */
export function base64ToFile(base64, filename) {
  // 将 Base64 字符串转换为 Blob 对象
  const arr = base64.split(",")
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  const blob = new Blob([u8arr], { type: mime })
  // 创建 File 对象
  const file = new File([blob], filename, { type: mime })
  return file
}
/* base64上传 */
export async function handleUploadFile(file) {
  const fileData = {
    file: file,
    batchId: $g.tool.uuid(8),
    ext: ".ggb",
    name: "默认ggb文件.ggb",
    id: $g.tool.uuid(8),
    size: file.size,
    status: "finished",
    suffix: "ggb",
    percentage: 100,
    resource_title: "默认ggb文件",
  }
  const aliOss = new OSS()
  return aliOss.uploadFile(fileData, {})
}
