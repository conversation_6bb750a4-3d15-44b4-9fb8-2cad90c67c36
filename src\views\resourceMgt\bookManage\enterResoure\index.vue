<template>
  <div class="enterResource-container-main flex flex-col">
    <div class="text-[16px] flex gap-16px">
      <div class="flex items-center flex-1 overflow-hidden">
        <div
          class="font-bold leading-[32px] truncate"
          :title="treeData?.bookName"
        >
          名称：{{ treeData?.bookName }}
        </div>
        <n-button type="primary" @click="toStar" class="ml-[20px]"
          >预览星空图</n-button
        >
      </div>
      <n-button
        :disabled="resourceMgt.getDisabled"
        :type="isSubmit ? 'error' : 'primary'"
        @click="onSubmit"
        >{{ isSubmit ? "取消" : "" }}发布</n-button
      >
    </div>
    <div class="flex w-full">
      <div
        class="border-0 border-r border-solid border-[#eee] flex-shrink-0 overflow-hidden pr-30px transition-all duration-100"
        :class="expandTree ? 'w-[415px]  ' : '!w-2px '"
      >
        <transition>
          <div v-show="expandTree" class="w-[400px]">
            <div
              class="my-10px flex items-center cursor-pointer w-full justify-between text-success"
            >
              <div class="text-[#333] flex items-center">
                <span class="mr-5px">目录树管理</span>
                <n-button
                  :disabled="resourceMgt.getDisabled"
                  type="primary"
                  text
                  @click="setOrderShow"
                  class="ml-10px"
                  >排序</n-button
                >
                <n-button
                  v-if="treeData?.catalogList?.length"
                  :disabled="resourceMgt.getDisabled"
                  type="primary"
                  text
                  class="ml-10px"
                  @click="showSortResource = true"
                  >资源排序</n-button
                >
                <!-- 绑定整书 -->
                <div
                  class="relative w-40px"
                  v-if="!treeData?.catalogList?.length"
                >
                  <n-button
                    :disabled="resourceMgt.getDisabled"
                    text
                    class="ml-10px"
                    @click.stop="toBind(1)"
                    ><g-icon
                      name="ri-links-fill"
                      size="18"
                      color="var(--g-primary)"
                      class="mt-4px"
                      style="transform: rotate(50deg)"
                  /></n-button>

                  <div class="absolute right-0 top-[-10px]">
                    <n-popover
                      trigger="hover"
                      :disabled="treeData?.unionCatalogNum == 0"
                    >
                      <template #trigger>
                        <n-badge
                          :value="treeData?.unionCatalogNum"
                          class="mr-10px"
                        >
                        </n-badge>
                      </template>
                      <div
                        v-if="treeData.unionCatalogNum"
                        class="max-h-[500px] overflow-auto"
                      >
                        <div
                          class="py-20px bg-[#F8F8F8] max-w-[450px] my-10px"
                          v-for="item in treeData.unionCatalog"
                          :key="item.sysTextbookId"
                        >
                          <div class="px-20px text-primary">
                            {{ item.sysTextbookVersionName }}
                            {{ item.sysTextbookName }}
                          </div>
                          <div
                            class="px-20px"
                            v-for="v in item.sysTextbookCatalogList"
                            :key="v.sysTextbookCatalogId"
                          >
                            {{ v.sysTextbookCatalogName }}
                          </div>
                        </div>
                      </div>
                    </n-popover>
                  </div>
                </div>
              </div>
              <n-button
                text
                :disabled="resourceMgt.getDisabled"
                @click="editNode(null, true)"
                type="success"
              >
                <g-icon name="ri-add-line" />添加一级节点
              </n-button>
            </div>

            <g-tree
              class="!border-none"
              :treeData="treeData?.catalogList"
              nodeKey="bookCatalogId"
              :highlightCurrent="true"
              :default-expanded-keys="[dirId]"
              :default-checked-keys="[dirId]"
              @handleNodeClick="handleClick"
              @node-expand="
                () => {
                  $g.tool.renderMathjax()
                }
              "
            >
              <template #body="{ data, node }">
                <div
                  class="flex items-center !text-[12px] gap-10px w-[calc(100%)] overflow-hidden justify-between min-h-[40px]"
                >
                  <!-- <el-scrollbar x-scrollable class="flex-1"> -->
                  <g-mathjax :text="data?.bookCatalogName" />
                  <div
                    class="flex-shrink-0 text-primary cursor-pointer reflex"
                    v-if="data?.isAiCourse == 2"
                    @click.stop="onCopyAi(data)"
                  >
                    AI课
                  </div>
                  <div v-if="activeName != 'ResourceList'">
                    {{ getNum(data) }}
                  </div>

                  <!-- </el-scrollbar> -->
                  <div
                    class="w-91px flex-shrink-0 flex justify-between items-center"
                  >
                    <!-- 章节绑定 -->
                    <div class="relative w-30px">
                      <n-button
                        text
                        :disabled="resourceMgt.getDisabled"
                        @click.stop="toBind(2, data, node)"
                        type="primary"
                      >
                        <g-icon
                          name="ri-links-fill"
                          size="18"
                          color="var(--g-primary)"
                          style="transform: rotate(50deg)"
                        />
                      </n-button>
                      <div class="absolute right-[-20px] top-[-8px]">
                        <n-popover
                          trigger="hover"
                          :disabled="!data.unionCatalog"
                        >
                          <template #trigger>
                            <n-badge
                              :value="data.unionCatalogNum"
                              class="mr-10px"
                              :offset="[0, 0]"
                            >
                            </n-badge>
                          </template>
                          <div
                            v-if="data.unionCatalogNum"
                            class="max-h-[500px] overflow-auto"
                          >
                            <div
                              class="py-20px bg-[#F8F8F8] max-w-[450px] my-10px"
                              v-for="item in data.unionCatalog"
                              :key="item.sysTextbookId"
                            >
                              <div class="px-20px text-primary">
                                {{ item.sysTextbookVersionName }}
                                {{ item.sysTextbookName }}
                              </div>
                              <div
                                class="px-20px my-5px"
                                v-for="v in item.sysTextbookCatalogList"
                                :key="v.sysTextbookCatalogId"
                              >
                                {{ v.sysTextbookCatalogName }}
                              </div>
                            </div>
                          </div>
                        </n-popover>
                      </div>
                    </div>
                    <n-button
                      text
                      :disabled="resourceMgt.getDisabled"
                      @click.stop="editNode(data, true)"
                      type="primary"
                    >
                      <g-icon
                        class="mr-[8px] cursor-pointer"
                        :class="data.desc && 'red-point'"
                        name="ri-add-line"
                        color="var(--g-primary)"
                        size="15"
                      />
                    </n-button>
                    <n-button
                      text
                      :disabled="resourceMgt.getDisabled"
                      @click.stop="editNode(data, false)"
                      type="primary"
                    >
                      <g-icon
                        class="mr-[8px] cursor-pointer"
                        name="ri-edit-line"
                        color="var(--g-primary)"
                        size="15"
                      />
                    </n-button>

                    <n-button
                      text
                      :disabled="resourceMgt.getDisabled"
                      @click.stop="delNode(data, node)"
                      type="primary"
                    >
                      <g-icon
                        class="cursor-pointer"
                        name="ri-delete-bin-line"
                        style="color: var(--g-danger) !important"
                        size="15"
                      />
                    </n-button>
                  </div>
                </div>
              </template>
            </g-tree>
          </div>
        </transition>
      </div>
      <div
        class="flex-1 pt-10px pl-14px relative overflow-hidden flex-shrink-0"
      >
        <div
          class="fixed top-[100px] left-[240px] h-50px flex items-center cursor-pointer box-border z-[100] bg-[#eee] transition-all duration-100 w-[20px]"
          style="
            box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px,
              rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
          "
          @click="expandTree = !expandTree"
        >
          <g-icon
            :name="
              expandTree
                ? 'ri-arrow-left-double-line'
                : 'ri-arrow-right-double-line'
            "
            size="20"
            color="#999"
          />
        </div>
        <el-button
          @click="goPage"
          type="primary"
          :disabled="!aiProcessList?.length"
          >快速录题</el-button
        >
        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane
            v-for="item in tabs"
            :key="item.value"
            :label="item.label"
            :name="item.value"
          ></el-tab-pane>
        </el-tabs>
        <div
          class="mb-10px leading-[32px] flex"
          v-show="activeName != 'ResourceList'"
        >
          <div>目录层级：</div>
          <div>
            <g-mathjax :text="dirName" />
          </div>
        </div>

        <component
          :is="comMap[activeName]"
          :chapterId="dirId"
          :expandTree="expandTree"
          :treeData="treeData"
          :dirName="dirName"
          @openImportDialog="openImportDialog"
          @refreshTree="getDirectoryListApi"
          ref="activeComponent"
        ></component>
      </div>
    </div>

    <g-dialog
      :title="nodeTitle"
      v-model:show="showNodeCreation"
      width="1600"
      @confirm="onConfirmFn"
      :z-index="1005"
    >
      <div class="h-[80vh] overflow-auto" v-loading="loading">
        <div class="flex items-center w-[400px]">
          <div
            class="flex items-center text-14px mr-10px whitespace-nowrap flex-shrink-0"
          >
            <span class="text-danger mr-5px">*</span>节点名称:
          </div>
          <n-input
            v-model:value="nodeName"
            placeholder="请输入节点名称"
          ></n-input>
          <n-button
            type="primary"
            class="ml-10px"
            @click="
              () => {
                showNodeName = true
                $g.tool.renderMathjax()
              }
            "
            >预览</n-button
          >
        </div>
        <div class="min-h-20px p-10px" v-if="showNodeName">
          <g-mathjax :text="nodeName" />
        </div>
        <div class="flex items-center mt-[16px] mb-10px">
          <span class="mr-[10px]">是否AI课程章节</span>
          <el-radio-group v-model="isAi">
            <el-radio :value="1">否</el-radio>
            <el-radio :value="2">是</el-radio>
          </el-radio-group>
        </div>
        <g-form :formOptions="formOptions">
          <template #bookCatalogArticleFormatType>
            <el-radio-group
              v-model="formOptions.data.bookCatalogArticleFormatType"
              @change="handleChange"
            >
              <el-radio
                v-for="item in formOptions.items.bookCatalogArticleFormatType
                  .options"
                :key="item.value"
                :value="item.value"
                >{{ item.label }}</el-radio
              >
            </el-radio-group>
          </template>
          <template #title>
            <g-ueditor
              v-model="formOptions.data.title"
              :config="{ initialFrameHeight: 60, maximumWords: 200 }"
              @click.prevent
            ></g-ueditor>
          </template>
          <template #content>
            <div class="w-full">
              <div v-if="currentType == 1" class="h-[500px]">
                <g-editor
                  v-model="formOptions.data.content"
                  :config="{ initialFrameHeight: 460 }"
                  :initProps="{
                    max: 99999,
                  }"
                ></g-editor>
              </div>
              <g-markdown
                class="w-full"
                height="500px"
                v-if="currentType == 2"
                v-model="formOptions.data.content"
              ></g-markdown>
              <div v-if="currentType == 3">
                <el-button
                  type="primary"
                  class="mb-20px"
                  @click="showMarkdownDialog = true"
                  >Markdown转换思维导图</el-button
                >
                <MindMap
                  v-if="showMindMap"
                  :class="{ border: currentType == 3 }"
                  ref="mindMapRef"
                  v-model:isFIB="isFIB"
                  :height="500"
                  :remoteMapData="remoteMapData"
                  :exportImg="false"
                />
              </div>
            </div>
          </template>
        </g-form>
      </div>
    </g-dialog>
    <!-- Markdown转思维导图对话框 -->
    <el-dialog
      title="Markdown转思维导图"
      v-model="showMarkdownDialog"
      width="1050px"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
      @closed="handleClosed"
      :z-index="3001"
    >
      <el-form>
        <el-form-item label="Markdown文本：" required>
          <g-markdown
            class="w-full"
            height="400px"
            v-model="markdownText"
          ></g-markdown>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showMarkdownDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="convertMarkdownToMindMap"
            :disabled="!markdownText"
            :loading="btnLoading"
          >
            确认转换
          </el-button>
        </div>
      </template>
    </el-dialog>

    <TreeSort v-model:show="showSort" :list="sortList" @change="onSort" />

    <!-- 关联已有资源 -->
    <ImportQuestion
      v-model:show="showImportQuestion"
      :bookCatalogId="dirId"
      @refreshQuestionList="handleRefresh"
    />
    <!-- 资源排序 -->
    <SortResource v-model:show="showSortResource" />
  </div>
</template>
<script lang="ts" setup name="EnterResoure">
import TreeSort from "./components/TreeSort.vue"
import ImportQuestion from "./components/ImportQuestion/index.vue"
import {
  getDirectoryList,
  addBookCatalog,
  updateBookCatalog,
  delBookCatalog,
  togglePublish,
  catalogSort,
} from "@/api/resourceMgt"
import SortResource from "./components/SortResource.vue"
import { getStatistics, getAiProcessList, exchangeArticle } from "@/api/bookMgt"
import { useResourceMgtStore } from "@/stores/modules/resourceMgt"
import ClipboardJS from "clipboard"
import MindMap from "@/views/resourceMgt/components/MindMap/index.vue"

const resourceMgt = useResourceMgtStore()
let showSortResource = $ref<boolean>(false)
const router = useRouter()
const route = useRoute()
let showImportQuestion = $ref(false)
let showSort = $ref<boolean>(false)
let sortList = $ref<any[]>([])
let isAi = $ref<any>(1)
let isSubmit = $ref(false)
let treeData = $ref<any>(null)
let showNodeCreation = $ref(false)
let isAdd = $ref(false)
let tempNode = $ref<any>(null)
let nodeName = $ref("")
const { copy, copied } = useClipboard({
  legacy: true,
})
/* 复制完整链接数据 */
async function onCopyAi(file) {
  await copy(file.aiCourseUrl)
  console.log(copied.value)
  if (copied.value) {
    $g.msg("已复制课程链接")
  } else {
    $g.msg("复制失败", "error")
  }
}

let dirName = $ref<any>("-")
let dirId = $ref<any>(null)
let showNodeName = $ref(false)
let expandTree = $ref(false)
let activeName = $ref("QuestionList")
let tabs = $ref([
  {
    label: "试题列表",
    value: "QuestionList",
  },
  {
    label: "文章列表",
    value: "ArticleList",
  },
  {
    label: "视频列表",
    value: "VideoList",
  },
  {
    label: "附件列表",
    value: "ResourceList",
  },
  {
    label: "课程描述",
    value: "CourseDescription",
  },
])

function toStar() {
  window.open(
    `${location.protocol}//${location.host}/#/star?bookId=${route.query.bookId}&paperName=${treeData?.bookName}`,
    "_blank",
  )
}
function getNum(item) {
  if (activeName == "CourseDescription") {
    const bookCatalogFileNum = item.bookCatalogFileNum || 0
    return "(" + bookCatalogFileNum + ")"
  }
  if (activeName == "QuestionList") {
    const questionNum = item.questionNum || 0
    return "(" + questionNum + ")"
  }
  if (activeName == "ArticleList") {
    const articleNum = item.articleNum || 0
    return "(" + articleNum + ")"
  }
  const videoNum = item.videoNum || 0
  return "(" + videoNum + ")"
}
const nodeTitle = $computed(() => {
  return isAdd ? "添加节点" : "修改节点"
})
let comMap = {
  QuestionList: defineAsyncComponent(
    () => import("./components/QuestionList.vue"),
  ),
  ArticleList: defineAsyncComponent(
    () => import("./components/ArticleList.vue"),
  ),
  VideoList: defineAsyncComponent(() => import("./components/VideoList.vue")),
  ResourceList: defineAsyncComponent(
    () => import("./components/ResourceList/index.vue"),
  ),
  CourseDescription: defineAsyncComponent(
    () => import("./components/CourseDescription.vue"),
  ),
}
// 当前激活的组件实例，是试题还是文章
let activeComponent = $ref<any>(null)
/* 打开关联资源弹窗 */
function openImportDialog() {
  showImportQuestion = true
}
function handleRefresh() {
  activeComponent?.handleRefresh()
}

/* 获取统计数量 */
async function getStatisticsApi() {
  if (!route.query.bookId) return
  let res = await getStatistics({
    bookId: route.query.bookId,
    bookCatalogId: dirId,
  })
  const videoNum = res?.videoNum || 0
  tabs[0].label = "试题列表" + "(" + res.questionNum + ")"
  tabs[1].label = "文章列表" + "(" + res.articleNum + ")"
  tabs[2].label = "视频列表" + "(" + videoNum + ")"
  tabs[3].label = "附件列表" + "(" + res.bookAttachNum + ")"
  tabs[4].label = "课程描述" + "(" + res.bookCatalogFileNum + ")"
}
let idPath = $ref<any>("")
onBeforeMount(() => {
  getDirectoryListApi()
})

async function getDirectoryListApi() {
  const data = await getDirectoryList({ bookId: route.query.bookId })
  treeData = data || null
  isSubmit = data?.state === 1 || data?.state === 2
  treeData.catalogList && (expandTree = true)
  await getStatisticsApi()

  nextTick(() => {
    $g.tool.renderMathjax()
  })
}

const onConfirmFn = useDebounceFn(() => {
  onConfirm()
}, 50)
async function onConfirm() {
  try {
    if (!nodeName.length) {
      $g.msg("节点名称不能为空", "error")
    } else {
      let mapData = "" as any
      if (currentType == 3) {
        loading = true
        mapData = await mindMapRef.getMindMapData()
        delete mapData.view
      }
      const desc =
        currentType == 3
          ? JSON.stringify(mapData, replacer)
          : formOptions.data.content
      if (isAdd) {
        const data = await addBookCatalog({
          bookId: route.query.bookId,
          bookCatalogId: tempNode?.bookCatalogId || 0,
          bookCatalogName: nodeName,
          desc,
          isAiCourse: isAi,
          descFormatType: formOptions.data.bookCatalogArticleFormatType,
          descTitle: formOptions.data.title,
        })
        const NEW_NODE: any = {
          bookCatalogId: data,
          bookCatalogName: nodeName,
          desc,
          parentBookCatalogId: 0,
          questionNum: 0,
          articleNum: 0,
          videoNum: 0,
          isAiCourse: isAi,
          aiCourseUrl: `${
            import.meta.env.VITE_APP_THREE_LANDSCAPE_URL
          }/#/aiDemo?bookId=${route.query.bookId}&bookCatalogId=${data}`,
          descFormatType: formOptions.data.bookCatalogArticleFormatType,
          descTitle: formOptions.data.title,
        }
        if (tempNode) {
          NEW_NODE.parentBookCatalogId = tempNode.bookCatalogId
          tempNode.children = Array.isArray(tempNode.children)
            ? [...tempNode.children, NEW_NODE]
            : [NEW_NODE]
        } else {
          treeData.catalogList = Array.isArray(treeData.catalogList)
            ? [...treeData.catalogList, NEW_NODE]
            : [NEW_NODE]
        }
        if (treeData?.catalogList.length) {
          // 新建之后排序一下
          sortList = $g._.cloneDeep(treeData?.catalogList)
          onSort()
        }
      } else {
        await updateBookCatalog({
          bookCatalogId: tempNode.bookCatalogId,
          bookCatalogName: nodeName,
          desc,
          isAiCourse: isAi,
          descFormatType: formOptions.data.bookCatalogArticleFormatType,
          descTitle: formOptions.data.title,
        })
        tempNode.bookCatalogName = nodeName
        tempNode.desc = desc
        tempNode.isAiCourse = isAi
        tempNode.aiCourseUrl = `${
          import.meta.env.VITE_APP_THREE_LANDSCAPE_URL
        }/#/aiDemo?bookId=${route.query.bookId}&bookCatalogId=${
          tempNode.bookCatalogId
        }`
        tempNode.descFormatType = formOptions.data.bookCatalogArticleFormatType
        tempNode.descTitle = formOptions.data.title
        if (dirId == tempNode.bookCatalogId) {
          let arr = dirName.split("/")
          let idArr = idPath.split("/")
          let index = idArr.findIndex((item) => item == dirId)
          arr[index] = nodeName
          dirName = arr.join("/")
        }
      }
    }
  } catch (err) {
    console.log(err)
  } finally {
    $g.tool.renderMathjax()
    loading = false
  }
}

function editNode(parent, add) {
  currentType = 1
  formOptions.data.bookCatalogArticleFormatType = 1
  cache = []
  tempNode = parent
  showNodeName = false
  isAdd = add
  if (!add && parent) {
    loading = true
    nodeName = parent.bookCatalogName
    isAi = parent.isAiCourse
    setTimeout(() => {
      formOptions.data.title = parent.descTitle
      formOptions.data.bookCatalogArticleFormatType = parent.descFormatType
      currentType = parent.descFormatType
      formOptions.data.content = currentType != 3 ? parent.desc || "" : ""
      remoteMapData = currentType == 3 ? JSON.parse(parent.desc) : null
      loading = false
    }, 500)
  } else {
    nodeName = ""
    formOptions.data.content = ""
    isAi = 1
    formOptions.data.bookCatalogArticleFormatType = 1
    currentType = 1
    remoteMapData = null
    formOptions.data.title = ""
  }
  showNodeCreation = true
}
/* 跳转绑定 */
function toBind(type, data?, node?) {
  // type 1整书模式 2章节模式
  let currentCatalogName = type == 1 ? "-" : getName(node).suffix
  router.push({
    name: "BindChapter",
    query: {
      activeName:
        (route.query.category as any) == 2 ? "PaperManage" : "BookManage",
      bookId: route.query.bookId,
      sysCourseId: route.query.sysCourseId,
      bookSource: route.query.bookSource,
      sysStageName: route.query.sysStageName,
      sysSubjectName: route.query.sysSubjectName,
      bookCatalogId: type == 2 ? data.bookCatalogId : null,
      type,
      currentCatalogName,
    },
  })
}
function delNode(data, node) {
  $g.confirm({
    type: "info",
    title: "提示",
    content: "是否删除该节点",
    positiveText: "确定",
    negativeText: "取消",
  })
    .then(async () => {
      await delBookCatalog({ bookCatalogId: data.bookCatalogId })
      if (node.level === 1) {
        const idx = node.parent.data.findIndex(
          (v) => v.bookCatalogId === data.bookCatalogId,
        )
        node.parent.data.splice(idx, 1)
      } else {
        const idx = node.parent.data.children.findIndex(
          (v) => v.bookCatalogId === data.bookCatalogId,
        )
        node.parent.data.children.splice(idx, 1)
      }
      if (data.bookCatalogId == dirId) {
        dirId = null
        dirName = "-"
      }
      if (!treeData.catalogList.length) {
        const data = await getDirectoryList({ bookId: route.query.bookId })
        treeData = data || null
        nextTick(() => {
          $g.tool.renderMathjax()
        })
      }
    })
    .catch((err) => {
      console.log(err)
    })
}

function onChange(data, node) {
  dirId = data.bookCatalogId
  dirName = getName(node).suffix
  idPath = getName(node).id
  $g.tool.renderMathjax()
}

function handleClick(data, node) {
  if (node.checked) {
    onChange(data, node)
  } else {
    dirId = null
    dirName = "-"
    idPath = ""
    $g.tool.renderMathjax()
  }
  getStatisticsApi()
}
function getName(node, suffix = "", id = "") {
  suffix = suffix
    ? node.data.bookCatalogName + "/" + suffix
    : node.data.bookCatalogName
  id = id
    ? String(node.data.bookCatalogId) + "/" + id
    : String(node.data.bookCatalogId)
  if (node.parent.level !== 0) {
    return getName(node.parent, suffix, id)
  }
  return {
    suffix,
    id,
  }
}

function onSubmit() {
  const type = isSubmit ? "warning" : "info"
  const content = isSubmit
    ? "是否要取消发布？"
    : "发布本资源将会修改所有试题状态为已审核,是否发布？"
  $g.confirm({
    type: type,
    title: "提示",
    content: content,
    positiveText: "确定",
    negativeText: "取消",
  }).then(async () => {
    await togglePublish({ bookId: route.query.bookId })
    // 刷新试题列表,发布以后，所有试题的审核状态会更改
    activeComponent?.getDirQuestionListApi?.()
    $g.msg("操作成功", "success")
    isSubmit = !isSubmit
  })
}

function setOrderShow() {
  showSort = true
  sortList = $g._.cloneDeep(treeData?.catalogList)
}

async function onSort() {
  try {
    await catalogSort({
      bookId: route.query.bookId,
      bookName: treeData.bookName,
      state: treeData.state,
      catalogList: sortList,
    })
    $g.msg("修改成功", "success")
    treeData.catalogList = sortList
  } catch (e) {
    console.error(e)
  }
}

function goPage() {
  // 判断是否有完成AI预处理的资源
  const isHasComplete = aiProcessList.some((item) => item.state == 3)
  // 是否所有的文件都在 处理中或者等待中
  const isAllProcessing = aiProcessList.every(
    (item) => item.state == 1 || item.state == 2,
  )
  // 是否所有文件都是null,代表 没开始任务
  const isAllNotStart = aiProcessList.every((item) => item.state === null)
  if (!isHasComplete) {
    if (isAllProcessing) {
      $g.msg("AI预处理正在进行中，请稍后再试", "warning")
    } else if (isAllNotStart) {
      $g.msg("请先进行AI预处理", "warning")
    } else {
      $g.msg("请等待AI预处理完成", "warning")
    }
    return
  }
  router.push({
    name: "QuickQuestion",
    query: {
      bookId: route.query.bookId,
      activeName: route.query.activeName,
      bookName: route.query.bookName,
      // 传递当前选中的目录信息
      dirId: dirId,
      sysCourseId: route.query.sysCourseId,
    },
  })
}
onMounted(() => {
  getAiProcessListApi()
})
let aiProcessList = $ref<any[]>([])
function getAiProcessListApi() {
  getAiProcessList({
    bookId: route.query.bookId,
    page: 1,
    pageSize: 50,
  }).then((res) => {
    aiProcessList = res.list
  })
}

// 节点弹窗新逻辑
let isFIB = $ref(false)
let showMarkdownDialog = $ref(false)
let markdownText: any = $ref("")
let showMindMap = $ref(true)
let currentType = $ref(1)
let remoteMapData = $ref<any>(null)
let mindMapRef = $ref<any>(null)
let btnLoading = $ref(false)
let loading = $ref(false)

let cache = $ref<any>([])
function replacer(key, value) {
  if (typeof value === "object" && value !== null) {
    if (cache.includes(value)) return
    cache.push(value)
  }
  return value
}

const formOptions = $ref<any>({
  ref: null as any,
  loading: false,
  labelWidth: "110px",
  items: {
    bookCatalogArticleFormatType: {
      type: "radio",
      label: "章节描述类型",
      options: [
        { label: "富文本", value: 1 },
        { label: "Markdown", value: 2 },
        { label: "思维导图", value: 3 },
      ],

      slot: true,
    },
    title: {
      type: "textarea",
      label: "章节标题",
      placeholder: "请输入标题，最多输入200字",
      maxlength: 200,
      showCount: true,
      slot: true,
    },
    content: {
      type: "text",
      label: "章节内容",
      slot: true,
    },
  },
  data: {
    bookCatalogArticleFormatType: 1,
    content: "",
    title: "",
  },
})

/* 切换编辑器 */
function handleChange(e) {
  if ($g.tool.isTrue(formOptions.data.content) || currentType == 3) {
    formOptions.data.bookCatalogArticleFormatType = currentType
    $g.confirm({
      content: "切换编辑器会导致内容丢失，是否确认切换？",
    })
      .then(() => {
        formOptions.data.content = ""
        nextTick(() => {
          formOptions.data.bookCatalogArticleFormatType = e
          currentType = e
        })
      })
      .catch(() => {})
  } else {
    formOptions.data.bookCatalogArticleFormatType = e
    currentType = e
  }
}

function handleClosed() {
  markdownText = ""
}

/* Markdown转思维导图 */
async function convertMarkdownToMindMap() {
  if (!markdownText) {
    $g.msg("请输入Markdown文本", "warning")
    return
  }
  btnLoading = true
  showMindMap = false
  // 这里处理Markdown转思维导图的逻辑
  loading = true
  exchangeArticle({
    content: markdownText,
    bookId: route.query.bookId,
  })
    .then(async (res) => {
      remoteMapData = JSON.parse(res) || null
      await nextTick()
      showMindMap = true
      setTimeout(() => {
        nextTick(async () => {
          loading = false
          showMarkdownDialog = false
          btnLoading = false
        })
      }, 500)
    })
    .catch((err) => {
      showMarkdownDialog = false
      btnLoading = false
      showMindMap = true
    })
}
</script>
<style scoped lang="scss">
:deep() {
  .n-scrollbar-container {
    background: rgba(255, 255, 255, 0) !important;
  }
  .el-tree-node__content {
    height: auto !important;
    background-color: transparent !important;
  }
  .el-dropdown-link {
    outline: none;
  }
  .el-tabs__item {
    font-size: 18px;
  }
  .el-tree-node {
    white-space: break-spaces;
  }
  .custom-tree-node {
    overflow: hidden;
    .element-tree-node-label-wrapper {
      overflow: hidden;
    }
  }

  .is-checked {
    .el-tree-node__content {
      background-color: #f1f8ff !important;
    }
    .el-tree-node__children {
      .el-tree-node__content {
        background-color: transparent !important;
      }
    }
  }
}
.v-enter-active,
.v-leave-active {
  transition: opacity 0.3s linear;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}

.red-point {
  position: relative;
  &::after {
    content: "";
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: red;
    border-radius: 100%;
    position: absolute;
    right: -3px;
    top: 3px;
  }
}
</style>
