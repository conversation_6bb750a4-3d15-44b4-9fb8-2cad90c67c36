<template>
  <div class="book-manage-container-main">
    <g-form :formOptions="formOptions" @reset="search" @search="search">
      <template #bookTypeId>
        <div class="flex gap-x-[10px]">
          <n-tree-select
            v-model:value="formOptions.data.bookTypeId"
            :options="formOptions.items.bookTypeId.options"
            clearable
            :render-label="renderLabel"
            placeholder="请选择书籍类型"
            class="w-[200px]"
          />
          <n-button
            type="primary"
            text
            @click="router.push({ name: 'TypeManage', query: { category: 1 } })"
            >维护类型</n-button
          >
        </div>
      </template>
    </g-form>
    <g-table :tableOptions="tableOptions" @changePage="initData">
      <template #header-left>
        <n-space>
          <n-button type="primary" @click="goSearchQuestion">
            题目搜索
          </n-button>
        </n-space>
      </template>
      <template #header-right>
        <n-space justify="center" class="items-center">
          <n-button type="primary" @click="syncExam" text>同步考试</n-button>
          <n-button type="primary" @click="onGoXKW">学科网导入</n-button>
          <n-button
            type="primary"
            @click="
              () => {
                isEdit = false
                showDialog = true
                isCopy = false
                info = null
              }
            "
          >
            <g-icon name="add-line" size="" color="" />
            新建资源
          </n-button>
        </n-space>
      </template>
      <template #bookName="{ row }">
        <div class="line-2">{{ row.bookName }}</div>
      </template>
      <template #bookTypeName="{ row }">
        <div>
          {{ row.bookTypeList?.map((v) => v.bookTypeName).join("、") }}
        </div>
      </template>
      <template #sourceName="{ row }">
        <n-tag
          :bordered="false"
          :color="sourceTagStyle[row.sourceName] || sourceTagStyle.default"
        >
          {{ row.sourceName }}
        </n-tag>
      </template>
      <template #schoolNum="{ row }">
        <n-popover
          trigger="hover"
          v-if="row.schoolNum && row.isAllSchool === 1"
        >
          <template #trigger>
            <div class="text-[#3caeff] cursor-pointer select-none">
              {{ row.schoolNum }}所
            </div>
          </template>
          <div class="max-h-200px overflow-y-auto">
            <div
              v-for="item in row.schoolList"
              :key="item.schoolId"
              class="text-14px mb-5px"
            >
              {{ item.schoolName }}
            </div>
          </div>
        </n-popover>
        <div v-else>-</div>
      </template>
      <template #version="{ row }">
        <Expand v-if="row.versionList?.length">
          <div v-for="(item, index) in row.versionList" :key="index">
            {{ item }}
          </div>
        </Expand>
        <template v-else>-</template>
      </template>
      <template #state="{ row }">
        <div :class="status[row.state].class">{{ status[row.state].name }}</div>
      </template>
      <template #bookAreaList="{ row }">
        <div v-if="row.bookAreaList?.length">
          <n-popover trigger="hover">
            <template #trigger>
              <n-button text type="primary"
                >{{ row.bookAreaList.length }}个</n-button
              >
            </template>
            <n-scrollbar class="max-h-200px" trigger="none">
              <div
                v-for="item in row.bookAreaList"
                :key="item.bookAreaId"
                class="mr-20px"
              >
                {{
                  [
                    ...new Set(
                      [item.provinceName, item.cityName].filter(Boolean),
                    ),
                  ].join("/")
                }}
              </div>
            </n-scrollbar>
          </n-popover>
        </div>
        <div v-else>-</div>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="primary" text @click="copy(row)">复制</n-button>
          <!-- <n-button type="primary" text @click="run(row)">预跑</n-button> -->
          <AIProcess :info="row" />
          <n-button type="primary" text @click="edit(row)">编辑</n-button>
          <n-button type="primary" text @click="toResource(row)"
            >进入资源</n-button
          >
          <n-button type="error" text @click="deleteRow(row)">删除</n-button>
        </n-space>
      </template>
      <template #createTime="{ row }">
        <div>{{ row.createTime.split(" ")[0] }}</div>
      </template>
    </g-table>
    <div
      class="fixed z-[99999] left-0 right-0 top-0 bottom-0"
      element-loading-text="同步中..."
      element-loading-background="rgba(0, 0, 0, 0.5)"
      v-show="asyncLoading"
      v-loading="asyncLoading"
    ></div>
    <Add
      v-model:show="showDialog"
      @refresh="initData"
      :info="info"
      :isEdit="isEdit"
      :isCopy="isCopy"
      :category="1"
    />
    <g-dialog
      title="同步考试"
      :formOptions="syncExamFromOptions"
      v-model:show="showTimeDialog"
      @confirm="syncExamConfirm"
    >
      <g-form :formOptions="syncExamFromOptions"> </g-form>
    </g-dialog>
  </div>
</template>

<script setup lang="ts" name="BookManage">
import config from "@/config/index"
import GTooltip from "@/components/global/g-tooltip/index.vue"
import { sourceTagStyle } from "./constant"
import AIProcess from "./components/AIProcess.vue"
const { baseOrigin } = config
import {
  getNewStageListApi,
  fetchExamXKWBind,
  fetchExamXKWOAuth,
  getNewSubjectListApi,
  bindExamXKWApi,
  getPaperQuestion,
} from "@/api/resourceMgt"
import {
  getStatusSelect,
  getNewTypeSelect,
  getBookTypeTree,
  getListApi,
  deleteBookApi,
  getSyncAdmin,
  getBookSource,
  runApi,
  hasLock,
} from "@/api/bookMgt"
import Expand from "./components/Expand.vue"
import Add from "./components/Add.vue"
import { useRouterStore } from "@/stores/modules/routes"
import { useResourceMgtStore } from "@/stores/modules/resourceMgt"

const resourceMgtStore = useResourceMgtStore()
const routerStore = useRouterStore()
const router = useRouter()
const route = useRoute()

let showDialog = $ref(false)
let xkwOpenId = $ref<any>(null)
let handler = $ref<any>(null)
let xkwPaperId = $ref<any>(null)
let isCopy = $ref(false)
const formOptions = $ref<any>({
  ref: null as any,
  filter: true,
  loading: false,
  items: {
    sysStage: {
      type: "select",
      label: "学段",
      options: [],
      labelWidth: "60px",
      width: "150px",
    },
    sysSubjectId: {
      type: "select",
      label: "学科",
      options: [],
      width: "150px",
      labelWidth: "60px",
    },
    bookTypeId: {
      type: "select",
      label: "类型",
      options: [],
      width: "150px",
      labelWidth: "60px",
      slot: true,
    },
    bookSource: {
      type: "select",
      label: "来源",
      options: [],
      labelField: "title",
      valueField: "id",
      width: "150px",
      labelWidth: "60px",
    },
    state: {
      type: "select",
      label: "发布状态",
      options: [],
      width: "150px",
      labelWidth: "100px",
    },
    keyword: {
      type: "text",
      label: "书籍名称检索",
      showLabel: false,
      width: "180px",
    },
  },
  data: {
    sysStage: null,
    sysSubjectId: null,
    bookTypeId: null,
    state: null,
    keyword: "",
    bookSource: null,
  },
})
const tableOptions = $ref<any>({
  ref: null as any,
  loading: false,
  column: [
    {
      prop: "bookId",
      label: "ID",
      width: "120px",
    },
    {
      prop: "bookName",
      label: "书籍名称",
      align: "left",
      headerAlign: "center",
      width: "200px",
      tooltip: true,
      slot: true,
    },
    {
      prop: "bookNameAlias",
      label: "别名",
      width: "200px",
    },
    {
      prop: "bookTypeName",
      label: "类型",
      slot: true,
      width: "150px",
    },
    {
      prop: "sourceName",
      label: "来源",
      slot: true,
      width: "130px",
    },
    {
      prop: "sysStageName",
      label: "学段",
      width: "110px",
    },
    {
      prop: "sysSubjectName",
      label: "学科",
      width: "110px",
    },
    {
      prop: "sysGradeName",
      label: "年级",
      width: "110px",
    },
    {
      prop: "sysTermName",
      label: "学期",
      width: "110px",
    },
    {
      prop: "num",
      label: "试题",
      width: "100px",
    },
    {
      prop: "createTime",
      label: "创建时间",
      slot: true,
      width: "150px",
    },
    {
      prop: "year",
      label: "年份",
      width: "100px",
    },
    {
      prop: "schoolNum",
      label: "学校",
      width: "100px",
      slot: true,
    },
    {
      prop: "bookAreaList",
      label: "所属地区",
      width: "100px",
      slot: true,
    },
    {
      prop: "version",
      label: "版本",
      slot: true,
      width: "200px",
      tooltip: false,
    },
    {
      prop: "state",
      label: "发布状态",
      slot: true,
      width: "100px",
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
      width: "280px",
      fixed: "right",
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})
const status = {
  0: {
    name: "未发布",
    class: "text-error",
  },
  1: {
    name: "审核中",
    class: "text-primary",
  },
  2: {
    name: "已发布",
    class: "text-success",
  },
  3: {
    name: "已驳回",
    class: "text-warning",
  },
}
let info = $ref<any>({})
function renderLabel({ option }) {
  return h(GTooltip, { content: option.label, refName: String(option.key) }, {})
}
let isEdit = $ref(false)
/* 获取学段 */
async function getStage() {
  let res = await getNewStageListApi()
  formOptions.items.sysStage.options = res.map((v) => {
    return {
      value: v.id,
      label: v.title,
    }
  })
}
/* 获取学科 */
async function getSubject() {
  let res = await getNewSubjectListApi({
    sysStageId: formOptions.data.sysStage,
  })
  formOptions.items.sysSubjectId.options = res.map((v) => {
    return {
      value: v.sysSubjectId,
      label: v.sysCourseName,
    }
  })
}
async function run(item) {
  try {
    const res = await runApi({ bookId: item?.bookId })
    $g.msg("预跑成功", "success")
  } catch (err) {
    console.log("err", err)
  }
}

/* 获取类型 */
async function getTypeSelectApi() {
  let res = await getBookTypeTree({
    sysStageId: formOptions.data.sysStage,
    category: 1,
  })
  formOptions.items.bookTypeId.options = transformDataStructure(res)
}
/* 获取来源 */
async function getBookSourceList() {
  let res = await getBookSource()
  formOptions.items.bookSource.options = res
}

/* 递归处理数据 */
function transformDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.label = newItem.bookTypeName
      newItem.key = newItem.bookTypeId
      newItem.children = newItem.list?.length
        ? transformDataStructure(newItem.list)
        : null

      return newItem
    })
  } else if (typeof data === "object" && data !== null) {
    return Object.keys(data).reduce((obj, key) => {
      obj[key] = transformDataStructure(data[key])
      return obj
    }, {})
  }
  return data
}
/* 获取发布状态 */
async function getStatusSelectApi() {
  let res = await getStatusSelect()
  formOptions.items.state.options = res.map((v) => {
    return {
      label: v.stateName,
      value: v.state,
    }
  })
}
function goToXKW() {
  console.error("xkwOpenId", location.protocol)
  const params = new URLSearchParams({
    _openid: xkwOpenId,
    _m: `${location.protocol}//zujuan.qimingdaren.com/#/resourceMgt/bookManage`,
  })
  const url = `${
    location.protocol
  }//zujuan.qimingdaren.com/#/iframe?${params.toString()}`
  handler = window.open(url)
}
async function goToXKWAuth() {
  let agreement =
    import.meta.env.VITE_APP_ENV != "development" ? "https" : "http"
  console.error("goToXKWAuth", agreement)
  const res = await fetchExamXKWOAuth({
    redirectUri: `${agreement}://zujuan.qimingdaren.com/#/resourceMgt/bookManage`,
    xkwService: "COMPOSITION",
  })
  if (res) {
    location.href = res
  }
}
async function bindExamXKW() {
  const { code, state } = route.query
  if (code && state) {
    const data = await bindExamXKWApi({
      code,
      state,
    })
    console.log("data", data)
    xkwOpenId = data?.openId
    if (data?.openId) {
      console.log("data?.openId", data?.openId)
      $g.msg("绑定成功", "success")
      location.replace(`${baseOrigin}/#/resourceMgt/bookManage`)
    }
  }
}
async function getXKWBind() {
  const data = await fetchExamXKWBind()
  xkwOpenId = data?.openId
  if (!xkwOpenId) {
    bindExamXKW()
  }
}
function postMessageToXKW() {
  if (handler) {
    handler.postMessage("close", `${location.protocol}//zujuan.qimingdaren.com`)
  }
}
function onGoXKW() {
  if (xkwOpenId) {
    goToXKW()
  } else {
    goToXKWAuth()
  }
}
watch(
  () => formOptions.data.sysStage,
  async (val) => {
    if (val) {
      formOptions.items.sysSubjectId.options = []
      formOptions.data.sysSubjectId = null
      formOptions.data.bookTypeId = null
      formOptions.items.bookTypeId.options = []
      await getSubject()
      await getTypeSelectApi()
    }
  },
)
/* 书籍列表 */
async function initData() {
  try {
    tableOptions.loading = true
    let res = await getListApi({
      ...formOptions.data,
      category: 1,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    tableOptions.data = res.list.map((v) => ({
      ...v,
      versionList: getVersionList(v.sysTextbookVersionList),
    }))
    tableOptions.pageOptions.total = res.total
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
  }
}

function getVersionList(list) {
  return (
    list?.flatMap((v) => {
      if (v.sysTextbookList.length) {
        return v.sysTextbookList.map(
          (vv) => v.sysTextbookVersionName + "/" + vv.sysTextbookName,
        )
      }
      return [v.sysTextbookVersionName]
    }) || []
  )
}

onBeforeUnmount(() => {
  window.removeEventListener("message", onPostMessage)
})
/* 搜索 */
async function search() {
  tableOptions.pageOptions.page = 1
  initData()
}
/* 编辑 */
async function edit(row) {
  // const { hasOk } = await hasLock({ bookId: row.bookId })
  // if (hasOk == 2) {
  //   $g.msg("该试卷有用户正在编辑，请稍后再试!", "warning")
  // } else {
  isEdit = true
  isCopy = false
  info = row
  showDialog = true
  // }
}

/*复制 */
function copy(row) {
  isCopy = true
  isEdit = true
  info = row
  showDialog = true
}
/* 删除 */
async function deleteRow(row) {
  // const { hasOk } = await hasLock({ bookId: row.bookId })
  // if (hasOk == 2) {
  //   $g.msg("该试卷有用户正在编辑，请稍后再试!", "warning")
  // } else {
  $g.confirm({
    content: "是否删除该书籍",
  })
    .then(async () => {
      await deleteBookApi({
        bookId: row.bookId,
      })
      $g.msg("删除成功")
      await initData()
    })
    .catch(() => {})
  // }
}
/* 进入资源 */
function toResource(row) {
  //修改面包屑title
  routerStore.changeMenuMeta({
    name: "EnterResoure",
    meta: {
      title: "书籍管理详情",
    },
  })
  resourceMgtStore.lockData.category = 1
  router.push({
    name: "EnterResoure",
    query: {
      bookId: row.bookId,
      sysCourseId: row.sysCourseId,
      thirdUniqueId: row.thirdUniqueId,
      bookSource: row.bookSource,
      sysStage: row.sysStageId,
      sysStageName: row.sysStageName,
      sysSubjectId: row.sysSubjectId,
      sysSubjectName: row.sysSubjectName,
      bookName: row.bookName || "默认",
      category: 1,
    },
  })
}
async function loadXKWQuestion() {
  if (xkwOpenId && xkwPaperId) {
    // @todo 添加到列表
    // 重新请求列表
    getQuestions()
  }
}

async function getQuestions() {
  const res = await getPaperQuestion({
    openId: xkwOpenId,
    paperId: xkwPaperId,
  })
  showDialog = true
  isEdit = false
  info = { ...res, sysStageId: res?.sysStage, sysSubjectId: res?.sysSubjectId }
  console.log("info", info)
}
function onPostMessage(event) {
  const { data } = event
  if (data) {
    const { paperid, openid } = data
    if (paperid && openid && openid === xkwOpenId) {
      xkwPaperId = paperid
      postMessageToXKW()
      loadXKWQuestion()
    }
  }
}

let showTimeDialog = $ref<boolean>(false)

const syncExamFromOptions = $ref<any>({
  ref: null as any,
  filter: false,
  loading: false,
  items: {
    asyncTime: {
      type: "daterange",
      label: "时间范围",
      isDateDisabled: (current, phase, value) => {
        if (phase === "start" && !value) return false
        return Math.abs(current - value[1]) > 1000 * 60 * 60 * 24 * 6
      },
      rule: {
        required: true,
      },
    },
  },
  data: {
    asyncTime: null,
  },
})

let asyncLoading = $ref<boolean>(false)

async function syncExamConfirm() {
  try {
    asyncLoading = true
    await getSyncAdmin({
      syncStartDate: syncExamFromOptions.data.asyncTime[0],
      syncEndDate: syncExamFromOptions.data.asyncTime[1],
    })
    showTimeDialog = false
    asyncLoading = false
    $g.msg("考试同步完成", "success")
    await initData()
  } catch (e) {
    asyncLoading = false
    syncExamFromOptions.loading = false
    $g.msg("考试同步失败", "error")
    console.log(e)
  }
}

// 同步考试
function syncExam() {
  syncExamFromOptions.loading = false
  showTimeDialog = true
  syncExamFromOptions.data.asyncTime = null
}

// 题目搜索
function goSearchQuestion() {
  router.push({
    name: "QuestionSearch",
  })
}

onMounted(() => {
  initData()
  getStage()
  getStatusSelectApi()
  getXKWBind()
  getBookSourceList()
  window.addEventListener("message", onPostMessage)
})

onActivated(() => {
  initData()
})
</script>

<style lang="scss" scoped></style>
