<template>
  <g-dialog title="预览" v-bind="$attrs" :show-footer="false">
    <g-img
      width="400"
      :src="url"
      :optimization="false"
      object-fit="contain"
      :preview-disabled="false"
    />
  </g-dialog>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
const props = defineProps({
  imgInfo: {
    type: Object as PropType<any>,
    required: true,
  },
})
const url = $computed(() => {
  const blob = new Blob([props.imgInfo], { type: "image/png" })
  const objectUrl = URL.createObjectURL(blob)
  return objectUrl
})
</script>

<style lang="scss" scoped></style>
