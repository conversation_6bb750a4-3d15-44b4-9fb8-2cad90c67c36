<template>
  <div v-loading="loading" ref="listRef">
    <template v-if="data.length" v-for="item in data" :key="item.questionId">
      <QuestionExhibition :question="item" :show-audio="true">
        <template #left-header>
          <span v-if="'searchScore' in item">
            搜索分数：{{ item.searchScore?.toFixed(2) }}
          </span>
        </template>
        <template #action>
          <n-button type="primary" text @click="editQues(item)">编辑</n-button>
        </template>
      </QuestionExhibition>
    </template>
    <g-empty v-else></g-empty>
    <g-page
      v-if="$g.tool.isTrue(pageOptions)"
      :pageOptions="pageOptions"
      @change="changePage"
    ></g-page>
    <!-- 播放音频 -->
    <Speaker v-model:show="showAudio" :width="width" ref="speakerRef" />
  </div>
</template>

<script setup lang="ts">
import Speaker from "@/views/resourceMgt/bookManage/enterResoure/components/SubtitleSpeaker.vue"
const router = useRouter()
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  pageOptions: {
    type: Object,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})
let showAudio = $ref(false)
const emit = defineEmits(["changePage", "fastGpt", "openAiExplain"])
function changePage() {
  emit("changePage")
}
let width = $ref(0)
let listRef = $ref<any>(null)
onMounted(() => {
  nextTick(() => {
    width = listRef.clientWidth
  })
})

const route = useRoute()
/* 编辑试题 */
function editQues(item) {
  router.push({
    name: "PaperEdit",
    query: {
      questionId: item.questionId,
      sysCourseId: item.sysCourseId,
      category: route.query.category,
      activeName:
        (route.query.category as any) == 2 ? "PaperManage" : "BookManage",
    },
  })
}
</script>

<style lang="scss" scoped></style>
