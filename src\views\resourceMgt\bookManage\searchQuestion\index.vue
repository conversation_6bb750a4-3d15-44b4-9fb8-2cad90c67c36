<template>
  <div class="search-question-container-main">
    <g-form
      :formOptions="formOptions"
      @search="search"
      @reset="ReSearch"
      :table-options="tableOptions"
    >
    </g-form>
    <div class="w-[500px]">
      <el-upload
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="removeFile"
        v-model:file-list="imgList"
        :limit="1"
        :on-preview="preview"
        :accept="acceptType"
      >
        <template #tip>
          <div class="text-gray-default">
            只允许上传{{ acceptType }},且图片最大不超过2M
          </div>
        </template>
        <el-button type="primary" :disabled="imgList.length == 1"
          >图片搜索</el-button
        >
      </el-upload>
    </div>
    <QuestionList
      :data="tableOptions.data"
      :loading="tableOptions.loading"
      :page-options="tableOptions.pageOptions"
      @changePage="getQuestionList"
    />
    <ImgDialog :imgInfo="imgInfo" v-model:show="showDialog" />
  </div>
</template>

<script setup lang="ts" name="QuestionSearch">
import QuestionList from "./components/QuestionList.vue"
import ImgDialog from "./components/ImgDialog.vue"
import { getEsQuestion, getEsQuestionImage, getImageToStr } from "@/api/bookMgt"
const formOptions = $ref<any>({
  ref: null as any,
  filter: true,
  loading: false,
  items: {
    keyword: {
      type: "text",
      label: "题目字段检索",
      showLabel: false,
      width: "500px",
      noSideSpace: false,
      allowInput: true,
    },
  },
  data: {
    keyword: "",
  },
})
let acceptType = $ref(".png,.jpg,.jpeg")
const emit = defineEmits(["changePage"])
const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: false,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
  data: [] as any[],
})
let imgList = $ref<any>([])
let imgText = $ref("")
let imgInfo = $ref<any>({})
let showDialog = $ref(false)
/* 预览 */
function preview(file) {
  imgInfo = file.raw
  showDialog = true
}
/* 获取文件对象 */
async function handleFileChange(file, fileList) {
  if (file.size > 1024 * 1024 * 2) {
    nextTick(() => {
      imgList = []
    })
    return $g.msg("图片不能超过2mb", "error")
  }
  try {
    let status = fileList.every((v) => v.status == "ready")
    if (status) {
      tableOptions.loading = true
      let res = await getImageToStr({ img: file.raw })
      imgText = res
      await getQuestionList()
    }
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
  }
}

function removeFile(file, fileList) {
  tableOptions.pageOptions.page = 1
  formOptions.data.keyword = ""
  imgText = ""
  getQuestionList()
}
/* 试题列表 */
async function search() {
  imgList = []
  imgText = ""
  await getQuestionList()
}
async function getQuestionList() {
  try {
    tableOptions.loading = true
    const data = await getEsQuestion({
      questionText: imgText ? imgText : formOptions.data.keyword,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    if (data) {
      tableOptions.pageOptions.total = data.total
      tableOptions.data = data.list.map((v) => ({
        ...v,
        explains: v.questionFiles
          .filter((vv) => vv.type === 1)
          .map((vv) => ({
            ...vv,
            name: vv.fileName,
            id: vv.questionFileId,
            status: "finished",
          })),
        files: v.questionFiles
          .filter((vv) => vv.type === 2)
          .map((vv) => ({
            ...vv,
            name: vv.fileName,
            id: vv.questionFileId,
            status: "finished",
          })),
      }))
      nextTick(() => {
        $g.tool.renderMathjax()
      })
    }
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
    tableOptions.data = []
  }
}

function ReSearch() {
  if (imgList.length) {
    imgList = []
    imgText = ""
  }
  tableOptions.pageOptions.page = 1
  tableOptions.pageOptions.total = 0
  tableOptions.data = []
}
onActivated(() => {
  getQuestionList()
})
</script>

<style lang="scss" scoped>
:deep() {
  .el-upload-list__item-name {
    cursor: pointer;
  }
}
</style>
