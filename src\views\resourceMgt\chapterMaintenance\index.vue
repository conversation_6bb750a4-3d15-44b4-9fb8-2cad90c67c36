<template>
  <div class="chapter-maintenance-container-main">
    <div class="flex gap-x-[40px] mb-20px">
      <div>学段：{{ route.query.sysStageName }}</div>
      <div>学科：{{ route.query.sysCourseName }}</div>
      <div>版本：{{ route.query.sysTextbookVersionsNameAlias }}</div>
      <div>教材：{{ route.query.sysTextbooksNameAlias }}</div>
    </div>
    <div class="flex mb-[20px]">
      <span class="flex-shrink-0 mr-[40px]">封面图片:</span>
      <g-upload
        v-model:fileList="list"
        type="image-card"
        :max="1"
        accept=".png,.jpeg,.jpg,.svg,.gif"
        @onChange="uploadFile"
      ></g-upload>
    </div>
    <div class="w-full">
      <div class="flex items-center justify-between">
        <div>
          章节树： <span class="text-gray-default">(支持同级拖拽排序)</span>
        </div>

        <n-button type="primary" @click="handleOperater('add')">
          <template #icon>
            <g-icon name="ri-add-fill" size="" color="" />
          </template>
          新建章节
        </n-button>
      </div>
      <div>
        <g-icon
          name="ri-information-line"
          size=""
          color="#fd9a69"
          class="mr-4px"
        />
        <span>考试说明、学习目标已编辑的为绿色，未编辑的为蓝色</span>
      </div>
      <g-tree
        :treeData="treeData"
        nodeKey="sysTextbookCatalogId"
        :highlight-check="false"
        draggable
        :default-expanded-keys="[currentId]"
        :allow-drop="allowDrop"
        @node-drop="handleDrop"
        style="width: 100%"
      >
        <template #body="{ data }">
          <div class="flex w-full justify-between items-center">
            <span class="title">{{ data.sysTextbookCatalogName }}</span>
            <n-space>
              <span class="text-[#8C74F2]">期末五星评价</span>
              <n-rate
                class="mr-15px"
                color="#8C74F2"
                v-model:value="data.endTermEvaluate"
                @update:value="
                  save(
                    {
                      endTermEvaluate: data.endTermEvaluate,
                    },
                    data,
                  )
                "
              ></n-rate>
              <span
                v-if="route.query.sysStageName?.includes('高')"
                class="text-[#1ea0f0]"
                >高考五星评价</span
              >
              <n-rate
                class="mr-15px"
                color="#1ea0f0"
                v-model:value="data.collegeExamEvaluate"
                @update:value="
                  save(
                    {
                      collegeExamEvaluate: data.collegeExamEvaluate,
                    },
                    data,
                  )
                "
                v-if="route.query.sysStageName?.includes('高')"
              ></n-rate>

              <n-button
                text
                :type="data.examDescription ? 'success' : 'primary'"
                @click="openExam(data)"
                >考试说明</n-button
              >
              <n-button
                text
                :type="data.learnTarget ? 'success' : 'primary'"
                @click="openTarget(data)"
                >学习目标</n-button
              >

              <n-button
                text
                type="primary"
                class="underline"
                @click="editDescribe(data)"
                >{{ data.description ? "编辑描述" : "添加描述" }}</n-button
              >
              <n-button
                text
                type="primary"
                class="underline"
                @click="handleOperater('add', data)"
                >添加子章节</n-button
              >
              <n-button
                text
                type="primary"
                class="underline"
                @click="handleOperater('edit', data)"
                >更名</n-button
              >
              <n-button
                text
                type="primary"
                class="underline"
                v-if="data.isDelete === 1"
                @click="handleOperater('disable', data)"
                >禁用</n-button
              >
              <n-button
                text
                type="primary"
                class="underline"
                v-if="data.isDelete === 2"
                @click="handleOperater('enable', data)"
                >启用</n-button
              >
            </n-space>
          </div>
        </template>
      </g-tree>
    </div>

    <g-dialog
      :title="isEdit ? '更名' : '新增'"
      v-model:show="showDialog"
      :formOptions="formOptions"
      @confirm="confirm"
      width="600"
    >
      <g-form :formOptions="formOptions"> </g-form>
    </g-dialog>
    <g-dialog
      :title="isDescribeEdit ? '编辑描述' : '添加描述'"
      v-model:show="showDescribeDialog"
      :formOptions="describeFormOptions"
      @confirm="describeConfirm"
      width="950"
    >
      <g-form :formOptions="describeFormOptions">
        <template #describeContext>
          <g-editor
            v-model="describeFormOptions.data.describeContext"
            :oss="{
              params: {
                application: 'homeworkDesc',
              },
            }"
            :initProps="{
              placeholder: '请输入章节描述',
              menubar: false,
              statusbar: false,
              max: 200,
              height: 300,
              ax_wordlimit_num: 200,
              toolbar: [
                'undo redo | blocks | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | forecolor backcolor removeformat | image table insertdatetime | preview code fullscreen',
              ],
            }"
          >
          </g-editor>
        </template>
      </g-form>
    </g-dialog>
    <el-dialog
      :title="title"
      v-model="showClarificationDialog"
      :close-on-click-modal="false"
      :z-index="1005"
      top="20vh"
      width="950"
    >
      <!-- <g-ueditor
        :config="{ initialFrameHeight: 200 }"
        v-model="dialogDescribe"
      ></g-ueditor> -->
      <g-markdown
        class="w-full"
        height="500px"
        v-model="dialogDescribe"
      ></g-markdown>
      <template #footer>
        <div>
          <el-button @click="showClarificationDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="
              save(
                {
                  [title === '学习目标' ? 'learnTarget' : 'examDescription']:
                    dialogDescribe,
                },
                curNode,
              )
            "
            :loading="btnLoading"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {
  getChapterTreeList,
  addChapter,
  updateChapter,
  disableChapter,
  enableChapter,
  sortChapter,
  uploadCover,
  getMainTree,
  getSaveDescribe,
  saveEvaluation,
} from "@/api/resourceMgt"
let list = $ref<any>([])
let treeData = $ref([])
let showDialog = $ref(false)
const route = useRoute()
let treeTableRef = $ref<any>(null)
let title = $ref("考试说明")
let showClarificationDialog = $ref(false)
let dialogDescribe = $ref("")
let btnLoading = $ref(false)
const initData = async () => {
  let res = await getChapterTreeList({
    sysTextbookId: route.query.sysTextbookId,
    isDelete: route.query.isDelete,
    source: route.query.source,
  })
  treeData = res || []
  if (isMainTree == 1) {
    treeTableRef.initializeTableData(treeData)
  }
}
let curNode = $ref<any>({})
let isEdit = $ref(false)
let isMainTree: any = $ref(0)
let mainTree: any = $ref({})
onMounted(async () => {
  // await getMainTreeApi()
  initData()
})
const currentId = $computed(() => {
  return curNode.sysTextbookCatalogId || 0
})
async function addQuery() {
  try {
    await addChapter({
      ...formOptions.data,
      sysTextbookId: route.query.sysTextbookId,
      parentSysTextbookCatalogId: curNode.sysTextbookCatalogId,
    })

    $g.msg("新增成功")
    initData()
    formOptions.loading = false
    showDialog = false
  } catch {
    formOptions.loading = false
  }
}
watch(
  () => route.query.sysTextbooksCover,
  (val) => {
    if (!val) return
    list = [{ url: val }]
  },
  {
    immediate: true,
  },
)
async function updateQuery() {
  try {
    await updateChapter({
      ...formOptions.data,
      sysTextbookCatalogId: curNode.sysTextbookCatalogId,
    })
    curNode.sysTextbookCatalogName = formOptions.data.sysTextbookCatalogName
    showDialog = false
    $g.msg("修改成功")
    treeTableRef.initializeTableData(treeData)
    formOptions.loading = false
  } catch {
    formOptions.loading = false
  }
}
async function confirm() {
  if (isEdit) {
    await updateQuery()
  } else {
    await addQuery()
    initData()
  }
}
function handleOperater(type, params?) {
  if (!params)
    curNode = {
      sysTextbookCatalogId: 0,
    }
  else {
    curNode = params
  }
  let funMap = {
    add: () => {
      isEdit = false
      formOptions.data.sysTextbookCatalogName = null
      showDialog = true
    },
    edit: () => {
      isEdit = true
      formOptions.data.sysTextbookCatalogName = curNode.sysTextbookCatalogName
      showDialog = true
    },
    disable: async () => {
      await disableChapter({
        sysTextbookCatalogId: curNode.sysTextbookCatalogId,
      })
      $g.msg("禁用成功")
      curNode.isDelete = 2
      // initData()
    },
    enable: async () => {
      await disableChapter({
        sysTextbookCatalogId: curNode.sysTextbookCatalogId,
      })
      $g.msg("启用成功")
      curNode.isDelete = 1
      // initData()
    },
  }
  funMap[type]()
}
const formOptions = reactive({
  ref: null as any,
  loading: false,
  items: {
    sysTextbookCatalogName: {
      type: "text",
      label: "章节名称",
      // placeholder: "请输入讲师姓名",
      width: "500px",
      rule: true,
      maxlength: 30,
      // span: 12,
    },
  },
  data: {
    sysTextbookCatalogName: null,
  },
})
async function uploadFile(file) {
  if (!file) return
  await uploadCover({
    sysTextbookId: route.query.sysTextbookId,
    sysTextbooksCover: file.resource_url,
  })
  $g.msg("上传成功")
}
const name = computed(() => {
  let {
    sysTextbooksNameAlias,
    sysCourseName,
    sysStagesName,
    sysTextbookVersionsNameAlias,
  } = route.query
  return [
    sysStagesName,
    sysCourseName,
    sysTextbookVersionsNameAlias,
    sysTextbooksNameAlias,
  ]
    .filter(Boolean)
    .join("/")
})
const mainTreeName = computed(() => {
  let { mainSysStagesName, mainSysSubjectName, mainSysTextbookVersionsName } =
    mainTree
  return [mainSysStagesName, mainSysSubjectName, mainSysTextbookVersionsName]
    .filter(Boolean)
    .join("/")
})
function allowDrop(draggingNode, dropNode, type) {
  let data1 = draggingNode.data
  let data2 = dropNode.data
  if (
    data1.parentSysTextbooksCatalogId !== data2.parentSysTextbooksCatalogId ||
    draggingNode.level !== dropNode.level
  )
    return false
  if (data1.sysTextbookCatalogId === data2.sysTextbookCatalogId) return false
  return type === "prev" || type === "next"
}
function getNode(node, findId) {
  if (node.sysTextbookCatalogId === findId) return node
  if (node.children?.length) {
    for (let item of node.children) {
      let res = getNode(item, findId)
      if (res) return res
    }
  }
}
async function handleDrop(node, ev) {
  let find = getNode(
    { children: treeData, sysTextbookCatalogId: 0 },
    node.data.parentSysTextbookCatalogId,
  )
  if (!find) return
  await sortChapter({
    sortData: find.children.map((item, index) => {
      return {
        sysTextbookCatalogId: item.sysTextbookCatalogId,
        ordinal: index,
      }
    }),
  })
  $g.msg("排序成功")
  initData()
}
async function getMainTreeApi() {
  let data = await getMainTree({
    sysTextbookVersionsId: route.query.sysTextbookVersionsId,
  })
  if (data == null) {
    isMainTree = 2
    return
  }
  if (data.mainSysTextbookVersionsId == route.query.sysTextbookVersionsId) {
    isMainTree = 0
  } else {
    mainTree = data
    isMainTree = 1
  }
}

function editDescribe(data) {
  curNode = data
  isDescribeEdit = false
  describeFormOptions.data = {
    describeContext: null,
  }
  if (data.description) {
    describeFormOptions.data.describeContext = data.description
    isDescribeEdit = true
  }
  showDescribeDialog = true
}
let isDescribeEdit = $ref<boolean>(false)
let showDescribeDialog = $ref<boolean>(false)
const describeFormOptions = reactive<any>({
  ref: null as any,
  loading: false,
  items: {
    describeContext: {
      type: "",
      label: "描述内容",
      rule: true,
      maxlength: 500,
      slot: true,
      span: 20,
    },
  },
  data: {
    describeContext: null,
  },
})

async function describeConfirm() {
  try {
    describeFormOptions.loading = true
    let params = {
      sysTextbookCatalogId: curNode.sysTextbookCatalogId,
      description: describeFormOptions.data.describeContext,
    }
    await getSaveDescribe(params)
    $g.msg("章节描述保存成功！", "success")
    showDescribeDialog = false
    initData()
  } catch (error) {
    console.log(error)
  } finally {
    describeFormOptions.loading = false
  }
}

async function save(params, data) {
  try {
    btnLoading = true
    await saveEvaluation({
      sysTextbookCatalogId: data.sysTextbookCatalogId,
      ...params,
    })
    $g.msg("保存成功")
    btnLoading = false
    showClarificationDialog = false
    Object.keys(params).forEach((key) => {
      data[key] = params[key]
    })
  } catch {
    btnLoading = false
  }
}

function openExam(data) {
  title = "考试说明"
  curNode = data
  dialogDescribe = data.examDescription ?? ""
  showClarificationDialog = true
}

function openTarget(data) {
  title = "学习目标"
  curNode = data
  dialogDescribe = data.learnTarget ?? ""
  showClarificationDialog = true
}
</script>
<style lang="scss" scoped>
.highlight-check {
  :deep() {
    .is-checked > .el-tree-node__content {
      .title {
        font-weight: bold;
      }
    }
  }
}

:deep() {
  .el-tree-node__content {
    min-height: 40px;
    height: auto;
  }
}
</style>
