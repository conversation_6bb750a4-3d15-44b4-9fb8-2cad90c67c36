<template>
  <g-dialog
    :title="props.data.sysTextbookId ? '修改教材' : '新建教材'"
    :formOptions="formOptions"
    v-bind="$attrs"
  >
    <g-form :formOptions="formOptions">
      <template #sysStageId> {{ data.sysStageName }} </template>
      <template #sysCourseId>
        {{ data.sysCourseName }}
      </template>
      <template #sysTextbookVersionId>
        {{ data.sysTextbookVersionName }}
      </template>
    </g-form>
  </g-dialog>
</template>
<script lang="ts" setup>
import {
  getNewStageListApi,
  getNewSubjectListApi,
  getNewVersionListApi,
  getTermSelect,
} from "@/api/resourceMgt"
import { getGradeListApi } from "@/api/common"
const attrs = useAttrs()
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
  filterFormOptions: {
    type: Object,
    default: () => {},
  },
})
const formOptions: any = reactive({
  ref: null as any,
  items: {
    sysStageId: {
      type: "select",
      label: "学段",
      options: [],
      labelField: "title",
      valueField: "id",
      width: "100%",
      span: 24,
      rule: true,
      slot: false,
    },
    sysCourseId: {
      type: "select",
      label: "学科",
      labelField: "sysCourseName",
      valueField: "sysCourseId",
      options: [],
      width: "100%",
      span: 24,
      rule: true,
      slot: false,
    },
    sysTextbookVersionId: {
      type: "select",
      label: "版本",
      labelField: "sysTextbookVersionName",
      valueField: "sysTextbookVersionId",
      options: [],
      width: "100%",
      span: 24,
      rule: true,
      slot: false,
    },
    sysTextbookName: {
      type: "text",
      label: "教材名称",
      placeholder: "最多30个字",
      width: "100%",
      maxlength: 30,
      span: 24,
      rule: true,
    },
    ordinal: {
      type: "number",
      label: "排序值",
      placeholder: "越小越靠前",
      width: "100%",
      span: 24,
      rule: true,
      showButton: false,
    },
    sysTermId: {
      type: "select",
      label: "学期",
      labelField: "sysTermName",
      valueField: "sysTermId",
      options: [],
      rule: true,
    },
    sysGradeId: {
      type: "select",
      label: "年级",
      options: [],
      labelField: "sysGradeName",
      valueField: "sysGradeId",
      rule: true,
    },
  },
  data: {
    sysTextbookName: null,
    ordinal: null,
    sysStageId: null,
    sysCourseId: null,
    sysTextbookVersionId: null,
    gradeId: null,
    sysTermId: null,
    sysGradeId: null,
  },
})
async function getNewVersionList() {
  const res = await getNewVersionListApi({
    sysCourseId: formOptions.data.sysCourseId,
  })
  formOptions.items.sysTextbookVersionId.options = res
}
async function getNewSubjectList() {
  const res = await getNewSubjectListApi({
    sysStageId: formOptions.data.sysStageId,
  })
  formOptions.items.sysCourseId.options = res
}

async function getNewStageList() {
  const data = await getNewStageListApi()
  formOptions.items.sysStageId.options = data
}

/* 获取学期 */
async function getTermList() {
  const res = await getTermSelect()
  formOptions.items.sysTermId.options = res
}
/* 获取年级 */
async function getGradeList() {
  let res = await getGradeListApi({
    sysStageId: formOptions.data.sysStageId,
  })
  formOptions.items.sysGradeId.options = res
  console.log(res)
}
watch(
  () => formOptions.data.sysStageId,
  (val) => {
    formOptions.items.sysCourseId.options = []
    formOptions.data.sysCourseId = null
    formOptions.items.sysGradeId.options = []
    if (!props.data.sysTextbookId) formOptions.data.sysGradeId = null
    if (val) {
      getNewSubjectList()
      getGradeList()
    }
  },
)

watch(
  () => formOptions.data.sysCourseId,
  (val) => {
    formOptions.items.sysTextbookVersionId.options = []
    formOptions.data.sysTextbookVersionId = null
    if (val) {
      getNewVersionList()
    }
  },
)
watch(
  () => props.data,
  () => {
    console.log(props.data)
    formOptions.data = { ...props.data }
  },
)

watch(
  () => attrs.show,
  (val) => {
    if (val) {
      getNewStageList()
      getTermList()
      const disabled = Boolean(props.data.sysTextbookId)
      formOptions.items.sysStageId.slot = disabled
      formOptions.items.sysStageId.rule = !disabled
      formOptions.items.sysCourseId.slot = disabled
      formOptions.items.sysCourseId.rule = !disabled
      formOptions.items.sysTextbookVersionId.slot = disabled
      formOptions.items.sysTextbookVersionId.rule = !disabled
    }
  },
)
</script>
<style scoped></style>
