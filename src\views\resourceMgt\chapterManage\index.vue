<template>
  <div class="chapter-manage-container-main">
    <g-form
      @search="getList"
      @reset="getList"
      :formOptions="filterFormOptions"
      :tableOptions="tableOptions"
    >
    </g-form>
    <g-table :tableOptions="tableOptions" @changePage="getList">
      <template #header-right>
        <n-button type="primary" @click="onAddClick()">
          <template #icon>
            <g-icon name="ri-add-fill" size="" color="" />
          </template>
          新建
        </n-button>
      </template>
      <template #isDelete="{ row }">
        {{ row.isDelete == 1 ? "启用" : "禁用" }}
      </template>
      <template #cz="{ row }">
        <n-space :inline="true">
          <n-button
            text
            :type="row.isDelete == 1 ? 'error' : 'success'"
            @click="open(row)"
            >{{ row.isDelete == 1 ? "禁用" : "启用" }}</n-button
          >
          <n-button text type="primary" @click="onAddClick(row)">修改</n-button>
          <n-button text type="primary" @click="setChapter(row)"
            >章节维护</n-button
          >
        </n-space>
      </template>
    </g-table>

    <AddDialog
      :data="data"
      v-model:show="showAddDialog"
      :filterFormOptions="filterFormOptions"
      @confirm="onConfirm($event)"
    >
    </AddDialog>
  </div>
</template>
<script setup lang="ts">
import {
  getBookList,
  stopBookApi,
  addBookApi,
  editBookApi,
} from "@/api/resourceMgt"
import {
  getStageSelect,
  getGradeSelect,
  getSubjectSelect,
} from "@/api/activity"
import AddDialog from "./components/AddDialog.vue"
const router = useRouter()

let showAddDialog = $ref(false)
let data: any = $ref({})
const gradeOption = [
  {
    label: "小学一年级",
    value: 1,
    sysStagesName: "小学",
    sysStagesId: 2,
  },
  {
    label: "小学二年级",
    value: 2,
    sysStagesName: "小学",
    sysStagesId: 2,
  },
  {
    label: "小学三年级",
    value: 3,
    sysStagesName: "小学",
    sysStagesId: 2,
  },
  {
    label: "小学四年级",
    value: 4,
    sysStagesName: "小学",
    sysStagesId: 2,
  },
  {
    label: "小学五年级",
    value: 5,
    sysStagesName: "小学",
    sysStagesId: 2,
  },
  {
    label: "小学六年级",
    value: 6,
    sysStagesName: "小学",
    sysStagesId: 2,
  },
  {
    label: "初一",
    value: 7,
    sysStagesName: "初中",
    sysStagesId: 3,
  },
  {
    label: "初二",
    value: 8,
    sysStagesName: "初中",
    sysStagesId: 3,
  },
  {
    label: "初三",
    value: 9,
    sysStagesName: "初中",
    sysStagesId: 3,
  },
  {
    label: "高一",
    value: 10,
    sysStagesName: "高中",
    sysStagesId: 4,
  },
  {
    label: "高二",
    value: 11,
    sysStagesName: "高中",
    sysStagesId: 4,
  },
  {
    label: "高三",
    value: 12,
    sysStagesName: "高中",
    sysStagesId: 4,
  },
]
const filterFormOptions = reactive({
  ref: null as any,
  filter: true,
  labelWidth: "84px",
  items: {
    sysStageId: {
      type: "select",
      label: "学段",
      width: "130px",
      options: [],
    },
    // 年级
    sysGradeId: {
      type: "select",
      label: "年级",
      width: "130px",
      options: [],
    },
    // 学科
    sysCourseId: {
      type: "select",
      label: "学科",
      width: "130px",
      options: [],
    },
    // 状态
    isDelete: {
      type: "select",
      label: "状态",
      width: "130px",
      options: [
        {
          label: "启用",
          value: 1,
        },
        {
          label: "禁用",
          value: 2,
        },
      ],
    },
    sysTextbookId: {
      type: "text",
      label: "教材ID",
      width: "140px",
    },
    keyword: {
      type: "text",
      label: "教材名称",
      width: "200px",
    },
  },
  // 列表接口使用到的筛选值,及时搜索时使用
  data: {
    sysTextbookId: null,
    keyword: null,
    sysStageId: null,
    sysGradeId: null,
    sysCourseId: null,
    isDelete: null,
  },
  // 列表接口使用到的筛选值,非及时搜索时使用
  filterData: {},
})
const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: false,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
  column: [
    { prop: "sysTextbookId", label: "教材ID" },
    { prop: "sysTextbookName", label: "教材名称" },
    { prop: "sysTextbookVersionName", label: "版本名称" },
    {
      prop: "sysGradeId",
      label: "年级",
      formatter(row) {
        let obj = gradeOption.find((item) => item.value == row.sysGradeId)
        return obj ? obj.label : "-"
      },
    },
    { prop: "sysStageName", label: "学段" },
    { prop: "sysCourseName", label: "学科" },
    { prop: "catalogNum", label: "章节数量" },
    { prop: "isDelete", label: "状态", slot: true },
    { prop: "cz", label: "操作", slot: true, width: "240px" },
  ],
  data: [],
})
async function getList() {
  try {
    tableOptions.loading = true
    const res = await getBookList({
      ...filterFormOptions.data,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.loading = false
    tableOptions.data = res.list
    tableOptions.pageOptions.total = res.total
  } catch (err) {
    tableOptions.loading = false
  }
}
/* 获取学段 */
async function getStageSelectApi() {
  const res = await getStageSelect()
  filterFormOptions.items.sysStageId.options = res?.map((h) => {
    return { ...h, label: h.title, value: h.id }
  })
}
/* 获取年级 */
async function getGradeSelectApi() {
  const res = await getGradeSelect({
    sysStageId: filterFormOptions.data.sysStageId,
  })
  filterFormOptions.items.sysGradeId.options = res?.map((h) => {
    return { ...h, label: h.title, value: h.id }
  })
}
/* 获取学科 */
async function getSubjectSelectApi() {
  const res = await getSubjectSelect({
    sysStageId: filterFormOptions.data.sysStageId,
  })
  filterFormOptions.items.sysCourseId.options = res?.map((h) => {
    return { ...h, label: h.sysCourseName, value: h.sysCourseId }
  })
}

async function addTextBook(val) {
  await addBookApi({
    sysCourseId: val.sysCourseId,
    sysTextbookVersionId: val.sysTextbookVersionId,
    sysTextbookName: val.sysTextbookName,
    ordinal: val.ordinal,
    sysTermId: val.sysTermId,
    sysGradeId: val.sysGradeId,
  })
  $g.msg("添加成功", "success")
  getList()
}
async function editTextBook(val) {
  await editBookApi({
    sysTextbookId: val.sysTextbookId,
    sysTextbookName: val.sysTextbookName,
    ordinal: val.ordinal,
    sysTermId: val.sysTermId,
    sysGradeId: val.sysGradeId,
  })
  $g.msg("修改成功", "success")
  getList()
}

function onConfirm(val) {
  if (data?.sysTextbookId) {
    editTextBook(val)
  } else {
    addTextBook(val)
  }
  showAddDialog = false
}

function onAddClick(row?) {
  data = row ? row : {}
  showAddDialog = !showAddDialog
}

async function open(row) {
  $g.confirm({
    content: `你确定要${row.isDelete == 1 ? "禁用" : "启用"}此版本？${
      row.isDelete == 1 ? "禁用" : "启用"
    }后用户端将${
      row.isDelete == 1 ? "不再展示" : "展示"
    }该版本及所包含的教材信息`,
  })
    .then(async () => {
      await stopBookApi({
        sysTextbookId: row.sysTextbookId,
      })
      $g.msg(`${row.isDelete == 1 ? "禁用" : "启用"}成功`)
      getList()
    })
    .catch((err) => {
      console.error(err)
    })
}

function setChapter(row) {
  router.push({
    name: "ChapterMaintenance",
    query: {
      sysTextbooksCover: row.sysTextbooksCover,
      sysTextbookId: row.sysTextbookId,
      sysTextbooksNameAlias: row.sysTextbookName,
      sysCourseName: row.sysCourseName,
      sysTextbookVersionId: row.sysTextbookVersionId,
      sysTextbookVersionsNameAlias: row.sysTextbookVersionName,
      source: row.source,
      isDelete: row.isDelete,
      sysStageName: row.sysStageName,
    },
  })
}
/* 监听学段 */
watch(
  () => filterFormOptions.data.sysStageId,
  async (val) => {
    // 置空
    filterFormOptions.data.sysGradeId = null
    filterFormOptions.data.sysCourseId = null
    filterFormOptions.items.sysGradeId.options = []
    filterFormOptions.items.sysCourseId.options = []
    if (!val) return
    await getGradeSelectApi()
    await getSubjectSelectApi()
  },
)

onBeforeMount(async () => {
  await getStageSelectApi()
  getList()
})
</script>
<style scoped></style>
