<template>
  <div class="fullscreenContainer">
    <div v-show="isZenMode">
      <el-tooltip class="item" effect="dark" content="全屏查看" placement="top">
        <div class="btn iconfont iconquanping" @click="toFullscreenShow"></div>
      </el-tooltip>
    </div>
    <div v-show="!isZenMode">
      <el-tooltip
        class="item"
        effect="dark"
        :content="mindMapStore.fullScreen ? '退出全屏' : '全屏编辑'"
        placement="top"
      >
        <g-icon
          :name="
            mindMapStore.fullScreen
              ? 'ri-fullscreen-exit-fill'
              : 'ri-fullscreen-fill'
          "
          size="18"
          @click="toFullscreenEdit"
        />
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { useMindMapStore } from "@/stores/modules/mindMap"
const mindMapStore = useMindMapStore()
const props = defineProps({
  mindMap: {
    type: Object,
  },
  isZenMode: {
    type: Boolean,
  },
})

//全屏事件检测
const fullScreenEvent = () => {
  if (document.documentElement.requestFullScreen) {
    return "onfullscreenchange"
  } else if (document.documentElement.webkitRequestFullScreen) {
    return "onwebkitfullscreenchange"
  } else if (document.documentElement.mozRequestFullScreen) {
    return "onmozfullscreenchange"
  } else if (document.documentElement.msRequestFullscreen) {
    return "onmsfullscreenchange"
  }
}

function fullScreen(el) {
  if (mindMapStore.fullScreen) {
    exitFullScreen(el)
  } else {
    openScreen(el)
  }
}

// 全屏
const openScreen = (element) => {
  if (element.requestFullScreen) {
    element.requestFullScreen()
  } else if (element.webkitRequestFullScreen) {
    element.webkitRequestFullScreen()
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen()
  }
}

// 退出全屏
const exitFullScreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen()
  } else if (document.msExitFullscreen) {
    document.msExitFullscreen()
  } else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen()
  } else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen()
  }
}

// 全屏查看
const toFullscreenShow = () => {
  fullScreen(props.mindMap.el)
  setTimeout(() => {
    props.mindMap.resize()
  }, 100)
}

// 全屏编辑
const toFullscreenEdit = () => {
  fullScreen(document.body)
}

function screenResize() {
  mindMapStore.fullScreen = document.fullscreenElement ? true : false
  setTimeout(() => {
    props.mindMap.resize()
  }, 100)
}

onMounted(() => {
  document[fullScreenEvent()] = screenResize
})

// onBeforeUnmount(() => {
//   document[fullScreenEvent()] = null
// })
</script>

<style lang="scss" scoped>
.fullscreenContainer {
  display: flex;
  align-items: center;
  // div:first-child {
  //   margin-right: 12px;
  // }
  .btn {
    cursor: pointer;
  }
}
</style>
