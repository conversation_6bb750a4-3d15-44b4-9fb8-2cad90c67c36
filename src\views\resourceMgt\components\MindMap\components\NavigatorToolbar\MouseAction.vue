<template>
  <div class="mouseActionContainer cursor-pointer">
    <el-tooltip
      class="item"
      effect="dark"
      :content="
        mindMapStore.localConfig.useLeftKeySelectionRightKeyDrag
          ? '当前：左键框选节点，右键拖动画布'
          : '当前：左键拖动画布，右键框选节点'
      "
      placement="top"
    >
      <div
        class="btn iconfont"
        :class="[
          mindMapStore.localConfig.useLeftKeySelectionRightKeyDrag
            ? 'iconmouseR'
            : 'iconmouseL',
        ]"
        @click="toggleAction"
      ></div>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { useMindMapStore } from "@/stores/modules/mindMap"
const mindMapStore: any = useMindMapStore()
const props = defineProps({
  mindMap: {
    type: Object,
    default: () => {},
  },
})

function toggleAction() {
  let val = !mindMapStore?.localConfig.useLeftKeySelectionRightKeyDrag
  props.mindMap.updateConfig({
    useLeftKeySelectionRightKeyDrag: val,
  })
  mindMapStore.setLocalConfig({
    useLeftKeySelectionRightKeyDrag: val,
  })
}
</script>

<style lang="scss" scoped></style>
