<template>
  <div class="scaleContainer select-none">
    <el-tooltip class="item" effect="dark" content="缩小" placement="top">
      <el-icon class="btn" @click="narrow">
        <Minus />
      </el-icon>
    </el-tooltip>
    <div class="scaleInfo">
      <input
        type="text"
        v-model="scaleNum"
        @change="onScaleNumChange"
        @focus="onScaleNumInputFocus"
      />%
    </div>
    <el-tooltip class="item" effect="dark" content="放大" placement="top">
      <el-icon class="btn" @click="enlarge">
        <Plus />
      </el-icon>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { Plus, Minus } from "@element-plus/icons-vue"
const props = defineProps({
  mindMap: {
    type: Object,
    default: () => {},
  },
})

let scaleNum: any = $ref(100)
let cacheScaleNum: any = $ref(0)

onMounted(() => {
  $g.bus.on("mind_map_view_data_change", watchMap)
})

const watchMap = $g._.throttle(() => {
  scaleNum = toPer(props.mindMap.view.scale)
}, 100)

//转换成百分数
const toPer = (scale) => {
  return (scale * 100).toFixed(0)
}

//缩小
const narrow = () => {
  props.mindMap.view.narrow()
  nextTick(() => {
    scaleNum = toPer(props.mindMap.view.scale)
  })
}

//放大
const enlarge = () => {
  props.mindMap.view.enlarge()
  nextTick(() => {
    scaleNum = toPer(props.mindMap.view.scale)
  })
}

// 聚焦时缓存当前缩放倍数
const onScaleNumInputFocus = () => {
  cacheScaleNum = scaleNum
}

// 手动输入缩放倍数
const onScaleNumChange = () => {
  const scaleNum2 = Number(scaleNum)
  if (Number.isNaN(scaleNum) || scaleNum2 <= 0) {
    scaleNum = cacheScaleNum
  } else {
    const cx = props.mindMap.width / 2
    const cy = props.mindMap.height / 2
    props.mindMap.view.setScale(scaleNum / 100, cx, cy)
  }
}
</script>

<style lang="scss" scoped>
.scaleContainer {
  display: flex;
  align-items: center;
  .btn {
    cursor: pointer;
  }

  .scaleInfo {
    margin: 0 5px;
    display: flex;
    align-items: center;
    input {
      width: 35px;
      text-align: center;
      background-color: transparent;
      border: none;
      outline: none;
    }
  }
}
</style>
