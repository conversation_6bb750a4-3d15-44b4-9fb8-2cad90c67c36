<template>
  <div class="searchContainer show">
    <div class="closeBtnBox">
      <el-icon class="closeBtn" @click="close">
        <Close />
      </el-icon>
    </div>
    <div class="searchInputBox">
      <el-input
        ref="searchInputRef"
        placeholder="请输入查找内容"
        v-model="searchText"
        @keyup.enter.stop="onSearchNext"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #append v-if="!isUndef(searchText)">
          <el-button @click="showReplaceInput = true"> 替换 </el-button>
        </template>
      </el-input>
      <div class="searchInfo" v-if="showSearchInfo">
        {{ currentIndex }} / {{ total }}
      </div>
    </div>
    <el-input
      v-if="showReplaceInput"
      ref="replaceInputRef"
      placeholder="请输入替换内容"
      v-model="replaceText"
      style="margin: 12px 0"
      @keydown.stop
    >
      <template #prefix>
        <el-icon><EditPen /></el-icon>
      </template>
      <template #append v-if="!!searchText.trim()">
        <el-button @click="hideReplaceInput"> 取消 </el-button>
      </template>
    </el-input>
    <div class="btnList" v-if="showReplaceInput">
      <el-button @click="replace">替换</el-button>
      <el-button @click="replaceAll">全部替换</el-button>
    </div>
  </div>
</template>

<script>
import { Close, Search, EditPen } from "@element-plus/icons-vue"
import { isUndef } from "simple-mind-map/src/utils/index"
let bus = $g.bus
// 搜索替换
export default {
  name: "Search2",
  components: {
    Close,
    Search,
    EditPen,
  },
  props: {
    mindMap: {
      type: Object,
    },
  },
  data() {
    return {
      show: false,
      searchText: "",
      replaceText: "",
      showReplaceInput: false,
      currentIndex: 0,
      total: 0,
      showSearchInfo: false,
    }
  },
  watch: {
    searchText() {
      if (isUndef(this.searchText)) {
        this.currentIndex = 0
        this.total = 0
        this.showSearchInfo = false
      }
    },
  },
  created() {
    this.mindMap.on("search_info_change", this.handleSearchInfoChange)
    this.mindMap.keyCommand.addShortcut("Control+f", this.search)
  },
  beforeUnmount() {
    this.mindMap.off("search_info_change", this.handleSearchInfoChange)
    this.mindMap.keyCommand.removeShortcut("Control+f", this.search)
  },
  methods: {
    isUndef,
    search() {
      this.$emit("changeShowSearch")
    },
    handleSearchInfoChange(data) {
      this.currentIndex = data.currentIndex + 1
      this.total = data.total
      this.showSearchInfo = true
    },
    showSearch() {
      bus.emit("closeSmind_map_close_sidebarideBar")
      this.show = true
    },
    hideReplaceInput() {
      this.showReplaceInput = false
      this.replaceText = ""
    },
    onSearchNext() {
      this.mindMap.search.search(this.searchText, () => {
        this.$refs.searchInputRef.focus()
      })
    },
    replace() {
      this.mindMap.search.replace(this.replaceText, true)
    },
    replaceAll() {
      this.mindMap.search.replaceAll(this.replaceText)
    },
    close() {
      this.$emit("changeShowSearch")
      this.showSearchInfo = false
      this.total = 0
      this.currentIndex = 0
      this.searchText = ""
      this.hideReplaceInput()
      this.mindMap.search.endSearch()
    },
  },
}
</script>

<style lang="scss" scoped>
.searchContainer {
  position: relative;
  background-color: #fff;
  padding: 16px;
  width: 296px;
  border-radius: 12px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 0;
  right: -296px;
  transition: all 0.3s;
  z-index: 2;
  &.isDark {
    background-color: #363b3f;
    .closeBtnBox {
      color: #fff;
      background-color: #363b3f;
    }
  }
  &.show {
    right: 20px;
  }
  .btnList {
    display: flex;
    justify-content: flex-end;
  }
  .closeBtnBox {
    position: absolute;
    right: -5px;
    top: -5px;
    width: 20px;
    height: 20px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
    .closeBtn {
      font-size: 16px;
    }
  }
  .searchInputBox {
    position: relative;
    .searchInfo {
      position: absolute;
      right: 70px;
      top: 50%;
      transform: translateY(-50%);
      color: #909090;
      font-size: 14px;
    }
  }
}
</style>
