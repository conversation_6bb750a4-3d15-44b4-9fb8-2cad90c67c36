<template>
  <div class="sidebarContainer" @click.stop :class="{ show: show }">
    <div class="relative">
      <div class="sidebarHeader" v-if="title">
        {{ title }}
      </div>
      <div @click="close" class="absolute top-5px right-0">
        <g-icon name="ri-close-line" size="" color="" />
      </div>
    </div>
    <div class="sidebarContent" ref="sidebarContent">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
})
let show = defineModel("modelValue")
const emit = defineEmits(["close"])
function close() {
  emit("close")
  show.value = false
}
</script>

<style lang="scss" scoped>
.sidebarContainer {
  position: absolute;
  right: -300px;
  top: 110px;
  bottom: 80px;
  width: 300px;
  z-index: 1;
  background-color: #fff;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;

  &.show {
    right: 0;
  }

  .closeBtn {
    position: absolute;
    right: 20px;
    top: 12px;
    font-size: 20px;
    cursor: pointer;
  }

  .sidebarHeader {
    width: 100%;
    height: 44px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-grow: 0;
    flex-shrink: 0;
  }

  .sidebarContent {
    width: 100%;
    height: 100%;
    overflow: auto;
  }
}
</style>
