<template>
  <DrawerLayout v-model="show" @close="emit('resetSidebar')" title="公式">
    <el-scrollbar>
      <div class="box">
        <div class="formulaInputBox">
          <el-input
            v-model="formulaText"
            :rows="4"
            resize="none"
            type="textarea"
            placeholder="请输入LaText语法"
            @keydown.stop
          />
          <el-button
            size="small"
            style="width: 100%; margin-top: 20px"
            @click="confirm"
            >完成</el-button
          >
        </div>
        <div class="title">常用公式</div>
        <div class="formulaList">
          <div class="formulaItem" v-for="(item, index) in list" :key="index">
            <div class="overview" v-html="item.overview"></div>
            <div class="text" @click="formulaText = item.text">
              {{ item.text }}
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </DrawerLayout>
</template>

<script setup lang="ts">
import DrawerLayout from "./DrawerLayout.vue"
import { formulaList } from "../../config/constant"
import { useMindMapStore } from "@/stores/modules/mindMap"
// import 'simple-mind-map/node_modules/katex/dist/katex.min.css'

const mindMapStore = useMindMapStore()
const props = defineProps({
  mindMap: {
    type: Object,
    default: () => {},
  },
})
const emit = defineEmits(["resetSidebar"])

let show = defineModel("modelValue")

let formulaText = $ref("")
let list: any = $ref([])
let activeNodes: any = $ref("")

onBeforeMount(() => {
  $g.bus.on("mind_map_node_active", handleNodeActive)
})

onBeforeUnmount(() => {
  $g.bus.off("mind_map_node_active", handleNodeActive)
})

onMounted(() => {
  init()
})
function init() {
  list = formulaList.map((item) => {
    return {
      overview: (window as any).katex.renderToString(
        item,
        props.mindMap.formula.getKatexConfig(),
      ),
      text: item,
    }
  })
}

function handleNodeActive(args) {
  activeNodes = [...args[1]]
  if (activeNodes.length <= 0) {
    $g.bus.emit("mind_map_click_bar", "FormulaSidebar", true)
  }
}
function confirm() {
  if (!mindMapStore.localConfig.openNodeRichText) {
    return $g.msg("未开启节点富文本")
  }
  let str = formulaText.trim()
  if (!str) return
  props.mindMap.execCommand("INSERT_FORMULA", str)
}
</script>

<style lang="scss" scoped>
.box {
  padding: 10px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  &.isDark {
    .title {
      color: #fff;
    }
    .formulaList {
      .formulaItem {
        .overview,
        .text {
          color: #fff;
        }
        .text {
          background-color: #363b3f;
        }
      }
    }
  }
  .title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 10px 0;
    flex-shrink: 0;
  }
  .formulaInputBox {
    flex-shrink: 0;
  }
  .formulaList {
    height: 100%;
    overflow-y: auto;
    .formulaItem {
      position: relative;
      display: flex;
      overflow: hidden;
      align-items: center;
      border: 1px solid #dcdfe6;
      border-bottom: none;
      &:last-of-type {
        border-bottom: 1px solid #dcdfe6;
      }
      .overview,
      .text {
        width: 50%;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
      }
      .overview {
        padding: 10px 0;
        border-right: none;
      }
      .text {
        cursor: pointer;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
        border-left: 1px solid #dcdfe6;
        background-color: #fafafa;
      }
    }
  }
}
</style>
