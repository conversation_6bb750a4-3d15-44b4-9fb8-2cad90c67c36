<template>
  <div>
    <DrawerLayout v-model="show" title="结构" @close="emit('resetSidebar')">
      <el-scrollbar>
        <div class="layoutList">
          <div
            class="layoutItem"
            v-for="item in layoutList"
            :key="item.value"
            @click="useLayout(item)"
            :class="{ active: item.value === activeStructure }"
          >
            <div class="imgBox">
              <img :src="layoutImgMap[item.value]" alt="" />
            </div>
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </el-scrollbar>
    </DrawerLayout>
  </div>
</template>

<script setup lang="ts">
import { layoutList, layoutImgMap } from "../../config/constant"
import DrawerLayout from "./DrawerLayout.vue"
let show = defineModel("modelValue")
const emit = defineEmits(["resetSidebar", "changeStructure"])
const props = defineProps({
  mindMap: {
    type: Object as any,
  },
})
let activeStructure = $ref(props.mindMap.getLayout())

/* 切换结构 */
function useLayout(val) {
  activeStructure = val.value
  emit("changeStructure", val.value)
}

// const activeStructure = $computed(() => {
//   return props.mindMap.getLayout()
// })
</script>

<style lang="scss" scoped>
.layoutList {
  padding: 20px;
  .layoutItem {
    width: 100%;
    cursor: pointer;
    border-bottom: 1px solid #e9e9e9;
    margin-bottom: 20px;
    padding-bottom: 20px;
    transition: all 0.2s;
    border: 1px solid transparent;

    &:last-of-type {
      border: none;
    }

    &:hover {
      box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16),
        0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
    }

    &.active {
      border: 1px solid #67c23a;
    }

    .imgBox {
      width: 100%;

      img {
        width: 100%;
      }
    }

    .name {
      text-align: center;
      font-size: 14px;
    }
  }
}
</style>
