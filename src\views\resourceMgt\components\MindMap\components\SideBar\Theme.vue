<template>
  <DrawerLayout v-model="show" :title="'主题'" @close="emit('resetSidebar')">
    <el-scrollbar>
      <div class="themeList">
        <div
          class="themeItem"
          v-for="item in themeAllList"
          :key="item.value"
          @click="useTheme(item)"
          :class="{ active: item.value === activeTheme }"
        >
          <div class="imgBox">
            <img :src="themeMap[item.value]" alt="" />
          </div>
          <div class="name">{{ item.name }}</div>
        </div>
      </div>
    </el-scrollbar>
  </DrawerLayout>
</template>

<script setup lang="ts">
import DrawerLayout from "./DrawerLayout.vue"
import customThemeList from "../../theme/index"
import { themeMap, themeList } from "../../config/constant"
const props = defineProps({
  mindMap: {
    type: Object as any,
  },
})
const emit = defineEmits(["resetSidebar", "changeTheme"])
let show = defineModel("modelValue")
const themeAllList = $ref([...themeList, ...customThemeList].reverse())
let activeTheme = $ref(props.mindMap.getTheme())
/* 使用主题 */
function useTheme(val) {
  activeTheme = val.value
  emit("changeTheme", val.value)
}
</script>

<style lang="scss" scoped>
.themeList {
  padding: 20px;
  padding-top: 0;
  &.isDark {
    .name {
      color: #fff;
    }
  }
  .themeItem {
    width: 100%;
    cursor: pointer;
    border-bottom: 1px solid #e9e9e9;
    margin-bottom: 20px;
    padding-bottom: 20px;
    transition: all 0.2s;
    border: 1px solid transparent;
    &:last-of-type {
      border: none;
    }
    &:hover {
      box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16),
        0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
    }
    &.active {
      border: 1px solid #67c23a;
    }
    .imgBox {
      width: 100%;
      img {
        width: 100%;
      }
    }
    .name {
      text-align: center;
      font-size: 14px;
    }
  }
}
</style>
