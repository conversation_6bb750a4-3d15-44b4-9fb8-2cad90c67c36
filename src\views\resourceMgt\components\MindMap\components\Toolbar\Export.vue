<template>
  <el-dialog v-model="dialogVisible" title="导出" width="700px">
    <div class="bg-[#f2f4f7] p-10px">
      <div class="mb-20px">
        <span class="mr-10px">导出文件夹名称</span>
        <el-input
          style="width: 300px"
          v-model="fileName"
          size="small"
          @keydown.stop
        ></el-input>
      </div>
      <div class="mb-20px h-24px flex">
        <div>
          <span class="mr-10px">水平内边距</span>
          <el-input
            style="width: 100px"
            v-model="paddingX"
            size="small"
            @change="onPaddingChange"
            @keydown.stop
          ></el-input>
        </div>
        <div>
          <span class="mx-10px">垂直内边距</span>
          <el-input
            style="width: 100px"
            v-model="paddingY"
            size="small"
            @change="onPaddingChange"
            @keydown.stop
          ></el-input>
        </div>
        <el-checkbox
          v-show="['png'].includes(exportType)"
          v-model="isTransparent"
          class="ml-12px"
          >背景是否透明</el-checkbox
        >
      </div>
      <div class="flex">
        <div
          v-for="item in downTypeList"
          :key="item.type"
          class="bg-white p-20px mr-20px br-[8px] flex cursor-pointer border border-[transparent]"
          :class="{ '!border-primary': exportType == item.type }"
          @click="exportType = item.type"
        >
          <div
            class="icon text-30px iconfont"
            :class="[
              item.icon,
              item.type,
              item.type == 'png' ? 'text-[#ffc038]' : 'text-[#4380ff]',
            ]"
          ></div>
          <div class="ml-10px">
            <div class="name">{{ item.name }}</div>
            <div class="desc">{{ item.desc }}</div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
const props = defineProps({
  mindMap: { type: Object },
})

let dialogVisible = $ref(false)
let exportType = $ref("png")
let fileName = $ref("思维导图")
let widthConfig = $ref(true)
let isTransparent = $ref(false)
let paddingX = $ref(10)
let paddingY = $ref(10)

const downTypeList = [
  {
    name: "图片",
    type: "png",
    icon: "iconPNG",
    desc: "适合查看分享",
  },
  {
    name: "SVG",
    type: "svg",
    icon: "iconSVG",
    desc: "可缩放矢量图形",
  },
]

onMounted(() => {
  $g.bus.on("showExport", handleShowExport)
})

onBeforeMount(() => {
  $g.bus.off("showExport", handleShowExport)
})

//导出
async function mapExport(args) {
  try {
    await props.mindMap.export(...args)
  } catch (error) {
    console.log(error)
  }
}

//修改导出内边距
function onPaddingChange() {
  props.mindMap.updateConfig({
    exportPaddingX: Number(paddingX),
    exportPaddingY: Number(paddingY),
  })
}

const handleShowExport = () => {
  dialogVisible = true
}

//取消导出
const cancel = () => {
  dialogVisible = false
}

//确定导出
const confirm = () => {
  if (exportType === "svg") {
    mapExport([
      exportType,
      true,
      fileName,
      `* {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }`,
      widthConfig,
    ])
  } else if (exportType === "png") {
    mapExport([exportType, true, fileName, isTransparent])
  }
  cancel()
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-checkbox {
    height: 26px !important;
  }
}
</style>
