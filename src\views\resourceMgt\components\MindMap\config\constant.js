// 结构图片Map
export const layoutImgMap = {
  logicalStructure: $g.tool.getFileUrl("mindMap/logicalStructure.png"),
  mindMap: $g.tool.getFileUrl("mindMap/mindMap.png"),
  organizationStructure: $g.tool.getFileUrl(
    "mindMap/organizationStructure.png",
  ),
  catalogOrganization: $g.tool.getFileUrl("mindMap/catalogOrganization.png"),
  timeline: $g.tool.getFileUrl("mindMap/timeline.png"),
  timeline2: $g.tool.getFileUrl("mindMap/timeline2.png"),
  fishbone: $g.tool.getFileUrl("mindMap/fishbone.png"),
  verticalTimeline: $g.tool.getFileUrl("mindMap/verticalTimeline.png"),
}
//  主题图片映射
export const themeMap = {
  oreo: $g.tool.getFileUrl("themes/oreo.jpg"),
  shallowSea: $g.tool.getFileUrl("themes/shallowSea.jpg"),
  lemonBubbles: $g.tool.getFileUrl("themes/lemonBubbles.jpg"),
  rose: $g.tool.getFileUrl("themes/rose.jpg"),
  seaBlueLine: $g.tool.getFileUrl("themes/seaBlueLine.jpg"),
  morandi: $g.tool.getFileUrl("themes/morandi.jpg"),
  classic5: $g.tool.getFileUrl("themes/classic5.jpg"),
  cactus: $g.tool.getFileUrl("themes/cactus.jpg"),
  gold: $g.tool.getFileUrl("themes/gold.jpg"),
  classic4: $g.tool.getFileUrl("themes/classic4.jpg"),
  vitalityOrange: $g.tool.getFileUrl("themes/vitalityOrange.jpg"),
  greenLeaf: $g.tool.getFileUrl("themes/greenLeaf.jpg"),
  minions: $g.tool.getFileUrl("themes/minions.jpg"),
  simpleBlack: $g.tool.getFileUrl("themes/simpleBlack.jpg"),
  courseGreen: $g.tool.getFileUrl("themes/courseGreen.jpg"),
  coffee: $g.tool.getFileUrl("themes/coffee.jpg"),
  redSpirit: $g.tool.getFileUrl("themes/redSpirit.jpg"),
  avocado: $g.tool.getFileUrl("themes/avocado.jpg"),
  autumn: $g.tool.getFileUrl("themes/autumn.jpg"),
}

// 公式列表
export const formulaList = [
  "a^2",
  "a_2",
  "a^{2+2}",
  "a_{i,j}",
  "x_2^3",
  "\\overbrace{1+2+\\cdots+100}",
  "\\sum_{k=1}^N k^2",
  "\\lim_{n \\to \\infty}x_n",
  "\\int_{-N}^{N} e^x\\, dx",
  "\\sqrt{3}",
  "\\sqrt[n]{3}",
  "\\sin\\theta",
  "\\log X",
  "\\log_{10}",
  "\\log_\\alpha X",
  "\\lim_{t\\to n}T",
  "\\frac{1}{2}=0.5",
  "\\binom{n}{k}",
  "\\begin{matrix}x & y \\\\z & v\\end{matrix}",
  "\\begin{cases}3x + 5y +  z \\\\7x - 2y + 4z \\\\-6x + 3y + 2z\\end{cases}",
]

// 常量
export const CONSTANTS = {
  CHANGE_THEME: "changeTheme",
  CHANGE_LAYOUT: "changeLayout",
  SET_DATA: "setData",
  TRANSFORM_TO_NORMAL_NODE: "transformAllNodesToNormalNode",
  MODE: {
    READONLY: "readonly",
    EDIT: "edit",
  },
  LAYOUT: {
    LOGICAL_STRUCTURE: "logicalStructure",
    MIND_MAP: "mindMap",
    ORGANIZATION_STRUCTURE: "organizationStructure",
    CATALOG_ORGANIZATION: "catalogOrganization",
    TIMELINE: "timeline",
    TIMELINE2: "timeline2",
    FISHBONE: "fishbone",
    VERTICAL_TIMELINE: "verticalTimeline",
  },
  DIR: {
    UP: "up",
    LEFT: "left",
    DOWN: "down",
    RIGHT: "right",
  },
  KEY_DIR: {
    LEFT: "Left",
    UP: "Up",
    RIGHT: "Right",
    DOWN: "Down",
  },
  SHAPE: {
    RECTANGLE: "rectangle",
    DIAMOND: "diamond",
    PARALLELOGRAM: "parallelogram",
    ROUNDED_RECTANGLE: "roundedRectangle",
    OCTAGONAL_RECTANGLE: "octagonalRectangle",
    OUTER_TRIANGULAR_RECTANGLE: "outerTriangularRectangle",
    INNER_TRIANGULAR_RECTANGLE: "innerTriangularRectangle",
    ELLIPSE: "ellipse",
    CIRCLE: "circle",
  },
  MOUSE_WHEEL_ACTION: {
    ZOOM: "zoom",
    MOVE: "move",
  },
  INIT_ROOT_NODE_POSITION: {
    LEFT: "left",
    TOP: "top",
    RIGHT: "right",
    BOTTOM: "bottom",
    CENTER: "center",
  },
  LAYOUT_GROW_DIR: {
    LEFT: "left",
    TOP: "top",
    RIGHT: "right",
    BOTTOM: "bottom",
  },
  PASTE_TYPE: {
    CLIP_BOARD: "clipBoard",
    CANVAS: "canvas",
  },
  SCROLL_BAR_DIR: {
    VERTICAL: "vertical",
    HORIZONTAL: "horizontal",
  },
  CREATE_NEW_NODE_BEHAVIOR: {
    DEFAULT: "default",
    NOT_ACTIVE: "notActive",
    ACTIVE_ONLY: "activeOnly",
  },
}
//  布局结构列表
export const layoutList = [
  {
    name: "逻辑结构图",
    value: CONSTANTS.LAYOUT.LOGICAL_STRUCTURE,
  },
  {
    name: "思维导图",
    value: CONSTANTS.LAYOUT.MIND_MAP,
  },
  {
    name: "组织结构图",
    value: CONSTANTS.LAYOUT.ORGANIZATION_STRUCTURE,
  },
  {
    name: "目录组织图",
    value: CONSTANTS.LAYOUT.CATALOG_ORGANIZATION,
  },
  {
    name: "时间轴",
    value: CONSTANTS.LAYOUT.TIMELINE,
  },
  {
    name: "时间轴2",
    value: CONSTANTS.LAYOUT.TIMELINE2,
  },
  {
    name: "竖向时间轴",
    value: CONSTANTS.LAYOUT.VERTICAL_TIMELINE,
  },
  {
    name: "鱼骨图",
    value: CONSTANTS.LAYOUT.FISHBONE,
  },
]
//  主题列表
export const themeList = [
  {
    name: "金色vip",
    value: "gold",
    dark: false,
  },
  {
    name: "活力橙",
    value: "vitalityOrange",
    dark: false,
  },
  {
    name: "绿叶",
    value: "greenLeaf",
    dark: false,
  },

  {
    name: "脑图经典4",
    value: "classic4",
    dark: false,
  },
  {
    name: "小黄人",
    value: "minions",
    dark: false,
  },
  {
    name: "简约黑",
    value: "simpleBlack",
    dark: false,
  },
  {
    name: "课程绿",
    value: "courseGreen",
    dark: false,
  },
  {
    name: "咖啡",
    value: "coffee",
    dark: false,
  },
  {
    name: "红色精神",
    value: "redSpirit",
    dark: false,
  },
  {
    name: "牛油果",
    value: "avocado",
    dark: false,
  },
  {
    name: "秋天",
    value: "autumn",
    dark: false,
  },
]
