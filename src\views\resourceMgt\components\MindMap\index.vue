<template>
  <div
    class="overflow-hidden"
    :class="mindMapStore.fullScreen ? 'fullScreen' : 'mindMapBox'"
  >
    <Toolbar
      v-if="mindMap && !isZenMode"
      :mindMap="mindMap"
      :isFIB="isFIB"
      @chang-status="changFIB"
    ></Toolbar>
    <SideBar v-if="mindMap && !isZenMode" :mindMap="mindMap" />
    <NavigatorToolbar
      @changeShowSearch="changeShowSearch"
      v-if="mindMap"
      :mindMap="mindMap"
      :isZenMode="isZenMode"
    ></NavigatorToolbar>
    <div v-if="mindMap">
      <SearchNode
        v-show="showSearch"
        :mindMap="mindMap"
        @changeShowSearch="changeShowSearch"
      ></SearchNode>
    </div>
    <!--  Edit不作为单独组件 内部组件放入#mindMapContainer容器内 -->
    <div
      :id="'mindMapContainer' + uuid"
      :style="{ height: height + 'px' }"
      class="mindMapContainer"
    >
      <RichTextToolbar v-if="mindMap" :mindMap="mindMap"></RichTextToolbar>
      <Contextmenu
        v-if="mindMap && mode == 1"
        :mindMap="mindMap"
        :isZenMode="isZenMode"
        @changeIsZenMode="changeIsZenMode"
      ></Contextmenu>
    </div>
  </div>
</template>

<script setup>
import MindMap from "simple-mind-map"
import Formula from "simple-mind-map/src/plugins/Formula.js" // 公式插件
import RichText from "simple-mind-map/src/plugins/RichText.js" // 富文本插件
import Export from "simple-mind-map/src/plugins/Export.js" // 导出插件
import Drag from "simple-mind-map/src/plugins/Drag.js" // 拖拽插件
import Select from "simple-mind-map/src/plugins/Select.js" // 节点多选插件
import Search from "simple-mind-map/src/plugins/Search.js" // 搜索插件

import customThemeList from "./theme/index.js"

import RichTextToolbar from "./components/RichTextToolbar.vue"
import Contextmenu from "./components/Contextmenu.vue"
import SideBar from "./components/SideBar/index.vue"
import SearchNode from "./components/SearchNode.vue"
import Toolbar from "./components/Toolbar/index.vue"
import NavigatorToolbar from "./components/NavigatorToolbar/index.vue"
import OSS from "@/plugins/OSS"
import { useMindMapStore } from "@/stores/modules/mindMap"

let mindMapStore = useMindMapStore()
let { mindMapData, localConfig, isReadonly } = $(storeToRefs(mindMapStore))

let props = defineProps({
  // 远程数据 用于远程数据初始化
  remoteMapData: {
    type: Object,
  },
  // 1.编辑模式 2.预览模式
  mode: {
    type: Number,
    default: 1,
  },
  height: {
    type: Number,
    default: 600,
  },
  exportImg: {
    type: Boolean,
    default: true,
  },
})
let showSearch = $ref(false)
let isZenMode = $ref(false)
let uuid = $g.tool.uuid()
let newData = $ref("")
// 是否是填空题,填空题模式下会把u标签内文字替换为空格,原始数据依然是root内的数据
// v-model:isFIB="isFIB"
let isFIB = defineModel("isFIB", { required: true, default: false })
watch(isFIB, (val) => {
  // 填空模式下禁止编辑
  isReadonly = val
  if (!newData) newData = getMindMap().getData(true)
  mindMap.setMode(isReadonly ? "readonly" : "edit")
  mindMap.setData(
    val ? replaceUTextWithNbsp($g._.cloneDeep(newData.root)) : newData.root,
  )
  if (!val) newData = ""
})

let bus = $g.bus
let mindMap = $ref(null)

function changFIB() {
  isFIB.value = !isFIB.value
}
function changeShowSearch() {
  // bus.emit("closeSmind_map_close_sidebarideBar")
  showSearch = !showSearch
}
function changeIsZenMode() {
  isZenMode = !isZenMode
  mindMap.setMode(isZenMode ? "readonly" : "edit")
}
onMounted(() => {
  registrationPlugIn()
  registrationTheme()
  initMindMap()
  registerEventForwarding()
  bus.on("mind_map_data_change", bindSaveEvent)
})

onBeforeUnmount(() => {
  handleEndTextEdit()
  bus.off("mind_map_data_change", bindSaveEvent)
})

function handleEndTextEdit() {
  mindMap.keyCommand.pause()
}

// 注册插件
function registrationPlugIn() {
  MindMap.usePlugin(Formula)
    .usePlugin(RichText)
    .usePlugin(Export)
    .usePlugin(Drag)
    .usePlugin(Select)
    .usePlugin(Search)
}

// 注册主题
function registrationTheme() {
  customThemeList.forEach((item) => {
    MindMap.defineTheme(item.value, item.theme)
  })
}

// 初始化脑图
function initMindMap() {
  mindMapStore.resetState()
  isReadonly = props.mode == 2 ? true : false
  if (props.remoteMapData) {
    mindMapStore.setMindMapData(props.remoteMapData)
  }
  mindMap = new MindMap({
    el: document.getElementById("mindMapContainer" + uuid),
    data: isFIB.value
      ? replaceUTextWithNbsp($g._.cloneDeep(deleteNode(mindMapData.root)))
      : deleteNode(mindMapData.root),
    initRootNodePosition: ["left", "center"],
    theme: mindMapData.theme.template,
    themeConfig: mindMapData.theme.config,
    layout: mindMapData.layout,
  })
  // 移除前进/后退快捷键
  mindMap.keyCommand.removeShortcut("Control+z")
  mindMap.keyCommand.removeShortcut("Control+y")
  if (props.mode == 2) {
    mindMap.setMode(isReadonly ? "readonly" : "edit")
    isZenMode = true
  }
}

// 注册事件转发
function registerEventForwarding() {
  let event = [
    "node_active",
    "data_change",
    "view_data_change",
    "back_forward",
    "node_contextmenu",
    "node_click",
    "draw_click",
    "expand_btn_click",
    "svg_mousedown",
    "mouseup",
    "mode_change",
    "node_tree_render_end",
    "rich_text_selection_change",
    "transforming-dom-to-images",
    "generalization_node_contextmenu",
    "painter_start",
    "painter_end",
    "scrollbar_change",
    "scale",
  ]
  event.forEach((e) => {
    mindMap.on(e, (...args) => {
      let newEventName = "mind_map_" + e
      if (
        [
          "node_contextmenu",
          "node_active",
          "rich_text_selection_change",
          "view_data_change",
        ].includes(e)
      ) {
        bus.emit(newEventName, args)
      } else {
        bus.emit(newEventName, ...args)
      }
    })
  })
}

function deleteNode(node) {
  if (node.data) {
    delete node._node
  }
  // 如果有子节点，递归处理
  if (node.children && node.children.length > 0) {
    node.children = node.children.map((child) => deleteNode(child))
  }
  return node
}

function replaceUTextWithNbsp(node) {
  // 先处理当前节点
  if (node.data && node.data.text) {
    let text = node.data.text
    // 处理 <u> 标签内的内容
    text = text.replace(/<u\b[^>]*>([\s\S]*?)<\/u>/gi, (match, p1) => {
      // 移除所有标签，保留纯文本
      let plainText = p1.replace(/<[^>]*>/g, "")
      plainText = plainText.replace(/<(?!\/?u\b)[^>]*>/gi, "")

      // 将纯文本中的每个字符替换为 &emsp; 或 &nbsp;
      let replacedContent = plainText.replace(/./g, (char) => {
        // 判断字符是否为中文
        if (/[\u4E00-\u9FFF]/.test(char)) {
          return "&emsp;"
        } else {
          return "&nbsp;"
        }
      })
      replacedContent = replacedContent.replace(/<(?!\/?u\b)[^>]*>/gi, "")

      // 返回替换后的内容，包含 <u> 标签
      return `<u>${replacedContent}</u>`
    })
    // 移除所有剩余的 HTML 标签，但保留 <u> 标签
    node.data.text = text
  }
  // 如果有子节点，递归处理
  if (node.children && node.children.length > 0) {
    node.children = node.children.map((child) => replaceUTextWithNbsp(child))
  }
  return node
}

/**
 * @Desc: 存储数据当数据有变时
 */
function bindSaveEvent(data) {
  mindMapData.root = data
}

function getMindMap() {
  return toRaw(mindMap)
}

/**
 * @Desc: 获取思维导图数据+配置
 */
async function getMindMapData() {
  let mapData = getMindMap().getData(true)
  mapData.root = $g._.cloneDeep(mindMapData.root)
  mapData.root = deleteNode(mapData.root)
  if (props.exportImg) mapData.images = await exportTwoPng()
  return mapData
}

// Base64 转 File
function base64ToFile(base64, mime, filename) {
  let arr = base64.split(",")
  let type = mime || arr[0].match(/:(.*?);/)[1]
  let suffix = "png"
  let fileName = filename || `未命名.${suffix}`
  let bstr = atob(arr[1])
  let n = bstr.length
  let u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], fileName, { type })
}

//图片上传
async function uploadFile(file) {
  try {
    let aliOss = new OSS()
    let data = {
      file,
      name: "image.png",
      size: file.size,
    }
    let res = await aliOss.uploadFile(data)
    // 上传成功后返回的文件路径(传给后端的地址)
    data.resource_url = res.resource_url
    data.fullUrl = res.fullUrl
    return data.fullUrl
  } catch (error) {
    console.log("⚡ error ==> ", error)
    $g.msg("上传失败", "error")
  }
}

//导出并上传图片
async function mapExport() {
  try {
    let data = await getMindMap().export("png", false, "img.png", false)
    if (!data) return
    let file = base64ToFile(data, "img.png")
    let url = ""
    if (file) {
      url = await uploadFile(file)
    }
    return url
  } catch (error) {
    console.log(error)
  }
}

//上传两种填空状态图片
async function exportTwoPng() {
  try {
    let list = []
    let url = ""
    isFIB.value = true
    await new Promise((resolve) => {
      setTimeout(() => {
        resolve(true)
      }, 300)
    })
    url = await mapExport()
    list.unshift({ fib: isFIB.value, url })
    isFIB.value = false
    await new Promise((resolve) => {
      setTimeout(() => {
        resolve(true)
      }, 300)
    })
    url = await mapExport()
    list.unshift({ fib: isFIB.value, url })
    return list
  } catch (error) {
    console.log("⚡ error ==> ", error)
  }
}

defineExpose({
  getMindMap: () => mindMap,
  getMindMapData,
  exportTwoPng,
  initMindMap,
})
</script>

<style lang="scss">
.mindMapBox {
  width: 100%;
  position: relative;
}

.mindMapContainer {
  width: 100%;
}

.mindMapContainer * {
  margin: 0;
  padding: 0;
}

.fullScreen {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 2100;
}
.fullScreen > .mindMapContainer {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100% !important;
}
</style>
