<template>
  <div v-if="fileType == 'pdf'" class="h-full">
    <div class="flex w-full gap-x-[10px]">
      <iframe class="w-1/2" :src="url" frameborder="0" ref="iframeRef"></iframe>
      <!-- markdown容器 -->
      <el-scrollbar
        class="flex-1 overflow-hidden bg-white p-10px"
        :height="height"
      >
        <div class="md-container">
          <div
            ref="mdWrapperRefs"
            v-for="(mdItem, index) in mdList"
            :key="index"
            class="md-item"
            :class="activePageNumber === index + 1 ? 'md-active' : ''"
            @click="handleItemClick(index + 1, 'md')"
          >
            <g-markdown :model-value="mdItem.md" mode="preview" />
            <!-- 复制按钮 -->
            <div class="copy-btn" @click="copyMarkdown(mdItem.md)">
              <el-popover placement="bottom-end" trigger="hover" width="auto">
                <span class="whitespace-nowrap">复制此页为markdown</span>
                <template #reference>
                  <g-icon
                    name="ri-file-copy-2-fill"
                    color="white"
                    size="18"
                  ></g-icon
                ></template>
              </el-popover>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
  <div v-else class="w-full">
    <div
      class="w-full m-auto flex items-center gap-x-[10px]"
      :style="{ height: `${height}px` }"
    >
      <div class="h-full" style="border: 1px solid #ccc">
        <g-img
          width="350"
          height="450"
          object-fit="contain"
          :src="fileUrl"
          :previewDisabled="false"
        />
      </div>
      <div class="flex-1 h-full">
        <g-markdown :model-value="mdList[0].md" :height="height + 'px'" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue"
const props = defineProps({
  // 文件地址，仅支持图片或者pdf格式
  fileUrl: {
    type: String,
    required: true,
  },
  mdList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  height: {
    type: [Number, String],
    default: 700,
  },
})
let iframeRef = $ref<any>(null)
// 文件类型
const fileType = $g.tool.getFileType(props.fileUrl.split(".").pop() || "")
let mdWrapperRefs = $ref<any>(null)
// 剪切板功能
const { copy, copied } = useClipboard({
  legacy: true,
})
// 当前激活的页码,pdf容器和markdown容器共享
let activePageNumber = $ref(0)
const url = $computed(() => {
  return `/pdfjs-4.5.136-dist/web/viewer.html?file=${props.fileUrl}`
})
/**
 * markdown点击时的处理函数
 */
function handleItemClick(pageNumber: number, source: "pdf" | "md") {
  if (fileType !== "pdf") return
  activePageNumber = pageNumber
  if (source === "md")
    iframeRef?.contentWindow.postMessage({ page: pageNumber }, "*")
}
/**
 * markdown某一页滚动到视口
 */
function mdItemScrollIntoView(pageNumber: number) {
  mdWrapperRefs[pageNumber - 1]?.scrollIntoView({
    behavior: "smooth",
  })
}
/**
 * 复制md到剪切板
 */
function copyMarkdown(content) {
  copy(content)
  copied && $g.msg("复制成功", "success")
}
/* 监听pdf容器点击事件 */
function handlePdfClick(event) {
  activePageNumber = Number(event.data.page)
  mdItemScrollIntoView(event.data.page)
}
onMounted(() => {
  window.addEventListener("message", handlePdfClick)
})
onBeforeUnmount(() => {
  window.removeEventListener("message", handlePdfClick)
})
</script>

<style lang="scss" scoped>
.md-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  .md-item {
    border-radius: 8px;
    border: 2px dashed transparent;
    margin-bottom: 16px;
    padding: 12px;
    position: relative;

    .copy-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 30px;
      height: 30px;
      background-color: rgb(119, 72, 249);
      border-radius: 50%;
      position: absolute;
      right: 12px;
      top: 12px;
      display: none;
      cursor: pointer;
    }

    &.md-active {
      border: 2px dashed rgb(119, 72, 249);
      background-color: rgb(119, 72, 249, 0.2);

      .copy-btn {
        display: flex;
      }
    }
  }
}
</style>
