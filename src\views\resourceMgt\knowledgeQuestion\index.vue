<template>
  <div>
    <FilterLayout
      :options="{
        title1: '教材（学科网）章节树',
        // title2: '知识点树（学科网）',
      }"
      @searchDataOption="searchDataOption = $event"
      @treeSearch="treeSearch"
      @getTree1="getTree1"
      @getTree2="getTree2"
      :mode="2"
      :type="1"
      ref="filterFormRef"
    >
      <template #left="{}">
        <g-tree
          ref="Tree1Ref"
          treeName="LeftTree"
          class="p-10px"
          :treeData="treeData1"
          style="height: calc(100vh - 350px); overflow: auto"
          nodeKey="sysTextbookCatalogId"
          :defaultProps="{
            label: 'sysTextbookCatalogName',
          }"
          expand-on-click-node
        >
          <template #body="{ data }">
            <div>
              <div class="flex items-center">
                <div
                  class="title max-w-[300px] whitespace-pre-wrap box-border flex-shrink-0"
                >
                  {{ data.sysTextbookCatalogName }}
                </div>
                <n-tag class="ml-5px" :bordered="false">
                  {{ getTotalPoints(data) }}知识点
                </n-tag>
              </div>
              <div
                class="text-12px text-[#909399] flex items-center"
                v-for="item in data.catalogKnowledgeList"
                :key="item.sysKnowledgePointId"
              >
                <g-icon name="lightbulb-flash-line" size="18" color="#3399ff" />
                {{ item.sysKnowledgePointName }}
                <span v-if="item.hidden">(已隐藏)</span>
              </div>
            </div>
          </template>
        </g-tree>
      </template>

      <template #right>
        <g-tree
          treeName="RightTree"
          ref="Tree2Ref"
          class="p-10px"
          :treeData="treeData2"
          nodeKey="sysKnowledgePointId"
          :default-expanded-keys="[currentKey]"
          :default-checked-keys="[currentKey]"
          auto-expand-parent
          render-after-expand
          style="height: calc(100vh - 350px); overflow: auto"
          :highlight-check="false"
          expand-on-click-node
          :defaultProps="{
            label: 'sysKnowledgePointName',
          }"
        >
          <template #body="{ data }">
            <div class="flex items-center">
              <div
                class="title max-w-[300px] whitespace-pre-wrap box-border flex-shrink-0"
              >
                {{ data.sysKnowledgePointName }}
              </div>
              <n-tag
                v-if="data.exclusiveQuestionCount"
                class="ml-5px"
                :bordered="false"
              >
                {{ data.exclusiveQuestionCount }}道题
              </n-tag>
              <div
                v-if="data.exclusiveQuestionCount"
                class="ml-40px flex items-center"
              >
                <div class="flex flex-wrap items-center">
                  <div
                    v-for="(item, index) in data.questionDiffCountList"
                    :key="index + 'diff'"
                    class="text-[#0f99eb] mr-20px"
                  >
                    {{ diffMap[item.sysQuestionDifficultyId]
                    }}{{ item.questionCount }}
                  </div>
                </div>
                <div class="flex items-center flex-shrink-0">
                  <span class="ml-20px text-[#8C74F2]">知识点等级：</span>
                  <n-select
                    v-model:value="data.level"
                    :options="knowledgeLevelSelect"
                    placeholder="请选择"
                    class="w-80px"
                    @update:value="changeLevel(data)"
                    size="small"
                    @click.stop
                  ></n-select>
                </div>
              </div>
            </div>
          </template>
        </g-tree>
      </template>
    </FilterLayout>
  </div>
</template>

<script setup lang="ts">
import FilterLayout from "./components/FilterLayout.vue"
import {
  getKnowledgeLevelSelect,
  saveKnowledgeLevelChange,
} from "@/api/resourceMgt"

// FilterLayout组件请求数据时携带的参数信息，保存下来用于导出数据时使用
let searchDataOption: { [k: string]: any } = {}
let filterFormRef = $ref<any>(null)
let currentKey = $ref<any>(null)
let knowledgeLevelSelect = $ref([])
let diffMap = $ref({
  17: "容易",
  18: "较易",
  19: "一般",
  20: "较难",
  21: "困难",
})
function getTotalPoints(data) {
  let sum = 0

  function recursion(data) {
    sum += data.catalogKnowledgeList?.length || 0
    if (data.children) {
      for (let child of data.children) {
        recursion(child)
      }
    }
  }
  recursion(data)
  return `${sum || 0}`
}
let Tree1Ref: any = $ref(null)
let Tree2Ref: any = $ref(null)
function treeSearch(v) {
  let node = v.mode == 1 ? Tree1Ref : Tree2Ref
  node.getFilterNode(v.keyword)
}
let treeData1 = $ref([])
let treeData2 = $ref([])

function getTree1(tree) {
  treeData1 = tree || []
}

function getTree2(tree) {
  treeData2 = transformDataStructure(tree) || []
  console.log(treeData2)
}

/* 递归处理数据 */
function transformDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.exclusiveQuestionCount = newItem.questionDiffCountList.reduce(
        (res, i) => res + i.questionCount,
        0,
      )
      newItem.name = newItem.sysKnowledgePointName
      newItem.children = newItem.children?.length
        ? transformDataStructure(newItem.children)
        : []

      return newItem
    })
  } else if (typeof data === "object" && data !== null) {
    return Object.keys(data).reduce((obj, key) => {
      obj[key] = transformDataStructure(data[key])
      return obj
    }, {})
  }
  return data
}

async function initData() {
  let res = await getKnowledgeLevelSelect()
  knowledgeLevelSelect =
    res?.map((v) => {
      return {
        label: v.title,
        value: v.id,
      }
    }) || []
}

async function changeLevel(data) {
  await saveKnowledgeLevelChange({
    sysKnowledgePointId: data.sysKnowledgePointId,
    level: data.level,
  })
  $g.msg("设置成功")
}

onBeforeMount(() => {
  initData()
})
</script>

<style lang="scss" scoped>
:deep() {
  .el-tree-node__content {
    height: auto;
    min-height: 30px;
  }
}

.highlight-check {
  :deep() {
    .is-checked > .el-tree-node__content {
      .title {
        font-weight: bold;
      }
    }
  }
}
</style>
