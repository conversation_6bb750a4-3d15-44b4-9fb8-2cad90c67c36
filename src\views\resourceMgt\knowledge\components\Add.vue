<template>
  <g-dialog
    :title="title"
    :formOptions="formOptions"
    v-model:show="showDialog"
    @confirm="confirm"
  >
    <g-form :formOptions="formOptions"> </g-form>
  </g-dialog>
</template>

<script setup lang="ts">
import {
  getIsSimple,
  getNodeType,
  getFirstKnowledge,
  addKnowledge,
  editKnowledge,
} from "@/api/resourceMgt"
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  show: {
    type: Boolean,
    default: false,
  },
  params: {
    type: Object,
    default: () => {},
  },
})
const title = $computed(() => {
  return props.isEdit ? "编辑" : "新增"
})
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
const formOptions = reactive<any>({
  loading: false,
  ref: null as any,
  items: {
    sysKnowledgePointName: {
      type: "text",
      label: "知识点名称",
      rule: true,
      labelWidth: "150px",
    },
    forLite: {
      type: "radio",
      options: [],
      label: "是否为精简版",
      labelWidth: "150px",
    },
    isHide: {
      type: "radio",
      options: [
        { label: "否", value: 1 },
        { label: "是", value: 2 },
      ],
      label: "是否隐藏",
      labelWidth: "150px",
    },
    sysKnowledgePointType: {
      type: "select",
      label: "节点类型",
      labelWidth: "150px",
      options: [],
    },
    sysKnowledgePointTag: {
      type: "select",
      label: "一级知识点标签",
      labelWidth: "150px",
      options: [],
    },
    ordinal: {
      type: "number",
      label: "排序值(越小越靠前)",
      labelWidth: "150px",
      min: 0,
    },
  },
  data: {
    sysKnowledgePointName: null,
    forLite: null,
    sysKnowledgePointType: null,
    sysKnowledgePointTag: null,
    ordinal: 0,
    isHide: 1,
  },
})
/* 获取精简版 */
async function getIsSimpleApi() {
  let res = await getIsSimple()
  formOptions.items.forLite.options = res.map((item) => {
    return {
      label: item.title,
      value: item.id,
    }
  })
}
/* 获取节点类型 */
async function getNodeTypeApi() {
  let res = await getNodeType()
  formOptions.items.sysKnowledgePointType.options = res.map((item) => {
    return {
      label: item.title,
      value: item.id,
    }
  })
}
/* 获取一级知识点标签 */
async function getFirstKnowledgeApi() {
  let res = await getFirstKnowledge()
  formOptions.items.sysKnowledgePointTag.options = res.map((item) => {
    return {
      label: item.title,
      value: item.id,
    }
  })
}
/* 新增知识点 */
async function add() {
  try {
    await addKnowledge({
      ...formOptions.data,
      sysCourseId: props.params.sysCourseId,
      parentSysKnowledgePointId: props.params.parentSysKnowledgePointId ?? 0,
    })
    $g.msg("新增成功")
  } catch (err) {
    console.log(err)
    formOptions.loading = false
  }
}
/* 编辑知识点 */
async function edit() {
  try {
    await editKnowledge({
      ...formOptions.data,
    })
    $g.msg("编辑成功")
  } catch (err) {
    console.log(err)
    formOptions.loading = false
  }
}
onMounted(() => {
  getIsSimpleApi()
  getNodeTypeApi()
  getFirstKnowledgeApi()
})
watch(
  () => props.show,
  (val) => {
    if (val) {
      formOptions.data = {
        ...props.params,
        forLite:
          props.params.forLite || formOptions.items.forLite.options[0].value,
        sysKnowledgePointType: props.params.sysKnowledgePointType || null,
        sysKnowledgePointTag: props.params.sysKnowledgePointTag || null,
        ordinal: props.params.ordinal || 0,
        isHide: props?.params?.isHide ?? 1,
      }
    }
  },
)
async function confirm() {
  props.isEdit ? await edit() : await add()
  await emit("refresh")
  await emit("update:show", false)
  formOptions.loading = false
}
</script>

<style lang="scss" scoped></style>
