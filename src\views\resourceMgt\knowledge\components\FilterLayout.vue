<template>
  <div class="resourceMgt-container-main">
    <div class="flex">
      <g-form class="!w-auto" @search="search" :formOptions="filterFormOptions">
      </g-form>
    </div>
    <n-grid x-gap="40" :cols="3">
      <n-gi>
        <div class="flex justify-between items-center">
          <div class="flex flex-1">
            <h3 class="flex-1 flex-shrink-0 w-auto mr-20px">
              {{ options.title1 }}
            </h3>
            <div class="flex flex-1 items-center">
              <slot name="left-action"></slot>
              <n-input
                v-model:value="keyword1"
                type="text"
                class="ml-[10px]"
                placeholder="输入章节名称检索"
                @input="treeSearch(1)"
              />
            </div>
          </div>
        </div>
        <slot name="left" :filterData="filterFormOptions.data"></slot>
      </n-gi>
      <n-gi>
        <div class="flex justify-between items-center">
          <div class="flex flex-1 flex-shrink-0">
            <h3 class="flex flex-1 items-center w-auto">
              <span class="flex-shrink-0">{{ options.title2 }}</span>
            </h3>

            <div class="flex flex-1 justify-end items-center">
              <slot name="right-action"> </slot>
            </div>
            <div class="flex flex-1 items-center ml-15px">
              <n-input
                v-model:value="keyword2"
                type="text"
                placeholder="输入知识点名称检索"
                @input="treeSearch(2)"
              />
            </div>
          </div>
        </div>
        <slot name="right"></slot>
      </n-gi>
      <n-gi>
        <div class="flex items-center">
          <h3 class="mr-80px">已选择</h3>
          <n-button
            class="px-20px h-32px ml-10px"
            type="primary"
            @click="$emit('submit')"
          >
            保存</n-button
          >
        </div>
        <slot name="bind"></slot>
      </n-gi>
    </n-grid>
  </div>
</template>

<script setup lang="ts">
import {
  getKnowledgeTree,
  getNewStageListApi,
  getNewSubjectListApi,
  getNewVersionListApi,
  getBookSelectApi,
  getChapterTreeList,
} from "@/api/resourceMgt"

const props = defineProps({
  // 用于控制左侧树的显示
  options: {
    type: Object,
    default() {
      return {
        title1: "新教材（学科网）章节树",
        title2: "老教材（阅卷系统）章节树",
      }
    },
  },
  // 1-章节绑定 2-知识点绑定
  mode: {
    type: Number,
    default: 1,
  },
  type: {
    type: Number,
    default: 1, // 1-章节知识点绑定 2-新老章节绑定
  },
})

let keyword1 = $ref("")
let keyword2 = $ref("")

const filterFormOptions = reactive<any>({
  ref: null as any,
  filter: true,
  labelWidth: "64px",
  showFilterButton: false,
  items: {
    stage: {
      type: "select",
      label: "学段",
      width: "150px",
      options: [],
      labelField: "name",
      valueField: "commonStagesId",
      clearable: false,
    },
    subject: {
      type: "select",
      label: "学科",
      options: [],
      labelField: "name",
      valueField: "commonSubjectsId",
      width: "150px",
      clearable: false,
    },
    version: {
      type: "select",
      label: "版本",
      options: [],
      width: "250px",
      labelField: "name",
      valueField: "commonTextbookVersionsId",
      clearable: false,
    },
    textbook: {
      type: "select",
      label: "教材",
      options: [],
      width: "250px",
      labelField: "volume",
      valueField: "id",
      tooltip: true,
      clearable: false,
    },
  },
  // 列表接口使用到的筛选值,及时搜索时使用
  data: {
    stage: null,
    subject: null,
    version: null,
    textbook: null,
  },
  // 列表接口使用到的筛选值,非及时搜索时使用
  filterData: {},
})

watch(
  () => filterFormOptions.data,
  (newVal) => {
    search($g._.cloneDeep(newVal))
  },
  {
    deep: true,
  },
)
const getNewSubjectList = async () => {
  const res = await getNewSubjectListApi({
    sysStageId: filterFormOptions.data.stage,
  })
  filterFormOptions.items.subject.options = res
  filterFormOptions.items.subject.labelField = "sysCourseName"
  filterFormOptions.items.subject.valueField = "sysCourseId"
  filterFormOptions.data.subject = res[0]?.sysCourseId
}
watch(
  () => filterFormOptions.data.stage,
  async (newVal) => {
    filterFormOptions.items.subject.options = []
    filterFormOptions.items.version.options = []
    filterFormOptions.items.textbook.options = []
    filterFormOptions.data.subject = null
    filterFormOptions.data.version = null
    filterFormOptions.data.textbook = null
    if (newVal) {
      await getNewSubjectList()
    }
  },
)

const getNewVersionList = async () => {
  const res = await getNewVersionListApi({
    sysCourseId: filterFormOptions.data.subject,
  })
  filterFormOptions.items.version.options = res
  filterFormOptions.items.version.labelField = "sysTextbookVersionName"
  filterFormOptions.items.version.valueField = "sysTextbookVersionId"
  filterFormOptions.data.version = res[0]?.sysTextbookVersionId
}
watch(
  () => filterFormOptions.data.subject,
  async (newVal) => {
    filterFormOptions.items.version.options = []
    filterFormOptions.items.textbook.options = []
    filterFormOptions.data.version = null
    filterFormOptions.data.textbook = null
    if (newVal) {
      await getNewVersionList()
      await getRightTree()
    } else {
      emit("getTree2", [])
    }
  },
)
const getTextbookList = async () => {
  const res = await getBookSelectApi({
    sysCourseId: filterFormOptions.data.subject,
    sysTextbookVersionId: filterFormOptions.data.version,
  })
  filterFormOptions.items.textbook.options = res
  filterFormOptions.items.textbook.labelField = "sysTextbookName"
  filterFormOptions.items.textbook.valueField = "sysTextbookId"
  filterFormOptions.data.textbook = res[0]?.sysTextbookId
}
watch(
  () => filterFormOptions.data.version,
  async (newVal) => {
    filterFormOptions.items.textbook.options = []
    filterFormOptions.data.textbook = null
    if (newVal) {
      await getTextbookList()
    }
  },
)

const emit = defineEmits([
  "search",
  "searchDataOption",
  "treeSearch",
  "getTree1",
  "getTree2",
  "submit",
])

async function search(form) {
  let flag = Object.values(form).every(function (value) {
    return value !== undefined && value !== null
  })

  // 获取筛选的数据项，传递到组件外部
  const searchOption = {}
  for (const [key, value] of Object.entries(form)) {
    let { options, valueField, labelField } = filterFormOptions.items[key]
    let option = options.find((item) => item[valueField] == value)
    searchOption[key] = { ...option, name: option?.[labelField], value }
  }
  emit("searchDataOption", searchOption)
  if (!flag) {
    emit("getTree1", [])
    return
  }
  await getSubjectChapterTreeApi()
}

function treeSearch(mode) {
  emit("treeSearch", { keyword: mode == 1 ? keyword1 : keyword2, mode })
}

onMounted(() => {
  initData()
})

const getNewStageList = async () => {
  const res = await getNewStageListApi()
  filterFormOptions.items.stage.options = res
  filterFormOptions.items.stage.labelField = "title"
  filterFormOptions.items.stage.valueField = "id"
  filterFormOptions.data.stage = res[0]?.id
}
async function initData() {
  switch (props.type) {
    case 1:
      await getNewStageList()
      break
  }
}

/* 获取左右树 */
async function getSubjectChapterTreeApi() {
  let api = getChapterTreeList
  let data: any = {}
  let item = filterFormOptions.items.textbook.options.find(
    (v: any) => v.sysTextbookId == filterFormOptions.data.textbook,
  )
  data = {
    sysTextbookId: filterFormOptions.data.textbook,
    isDelete: item?.isDelete,
    source: item?.source,
    withKnowledge: "YES",
  }
  api(data).then((res) => {
    emit("getTree1", res)
  })
  // let api2 = getKnowledgeTree
  // let params = {
  //   sysCourseId: filterFormOptions.data.subject,
  // }
  // api2(params).then((res) => {
  //   emit("getTree2", res)
  // })
}

/* 获取右边树 */
async function getRightTree() {
  let res = await getKnowledgeTree({
    sysCourseId: filterFormOptions.data.subject,
    isQueryAll: 2,
  })
  emit("getTree2", res)
}
defineExpose({
  refreshTree: getSubjectChapterTreeApi,
  refreshRightTree: getRightTree,
})
</script>

<style lang="scss" scoped></style>
