import { utils, writeFile } from "xlsx-js-style"
type TreeData = { name: string; children?: TreeData[]; [k: string]: any }[]

/**
  * 处理树形数据，递归遍历树形结构，将树组件数据处理成二维数组的层级结构，如下
    {
      name: "0",
      children: [
        {
          name: "0-0",
          children: [
            { name: "0-0-0" },
            { name: "0-0-1" },
          ],
        },
        {
          name:"0-1",
          children: [
            { name: "0-1-0" },
            { name: "0-1-1" },
          ],
        }
      ],
    }
    转换成
    [
      ["0", "0-0", "0-0-0"],
      ["",  "",    "0-0-1"],
      ["",  "0-1", "0-1-0"],
      ["",  "",    "0-1-1"],
    ]
 */
function processData(treeData: TreeData) {
  const processedData: any[] = []
  let merges: any[] = []
  //单行最大单元格数量
  let maxLength = 0
  const loop = (childArr, parentNode: { [k: string]: any } = {}) => {
    for (let index = 0; index < childArr.length; index++) {
      const child = childArr[index]
      // 每个节点的info属性是一个数组，用于保存当前节点的父节点的name属性
      if (parentNode.info) {
        // 如果当前节点是是一个父节点的第一个子节点，则保存父节点的name属性，否则父节点name信息设置为空字符串
        if (index === 0) {
          child.info = [...parentNode.info, child.name]
        } else {
          child.info = [...Array(parentNode.info.length).fill(""), child.name]
        }
      } else {
        // 如果父节点没有info属性，则子节点的info属性是自己的name
        child.info = [child.name]
      }
      // 如果当前节点有子节点，则递归调用loop函数，否则将当前节点的info属性push到data数组中
      if (child.children && child.children.length > 0) {
        loop(child.children, child)
      } else {
        processedData.push(child.info)
        maxLength = Math.max(maxLength, child.info.length)
      }
    }
  }
  loop(treeData)

  //计算合并单元格的数据
  merges = mergeCells(processedData)

  //保证每行单元格数量一致(如果单元格数量不一致，会导致单元格样式出现问题)
  processedData.forEach((item) => {
    if (item.length < maxLength) {
      item.push(...Array(maxLength - item.length).fill(""))
    }
  })
  return { data: processedData, merges }
}

// 根据二维数组计算合并的单元格数据
function mergeCells(data) {
  const merges: any[] = []
  // 计算二维数组中的最大的列数
  const maxColumns = Math.max(...data.map((row) => row.length))
  for (let c = 0; c < maxColumns; c++) {
    // 遍历每一列
    let startMergeIdx: null | number = null
    for (let r = 0; r <= data.length; r++) {
      // 遍历每一行
      // 如果当前开始合并索引为空，并且当前单元格非空，则把当前行数设置为开始合并索引。
      if (startMergeIdx === null && r !== data.length && data[r][c] !== "") {
        startMergeIdx = r
      } else if (
        startMergeIdx !== null &&
        (r === data.length || data[r][c] !== "")
      ) {
        // 如果开始合并索引非空，并且已经到达数据末尾或者遇到了一个非空的单元格则执行下面的操作
        if (r - startMergeIdx > 1 || r === data.length) {
          // 如果当前行与开始合并索引之间至少有两行，或者已经到达了数据的末尾。
          merges.push({
            s: { r: startMergeIdx, c: c },
            e: { r: r - 1, c: c },
          })
        }
        // 如果没到数据末尾并且当前单元格非空，则把当前行数设置为开始合并索引。否则，将开始合并索引设为空。
        startMergeIdx = r !== data.length && data[r][c] !== "" ? r : null
      }
    }
  }
  return merges
}

// 处理单元格样式
function processCellStyle(ws) {
  if (typeof ws["!ref"] === "string") {
    const range = utils.decode_range(ws["!ref"])
    for (let C = range.s.c; C <= range.e.c; ++C) {
      // 单元格宽度固定
      ws["!cols"].push({ wpx: 150 })
      for (let R = range.s.r; R <= range.e.r; ++R) {
        const cell_address = { c: C, r: R }
        // 解析位置
        const cell_ref = utils.encode_cell(cell_address)
        if (ws[cell_ref]) {
          ws[cell_ref].s = {
            alignment: {
              horizontal: "left",
              vertical: "center",
              wrapText: true,
            },
            border: {
              top: { style: "thin", color: { rgb: "000000" } },
              left: { style: "thin", color: { rgb: "000000" } },
              bottom: { style: "thin", color: { rgb: "000000" } },
              right: { style: "thin", color: { rgb: "000000" } },
            },
          }
        }
      }
    }
  }
}

export default function exportTreeDataExcel(
  treeData: TreeData,
  fileName: string = "output.xlsx",
) {
  const { data, merges } = processData(treeData)
  const wb = utils.book_new()
  const ws = utils.aoa_to_sheet(data)

  ws["!merges"] = merges
  ws["!cols"] = ws["!cols"] || []

  processCellStyle(ws)

  utils.book_append_sheet(wb, ws, "Sheet1")
  writeFile(wb, fileName)
}
