<template>
  <div>
    <FilterLayout
      :options="{
        title1: '教材（学科网）章节树',
        title2: '知识点树（学科网）',
      }"
      @searchDataOption="searchDataOption = $event"
      @treeSearch="treeSearch"
      @getTree1="getTree1"
      @getTree2="getTree2"
      @submit="submit"
      :mode="2"
      :type="1"
      ref="filterFormRef"
    >
      <template #left="{}">
        <g-tree
          ref="Tree1Ref"
          treeName="LeftTree"
          class="p-10px"
          :treeData="treeData1"
          style="height: calc(100vh - 350px); overflow: auto"
          @radioChange="radioChange"
          nodeKey="sysTextbookCatalogId"
          :defaultProps="{
            label: 'sysTextbookCatalogName',
          }"
        >
          <template #body="{ data }">
            <div>
              <div class="flex items-center">
                <div
                  class="title max-w-[300px] whitespace-pre-wrap box-border flex-shrink-0"
                >
                  {{ data.sysTextbookCatalogName }}
                </div>
                <n-tag class="ml-5px" :bordered="false">
                  {{ getTotalPoints(data) }}知识点
                </n-tag>
              </div>
              <div
                class="text-12px text-[#909399] flex items-center"
                v-for="item in data.catalogKnowledgeList"
                :key="item.sysKnowledgePointId"
              >
                <g-icon name="lightbulb-flash-line" size="18" color="#3399ff" />
                {{ item.sysKnowledgePointName }}
                <span v-if="item.hidden">(已隐藏)</span>
              </div>
            </div>
          </template>
        </g-tree>
      </template>
      <template #left-action>
        <n-space justify="center">
          <n-button
            text
            type="primary"
            :loading="exportLeftLoading"
            :disabled="!treeData1.length"
            @click="handleLeftExport"
          >
            导出
          </n-button>
        </n-space>
      </template>
      <template #right-action>
        <n-space justify="center">
          <n-button
            text
            type="primary"
            :loading="exportLoading"
            :disabled="!treeData2.length"
            @click="handleExport"
          >
            导出
          </n-button>
          <n-button type="primary" text @click="open"> 新增</n-button>
        </n-space>
      </template>
      <template #right>
        <g-tree
          treeName="RightTree"
          ref="Tree2Ref"
          class="p-10px"
          :treeData="treeData2"
          nodeKey="sysKnowledgePointId"
          :default-expanded-keys="[currentKey]"
          :default-checked-keys="[currentKey]"
          auto-expand-parent
          render-after-expand
          style="height: calc(100vh - 350px); overflow: auto"
          :highlight-check="false"
          @node-click="nodeClick"
          :defaultProps="{
            label: 'sysKnowledgePointName',
          }"
        >
          <template #body="{ data }">
            <div class="flex justify-between items-center w-full">
              <g-tooltip
                class="w-[60%]"
                :content="data.sysKnowledgePointName"
                :ref-name="
                  data.sysKnowledgePointId + data.sysKnowledgePointName
                "
              ></g-tooltip>

              <div class="flex-shrink-0 flex justify-end gap-x-[8px]">
                <g-icon
                  class="cursor-pointer"
                  name="ri-add-line"
                  color="var(--g-success)"
                  size="18"
                  @click.stop="open(data)"
                />
                <g-icon
                  class="cursor-pointer"
                  name="ri-edit-line"
                  color="var(--g-primary)"
                  size="18"
                  @click.stop="edit(data)"
                />
                <g-icon
                  v-if="data.source == 2"
                  class="cursor-pointer"
                  name="ri-delete-bin-line"
                  style="color: var(--g-danger) !important"
                  size="18"
                  @click.stop="delKnowledgeApi(data)"
                />
              </div>
            </div>
          </template>
        </g-tree>
      </template>
      <template #bind>
        <n-space vertical>
          <Draggable
            v-model="newBindKnowledge"
            group="people"
            @start="drag = true"
            @end="drag = false"
            item-key="sysKnowledgePointName"
            v-if="newBindKnowledge?.length > 0"
            @change="onMoveCallback"
          >
            <template #item="{ element, index }">
              <div class="cursor-pointer my-5px">
                <n-tag
                  class="cursor-pointer"
                  type="primary"
                  closable
                  size="large"
                  round
                  @close="deleteKnowledge(index)"
                  >{{ element.sysKnowledgePointName }}</n-tag
                >
              </div>
            </template>
          </Draggable>
        </n-space>
        <g-empty v-if="!newBindKnowledge?.length"></g-empty>
      </template>
    </FilterLayout>
    <Add
      v-model:show="showDialog"
      :params="params"
      :isEdit="isEdit"
      @refresh="refreshRightTree"
    />
  </div>
</template>

<script setup lang="ts">
import Draggable from "vuedraggable"
import FilterLayout from "./components/FilterLayout.vue"
import { saveChapterKnowledgeRelation } from "@/api/resourceMgt"
import exportTreeDataExcel from "./exportExcel"
import Add from "./components/Add.vue"
import { delKnowledge } from "@/api/resourceMgt"
// FilterLayout组件请求数据时携带的参数信息，保存下来用于导出数据时使用
let searchDataOption: { [k: string]: any } = {}
let showDialog = $ref(false)
let drag = $ref(false)
let params = $ref<any>({})
let filterFormRef = $ref<any>(null)
let isEdit = $ref(false)
let currentKey = $ref<any>(null)
function getTotalPoints(data) {
  let sum = 0

  function recursion(data) {
    sum += data.catalogKnowledgeList?.length || 0
    if (data.children) {
      for (let child of data.children) {
        recursion(child)
      }
    }
  }
  recursion(data)
  return `${sum || 0}`
}
let exportLoading = $ref(false)
let exportLeftLoading = $ref<any>(false)
let Tree1Ref: any = $ref(null)
let Tree2Ref: any = $ref(null)
let exportTreeData1 = $ref<any>(null)
const onMoveCallback = () => {}
function treeSearch(v) {
  let node = v.mode == 1 ? Tree1Ref : Tree2Ref
  node.getFilterNode(v.keyword)
}
let treeData1 = $ref([])
let treeData2 = $ref([])

/* 递归处理数据 */
function transformLeftDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.name = newItem.sysTextbookCatalogName
      newItem.children = newItem.children?.length
        ? transformLeftDataStructure(newItem.children)
        : []
      return newItem
    })
  } else if (typeof data === "object" && data !== null) {
    return Object.keys(data).reduce((obj, key) => {
      obj[key] = transformLeftDataStructure(data[key])
      return obj
    }, {})
  }
  return data
}
function getTree1(tree) {
  treeData1 = tree || []
  exportTreeData1 = transformLeftDataStructure(tree) || []
  newBindKnowledge = []
  currentKnowledge = {}
}
/* 新增知识点 */
function open(row?) {
  params = {}
  isEdit = false
  currentKey = row?.sysKnowledgePointId
  params.parentSysKnowledgePointId = row?.sysKnowledgePointId
  params.sysCourseId = searchDataOption.subject.sysCourseId
  showDialog = true
}
/* 编辑知识点 */
function edit(row) {
  params = {}
  isEdit = true
  currentKey = row.sysKnowledgePointId
  params = row
  params.sysCourseId = searchDataOption.subject.sysCourseId
  params.parentSysKnowledgePointId = row.parentSysKnowledgePointId || null
  showDialog = true
}
/* 刷新右侧树 */
function refreshRightTree() {
  filterFormRef?.refreshRightTree()
}
/* 刷新左右树 */
function refreshTree() {
  filterFormRef?.refreshTree()
}
/* 删除知识点 */
function delKnowledgeApi(row) {
  if (row.children.length) {
    return $g.msg("无法删除包含子集的知识点", "warning")
  }
  $g.confirm({ content: "是否删除知识点" })
    .then(async () => {
      try {
        await delKnowledge({
          sysKnowledgePointId: row.sysKnowledgePointId,
        })
        $g.msg("删除成功")
        await refreshRightTree()
        searchDataOption.textbook.value && (await refreshTree())
      } catch (err) {
        console.log(err)
      }
    })
    .catch((err) => {})
}
function getTree2(tree) {
  treeData2 = transformDataStructure(tree) || []
}
function handleLeftExport() {
  try {
    const { stage, subject, version, textbook } = searchDataOption
    let fileName = `${stage.name}-${subject.name}-${version.name}-${textbook.name}-章节树.xlsx`
    exportLeftLoading = true
    exportTreeDataExcel(exportTreeData1, fileName)
  } catch (e) {
    console.log("导出表格出现错误", e)
  } finally {
    exportLeftLoading = false
  }
}
/* 导出知识点 */
function handleExport() {
  try {
    const { stage, subject, version, textbook } = searchDataOption
    let fileName = `${stage.name}-${subject.name}-${version.name}-${textbook.name}-知识点树.xlsx`
    exportLoading = true
    exportTreeDataExcel(treeData2, fileName)
  } catch (e) {
    console.log("导出表格出现错误", e)
  } finally {
    exportLoading = false
  }
}
/* 递归处理数据 */
function transformDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.name = newItem.sysKnowledgePointName
      newItem.children = newItem.children?.length
        ? transformDataStructure(newItem.children)
        : []

      return newItem
    })
  } else if (typeof data === "object" && data !== null) {
    return Object.keys(data).reduce((obj, key) => {
      obj[key] = transformDataStructure(data[key])
      return obj
    }, {})
  }
  return data
}
// 新绑定章节
let newBindKnowledge: any = $ref([])
let currentKnowledge: any = $ref({})
function radioChange({ data, checked }) {
  if (checked) {
    if (!Array.isArray(data?.catalogKnowledgeList)) {
      data.chapters = []
    }
    newBindKnowledge = $g._.cloneDeep(
      data?.catalogKnowledgeList?.filter((v) => !v.hidden) || [],
    )
    currentKnowledge = data
  } else {
    newBindKnowledge = []
    currentKnowledge = {}
  }
}

function nodeClick(data) {
  if (!$g.tool.isTrue(currentKnowledge)) {
    $g.msg("请先选择左侧章节树！！", "warning")
    return
  }
  const index = newBindKnowledge.findIndex((e: any) => {
    return e.sysKnowledgePointId == data.sysKnowledgePointId
  })
  if (index == -1) {
    newBindKnowledge.push({
      ...data,
    })
  }
}

function deleteKnowledge(index) {
  newBindKnowledge.splice(index, 1)
}

function submit() {
  if (!$g.tool.isTrue(currentKnowledge)) {
    $g.msg("请先选择左侧章节树！！", "warning")
    return
  }
  saveChapterKnowledgeRelation({
    sysTextbookCatalogId: currentKnowledge.sysTextbookCatalogId,
    sysKnowledgePointId: newBindKnowledge.map((e) => {
      return Number(e.sysKnowledgePointId)
    }),
  }).then((res) => {
    let data = Tree1Ref.getCheckedData(false)
    data.nodes[0].catalogKnowledgeList = $g._.cloneDeep(newBindKnowledge)
    $g.msg("保存成功")
  })
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-tree-node__content {
    height: auto;
    min-height: 30px;
  }
}

.highlight-check {
  :deep() {
    .is-checked > .el-tree-node__content {
      .title {
        font-weight: bold;
      }
    }
  }
}
</style>
