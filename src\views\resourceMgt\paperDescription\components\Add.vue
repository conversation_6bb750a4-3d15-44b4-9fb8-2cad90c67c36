<template>
  <div>
    <g-dialog
      :title="title"
      :formOptions="formOptions"
      v-model:show="showDialog"
      @confirm="confirm"
      width="1000"
      :show-footer="type != 'preview'"
    >
      <g-form :formOptions="formOptions">
        <template #contentType>
          <n-radio
            v-for="item in formOptions.items.contentType.options"
            :checked="formOptions.data.contentType === item.value"
            :value="item.value"
            name="basic-demo"
            @change="handleChange"
            :disabled="disabled"
          >
            {{ item.label }}
          </n-radio>
        </template>
        <template #text>
          <div v-if="formOptions.data.contentType == 'richText'">
            <g-editor
              v-model="formOptions.data.text"
              :config="{ initialFrameHeight: 380 }"
              :initProps="{
                max: 9999,
              }"
              :disabled="disabled"
            ></g-editor>
          </div>
          <g-markdown
            class="w-full"
            height="380px"
            v-if="formOptions.data.contentType == 'markdown'"
            v-model="formOptions.data.text"
            :mode="disabled ? 'preview' : 'edit'"
          ></g-markdown>
        </template>
        <template #file>
          <g-upload
            class="w-1/2"
            v-model:fileList="formOptions.data.file"
            type="drag"
            :disabled="disabled"
            accept=".zip,.rar,.doc,.docx,.pdf,.png,.jpg,.gif,.txt,.xls,.xlsx,.ppt,.pptx"
          ></g-upload>
        </template>
      </g-form>
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  saveQuestionDesc,
  updateQuestionDesc,
  getQuestionDescDetail,
} from "@/api/bookMgt"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "add",
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  questionDescribeId: {
    type: [Number, String],
  },
})
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  items: {
    describeTitle: {
      type: "text",
      label: "标题",
      rule: true,
      length: 200,
      disabled: false,
    },
    contentType: {
      type: "radio",
      label: "编辑器",
      options: [
        {
          label: "富文本",
          value: "richText",
        },
        {
          label: "Markdown",
          value: "markdown",
        },
      ],
      slot: true,
    },
    text: {
      type: "text",
      label: "内容",
      slot: true,
    },
    file: {
      type: "upload",
      label: "附件",
      slot: true,
    },
  },
  data: {
    describeTitle: "",
    contentType: "richText",
    text: "",
    file: [],
  },
})
let titleMap = {
  add: "新增",
  edit: "编辑",
  preview: "查看",
}
let disabled = $ref(false)
const route = useRoute()
/* 标题 */
const title = $computed(() => {
  return titleMap[props.type]
})
/* 获取详情 */
async function getQuestionDescDetailApi() {
  let res = await getQuestionDescDetail({
    questionDescribeId: props.questionDescribeId,
  })
  formOptions.data.describeTitle = res.describeTitle
  formOptions.data.contentType = res.formatType == 1 ? "richText" : "markdown"
  formOptions.data.text = res.describeContext
  formOptions.data.file = res.attach.map((v) => {
    return {
      name: v.attachName,
      fullUrl: v.attachUrl,
      size: v.attachSize,
      suffix: v.attachExtension,
      status: "finished",
      thumbnailUrl: $g.tool.getFileTypeIcon(v.attachExtension),
      id: v.questionDescribeAttachId,
    }
  })
}
watch(
  () => props.show,
  (val) => {
    if (val) {
      if (props.isEdit || props.type == "preview") getQuestionDescDetailApi()
      if (props.type == "preview") {
        formOptions.items.describeTitle.disabled = true
        disabled = true
      } else {
        formOptions.items.describeTitle.disabled = false
        disabled = false
      }
    }
  },
)
/* 切换类型 */
function handleChange(e) {
  if ($g.tool.isTrue(formOptions.data.text)) {
    $g.confirm({
      content: "切换编辑器会导致内容丢失，是否确认切换？",
    }).then(() => {
      formOptions.data.text = ""
      nextTick(() => {
        formOptions.data.contentType = (e.target as HTMLInputElement).value
      })
    })
  } else {
    formOptions.data.contentType = (e.target as HTMLInputElement).value
  }
}
/* 保存 */
async function save() {
  try {
    await saveQuestionDesc({
      questionId: route.query.questionId,
      describeTitle: formOptions.data.describeTitle,
      describeContext: formOptions.data.text,
      formatType: formOptions.data.contentType == "richText" ? 1 : 2,
      attachList: formOptions.data.file.map((v) => {
        return {
          attachName: v.name,
          attachUrl: v.fullUrl,
          attachSize: v.size,
          attachExtension: v.suffix,
        }
      }),
    })
    $g.msg("保存成功")
  } catch (err) {
    console.log(err)
  } finally {
    formOptions.loading = false
  }
}
/* 编辑 */
async function edit() {
  try {
    await updateQuestionDesc({
      questionDescribeId: props.questionDescribeId,
      questionId: route.query.questionId,
      describeTitle: formOptions.data.describeTitle,
      describeContext: formOptions.data.text,
      formatType: formOptions.data.contentType == "richText" ? 1 : 2,
      attachList: formOptions.data.file.map((v) => {
        return {
          attachName: v.name,
          attachUrl: v.fullUrl,
          attachSize: v.size,
          attachExtension: v.suffix,
        }
      }),
    })
    $g.msg("编辑成功")
  } catch (err) {
    console.log(err)
  } finally {
    formOptions.loading = false
  }
}
async function confirm() {
  props.isEdit ? await edit() : await save()
  emit("update:show", false)
  emit("refresh")
}
</script>

<style lang="scss" scoped></style>
