<template>
  <div class="paper-description-container-main">
    <g-table :tableOptions="tableOptions" @changePage="initData">
      <template #header-right>
        <n-button type="primary" @click="openDialog('add')">
          <g-icon name="add-line" size="" color="" />
          新增描述
        </n-button>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button
            type="primary"
            text
            @click="openDialog('preview', row.questionDescribeId)"
            >查看</n-button
          >
          <n-button
            type="primary"
            text
            @click="openDialog('edit', row.questionDescribeId)"
            >编辑</n-button
          >
          <n-button type="error" text @click="deleteItem(row)">删除</n-button>
        </n-space>
      </template>
    </g-table>
    <Add
      v-model:show="showDialog"
      :type="actionType"
      :isEdit="isEdit"
      :questionDescribeId="questionDescribeId"
      @refresh="initData"
    />
  </div>
</template>

<script setup lang="ts" name="PaperDescription">
import Add from "./components/Add.vue"
import { getQuestionDescList, deleteQuestionDesc } from "@/api/bookMgt"
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      prop: "describeTitle",
      label: "标题",
    },
    {
      prop: "userName",
      label: "添加人",
    },
    {
      prop: "createTime",
      label: "添加时间",
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    total: 0,
    page: 1,
    page_size: 10,
  },
})
let actionType = $ref("")
let showDialog = $ref(false)
let isEdit = $ref(false)
const route = useRoute()
let questionDescribeId = $ref<any>(null)
async function initData() {
  try {
    tableOptions.loading = true
    let res = await getQuestionDescList({
      questionId: route.query.questionId,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.data = res.list
    tableOptions.pageOptions.total = res.total
  } catch (err) {
    console.log(err)
    tableOptions.data = []
  } finally {
    tableOptions.loading = false
  }
}
/* 删除描述 */
function deleteItem(row) {
  $g.confirm({ content: "确定删除该条描述？" }).then(async () => {
    await deleteQuestionDesc({
      questionDescribeId: row.questionDescribeId,
    })
    $g.msg("删除成功")
    await initData()
  })
}
onMounted(() => {
  initData()
})
/* 打开弹窗 */
function openDialog(type, id?) {
  isEdit = type === "edit" ? true : false
  questionDescribeId = id
  showDialog = true
  actionType = type
}
</script>

<style lang="scss" scoped></style>
