<template>
  <g-dialog
    :title="title"
    v-bind="$attrs"
    width="1600"
    :show-footer="false"
    @close-x="open"
  >
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="item in tabs"
        :key="item.name"
        :label="item.label"
        :name="item.name"
      >
      </el-tab-pane>
    </el-tabs>
    <QuestionList
      v-show="activeName == 'QuestionList'"
      :bookAttachId="info.id"
    />
    <OcrResult v-if="activeName == 'OcrResult'" :bookAttachId="info.id" />
  </g-dialog>
</template>

<script setup lang="ts">
import QuestionList from "./QuestionList.vue"
import OcrResult from "./OcrResult.vue"
const props = defineProps({
  info: {
    type: Object,
    required: true,
  },
})
let activeName = $ref("QuestionList")
let tabs = [
  { name: "QuestionList", label: "识别后试题清单" },
  { name: "OcrResult", label: "原始识别结果" },
]
const title = $computed(() => {
  return props.info.title
})
function open() {
  activeName = "QuestionList"
}
</script>

<style lang="scss" scoped></style>
