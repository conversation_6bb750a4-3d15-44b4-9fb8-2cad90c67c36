<template>
  <g-dialog
    title="背景资料"
    :formOptions="formOptions"
    v-model:show="showDialog"
    width="1000"
    :on-after-enter="open"
    @confirm="confirm"
  >
    <g-form :formOptions="formOptions">
      <template #content>
        <g-markdown
          class="w-full"
          height="550px"
          v-model="formOptions.data.content"
        ></g-markdown>
      </template>
    </g-form>
  </g-dialog>
</template>

<script setup lang="ts">
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  activeData: {
    type: Object,
    default: () => {},
  },
})
const emit = defineEmits(["update:show"])
const showDialog = useVModel(props, "show", emit)
const formOptions = reactive<any>({
  loading: false,
  ref: null as any,
  items: {
    content: {
      type: "text",
      label: "背景资料",
      slot: true,
    },
  },
  data: {
    content: "",
  },
})
function open() {
  formOptions.data.content =
    $g._.cloneDeep(props.activeData.backgroundMaterials) || ""
}
function confirm() {
  props.activeData.backgroundMaterials = formOptions.data.content
  emit("update:show", false)
  formOptions.loading = false
}
</script>

<style lang="scss" scoped></style>
