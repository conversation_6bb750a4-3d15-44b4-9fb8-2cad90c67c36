<template>
  <div class="m-chckbox--container" :class="[classes]">
    <div
      class="m-chckbox--group"
      :style="mainStyle + sizeStyles"
      @click="toggle"
    >
      <div v-if="checkboxState">
        <svg xmlns="http://www.w3.org/2000/svg" fill="#fff" viewBox="0 0 24 24">
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
        </svg>
      </div>
      <div class="m-chckbox--ripple" :style="rippleSizeStyles">
        <input
          :id="id || uniqueId"
          type="checkbox"
          :name="name"
          :value="value"
          :disabled="disabled"
          :required="required"
          :color="color"
          :checked="checkboxState"
        />
      </div>
    </div>
    <label
      :style="fontSizeStyles"
      class="m-chckbox--label"
      :for="allowLabelClick ? id || uniqueId : ''"
    >
      <slot />
    </label>
  </div>
</template>

<script>
export default {
  name: "GCheckbox",
  model: {
    prop: "model",
    event: "change",
  },
  props: {
    id: {
      type: String,
      default: undefined,
    },
    model: {
      type: [Boolean, Array],
      default: undefined,
    },
    checked: Boolean,
    value: {
      type: [String, Boolean, Number, Object, Array, Function],
      default: undefined,
    },
    name: String,
    required: Boolean,
    disabled: Boolean,
    color: {
      type: String,
      default: "#409eff",
    },
    size: {
      type: Number,
      default: 16,
    },
    fontSize: Number,
    max: {
      type: [Number, String],
      default: "",
    },
    allowLabelClick: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      uniqueId: "",
      lv: this.model,
    }
  },
  computed: {
    checkboxState() {
      if (Array.isArray(this.model))
        return this.model.indexOf(this.value) !== -1
      return this.model || Boolean(this.lv)
    },
    classes() {
      return {
        disabled: this.disabled,
        // active: this.checkboxState,
      }
    },
    mainStyle() {
      return this.checkboxState
        ? this.color &&
            `background-color: ${this.color}; border-color: ${this.color};`
        : ""
    },
    sizeStyles() {
      return this.size ? `width: ${this.size}px; height: ${this.size}px; ` : ""
    },
    rippleSizeStyles() {
      return this.size ? `width: ${this.size}px; height: ${this.size}px;` : ""
    },
    fontSizeStyles() {
      return this.fontSize ? `font-size: ${this.fontSize}px` : ""
    },
  },
  watch: {
    checked(v) {
      if (v !== this.checkboxState) this.toggle()
    },
    model(v) {
      this.lv = v
    },
  },
  mounted() {
    this.genId()

    if (this.checked && !this.checkboxState) {
      this.toggle()
    }
  },
  methods: {
    toggle() {
      if (this.disabled) return
      let v = this.model || this.lv
      if (v?.length == this.max && !v.includes(this.value)) {
        v.pop()
      }
      if (Array.isArray(v)) {
        const i = v.indexOf(this.value)
        if (i === -1) v.push(this.value)
        else v.splice(i, 1)
      } else v = !v

      if (Array.isArray(v)) {
        v = v.sort((a, b) => a.charCodeAt(0) - b.charCodeAt(0))
      }

      this.lv = v
      this.$emit("change", v, this.value)
    },

    genId() {
      if (this.id === undefined || typeof String) {
        this.uniqueId = `m-checkbox--${Math.random()
          .toString(36)
          .substring(2, 10)}`
      } else {
        this.uniqueId = this.id
      }
    },
  },
}
</script>

<style lang="scss">
.__ripple__container {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  color: inherit;
  pointer-events: none;
  border-radius: inherit;
  contain: strict;
}
/* .__ripple__animation {
    color: inherit;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 50%;
    background: currentColor;
    opacity: 0;
    transition: 0.4s cubic-bezier(0, 0, 0.2, 1);
    pointer-events: none;
    overflow: hidden;
    will-change: transform, opacity;
  } */
.__ripple__animation--enter {
  transition: none;
}
.__ripple__animation--visible {
  opacity: 0.15;
}
.m-chckbox--container {
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: 100%;
  margin: 10px 0;
  margin-right: 1rem;
  line-height: 20px;
  cursor: pointer;
  &.active {
    .m-chckbox--group {
      background-color: #009688;
      border-color: #009688;
    }
  }
  &.disabled {
    cursor: not-allowed;
    .m-chckbox--group {
      opacity: 0.14;
    }
    .m-chckbox--label {
      cursor: not-allowed;
      opacity: 0.24;
    }
  }
}
.m-chckbox--ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 1;
  box-sizing: border-box;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}
.m-chckbox--label {
  position: relative;
  width: auto !important;
  padding-left: 10px;
  cursor: pointer;
}
.m-chckbox--group {
  position: relative;
  box-sizing: border-box;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0, 0.54);
  border-radius: 2px;
  transition: 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  input[type="checkbox"] {
    position: absolute;
    left: -999rem;
    -webkit-appearance: none;
    appearance: none;
  }
}
</style>
