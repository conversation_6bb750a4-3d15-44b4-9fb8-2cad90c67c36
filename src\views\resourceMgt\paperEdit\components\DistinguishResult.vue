<template>
  <div>
    <div class="flex items-center gap-x-10px">
      <div>识别结果</div>
      <n-button type="primary" text @click="back">返回</n-button>
    </div>
    <el-scrollbar
      class="h-[600px] pr-10px"
      :class="type === 0 ? 'h-[648px]' : 'h-[600px]'"
      ref="scrollbarRef"
    >
      <List :data="data">
        <template #action="{ row }">
          <n-space justify="center">
            <n-button @click="selectRow(row, 'edit')">编辑此题</n-button>
            <n-button type="primary" @click="selectRow(row, 'import')"
              >导入此题</n-button
            >
          </n-space>
        </template>
      </List>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import List from "@/views/resourceMgt/bookManage/enterResoure/components/List.vue"
import { bindQuestion } from "@/api/bookMgt"
import type { PropType } from "vue"

let scrollbarRef = $ref<any>(null)
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
  type: {
    type: Number,
    default: 0,
  },
})
const route = useRoute()
const emit = defineEmits(["back", "close", "importQuestion"])
function back() {
  emit("back")
}
/* 绑定试题 */
async function bindQuestionApi(val) {
  await bindQuestion({
    bookId: route.query.bookId,
    bookCatalogId: props.chapterId || route.query.bookCatalogId,
    bindQuestionIdList: [val.questionId],
  })
}
/* 选中此题 */
async function selectRow(row, type) {
  if (type == "import") await bindQuestionApi(row)
  emit("importQuestion", row, type)
  emit("close")
}
</script>

<style lang="scss" scoped></style>
