<template>
  <g-dialog
    title="选择知识点"
    v-model:show="showDialog"
    @confirm="confirm"
    :width="1200"
    confirmName="同步到其他小题"
  >
    <div>
      <div
        class="max-h-[300px] overflow-auto mb-[10px] rounded-[5px] p-[5px]"
        :class="{
          border: questionTitle || subQuestion.length > 0,
        }"
      >
        <g-mathjax :text="questionTitle" v-if="questionTitle"></g-mathjax>
        <div v-for="(item, index) in subQuestion" :key="item.subQuestionId">
          <div class="flex gap-[5px]">
            <div class="text-[#0f99eb] w-[40px]">小题{{ index + 1 }}</div>
            <g-mathjax
              v-if="item?.subQuestionTitle"
              :text="item?.subQuestionTitle"
            />
          </div>

          <div v-if="item?.options?.some((item) => item?.value)">
            <div
              v-for="(items, index) in item?.options"
              :key="index"
              class="flex items-center gap-[5px]"
            >
              <div>{{ items.label }}</div>
              <g-mathjax :text="items.value"></g-mathjax>
            </div>
          </div>
        </div>
      </div>
      <div class="flex h-[445px]">
        <!-- 左边树 -->
        <div class="flex-1 pr-10px" v-loading="loading">
          <el-input
            v-model="keyword"
            style="width: 240px"
            placeholder="请输入关键词检索"
            @input="treeSearch()"
          />
          <g-tree
            treeName="RightTree"
            ref="Tree2Ref"
            class="pr-10px border-0 mt-10px max-h-[400px] overflow-auto"
            :treeData="treeData"
            check-strictly
            :highlight-check="false"
            @node-click="nodeClick"
            render-after-expand
            :defaultProps="{
              label: 'sysKnowledgePointName',
            }"
          >
            <template #body="{ data }">
              <div>
                <span>{{ data.sysKnowledgePointName }} </span>
              </div>
            </template>
          </g-tree>
        </div>
        <div
          class="flex-1 ml-10px pl-20px max-h-[640px] overflow-auto"
          style="border-left: 1px solid #ccc"
        >
          <div
            v-for="(item, index) in knowledgeList"
            :key="item.sysKnowledgePointId"
            class="mt-10px"
          >
            <n-tag
              class="cursor-pointer"
              type="primary"
              closable
              size="large"
              round
              @close="deleteKnowledge(index)"
              >{{ item.sysKnowledgePointName }}</n-tag
            >
          </div>
        </div>
      </div>
    </div>
  </g-dialog>
</template>

<script setup lang="ts">
import {
  getKnowledgeTree,
  syncKnowledgeToOtherSubQuestion,
} from "@/api/resourceMgt"
import type { PropType } from "vue"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  knowledgeList: {
    type: Array as PropType<any>,
    default: () => [],
  },
  mainKnowledge: {
    type: Array as PropType<any>,
    default: () => [],
  },
  subQuestion: {
    type: [Array, Object] as PropType<any>,
    default: () => [],
  },
  questionTitle: {
    type: String,
    default: "",
  },
})
let loading = $ref(false)
let Tree2Ref = $ref<any>(null)
const emit = defineEmits([
  "update:show",
  "editKnowledge",
  "deleteSubKnowledge",
  "getSubQuestion",
  "manualSync",
])
const showDialog = useVModel(props, "show", emit)
async function confirm() {
  if (props.subQuestion?.subQuestionId) {
    await syncKnowledgeToOtherSubQuestion({
      subQuestionId: props.subQuestion?.subQuestionId ?? "",
      sysKnowledgePointIdList:
        props?.knowledgeList?.map((item) => item?.sysKnowledgePointId) ?? [],
    })
    emit("update:show", false)
    emit("getSubQuestion")
  } else {
    //没有subQuestionId 手动同步
    emit("manualSync", props?.knowledgeList ?? [])
    emit("update:show", false)
  }
}
let keyword = $ref<any>("")
let treeData = $ref<any>([])
const route = useRoute()

/* 获取左侧树 */
async function initData() {
  try {
    loading = true
    let res = await getKnowledgeTree({
      sysCourseId: route.query.sysCourseId,
    })
    loading = false
    treeData = res
  } catch (err) {
    console.log(err)
    loading = false
    treeData = []
  }
}
/* 左侧树点击 */
function nodeClick(data) {
  const index = props.knowledgeList.findIndex((e: any) => {
    return e.sysKnowledgePointId == data.sysKnowledgePointId
  })
  if (index == -1) {
    props.knowledgeList.push({
      ...data,
    })
  }
}
/* 删除知识点 */
function deleteKnowledge(index) {
  props.knowledgeList.splice(index, 1)
}
/* 关键词检索 */
function treeSearch() {
  let node = Tree2Ref
  node.getFilterNode(keyword)
}
watch(
  () => props.show,
  (val) => {
    if (val) {
      initData()
      $g.tool.renderMathjax()
    }
  },
)
</script>

<style lang="scss" scoped></style>
