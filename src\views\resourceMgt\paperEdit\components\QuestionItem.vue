<template>
  <div
    class="p-15px border-2 border-[#75C6FE] mb-16px rounded-[24px] cursor-pointer border-solid"
    @click="handleExpandChange"
  >
    <!-- 试题部分 -->
    <div class="mt-16px relative overflow-auto">
      <!-- 大题题目 -->
      <g-mathjax :text="mainQuestion.questionTitle" />
      <!-- 子题部分 -->
      <div
        v-for="item in mainQuestion.subQuestions"
        :key="item.subQuestionId"
        class="my-10px"
      >
        <div class="flex items-start">
          <div v-if="mainQuestion.subQuestions.length > 1">
            {{ item.structureNumber }}
          </div>
          <g-mathjax :text="item.subQuestionTitle" />
        </div>
        <!-- 选择题 -->
        <template v-if="[1, 2, 3].includes(item.subQuestionType)">
          <div
            v-for="option in item.optionArr"
            :key="option.label"
            class="flex my-5px"
          >
            <div class="w-20px mr-6px">{{ option.label }}.</div>
            <div>
              <g-mathjax :text="option.value"></g-mathjax>
            </div>
          </div>
        </template>

        <!-- 答案/解析 -->
        <Transition>
          <div
            v-if="questionItem.expanded"
            class="rounded-[8px] text-[#888] mt-10px border border-[#fff] px-20px py-0 transition-[padding] duration-300"
            :class="[questionItem.expand ? '!border-[#e7e7e7] !py-20px' : '']"
          >
            <Transition>
              <div v-show="questionItem.expand" class="overflow-hidden">
                <div>【答案】</div>
                <div class="pl-7px mt-10px mb-20px">
                  <g-mathjax :text="item.subQuestionAnswer" />
                </div>
                <div>【解析】</div>
                <div
                  v-for="(val, num) in item.subQuestionParseList"
                  :key="val.subQuestionParseId"
                  class="my-5px"
                >
                  <div v-if="item.subQuestionParseList.length > 1">
                    ({{ num + 1 }})
                  </div>
                  <div class="pl-7px">
                    <g-mathjax :text="val.content" />
                  </div>
                </div>
              </div>
            </Transition>
          </div>
        </Transition>
      </div>
    </div>
    <hr />
    <div class="flex justify-end">
      <n-button type="primary" @click.stop="confirm">导入</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  questionItem: {
    type: Object,
    required: true,
  },
})

// 是否展开答案和解析
// let expand = $ref(false)
// 标志答案和解析是否已经展开过，使用V-IF控制答案和解析的第一次渲染，第一次渲染以后则使用v-show控制显示，不再进行dom的插入
// let expanded = $ref(false)

const mainQuestion = $g._.cloneDeep(props.questionItem)
mainQuestion.subQuestions.forEach((v) => {
  if ([1, 2].includes(v.subQuestionType)) {
    v.optionArr = Object.keys(v)
      .filter(
        (key) => key.includes("option") && v[key] && key !== "optionNumber",
      )
      .map((realKey) => {
        return {
          label: realKey.charAt(realKey.length - 1).toLocaleUpperCase(),
          value: v[realKey],
        }
      })
  } else if (v.subQuestionType == 3) {
    v.optionArr = [
      {
        id: 1,
        label: "√",
        value: null,
      },
      {
        id: 2,
        label: "×",
        value: null,
      },
    ]
  }
})
let importQuestion = inject("importQuestion") as Function
/* 导入 */
function confirm() {
  importQuestion(mainQuestion)
}
onMounted(() => {
  $g.tool.renderMathjax()
})

// 点击展开收起
function handleExpandChange() {
  if (!props.questionItem.expanded) {
    props.questionItem.expanded = true
    nextTick($g.tool.renderMathjax)
  }
  props.questionItem.expand = !props.questionItem.expand
}
</script>

<style lang="scss" scoped>
:deep() {
  .mathjax-qmdr-yf,
  .mj-qmdr-yf {
    max-height: 40px;
  }
}
.v-enter-active,
.v-leave-active {
  transition: opacity 0.3s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>
