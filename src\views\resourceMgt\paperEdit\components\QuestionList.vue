<template>
  <div>
    <el-scrollbar height="650" class="px-10px" v-loading="loading">
      <template v-if="list.length">
        <QuestionItem
          v-for="item in list"
          :key="item.questionId"
          :questionItem="item"
        />
      </template>
      <g-empty v-else></g-empty>
    </el-scrollbar>
    <g-page :pageOptions="pageOptions" @change="getDirQuestionListApi"></g-page>
  </div>
</template>

<script setup lang="ts">
import { getQuestionAttachOcrAi } from "@/api/bookMgt"
import QuestionItem from "./QuestionItem.vue"
const props = defineProps({
  bookAttachId: {
    type: [Number, String],
    required: true,
  },
})
let list = $ref<any>([])
let pageOptions = $ref({
  page: 1,
  page_size: 10,
  total: 0,
})
let loading = $ref(false)
/* 试题列表 */
async function getDirQuestionListApi() {
  try {
    loading = true
    const data = await getQuestionAttachOcrAi({
      bookAttachId: props.bookAttachId,
      page: pageOptions.page,
    })
    loading = false
    list = data
    pageOptions.total = data.length
  } catch (err) {
    console.log(err)
    loading = false
    list = []
  }
}
onMounted(() => {
  getDirQuestionListApi()
})
</script>

<style lang="scss" scoped></style>
