<template>
  <g-dialog
    title=""
    v-bind="$attrs"
    :show-footer="false"
    :width="1200"
    @close-x="close"
  >
    <n-tabs type="line" animated v-model:value="activeTab">
      <n-tab-pane
        :name="0"
        tab="学科网搜索（请输入题干信息或者上传图片进行识别）"
      >
      </n-tab-pane>
      <n-tab-pane :name="1" tab="本地搜索"> </n-tab-pane>
    </n-tabs>

    <div v-loading="loading">
      <div class="w-[800px] h-[670px] mx-auto pt-50px" v-if="!finished">
        <div>
          <n-input
            v-model:value="keyword"
            type="textarea"
            :disabled="$g.tool.isTrue(fileList)"
            placeholder="请输入题干信息"
          />
        </div>
        <div class="mt-20px h-[300px]">
          <div class="text-gray-default">
            tips:鼠标移入上传组件可粘贴图片进行上传
          </div>
          <!-- <Suspense> -->
          <g-upload
            v-model:fileList="fileList"
            type="drag"
            :disabled="$g.tool.isTrue(keyword)"
            accept=".png,.jpg,.jpeg"
            :max="1"
            @onChange="handleChange"
            :ossConfig="{
              timeout: 20 * 1000,
              timeoutMsg: '图片识别失败,请调整图片后重新上传',
            }"
            :fileConfig="{
              img: {
                maxSize: 2 * 1024 * 1024,
              },
            }"
          ></g-upload>
          <!-- </Suspense> -->
        </div>
        <div>
          <n-button
            type="primary"
            block
            @click="distinguish"
            :disabled="disabledBtn"
            :loading="btnLoading"
            >开始识别</n-button
          >
        </div>
      </div>
      <div v-else>
        <DistinguishResult
          :data="questionList"
          @back="enterDialog"
          @importQuestion="importQuestion"
          @close="close"
          :chapterId="chapterId"
          :type="activeTab"
        />
        <g-page
          v-if="activeTab === 1"
          :pageOptions="pageOptions"
          @change="distinguish"
        ></g-page>
      </div>
    </div>
  </g-dialog>
</template>

<script setup lang="ts">
import DistinguishResult from "./DistinguishResult.vue"
import { getXKWSearch, imageToText } from "@/api/bookMgt"
import { getEsQuestion } from "@/api/bookMgt"
import type { PropType } from "vue"

const emit = defineEmits(["update:show", "importQuestion"])
let finished = $ref(false)
let keyword = $ref<any>("")
let fileList = $ref<any>([])
const route = useRoute()
let imgStr = $ref<any>("")
let questionList = $ref<any>([])
let loading = $ref(false)
let btnLoading = $ref(false)
let activeTab = $ref(0) //0-学科网搜索 1-本地搜索
let xkwList = $ref([])
let localList = $ref([])

const pageOptions = $ref({
  page: 1,
  page_size: 10,
  total: 0,
})

const props = defineProps({
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
})

function close() {
  emit("update:show", false)
  loading = false
  btnLoading = false
  activeTab = 0
  enterDialog()
}
const disabledBtn = $computed(() => {
  return (
    (!$g.tool.isTrue(imgStr) || !fileList.length) && !$g.tool.isTrue(keyword)
  )
})
/* 导入试题 */
function importQuestion(val, type) {
  emit("importQuestion", val, type)
}
/* 图片识别 */
async function handleChange(file) {
  try {
    if (file.status == "finished") {
      btnLoading = true
      let res = ""
      res = await imageToText({
        url: file.fullUrl,
      })
      imgStr = res
      btnLoading = false
    }
  } catch (err) {
    btnLoading = false
    console.log("识别图片错误", err)
  }
}

/* 开始识别 */
async function distinguish() {
  try {
    questionList = []
    loading = true
    if (activeTab === 0) {
      const res = await getXKWSearch({
        sysCourseId: route.query.sysCourseId,
        text: imgStr || keyword,
      })
      questionList = res || []
      xkwList = res
    } else {
      const res = await getEsQuestion({
        questionText: imgStr || keyword,
        sysCourseId: route.query.sysCourseId,
        page: pageOptions.page,
        pageSize: pageOptions.page_size,
      })
      questionList = res.list || []
      pageOptions.total = res.total
      localList = res.list
    }
    loading = false
    finished = true
  } catch (err) {
    console.log("识别试题错误", err)
    loading = false
  }
}

function enterDialog() {
  finished = false
  keyword = ""
  fileList = []
  imgStr = ""
  pageOptions.page = 1
  xkwList = []
  localList = []
}

watch(
  () => activeTab,
  async (newTab) => {
    // 如果未开始识别，则直接返回
    if (!finished) return

    const lists = {
      0: xkwList,
      1: localList,
    }

    const targetList = lists[newTab]

    if (targetList.length) {
      // 如果目标列表有数据，直接赋值给 questionList
      questionList = $g._.cloneDeep(targetList)
      nextTick(() => {
        $g.tool.renderMathjax()
      })
    } else {
      // 如果目标列表无数据，调用识别函数请求试题列表
      try {
        await distinguish()
      } catch (error) {
        console.error(error)
      }
    }
  },
)
</script>

<style lang="scss" scoped></style>
