<template>
  <div class="paper-edit-container-main">
    <div>
      <div class="flex items-center">
        <div class="w-[80px] flex-shrink-0">题型:</div>
        <div
          class="flex flex-wrap gap-[10px] relative flex-1"
          ref="containerRef"
        >
          <template v-for="(item, index) in questionType" :key="item.value">
            <!-- 主选项 -->
            <div
              class="py-5px px-10px rounded-[5px] cursor-pointer shrink-0"
              :class="{
                active: parentTypeObj?.value === item.value,
                itemBox: item.value !== parentTypeObj?.value,
              }"
              @click="changeTypeBtn(item, 'parent')"
            >
              <span>{{ item.label }}</span>
            </div>

            <!-- children 行 -->
            <template v-if="shouldShowChildrenAfter(index)">
              <div
                v-if="questionType[selectedIndex]?.children?.length"
                class="w-full bg-[#E8F5FD] text-12px px-[10px] py-[5px] rounded-[4px] cursor-pointer -mt-8px"
              >
                <div class="flex flex-wrap gap-y-[10px] gap-x-[25px]">
                  <span
                    v-for="child in questionType[selectedIndex].children"
                    :key="child.value"
                    :class="{
                      active: childTypeObj?.value === child.value,
                    }"
                    @click="changeTypeBtn(child, 'children')"
                  >
                    {{ child.label }}
                  </span>
                </div>
              </div>
            </template>
          </template>
        </div>
      </div>

      <div class="flex items-center mt-10px">
        <div class="w-[80px] flex-shrink-0">知识点:</div>
        <div class="flex flex-1 items-center flex-wrap gap-[10px]">
          <div v-if="mainKnowledge.length">
            <n-tag
              v-for="item in mainKnowledge"
              :key="item.sysKnowledgePointId"
              class="mr-10px mb-10px"
              type="primary"
              size="large"
              round
              >{{ item.sysKnowledgePointName }}</n-tag
            >
          </div>
          <div v-else class="text-info">暂无知识点</div>
        </div>
      </div>
      <div class="mt-10px flex items-center gap-x-[20px]">
        <div class="flex items-center gap-x-[10px]">
          <span>关键能力:</span>
          <n-tree-select
            multiple
            v-model:value="mainQuestion.sysCourseAbilityIdList"
            :options="abilityList"
            clearable
            :render-label="renderLabel"
            placeholder="请选择关键能力"
            class="w-[350px]"
          />
        </div>
        <div class="flex items-center gap-x-[10px]">
          <span>学科素养:</span>
          <n-tree-select
            multiple
            v-model:value="mainQuestion.sysCourseLiteracyIdList"
            :options="literacyList"
            clearable
            :render-label="renderLabel"
            placeholder="请选择学科素养"
            class="w-[350px]"
          />
        </div>
        <div class="flex items-center gap-x-[10px]">
          <span>知识能力:</span>
          <n-tree-select
            multiple
            v-model:value="mainQuestion.sysCourseKnowAbilityIdList"
            :options="knowledgeAbilityList"
            clearable
            :render-label="renderLabel"
            placeholder="请选择知识能力"
            class="w-[350px]"
          />
        </div>
      </div>
      <div class="flex justify-end mt-10px">
        <n-space justify="center">
          <n-button
            v-if="
              route.query?.questionId || mainQuestion?.questionId || isXKWEdit
            "
            type="success"
            @click="confirm('save')"
            :loading="loading"
            :disabled="outLoading"
            >保存修改</n-button
          >
          <n-button
            v-else
            type="success"
            @click="confirm('save', 1)"
            :loading="loading"
            :disabled="outLoading || loading"
            >保存进入下一题</n-button
          >
          <n-button
            type="error"
            @click="confirm('out')"
            :loading="outLoading"
            :disabled="outLoading || loading"
            >保存后退出</n-button
          >
        </n-space>
      </div>
      <div class="flex justify-between items-center">
        <div class="flex items-start mb-10px">
          <div class="main-title w-[80px] flex-shrink-0">题干</div>
          <div class="w-[880px]">
            <Suspense>
              <g-upload
                :fileConfig="{
                  video: {
                    maxSize: 500 * 1024 * 1024,
                  },
                }"
                v-model:fileList="mainQuestion.questionFiles"
                type="button"
                tips="请上传分辨率为1920X1080 、1280x720且为横屏的mp4视频,视频最大500MB"
                accept=".mp4"
              >
                <n-button>上传视频讲解</n-button>
              </g-upload>
            </Suspense>
          </div>
        </div>
        <div class="flex items-center gap-x-[10px]">
          <n-button
            type="primary"
            v-if="!(route.query?.questionId || mainQuestion?.questionId)"
            @click="toAi"
            >AI录题</n-button
          >
          <n-button
            type="primary"
            v-if="!(route.query?.questionId || mainQuestion?.questionId)"
            @click="showXKWDialog = true"
            >学科网搜索</n-button
          >
          <!-- <div v-if="route.query.bookId" class="flex items-center gap-x-[10px]">
            <n-select
              v-model:value="currentAttach"
              :options="attachOptions"
              label-field="fileName"
              value-field="bookAttachId"
              placeholder="请选择附件"
              :renderOption="renderTooltipOption"
              class="w-300px"
            />
            <n-button type="primary" text @click="openPage">查看</n-button>
          </div> -->
        </div>
      </div>
      <g-ueditor
        v-model="mainQuestion.questionTitle"
        img2Formula
        :questionId="route.query.questionId || mainQuestion.questionId"
      >
      </g-ueditor>
      <div class="flex justify-between mt-20px mb-10px">
        <div class="sub-title w-[80px] pb-2px">小题</div>
      </div>
      <div class="pb-40px">
        <SubQuestion
          ref="subQuestionRef"
          :subQuestion="subQuestion"
          :subQuestionType="subQuestionType"
          :isEdit="Boolean(route.query.questionId)"
          :questionTitle="mainQuestion.questionTitle"
          :questionId="route.query.questionId || mainQuestion.questionId"
          @getSubQuestion="getDetail"
        />
      </div>
    </div>
    <!-- 附件弹窗 -->
    <AttachDialog v-model:show="showAttachDialog" :info="attachInfo" />
    <!-- 学科网搜索 -->
    <XKWDialog v-model:show="showXKWDialog" @importQuestion="bindQuestion" />
  </div>
</template>

<script setup lang="ts" name="PaperEdit">
import { useResourceMgtStore } from "@/stores/modules/resourceMgt"
import GTooltip from "@/components/global/g-tooltip/index.vue"
import AttachDialog from "./components/AttachDialog.vue"
import XKWDialog from "./components/XKWDialog.vue"
import { NTooltip } from "naive-ui"
import {
  getQuestionTypeTree,
  getSubQuestionTypeList,
  saveQuestion,
  getQuestionDetail,
  editQuestion,
  getSubjectLiteracyList,
  getSubjectAbilityList,
  getQuestionAttachList,
} from "@/api/bookMgt"
import { getKnowledgeAbilityList } from "@/api/resourceMgt"
import SubQuestion from "./components/SubQuestion.vue"
import chooseData from "./index"

const resourceMgt = useResourceMgtStore()
let questionType = $ref<any>([])
const route = useRoute()
const router = useRouter()
let showAttachDialog = $ref(false) //附件弹窗
let currentAttach = $ref<any>(null)
let attachOptions = $ref<any>([])
let mainQuestion = $ref<any>({
  sysQuestionTypeId: "",
  questionTitle: "",
  questionFiles: [],
  sysCourseLiteracyIdList: [], //学科素养
  sysCourseAbilityIdList: [], //学科能力
  sysCourseKnowAbilityIdList: [], //知识能力
})
let showXKWDialog = $ref(false)
const mainKnowledge = $computed<any>(() => {
  let arr = [] as any
  subQuestion.forEach((item) => {
    arr = arr.concat(item.knowledgePoints)
  })
  return $g._.uniqBy(arr, "sysKnowledgePointId") || []
})
let subQuestion = $ref<any>([
  {
    subQuestionTitle: "", // 问题描述
    subQuestionType: "",
    structureNumber: "(1)",
    score: 1, // 小题分数
    subQuestionAnswer: "", // 答案
    subQuestionParse: "", // 详细解析
    subQuestionParseList: [{ content: "" }], //解析列表
    knowledgePoints: [], // 知识点
    // 下面属于适配数据
    knowledge: [],
    optionsAnswer: [],
    options: $g._.cloneDeep(chooseData.chooseOption.slice(0, 4)),
  },
])
let subQuestionRef = $ref<any>(null)
let subQuestionType = $ref<any>([])
let loading = $ref(false)
let outLoading = $ref(false)

function renderLabel({ option }) {
  return h(GTooltip, { content: option.label, refName: String(option.key) }, {})
}
function renderTooltipOption({ node, option }: { node: any; option: any }) {
  return h(NTooltip, null, {
    trigger: () => node,
    default: () => option.fileName,
  })
}
let abilityList = $ref<any>([]) //学科能力
let literacyList = $ref<any>([]) //学科素养
let knowledgeAbilityList = $ref<any>([]) //知识能力
let attachInfo = $ref<any>({})

// 计算每行能容纳的元素数量（假设容器宽度为 1000px，每个元素宽度加间距约为 100px）
let itemsPerRow = $ref(0)
const containerRef = $ref<any>(null)
// 当前选中的子题类型
let parentTypeObj = $ref<any>(null)
let childTypeObj = $ref<any>(null)

const manualSwitchTypeId = $computed(() => {
  return childTypeObj?.value ? childTypeObj?.value : parentTypeObj?.value
})

/* 跳转AI录题 */
function toAi() {
  router.push({
    name: "AiRecognition",
    query: {
      sysCourseId: route.query.sysCourseId,
      bookId: route.query.bookId,
      bookName: route.query.bookName,
      activeName:
        (route.query.category as any) == 2 ? "PaperManage" : "BookManage",
    },
  })
}
/* 打开附件弹窗 */
function openPage() {
  if (currentAttach) {
    attachInfo.title = attachOptions.find(
      (v) => v.bookAttachId === currentAttach,
    ).fileName
    attachInfo.id = currentAttach
    showAttachDialog = true
  } else {
    $g.msg("请选择附件进行查看", "warning")
  }
}
/* 获取已识别附件 */
async function getQuestionAttachListApi() {
  let res = await getQuestionAttachList({
    bookId: route.query.bookId,
  })
  attachOptions = res || []
}
/* 获取学科素养列表 */
async function getSubjectLiteracyListApi() {
  let res = await getSubjectLiteracyList({
    sysCourseId: route.query.sysCourseId,
  })
  literacyList = res ? transformDataStructure(res) : []
}
/* 获取学科能力列表 */
async function getSubjectAbilityListApi() {
  let res = await getSubjectAbilityList({
    sysCourseId: route.query.sysCourseId,
  })
  abilityList = res ? transformDataStructure(res) : []
}
/* 获取知识能力列表 */
async function getKnowledgeAbilityListApi() {
  let res = await getKnowledgeAbilityList({
    sysCourseId: route.query.sysCourseId,
  })
  knowledgeAbilityList = res ? transformDataStructure(res) : []
}
/* 递归处理数据 */
function transformDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.label = newItem.name
      newItem.key = newItem.id
      newItem.children = newItem.children?.length
        ? transformDataStructure(newItem.children)
        : null

      return newItem
    })
  } else if (typeof data === "object" && data !== null) {
    return Object.keys(data).reduce((obj, key) => {
      obj[key] = transformDataStructure(data[key])
      return obj
    }, {})
  }
  return data
}
/* 获取大题题型 */
async function getQuestionTypeListApi() {
  let res = await getQuestionTypeTree({
    sysCourseId: route.query.sysCourseId,
  })
  questionType = res.map((v, i) => {
    return {
      label: v.sysQuestionTypeName,
      value: v.sysQuestionTypeId,
      children: v.children?.map((c) => {
        return {
          label: c.sysQuestionTypeName,
          value: c.sysQuestionTypeId,
        }
      }),
    }
  })
  parentTypeObj = questionType?.[0] ?? {}
}
/* 获取子题类型 */
async function getSubQuestionTypeListApi() {
  let res = await getSubQuestionTypeList()
  subQuestionType = res.map((v) => {
    return {
      label: v.title,
      value: v.id,
    }
  })
  if (!route.query.questionId) subQuestion[0].subQuestionType = res[0]?.id
}
/* 题目验证 */
function questionVerify({ mainQuestion, subQuestion }) {
  let msg = ""
  if (!parentTypeObj?.value && !childTypeObj?.value) {
    $g.msg("请选择题型！", "warning")
    return false
  }

  $g._.forEach(subQuestion, (e, i) => {
    let isTypeID12 = e.subQuestionType == 1 || e.subQuestionType == 2

    // 验证小题题号
    // if (!e.structure_number) {
    //   msg = `第${i + 1}小题：请输入小题题号！`
    //   return false
    // }
    // if (!e.score) {
    //   // 验证小题分数
    //   msg = `第${i + 1}小题：请输入小题分数！`
    //   return false
    // }

    // 如果是选择题 验证选项答案
    if (isTypeID12) {
      let flag = $g._.every(e.options, (e2) => {
        if (!e2.value) {
          msg = `第${i + 1}小题：请输入答案(${e2.label})！`
        }
        return e2.value
      })
      if (!flag) return false
    }

    // 验证详细解析
    if (!e.subQuestionParseList[0].content) {
      msg = `第${i + 1}小题：请输入详细解析！`
      return false
    }
    // 验证选择题 答案个数
    if ((isTypeID12 || e.subQuestionType == 3) && !e.optionsAnswer.length) {
      msg = `第${i + 1}小题：请选择正确答案！`
      return false
    }

    // 验证应答时间(编辑时)
    if (
      route.query.questionId &&
      (e.reasonableMinTime || e.reasonableMaxTime)
    ) {
      const minTime = e.reasonableMinTime
      const maxTime = e.reasonableMaxTime

      // 如果有一个值存在，需要验证另一个值
      if (!minTime || !maxTime) {
        msg = `第${i + 1}小题：请完整填写应答时间！`
        return false
      }

      // 验证是否为合法数字（不允许多位数以0开头）
      const validNumberReg = /^\d$|^[1-9]\d*$/
      if (!validNumberReg.test(minTime) || !validNumberReg.test(maxTime)) {
        msg = `第${i + 1}小题：应答时间必须为有效数字且多位数不能以0开头！`
        return false
      }

      // 验证最大值必须大于最小值
      if (Number(maxTime) <= Number(minTime)) {
        msg = `第${i + 1}小题：最大应答时间必须大于最小应答时间！`
        return false
      }
      // 验证最大值9位数
      if (Number(maxTime) > 3600) {
        msg = `第${i + 1}小题：最大应答时间不能大于3600秒！`
        return false
      }
    }
  })
  // if (!mainQuestion.questionTitle) {
  //   msg = "请输入题干！"
  // }

  if (!msg) return true
  $g.msg(msg, "warning")
}
async function save(type, next?) {
  try {
    // 验证
    if (!questionVerify({ mainQuestion, subQuestion })) return
    subQuestion.forEach((v) => {
      if ([1, 2, 3].includes(v.subQuestionType)) {
        v.subQuestionAnswer = v.optionsAnswer.join("")
        if (v.subQuestionType != 3) {
          v.options.forEach((vv) => {
            v["option" + vv.label] = vv.value
          })
        }
      }
      v.subQuestionParseList = v.subQuestionParseList.filter(
        (vv) => vv.content.length,
      )
      v.subQuestionParse = v.subQuestionParseList[0].content
    })
    mainQuestion.sysQuestionTypeId =
      manualSwitchTypeId ?? mainQuestion.sysQuestionTypeId
    mainQuestion.questionFiles = mainQuestion.questionFiles?.map((v) => {
      return {
        ...v,
        fileName: v.name,
        fileAbsoluteUrl: v.fullUrl,
        fileSize: v.size,
        fileDuration: v.time_length,
      }
    })
    if (type == "save") {
      loading = true
    } else {
      outLoading = true
    }
    const query = {
      ...mainQuestion,
      subQuestions: subQuestion,
      bookCatalogId: route.query.bookCatalogId,
      bookId: route.query.bookId,
    }
    if (isXKWEdit) query.scene = 1
    const data = await saveQuestion(query)
    let questionId = data.questionId
    //学科网搜索---编辑此题点击保存修改,给题目加上返回的题目id 再次点击 保存后退出/保存修改,走编辑逻辑
    if (data?.questionId && isXKWEdit && type == "save") {
      isXKWEdit = false
      mainQuestion.questionId = data.questionId
      await getDetail()
    }
    setTimeout(() => {
      loading = false
      outLoading = false
      $g.msg("保存成功")
      if (type == "out") {
        resourceMgt.setQuestionId(questionId)
        nextTick(() => {
          router.go(-1)
        })
      }
      if (next) {
        reset()
      }
      localStorage.getItem("questionData") &&
        localStorage.removeItem("questionData")
    }, 500)
  } catch (err) {
    console.log(err)
    loading = false
    outLoading = false
  }
}
/* 编辑试题 */
async function edit(type) {
  try {
    // 验证
    if (!questionVerify({ mainQuestion, subQuestion })) return
    subQuestion.forEach((v) => {
      if ([1, 2, 3].includes(v.subQuestionType)) {
        v.subQuestionAnswer = v.optionsAnswer.join("")
        if (v.subQuestionType != 3) {
          v.options.forEach((vv) => {
            v["option" + vv.label] = vv.value
          })
        }
      }
      v.subQuestionParseList = v.subQuestionParseList.filter(
        (vv) => vv.content.length,
      )
      v.subQuestionParse = v.subQuestionParseList[0].content
    })
    mainQuestion.sysQuestionTypeId =
      manualSwitchTypeId ?? mainQuestion.sysQuestionTypeId
    mainQuestion.questionFiles = mainQuestion.questionFiles?.map((v) => {
      return {
        ...v,
        fileName: v.name,
        fileAbsoluteUrl: v.fullUrl,
        fileSize: v.size,
        fileDuration: v.time_length,
      }
    })
    if (mainQuestion.questionSource != 2) {
      $g.confirm({
        content: "保存后，已经修改的试题，再次同步时将跳过。",
        type: "warning",
        title: "确认保存吗？",
      })
        .then(async () => {
          if (type == "save") {
            loading = true
          } else {
            outLoading = true
          }
          await editQuestion({
            ...mainQuestion,
            subQuestions: subQuestion,
            bookCatalogId: route.query.bookCatalogId,
            questionId: route.query.questionId || mainQuestion.questionId,
            bookId: route.query.bookId,
            bookCatalogQuestionId: route.query.bookCatalogQuestionId,
          })
          setTimeout(async () => {
            loading = false
            outLoading = false
            $g.msg("保存成功")
            if (type == "out") {
              router.go(-1)
            } else {
              await getDetail()
            }
          }, 500)
        })
        .catch((e) => {
          console.log(e)
          loading = false
          outLoading = false
        })
    } else {
      if (type == "save") {
        loading = true
      } else {
        outLoading = true
      }

      await editQuestion({
        ...mainQuestion,
        subQuestions: subQuestion,
        bookCatalogId: route.query.bookCatalogId,
        questionId: route.query.questionId || mainQuestion.questionId,
        bookId: route.query.bookId,
        bookCatalogQuestionId: route.query.bookCatalogQuestionId,
      })
      setTimeout(async () => {
        loading = false
        outLoading = false
        $g.msg("保存成功")
        if (type == "out") {
          router.go(-1)
        } else {
          await getDetail()
        }
      }, 500)
    }
  } catch (err) {
    console.log(err)
    loading = false
    outLoading = false
  }
}
/* 保存 */
async function confirm(type, next?) {
  try {
    if (!mainQuestion.questionFiles.every((v) => v.status == "finished")) {
      $g.msg("请等待文件上传完毕在保存", "warning")
      return
    }

    if ((route.query.questionId || mainQuestion.questionId) && !isXKWEdit) {
      await edit(type)
    } else {
      await save(type, next)
    }
  } catch (err) {
    console.log(err)
  }
}
/* 重置试题 */
function reset() {
  subQuestionRef?.initCurrent()
  mainQuestion = {
    sysQuestionTypeId: questionType[0].value,
    questionTitle: "",
    questionFiles: [],
    sysCourseLiteracyIdList: [], //学科素养
    sysCourseAbilityIdList: [], //学科能力
    sysCourseKnowAbilityIdList: [], //知识能力
  }
  subQuestion = [
    {
      subQuestionTitle: "", // 问题描述
      subQuestionType: subQuestionType[0].value,
      structureNumber: "(1)",
      score: 1, // 小题分数
      subQuestionAnswer: "", // 答案
      subQuestionParse: "", // 详细解析
      subQuestionParseList: [{ content: "" }], //解析列表
      knowledgePoints: [], // 知识点
      // 下面属于适配数据
      knowledge: [],
      optionsAnswer: [],
      options: $g._.cloneDeep(chooseData.chooseOption.slice(0, 4)),
    },
  ]
}
provide("importQuestion", importQuestion)
/* 导入试题 */
function importQuestion(res) {
  if (route.query.questionId) return $g.msg("编辑时不支持导入试题", "warning")
  mainQuestion.questionTitle = res.questionTitle
  mainQuestion.sysQuestionTypeId = res.sysQuestionTypeId ?? manualSwitchTypeId
  res.subQuestions.forEach((v, i) => {
    v.structureNumber = v.structureNumber ?? `(${i + 1})`
    v.score = v.score ?? 1
    v.knowledgePoints = v.knowledgePoints ?? []
    if ([1, 2, 3].includes(v.subQuestionType)) {
      v.optionsAnswer = v.subQuestionAnswer?.split("") ?? []
      v.options = v.optionArr
    }
  })
  subQuestion = res.subQuestions
  showAttachDialog = false
}

let isXKWEdit = $ref(false) //学科网搜索--编辑此题

/* 绑定试题 */
async function bindQuestion(res, type) {
  if (type == "import") {
    $g.msg("导入成功")
    resourceMgt.setQuestionId(res.questionId)
    router.back()
    return
  }

  resourceMgt.setQuestionId("")
  resourceMgt.setQuestion({})
  isXKWEdit = true
  mainQuestion.questionTitle = res.questionTitle
  mainQuestion.sysQuestionTypeId = res.sysQuestionTypeId
  const findParentId = handleFindParentId(res.sysQuestionTypeId)
  parentTypeObj = questionType?.find((v) => v.value == findParentId)
  childTypeObj = parentTypeObj?.children?.find(
    (v) => v.value == res.sysQuestionTypeId,
  )
  mainQuestion.originalQuestionId = res.questionId
  mainQuestion.sysQuestionDifficultyId = res?.sysQuestionDifficultyId
  res.subQuestions.forEach((v) => {
    if ([1, 2, 3].includes(v.subQuestionType)) {
      v.optionsAnswer = v.subQuestionAnswer?.split("") ?? []
      v.options = Object.keys(v)
        .map((vv) => {
          if (vv.indexOf("option") != -1 && !vv.includes("Answer")) {
            return {
              label: vv.replace("option", ""),
              value: v[vv],
              key: vv,
            }
          }
        })
        .filter((item) => item != null && item.value)
    }
    //删除所有大题id、子题id
    v.knowledgePoints.forEach((vv) => {
      if (vv.subQuestionId) delete vv.subQuestionId
      if (vv.questionId) delete vv.questionId
    })
    v.subQuestionParseList = v.subQuestionParseList.map((vv) => {
      return { content: vv.content }
    })
    delete v.subQuestionId
    delete v.questionId
  })
  subQuestion = res.subQuestions
  showAttachDialog = false
}
/* 获取试题详情 */
async function getDetail() {
  let res = await getQuestionDetail({
    questionId: route.query.questionId || mainQuestion.questionId,
    bookCatalogQuestionId: route.query.bookCatalogQuestionId,
  })
  mainQuestion.questionFiles = res?.questionFiles?.map((v) => {
    return {
      ...v,
      name: v.fileName,
      fullUrl: v.fileAbsoluteUrl,
      size: v.fileSize,
      time_length: v.fileDuration,
      id: v.questionFileId,
      status: "finished",
      suffix: v.fileExtension,
      thumbnailUrl: $g.tool.getFileTypeIcon(v.fileExtension),
    }
  })
  mainQuestion.questionTitle = res.questionTitle

  // 反向查找父级id
  const findParentId = handleFindParentId(res.sysQuestionTypeId)
  parentTypeObj = questionType?.find((v) => v.value == findParentId)

  childTypeObj = parentTypeObj?.children?.find(
    (v) => v.value == res.sysQuestionTypeId,
  )

  mainQuestion.questionSource = res.questionSource
  mainQuestion.sysCourseLiteracyIdList = res.sysCourseLiteracyIdList
  mainQuestion.sysCourseAbilityIdList = res.sysCourseAbilityIdList
  mainQuestion.sysCourseKnowAbilityIdList = res.sysCourseKnowAbilityIdList
  res.subQuestions.forEach((v) => {
    if ([1, 2, 3].includes(v.subQuestionType)) {
      v.optionsAnswer = v.subQuestionAnswer?.split("") ?? []
      v.options = Object.keys(v)
        .map((vv) => {
          if (vv.indexOf("option") != -1 && !vv.includes("Answer")) {
            return {
              label: vv.replace("option", ""),
              value: v[vv],
              key: vv,
            }
          }
        })
        .filter((item) => item != null && item.value)
    }
  })
  subQuestion = res.subQuestions
}
function initData() {
  let data = JSON.parse(localStorage.getItem("questionData") as any)
  mainQuestion.questionTitle = data.questionTitle
  mainQuestion.sysQuestionTypeId = data.sysQuestionTypeId
  subQuestion = data.subQuestions
}
onMounted(async () => {
  await getQuestionTypeListApi()
  await getSubQuestionTypeListApi()
  getSubjectLiteracyListApi()
  getSubjectAbilityListApi()
  getKnowledgeAbilityListApi()
  if (route.query.bookId) getQuestionAttachListApi()
  if (route.query.questionId) getDetail()
  localStorage.getItem("questionData") &&
    !route.query.questionId &&
    (await initData())
  //列表页中的弹窗进入编辑试题
  if (resourceMgt.getQuestionId && $g.tool.isTrue(resourceMgt.getQuestion)) {
    bindQuestion(resourceMgt.getQuestion, "edit")
  }
  const observer = new ResizeObserver((entries) => {
    const containerWidth = entries[0].contentRect.width
    const itemWidth = 100 // 每个元素的大致宽度（包含间距）
    // 设置最小值为1，避免除以0的情况
    itemsPerRow = Math.max(Math.floor(containerWidth / itemWidth), 1)
  })

  observer.observe(containerRef)
})

// 计算当前选中项的索引
const selectedIndex = $computed(() =>
  questionType.findIndex((q) => q.value === parentTypeObj?.value),
)

// 判断是否应该在当前位置显示 children
function shouldShowChildrenAfter(index) {
  if (selectedIndex === -1) return false
  // 确保 itemsPerRow 至少为 1
  const currentItemsPerRow = Math.max(itemsPerRow, 1)

  // 计算当前选中项所在的行
  const selectedRow = Math.floor(selectedIndex / currentItemsPerRow)

  // 计算当前索引所在的行
  const currentRow = Math.floor(index / currentItemsPerRow)

  // 如果是选中项所在行的最后一个位置，则显示子选项
  return (
    currentRow === selectedRow &&
    index ===
      Math.min(
        (currentRow + 1) * currentItemsPerRow - 1,
        questionType.length - 1,
      )
  )
}

function changeTypeBtn(item, type) {
  if (type == "parent") {
    childTypeObj = item?.children?.length ? item.children[0] : {}
    parentTypeObj = item
  } else {
    childTypeObj = item
  }
}

/**
 * 反向查找父级id
 */
function handleFindParentId(childId, items = questionType) {
  for (const item of items) {
    // 检查当前项是否就是要找的 id
    if (item.value === childId) {
      return childId // 如果是，返回 childId
    }

    // 检查 children
    if (item.children && item.children.length) {
      const hasChild = item.children.some((child) => child.value === childId)
      if (hasChild) {
        return item.value
      }

      // 递归检查更深层级
      const parentId = handleFindParentId(childId, item.children)
      if (parentId) {
        return parentId
      }
    }
  }
  return null // 未找到返回 null
}
</script>

<style lang="scss" scoped>
.main-title {
  border-left: 4px solid #67c23a;
  padding-left: 5px;
}
.sub-title {
  border-left: 4px solid #e6a23c;
  padding-left: 5px;
}

.active {
  background: #e8f5fd;
  color: #0f99eb;
  font-weight: 600;
}
.itemBox:hover {
  background: #e8f5fd;
  color: #0f99eb;
}
</style>
