<script setup lang="ts">
import {
  editQuickQuestionImage,
  deleteQuickQuestionImage,
  addQuickQuestionImage,
} from "@/api/bookMgt"
import { ElLoading } from "element-plus"
import { globalRegistry } from "@/views/resourceMgt/quickQuestion/globalComponentRegistry"
import { useQuickQuestionStore } from "@/stores/modules/quickQuestion"
let { currentQuestion, currentBox } = $(storeToRefs(useQuickQuestionStore()))

/** 题目框信息接口 */
interface IQuestionBox {
  quickQuestionImageBoxId: number
  ocrState: number
  ocrResult: string
  xkwSearchState: number
  xkwSearchResult: string
  isFinish: number
  questionId: number | null
  bookCatalogQuestionId: number | null
  subBoxList: ISubBox[]
}

/** 子题目框接口 */
interface ISubBox {
  quickQuestionImageSubBoxId: number
  questionBox: IPoint[]
  questionBoxImageUrl: string
}

/** 坐标点接口 */
interface IPoint {
  x: number
  y: number
}

/** 组件props接口 */
interface IProps {
  questionList: IQuestionBox[]
  scaleRatio: number
  imageLoaded: boolean
  imageWidth?: number
  imageHeight?: number
}

/** 锚点类型 */
type TAnchorType = "nw" | "n" | "ne" | "e" | "se" | "s" | "sw" | "w"

/** 组件事件接口 */
interface IEmits {
  /** 激活题目事件 */
  (e: "activeQuestion", questionBox: IQuestionBox, subBox: ISubBox): void
  /** 添加新切图事件 */
  (
    e: "addNewSubBox",
    boxData: { questionBoxId: number; points: IPoint[] },
  ): void
  /** 刷新数据事件 */
  (e: "refreshData"): void
}

const emit = defineEmits<IEmits>()

const props = withDefaults(defineProps<IProps>(), {
  questionList: () => [],
  scaleRatio: 1,
  imageLoaded: false,
  imageWidth: 0,
  imageHeight: 0,
})

/** 重置所有状态 */
function resetAllStates(): void {
  selectedBoxIndex = null
  isDragging = false
  isDraggingBox = false
  justFinishedDragging = false
  dragAnchorType = null
  hasUnconfirmedChanges = false
  tempBoxPositions = {}
  originalBoxPositions = {}
}

// 只监听图片加载状态或图片尺寸变化，重置所有状态
// 移除对questionList的监听，避免与父组件reset()函数冲突
watch(
  [() => props.imageLoaded, () => props.imageWidth, () => props.imageHeight],
  () => {
    resetAllStates()
  },
  { deep: true },
)

// 监听题目列表变化，当切换页码后重新激活框
watch(
  () => props.questionList,
  () => {
    nextTick(() => {
      if (currentBox?.quickQuestionImageSubBoxId) {
        activateBoxById(currentBox.quickQuestionImageSubBoxId)
      }
    })
  },
  { deep: true },
)

// 选中的方框索引
let selectedBoxIndex = $ref<number | null>(null)
// 拖拽状态
let isDragging = $ref(false)
// 拖拽的锚点类型
let dragAnchorType = $ref<TAnchorType | null>(null)
// 拖拽起始位置
let dragStartPos = $ref({ x: 0, y: 0 })
// 原始方框尺寸
let originalBoxBounds = $ref({ left: 0, top: 0, width: 0, height: 0 })
// 是否正在拖动方框
let isDraggingBox = $ref(false)
// 方框拖动起始位置
let boxDragStartPos = $ref({ x: 0, y: 0 })
// 原始方框位置
let originalBoxPos = $ref({ left: 0, top: 0 })
// 是否有未确认的更改
let hasUnconfirmedChanges = $ref(false)
// 临时存储的方框位置数据
let tempBoxPositions = $ref<Record<number, IPoint[]>>({})
// 原始方框位置数据（用于取消时恢复）
let originalBoxPositions = $ref<Record<number, IPoint[]>>({})
// 拖动完成标识（防止立即触发点击事件）
let justFinishedDragging = $ref(false)
// 是否处于添加切图模式
let isAddingMode = $ref(false)
// 添加切图时的临时数据
let newBoxStart = $ref<IPoint | null>(null)
let newBoxCurrent = $ref<IPoint | null>(null)
// 当前操作的题目框ID
let currentBoxId = $ref<number | null>(null)
// 确认按钮loading状态
let isConfirmLoading = $ref(false)

// 计算题目框样式
const questionBoxStyles = computed(() => {
  if (!props.imageLoaded || !props.questionList.length) return []

  const styles: Array<{
    left: string
    top: string
    width: string
    height: string
    borderColor: string
    backgroundColor: string
    isSelected: boolean
    boxIndex: number
    subBoxIndex: number
  }> = []

  props.questionList.forEach((item, boxIndex) => {
    item.subBoxList.forEach((subBox, subBoxIndex) => {
      // 优先使用临时位置，否则使用原始位置
      const currentIndex = styles.length
      const questionBox = tempBoxPositions[currentIndex] || subBox.questionBox

      if (questionBox && questionBox.length >= 4) {
        // 获取矩形的边界
        const xCoords = questionBox.map((point) => point.x)
        const yCoords = questionBox.map((point) => point.y)

        const minX = Math.min(...xCoords)
        const maxX = Math.max(...xCoords)
        const minY = Math.min(...yCoords)
        const maxY = Math.max(...yCoords)

        // 根据缩放比例调整坐标
        const scaledLeft = minX * props.scaleRatio
        const scaledTop = minY * props.scaleRatio
        const scaledWidth = (maxX - minX) * props.scaleRatio
        const scaledHeight = (maxY - minY) * props.scaleRatio

        // 根据完成状态设置颜色
        const isFinished = item.isFinish === 2
        const isSelected = selectedBoxIndex === currentIndex
        const borderColor = isSelected
          ? "#1EA0F0"
          : isFinished
          ? "#52c41a"
          : "#ff4d4f"
        const backgroundColor = isSelected
          ? "rgba(24, 144, 255, 0.1)"
          : isFinished
          ? "rgba(82, 196, 26, 0.1)"
          : "rgba(255, 77, 79, 0.1)"

        styles.push({
          left: `${scaledLeft}px`,
          top: `${scaledTop}px`,
          width: `${scaledWidth}px`,
          height: `${scaledHeight}px`,
          borderColor,
          backgroundColor,
          isSelected,
          boxIndex,
          subBoxIndex,
        })
      }
    })
  })

  return styles
})

// 计算锚点位置
const anchorPositions = computed(() => {
  if (selectedBoxIndex === null || !questionBoxStyles.value[selectedBoxIndex])
    return []

  const box = questionBoxStyles.value[selectedBoxIndex]
  const left = parseFloat(box.left)
  const top = parseFloat(box.top)
  const width = parseFloat(box.width)
  const height = parseFloat(box.height)

  return [
    { type: "nw", x: left, y: top },
    { type: "n", x: left + width / 2, y: top },
    { type: "ne", x: left + width, y: top },
    { type: "e", x: left + width, y: top + height / 2 },
    { type: "se", x: left + width, y: top + height },
    { type: "s", x: left + width / 2, y: top + height },
    { type: "sw", x: left, y: top + height },
    { type: "w", x: left, y: top + height / 2 },
  ] as Array<{ type: TAnchorType; x: number; y: number }>
})

// 计算确认/取消按钮位置
const actionButtonsPosition = computed(() => {
  if (selectedBoxIndex === null || !questionBoxStyles.value[selectedBoxIndex])
    return { top: "0px", right: "0px" }

  const box = questionBoxStyles.value[selectedBoxIndex]
  const left = parseFloat(box.left)
  const top = parseFloat(box.top)
  const width = parseFloat(box.width)
  const height = parseFloat(box.height)

  // 计算按钮需要的空间高度（按钮高度 + 边距）
  const buttonSpace = 30

  // 检查底部是否有足够空间
  const bottomSpace = props.imageHeight * props.scaleRatio - (top + height)
  const showAtTop = bottomSpace < buttonSpace

  return {
    // 如果底部空间不足，显示在框的上方，否则显示在下方
    top: showAtTop ? `${top - 23}px` : `${top + height + 5}px`,
    left: showAtTop ? `${left + width - 90}px` : `${left + width - 60}px`,
  }
})

// 计算顶部控制栏位置
const topControlPosition = computed(() => {
  if (selectedBoxIndex === null || !questionBoxStyles.value[selectedBoxIndex])
    return { top: "0px", left: "0px" }

  const box = questionBoxStyles.value[selectedBoxIndex]
  const left = parseFloat(box.left) - 2
  const top = parseFloat(box.top)
  const width = parseFloat(box.width) + 5

  return {
    top: `${top - 25}px`,
    left: `${left}px`,
    width: `${width}px`,
  }
})

/** 处理方框点击选中 */
function handleBoxClick(index: number): void {
  // 如果刚完成拖动，不处理点击事件
  if (justFinishedDragging) {
    return
  }

  // 如果已经选中了这个框，不需要重复处理
  if (selectedBoxIndex === index) {
    return
  }

  // 获取当前方框信息
  const boxStyle = questionBoxStyles.value[index]
  const questionBox = props.questionList[boxStyle.boxIndex]
  const subBox = questionBox.subBoxList[boxStyle.subBoxIndex]

  // 强制设置选中状态
  selectedBoxIndex = index

  // 通知父级组件激活对应的题目
  emit("activeQuestion", questionBox, subBox)

  // 更新 currentBox 为当前选中的子框
  currentBox = subBox

  // 保存当前选中框的原始位置
  if (!originalBoxPositions[index]) {
    originalBoxPositions[index] = JSON.parse(JSON.stringify(subBox.questionBox))
  }
}

/** 通过quickQuestionImageSubBoxId激活方框 */
function activateBoxById(subBoxId: number): void {
  // 遍历所有方框查找匹配的子框ID
  for (let i = 0; i < questionBoxStyles.value.length; i++) {
    const boxStyle = questionBoxStyles.value[i]
    const questionBox = props.questionList[boxStyle.boxIndex]
    const subBox = questionBox.subBoxList[boxStyle.subBoxIndex]

    // 判断是否找到匹配的子框ID
    if (subBox.quickQuestionImageSubBoxId === subBoxId) {
      // 激活该方框
      handleBoxClick(i)
      break
    }
  }
}

/** 处理方框拖动开始 */
function handleBoxMouseDown(event: MouseEvent, index: number): void {
  event.preventDefault()
  event.stopPropagation()

  if (selectedBoxIndex !== index) return

  // 如果正在拖拽锚点，则不开始方框拖动
  if (isDragging) return

  isDraggingBox = true
  boxDragStartPos = { x: event.clientX, y: event.clientY }

  const box = questionBoxStyles.value[selectedBoxIndex]
  originalBoxPos = {
    left: parseFloat(box.left),
    top: parseFloat(box.top),
  }

  // 保存原始位置（如果还没保存）
  if (!originalBoxPositions[index]) {
    const boxStyle = questionBoxStyles.value[index]
    const questionBox = props.questionList[boxStyle.boxIndex]
    const subBox = questionBox.subBoxList[boxStyle.subBoxIndex]
    originalBoxPositions[index] = JSON.parse(JSON.stringify(subBox.questionBox))
  }

  // 添加全局鼠标事件
  document.addEventListener("mousemove", handleBoxMouseMove)
  document.addEventListener("mouseup", handleBoxMouseUp)
}

/** 处理方框拖动移动 */
function handleBoxMouseMove(event: MouseEvent): void {
  if (!isDraggingBox || selectedBoxIndex === null) return

  const deltaX = event.clientX - boxDragStartPos.x
  const deltaY = event.clientY - boxDragStartPos.y

  const newLeft = originalBoxPos.left + deltaX
  const newTop = originalBoxPos.top + deltaY

  const box = questionBoxStyles.value[selectedBoxIndex]
  const width = parseFloat(box.width)
  const height = parseFloat(box.height)

  // 不再使用checkBounds返回值判断，而是直接更新位置
  // updateBoxPosition内部会应用边界限制
  updateBoxPosition(selectedBoxIndex, newLeft, newTop)
  hasUnconfirmedChanges = true
}

/** 处理方框拖动结束 */
function handleBoxMouseUp(event?: MouseEvent): void {
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  const wasDragging = isDraggingBox
  isDraggingBox = false

  // 移除全局鼠标事件
  document.removeEventListener("mousemove", handleBoxMouseMove)
  document.removeEventListener("mouseup", handleBoxMouseUp)

  // 如果刚完成拖动，设置标识防止立即触发点击事件
  if (wasDragging) {
    justFinishedDragging = true
    setTimeout(() => {
      justFinishedDragging = false
    }, 300) // 延长至300ms，确保覆盖所有延迟点击事件
  }
}

/** 处理锚点拖拽开始 */
function handleAnchorMouseDown(
  event: MouseEvent,
  anchorType: TAnchorType,
): void {
  event.preventDefault()
  event.stopPropagation()

  if (selectedBoxIndex === null) return

  // 停止方框拖动（如果正在进行）
  if (isDraggingBox) {
    handleBoxMouseUp()
  }

  isDragging = true
  dragAnchorType = anchorType
  dragStartPos = { x: event.clientX, y: event.clientY }

  const box = questionBoxStyles.value[selectedBoxIndex]
  originalBoxBounds = {
    left: parseFloat(box.left),
    top: parseFloat(box.top),
    width: parseFloat(box.width),
    height: parseFloat(box.height),
  }

  // 保存原始位置（如果还没保存）
  if (!originalBoxPositions[selectedBoxIndex]) {
    const boxStyle = questionBoxStyles.value[selectedBoxIndex]
    const questionBox = props.questionList[boxStyle.boxIndex]
    const subBox = questionBox.subBoxList[boxStyle.subBoxIndex]
    originalBoxPositions[selectedBoxIndex] = JSON.parse(
      JSON.stringify(subBox.questionBox),
    )
  }

  // 添加全局鼠标事件
  document.addEventListener("mousemove", handleMouseMove)
  document.addEventListener("mouseup", handleMouseUp)
}

/** 处理锚点拖拽移动 */
function handleMouseMove(event: MouseEvent): void {
  if (!isDragging || !dragAnchorType || selectedBoxIndex === null) return

  const deltaX = event.clientX - dragStartPos.x
  const deltaY = event.clientY - dragStartPos.y

  // 计算新的方框边界
  let newLeft = originalBoxBounds.left
  let newTop = originalBoxBounds.top
  let newWidth = originalBoxBounds.width
  let newHeight = originalBoxBounds.height

  // 根据锚点类型调整边界
  switch (dragAnchorType) {
    case "nw":
      newLeft += deltaX
      newTop += deltaY
      newWidth -= deltaX
      newHeight -= deltaY
      break
    case "n":
      newTop += deltaY
      newHeight -= deltaY
      break
    case "ne":
      newTop += deltaY
      newWidth += deltaX
      newHeight -= deltaY
      break
    case "e":
      newWidth += deltaX
      break
    case "se":
      newWidth += deltaX
      newHeight += deltaY
      break
    case "s":
      newHeight += deltaY
      break
    case "sw":
      newLeft += deltaX
      newWidth -= deltaX
      newHeight += deltaY
      break
    case "w":
      newLeft += deltaX
      newWidth -= deltaX
      break
  }

  // 保证最小尺寸但允许继续拖动
  const minSize = 20
  if (newWidth < minSize) {
    if (["nw", "w", "sw"].includes(dragAnchorType)) {
      newLeft = originalBoxBounds.left + originalBoxBounds.width - minSize
    }
    newWidth = minSize
  }
  if (newHeight < minSize) {
    if (["nw", "n", "ne"].includes(dragAnchorType)) {
      newTop = originalBoxBounds.top + originalBoxBounds.height - minSize
    }
    newHeight = minSize
  }

  // 不再使用checkBounds返回值判断，而是直接更新位置
  // updateBoxBounds内部会应用边界限制
  updateBoxBounds(selectedBoxIndex, newLeft, newTop, newWidth, newHeight)
  hasUnconfirmedChanges = true
}

/** 处理鼠标松开 */
function handleMouseUp(event?: MouseEvent): void {
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  const wasDragging = isDragging
  isDragging = false
  dragAnchorType = null

  // 移除全局鼠标事件
  document.removeEventListener("mousemove", handleMouseMove)
  document.removeEventListener("mouseup", handleMouseUp)

  // 如果刚完成拖动，设置标识防止立即触发点击事件
  if (wasDragging) {
    justFinishedDragging = true
    setTimeout(() => {
      justFinishedDragging = false
    }, 300) // 延长至300ms，确保覆盖所有延迟点击事件
  }
}

/** 更新方框边界到临时状态 */
function updateBoxBounds(
  index: number,
  left: number,
  top: number,
  width: number,
  height: number,
): void {
  // 这里需要将显示坐标转换回原始图片坐标
  const originalLeft = left / props.scaleRatio
  const originalTop = top / props.scaleRatio
  const originalWidth = width / props.scaleRatio
  const originalHeight = height / props.scaleRatio

  // 应用边界限制
  let clampedLeft = Math.max(0, originalLeft)
  let clampedTop = Math.max(0, originalTop)
  let clampedWidth = originalWidth
  let clampedHeight = originalHeight

  // 如果有图片尺寸信息，进行边界限制
  if (props.imageWidth && props.imageHeight) {
    // 限制右边界
    if (clampedLeft + clampedWidth > props.imageWidth) {
      clampedWidth = props.imageWidth - clampedLeft
    }
    // 限制下边界
    if (clampedTop + clampedHeight > props.imageHeight) {
      clampedHeight = props.imageHeight - clampedTop
    }
  }

  // 保留2位小数的坐标值
  const roundedLeft = Math.round(clampedLeft * 100) / 100
  const roundedTop = Math.round(clampedTop * 100) / 100
  const roundedRight = Math.round((clampedLeft + clampedWidth) * 100) / 100
  const roundedBottom = Math.round((clampedTop + clampedHeight) * 100) / 100

  // 更新临时位置数据
  tempBoxPositions[index] = [
    { x: roundedLeft, y: roundedTop },
    { x: roundedRight, y: roundedTop },
    { x: roundedRight, y: roundedBottom },
    { x: roundedLeft, y: roundedBottom },
  ]
}

/** 更新方框位置到临时状态 */
function updateBoxPosition(index: number, left: number, top: number): void {
  // 获取当前方框的尺寸
  const box = questionBoxStyles.value[index]
  const width = parseFloat(box.width)
  const height = parseFloat(box.height)

  // 将显示坐标转换回原始图片坐标
  const originalLeft = left / props.scaleRatio
  const originalTop = top / props.scaleRatio
  const originalWidth = width / props.scaleRatio
  const originalHeight = height / props.scaleRatio

  // 应用边界限制
  let clampedLeft = Math.max(0, originalLeft)
  let clampedTop = Math.max(0, originalTop)

  // 如果有图片尺寸信息，进行边界限制
  if (props.imageWidth && props.imageHeight) {
    // 限制右边界
    if (clampedLeft + originalWidth > props.imageWidth) {
      clampedLeft = props.imageWidth - originalWidth
    }
    // 限制下边界
    if (clampedTop + originalHeight > props.imageHeight) {
      clampedTop = props.imageHeight - originalHeight
    }
  }

  // 保留2位小数的坐标值
  const roundedLeft = Math.round(clampedLeft * 100) / 100
  const roundedTop = Math.round(clampedTop * 100) / 100
  const roundedRight = Math.round((clampedLeft + originalWidth) * 100) / 100
  const roundedBottom = Math.round((clampedTop + originalHeight) * 100) / 100

  // 更新临时位置数据
  tempBoxPositions[index] = [
    { x: roundedLeft, y: roundedTop },
    { x: roundedRight, y: roundedTop },
    { x: roundedRight, y: roundedBottom },
    { x: roundedLeft, y: roundedBottom },
  ]
}

/** 确认更改 */
function handleConfirmChanges(): void {
  if (selectedBoxIndex === null) return

  // 将临时位置数据应用到原始数据
  const boxStyle = questionBoxStyles.value[selectedBoxIndex]
  const questionBox = props.questionList[boxStyle.boxIndex]
  const subBox = questionBox.subBoxList[boxStyle.subBoxIndex]
  const boxIndex = selectedBoxIndex // 创建一个临时变量存储当前索引，避免后面使用null值

  if (tempBoxPositions[boxIndex]) {
    // 创建深拷贝，然后以响应式方式更新
    const newData = JSON.parse(JSON.stringify(tempBoxPositions[boxIndex]))

    // 设置loading状态
    isConfirmLoading = true

    // 调用API保存更改
    const params = {
      quickQuestionImageId: questionBox.quickQuestionImageBoxId,
      quickQuestionImageSubBoxId: subBox.quickQuestionImageSubBoxId,
      quickQuestionImageBoxId: questionBox.quickQuestionImageBoxId,
      posList: newData,
    }

    editQuickQuestionImage(params)
      .then((res) => {
        // 提交更改，使用索引方式更新以确保响应性
        for (let i = 0; i < newData.length; i++) {
          if (i < subBox.questionBox.length) {
            // 更新现有点
            subBox.questionBox[i].x = newData[i].x
            subBox.questionBox[i].y = newData[i].y
          } else {
            // 添加新点
            subBox.questionBox.push({ ...newData[i] })
          }
        }

        // 如果原数组更长，截断它
        if (subBox.questionBox.length > newData.length) {
          subBox.questionBox.length = newData.length
        }

        // 更新题目区域图片URL
        if (res && res.questionBoxImageUrl) {
          subBox.questionBoxImageUrl = res.questionBoxImageUrl
        }
        currentQuestion.isFinish = 1

        delete tempBoxPositions[boxIndex]
        delete originalBoxPositions[boxIndex]

        hasUnconfirmedChanges = false

        // 通知父组件刷新数据
        emit("refreshData")
      })
      .catch((error) => {
        console.error("API调用异常:", error)
      })
      .finally(() => {
        // 重置loading状态
        isConfirmLoading = false
      })
  } else {
    hasUnconfirmedChanges = false
  }
}

/** 取消更改 */
function handleCancelChanges(): void {
  if (selectedBoxIndex === null) return

  // 恢复到原始位置
  if (originalBoxPositions[selectedBoxIndex]) {
    // 获取方框引用
    const boxStyle = questionBoxStyles.value[selectedBoxIndex]
    const questionBox = props.questionList[boxStyle.boxIndex]
    const subBox = questionBox.subBoxList[boxStyle.subBoxIndex]

    // 以响应式方式恢复原始数据
    const origData = originalBoxPositions[selectedBoxIndex]

    // 清空临时位置数据
    delete tempBoxPositions[selectedBoxIndex]

    // 使用响应式方法应用原始位置
    for (let i = 0; i < origData.length; i++) {
      if (i < subBox.questionBox.length) {
        subBox.questionBox[i].x = origData[i].x
        subBox.questionBox[i].y = origData[i].y
      } else {
        subBox.questionBox.push({ ...origData[i] })
      }
    }

    // 如果原数组更长，截断它
    if (subBox.questionBox.length > origData.length) {
      subBox.questionBox.length = origData.length
    }

    // 清空原始位置数据
    delete originalBoxPositions[selectedBoxIndex]
  }

  hasUnconfirmedChanges = false
}

/** 处理容器点击取消选中 */
function handleContainerClick(event: MouseEvent): void {
  // 如果处于添加模式，不取消选中
  if (isAddingMode) {
    return
  }

  // 检查是否点击了按钮或按钮容器
  const target = event.target as HTMLElement
  if (
    target.classList.contains("confirm-btn") ||
    target.classList.contains("cancel-btn") ||
    target.classList.contains("delete-btn") ||
    target.classList.contains("add-box-btn") ||
    target.closest(".action-buttons") ||
    target.closest(".top-control-bar")
  ) {
    return
  }

  // 如果正在拖动或刚完成拖动，不处理容器点击
  if (isDragging || isDraggingBox || justFinishedDragging) {
    return
  }

  // 如果有未确认的更改，先取消更改
  if (hasUnconfirmedChanges) {
    handleCancelChanges()
  }
  selectedBoxIndex = null

  // 清空 currentBox
  currentBox = null
}

/** 检查方框是否在图片边界内 */
function checkBounds(
  left: number,
  top: number,
  width: number,
  height: number,
): boolean {
  if (!props.imageWidth || !props.imageHeight) return true

  // 将显示坐标转换为原始图片坐标
  const originalLeft = left / props.scaleRatio
  const originalTop = top / props.scaleRatio
  const originalWidth = width / props.scaleRatio
  const originalHeight = height / props.scaleRatio

  // 检查边界
  if (originalLeft < 0) return false
  if (originalTop < 0) return false
  if (originalLeft + originalWidth > props.imageWidth) return false
  if (originalTop + originalHeight > props.imageHeight) return false

  return true
}

/** 处理删除方框 */
function handleDeleteBox(): void {
  if (selectedBoxIndex === null) return

  const boxStyle = questionBoxStyles.value[selectedBoxIndex]
  const questionBox = props.questionList[boxStyle.boxIndex]
  const subBox = questionBox.subBoxList[boxStyle.subBoxIndex]
  const subBoxId = subBox.quickQuestionImageSubBoxId
  const boxIndex = boxStyle.boxIndex
  const subBoxIndex = boxStyle.subBoxIndex

  // 保存当前selectedBoxIndex的值
  const currentBoxIndex = selectedBoxIndex

  // 调用删除API
  deleteQuickQuestionImage({ quickQuestionImageSubBoxId: subBoxId })
    .then(() => {
      // 删除本地数据
      questionBox.subBoxList.splice(subBoxIndex, 1)

      // 重置状态
      selectedBoxIndex = null

      // 清空 currentBox
      currentBox = null

      hasUnconfirmedChanges = false
      currentQuestion.isFinish = 1
      // 使用保存的索引值删除临时数据
      if (currentBoxIndex !== null) {
        delete tempBoxPositions[currentBoxIndex]
        delete originalBoxPositions[currentBoxIndex]
      }

      // 通知父组件刷新数据
      emit("refreshData")
    })
    .catch((error) => {
      console.error("删除方框失败:", error)
    })
}

/** 进入添加切图模式 */
function startAddingMode(): void {
  // 如果有未确认的更改，先取消更改
  if (hasUnconfirmedChanges) {
    handleCancelChanges()
  }

  if ($g.tool.isTrue(currentQuestion)) {
    // 直接使用currentQuestion的ID
    currentBoxId = currentQuestion.quickQuestionImageBoxId

    // 进入添加模式
    isAddingMode = true

    // 重置临时数据
    newBoxStart = null
    newBoxCurrent = null

    // 取消当前选中状态
    selectedBoxIndex = null
  } else {
    // 如果没有当前题目，提示用户
    $g.msg("请先选择一个题目", "warning")
  }
}

/** 处理新框选开始 */
function handleNewBoxStart(event: MouseEvent): void {
  if (!isAddingMode) return

  // 获取相对于容器的坐标
  const container = event.currentTarget as HTMLElement
  const rect = container.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 设置起始点
  newBoxStart = { x, y }
  newBoxCurrent = { x, y }
}

/** 处理新框选移动 */
function handleNewBoxMove(event: MouseEvent): void {
  if (!isAddingMode || !newBoxStart) return

  // 获取相对于容器的坐标
  const container = event.currentTarget as HTMLElement
  const rect = container.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 更新当前点
  newBoxCurrent = { x, y }
}

/** 处理新框选结束 */
function handleNewBoxEnd(event: MouseEvent): void {
  if (!isAddingMode || !newBoxStart || !newBoxCurrent || !currentBoxId) return

  // 获取相对于容器的坐标
  const container = event.currentTarget as HTMLElement
  const rect = container.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 更新最终点
  newBoxCurrent = { x, y }

  // 计算矩形的边界
  const minX = Math.min(newBoxStart.x, newBoxCurrent.x)
  const maxX = Math.max(newBoxStart.x, newBoxCurrent.x)
  const minY = Math.min(newBoxStart.y, newBoxCurrent.y)
  const maxY = Math.max(newBoxStart.y, newBoxCurrent.y)

  // 检查最小尺寸
  const minSize = 20
  if (maxX - minX < minSize || maxY - minY < minSize) {
    console.warn("框选区域太小，请重新框选")
    isAddingMode = false
    newBoxStart = null
    newBoxCurrent = null
    return
  }

  // 将显示坐标转换回原始图片坐标
  const originalMinX = minX / props.scaleRatio
  const originalMinY = minY / props.scaleRatio
  const originalMaxX = maxX / props.scaleRatio
  const originalMaxY = maxY / props.scaleRatio

  // 保留2位小数的坐标值
  const roundedMinX = Math.round(originalMinX * 100) / 100
  const roundedMinY = Math.round(originalMinY * 100) / 100
  const roundedMaxX = Math.round(originalMaxX * 100) / 100
  const roundedMaxY = Math.round(originalMaxY * 100) / 100

  // 创建新的框选数据
  const newBoxPoints = [
    { x: roundedMinX, y: roundedMinY },
    { x: roundedMaxX, y: roundedMinY },
    { x: roundedMaxX, y: roundedMaxY },
    { x: roundedMinX, y: roundedMaxY },
  ]

  if ($g.tool.isTrue(currentQuestion)) {
    // 使用currentQuestion数据
    // 调用API添加新框
    const params = {
      quickQuestionImageId: currentQuestion.quickQuestionImageBoxId,
      quickQuestionImageBoxId: currentQuestion.quickQuestionImageBoxId,
      posList: newBoxPoints,
    }

    // 显示加载中提示
    const loadingMsg = "正在添加新切图..."
    const loadingInstance = ElLoading.service({
      text: loadingMsg,
      background: "rgba(255, 255, 255, 0.7)",
    })

    addQuickQuestionImage(params)
      .then((res) => {
        if (res) {
          // 创建新的子框对象
          const newSubBox: ISubBox = {
            quickQuestionImageSubBoxId: res.quickQuestionImageSubBoxId,
            questionBox: newBoxPoints,
            questionBoxImageUrl: res.questionBoxImageUrl || "",
          }

          // 找到当前题目对应的本地数据
          const questionBox = props.questionList.find(
            (item) =>
              item.quickQuestionImageBoxId ===
              currentQuestion.quickQuestionImageBoxId,
          )

          if (questionBox) {
            // 添加到题目框的子框列表中
            questionBox.subBoxList.push(newSubBox)

            currentBox = newSubBox
            // 更新 currentBox 为新创建的子框

            // 通知父组件刷新数据
            emit("refreshData")

            // 等待DOM更新后查找并高亮新添加的框
            nextTick(() => {
              // 遍历所有框找到新添加的那个
              for (let i = 0; i < questionBoxStyles.value.length; i++) {
                const style = questionBoxStyles.value[i]
                const boxData = props.questionList[style.boxIndex]
                const subBoxData = boxData.subBoxList[style.subBoxIndex]

                // 通过ID匹配找到新添加的框
                if (
                  subBoxData.quickQuestionImageSubBoxId ===
                  res.quickQuestionImageSubBoxId
                ) {
                  // 高亮显示该框
                  handleBoxClick(i)
                  break
                }
              }
            })
          }
        }
      })
      .catch((error) => {
        console.error("添加新切图失败:", error)
      })
      .finally(() => {
        // 关闭加载提示
        loadingInstance.close()
      })
  }

  // 退出添加模式
  isAddingMode = false
  newBoxStart = null
  newBoxCurrent = null
}

/** 获取新框选的样式 */
const newBoxStyle = computed(() => {
  if (!isAddingMode || !newBoxStart || !newBoxCurrent) return {}

  const minX = Math.min(newBoxStart.x, newBoxCurrent.x)
  const maxX = Math.max(newBoxStart.x, newBoxCurrent.x)
  const minY = Math.min(newBoxStart.y, newBoxCurrent.y)
  const maxY = Math.max(newBoxStart.y, newBoxCurrent.y)

  return {
    left: `${minX}px`,
    top: `${minY}px`,
    width: `${maxX - minX}px`,
    height: `${maxY - minY}px`,
  }
})

// 在组件挂载时注册方法
onMounted(() => {
  globalRegistry.register("resizeDivBox", {
    startAddingMode,
    handleBoxClick,
    activateBoxById,
    clearSelection: () => {
      // 清空选中状态
      selectedBoxIndex = null
    },
  })

  // 组件挂载后，如果currentBox已经有值，则在下一帧激活对应的框
  nextTick(() => {
    if (currentBox?.quickQuestionImageSubBoxId) {
      activateBoxById(currentBox.quickQuestionImageSubBoxId)
    }
  })
})
// 在组件卸载时清理
onUnmounted(() => {
  globalRegistry.unregister("resizeDivBox")
})
</script>

<template>
  <!-- 题目方框覆盖层 -->
  <div
    v-if="imageLoaded"
    class="question-boxes-overlay absolute top-0 left-0"
    :style="{
      width: `${imageWidth * scaleRatio}px`,
      height: `${imageHeight * scaleRatio}px`,
    }"
    @click.capture="handleContainerClick"
    @mousedown="isAddingMode && handleNewBoxStart($event)"
    @mousemove="isAddingMode && handleNewBoxMove($event)"
    @mouseup="isAddingMode && handleNewBoxEnd($event)"
    :class="{ 'cursor-crosshair': isAddingMode }"
  >
    <!-- 添加切图按钮 -->
    <!-- <div
      v-if="$g.tool.isTrue(currentQuestion)"
      class="add-box-btn-wrapper absolute top-10px right-10px z-50"
      @click.stop.prevent
    >
      <button
        class="add-box-btn h-24px bg-[#1EA0F0] text-[#fff] border-none rounded-2px cursor-pointer flex items-center justify-center transition-colors px-8px text-12px"
        title="添加切图"
        @click.stop.prevent="startAddingMode"
      >
        添加切图
      </button>
    </div> -->

    <!-- 新框选区域 -->
    <div
      v-if="isAddingMode && newBoxStart && newBoxCurrent"
      class="new-box-selection absolute border-2 border-[#1EA0F0] bg-[rgba(24,144,255,0.1)]"
      :style="newBoxStyle"
    ></div>

    <!-- 题目方框 -->
    <div
      v-for="(boxStyle, index) in questionBoxStyles"
      :key="index"
      class="question-box absolute"
      :class="{
        selected: boxStyle.isSelected,
        'cursor-move': boxStyle.isSelected && !isAddingMode,
        'cursor-pointer': !boxStyle.isSelected && !isAddingMode,
        'cursor-crosshair': isAddingMode,
        'pointer-events-none': isAddingMode,
      }"
      :style="{
        left: boxStyle.left,
        top: boxStyle.top,
        width: boxStyle.width,
        height: boxStyle.height,
        backgroundColor: boxStyle.backgroundColor,
        border: boxStyle.isSelected ? '2px solid #1EA0F0' : 'none',
        zIndex: boxStyle.isSelected ? 10 : 1,
      }"
      @click.stop.prevent="!isAddingMode && handleBoxClick(index)"
      @mousedown="!isAddingMode && handleBoxMouseDown($event, index)"
    />

    <!-- 题目方框顶部控制栏 -->
    <div
      v-if="selectedBoxIndex !== null && questionBoxStyles[selectedBoxIndex]"
      class="top-control-bar absolute z-10 bg-[rgba(194,234,251,0.7)] text-error h-24px flex items-center justify-between px-4px text-12px"
      :style="topControlPosition"
      @click.stop.prevent
    >
      <div class="flex items-center gap-4px">
        <span class="font-bold"
          >序号:{{
            questionBoxStyles[selectedBoxIndex]
              ? questionBoxStyles[selectedBoxIndex].boxIndex + 1
              : ""
          }}</span
        >
        <span class="opacity-80">
          ID:
          {{
            questionBoxStyles[selectedBoxIndex]
              ? props.questionList[questionBoxStyles[selectedBoxIndex].boxIndex]
                  .subBoxList[questionBoxStyles[selectedBoxIndex].subBoxIndex]
                  .quickQuestionImageSubBoxId
              : ""
          }}
        </span>
      </div>
      <button
        class="delete-btn h-20px bg-[#ff4d4f] text-[#fff] border-none rounded-2px cursor-pointer flex items-center justify-center transition-colors text-10px px-4px"
        title="删除方框"
        @click.stop.prevent="handleDeleteBox"
      >
        删除
      </button>
    </div>

    <!-- 选中方框的锚点 -->
    <template v-if="selectedBoxIndex !== null">
      <div
        v-for="anchor in anchorPositions"
        :key="anchor.type"
        class="anchor-point absolute bg-[#1EA0F0] border-2 border-[#fff] rounded-[50%] cursor-pointer"
        :class="`cursor-${anchor.type}-resize`"
        :style="{
          left: `${anchor.x - 4}px`,
          top: `${anchor.y - 4}px`,
        }"
        @mousedown="handleAnchorMouseDown($event, anchor.type)"
      />
    </template>

    <!-- 确认/取消按钮 -->
    <div
      v-if="selectedBoxIndex !== null && hasUnconfirmedChanges"
      class="action-buttons absolute z-20 flex gap-2px"
      :style="actionButtonsPosition"
      @click.stop.prevent
    >
      <!-- 取消按钮 -->
      <button
        class="cancel-btn h-20px bg-info text-[#fff] border-none rounded-2px cursor-pointer flex items-center justify-center transition-colors text-10px px-4px"
        title="取消更改"
        @click.stop.prevent="handleCancelChanges"
      >
        取消
      </button>
      <!-- 确认按钮 -->
      <button
        class="confirm-btn w-28px h-20px bg-primary text-[#fff] border-none rounded-2px cursor-pointer flex items-center justify-center transition-colors text-10px px-4px"
        title="确认更改"
        @click.stop.prevent="handleConfirmChanges"
        :disabled="isConfirmLoading"
      >
        <div v-if="isConfirmLoading" class="icon-spin">
          <g-icon name="ri-loader-2-line" size="12" color="" />
        </div>
        <span v-else>确认</span>
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.question-boxes-overlay {
  pointer-events: auto;

  .question-box {
    border-radius: 2px;

    &:hover {
      border-width: 3px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.selected {
      border-width: 3px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }

  .anchor-point {
    width: 8px;
    height: 8px;
    z-index: 10;

    &:hover {
      transform: scale(1.2);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
  }

  .action-buttons {
    .confirm-btn,
    .cancel-btn {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:active {
        transform: translateY(1px);
      }
    }
  }

  .add-box-btn-wrapper {
    .add-box-btn {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        background-color: #0c8de0;
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }

  .new-box-selection {
    z-index: 8;
    pointer-events: none;
  }
}

// 锚点光标样式
.cursor-nw-resize {
  cursor: nw-resize;
}

.cursor-n-resize {
  cursor: n-resize;
}

.cursor-ne-resize {
  cursor: ne-resize;
}

.cursor-e-resize {
  cursor: e-resize;
}

.cursor-se-resize {
  cursor: se-resize;
}

.cursor-s-resize {
  cursor: s-resize;
}

.cursor-sw-resize {
  cursor: sw-resize;
}

.cursor-w-resize {
  cursor: w-resize;
}

.cursor-crosshair {
  cursor: crosshair;
}

.icon-spin {
  animation: spin 1s linear infinite;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transform-origin: center center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
