<script setup lang="ts">
import ResizeDivBox from "./ResizeDivBox.vue"

/** 试卷信息接口 */
interface IExamPaperInfo {
  quickQuestionId: number
  quickQuestionImageId: number
  imageUrl: string
  list: IQuestionBox[]
}

/** 题目框信息接口 */
interface IQuestionBox {
  quickQuestionImageBoxId: number
  ocrState: number
  ocrResult: string
  xkwSearchState: number
  xkwSearchResult: string
  isFinish: number
  questionId: number | null
  bookCatalogQuestionId: number | null
  subBoxList: ISubBox[]
}

/** 子题目框接口 */
interface ISubBox {
  quickQuestionImageSubBoxId: number
  questionBox: IPoint[]
  questionBoxImageUrl: string
}

/** 坐标点接口 */
interface IPoint {
  x: number
  y: number
}

const props = defineProps<{
  info: IExamPaperInfo
}>()

/** 组件事件接口 */
interface IEmits {
  /** 激活题目事件，传递题目ID到父级组件 */
  (e: "activeQuestion", questionBox: IQuestionBox, subBox: ISubBox): void
  /** 刷新数据事件 */
  (e: "refreshData"): void
}

const emit = defineEmits<IEmits>()

// 容器引用
let container = $ref<HTMLDivElement>()
// 图片元素引用
let imageElement = $ref<HTMLImageElement>()
// 容器尺寸
let containerWidth = $ref(0)
let containerHeight = $ref(0)
// 缩放比例
let scaleRatio = $ref(1)
// 原始图片尺寸
let originalImageWidth = $ref(0)
let originalImageHeight = $ref(0)
// 图片加载状态
let imageLoaded = $ref(false)
// loading状态
let isLoading = $ref(false)

/** 获取容器尺寸 */
function getContainerSize(): void {
  if (!container) return

  containerWidth = container.clientWidth
  containerHeight = container.clientHeight
}

/** 处理图片加载完成 */
function handleImageLoad(): void {
  if (!imageElement) return

  try {
    // 获取原始图片尺寸
    originalImageWidth = imageElement.naturalWidth
    originalImageHeight = imageElement.naturalHeight

    // 计算缩放比例，保证图片完全显示在容器内
    const widthRatio = containerWidth / originalImageWidth
    const heightRatio = containerHeight / originalImageHeight

    // 选择较小的比例，确保图片完整显示
    scaleRatio = Math.min(widthRatio, heightRatio)

    // 计算缩放后的图片尺寸
    const scaledWidth = originalImageWidth * scaleRatio
    const scaledHeight = originalImageHeight * scaleRatio

    // 设置图片显示尺寸
    imageElement.style.width = `${scaledWidth}px`
    imageElement.style.height = `${scaledHeight}px`

    // 延迟执行，确保图片实际渲染完毕后再关闭loading
    nextTick(() => {
      // 标记图片已加载
      imageLoaded = true
      isLoading = false
    })
  } catch (error) {
    console.error("处理图片加载失败:", error)
    isLoading = false
  }
}

/** 处理图片加载错误 */
function handleImageError(): void {
  console.error("图片加载失败:", props.info?.imageUrl)
  imageLoaded = false
  isLoading = false
}

/** 处理子组件传来的激活题目事件 */
function handleActiveQuestion(
  questionBox: IQuestionBox,
  subBox: ISubBox,
): void {
  // 向父级组件传递事件，用于题目列表高亮
  emit("activeQuestion", questionBox, subBox)
}

/** 处理子组件传来的刷新数据事件 */
function handleRefreshData(): void {
  // 向父级组件传递事件，用于刷新数据
  emit("refreshData")
}

/** 处理窗口大小变化（防抖处理） */
const handleResize = $g._.debounce(() => {
  getContainerSize()

  // 如果图片已加载，重新计算缩放
  if (imageLoaded && imageElement) {
    handleImageLoad()
  }
}, 200)

// 监听props变化重新渲染
watch(
  () => props.info?.imageUrl,
  () => {
    if (props.info?.imageUrl) {
      isLoading = true
      imageLoaded = false
      scaleRatio = 1
      getContainerSize()
    }
  },
  { immediate: true },
)

// 组件挂载后初始化
onMounted(() => {
  nextTick(() => {
    getContainerSize()
  })

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize)
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener("resize", handleResize)
})

const changeLoading = (loading: boolean) => {
  isLoading = loading
}

defineExpose({
  changeLoading,
})
</script>

<template>
  <div
    ref="container"
    class="exam-paper-container select-none w-full h-full bg-[#f5f5f5] flex items-center justify-center"
  >
    <!-- 图片加载中状态 -->
    <g-loading class="h-200px" v-if="isLoading"></g-loading>

    <!-- 图片和矩形框容器 -->
    <div class="image-wrapper relative" v-show="info?.imageUrl && !isLoading">
      <!-- 试卷图片 -->
      <img
        ref="imageElement"
        :src="info.imageUrl"
        alt="试卷图片"
        class="exam-paper-image"
        @load="handleImageLoad"
        @error="handleImageError"
      />

      <!-- 题目方框组件 -->
      <ResizeDivBox
        v-if="imageLoaded"
        :question-list="info.list || []"
        :scale-ratio="scaleRatio"
        :image-loaded="imageLoaded"
        :image-width="originalImageWidth"
        :image-height="originalImageHeight"
        @active-question="handleActiveQuestion"
        @refresh-data="handleRefreshData"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.exam-paper-container {
  position: relative;

  .image-wrapper {
    display: inline-block;
    position: relative;
  }

  .exam-paper-image {
    max-width: 100%;
    max-height: 100%;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: block;
  }
}
</style>
