class GlobalComponentRegistry {
  private instances = new Map<string, any>()

  register(componentKey: string, methods: Record<string, Function>) {
    this.instances.set(componentKey, methods)
    console.log(`组件 ${componentKey} 已注册`)
  }

  call(componentKey: string, methodName: string, ...args: any[]) {
    const componentMethods = this.instances.get(componentKey)
    if (componentMethods && componentMethods[methodName]) {
      console.log(`调用 ${componentKey} 的 ${methodName} 方法`)
      return componentMethods[methodName](...args)
    } else {
      console.warn(`找不到组件 ${componentKey} 的方法 ${methodName}`)
    }
  }

  unregister(componentKey: string) {
    this.instances.delete(componentKey)
    console.log(`组件 ${componentKey} 已注销`)
  }
}

// 导出全局实例
export const globalRegistry = new GlobalComponentRegistry()
