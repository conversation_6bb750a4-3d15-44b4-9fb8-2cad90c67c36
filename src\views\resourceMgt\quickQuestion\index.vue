<template>
  <div class="flex h-screen overflow-hidden">
    <div class="border w-1/2 h-[100vh]">
      <div class="border h-50px items-center justify-between px-3">
        <div class="flex items-center">
          <span
            :title="(route.query.bookName as string)"
            class="text-18px font-bold mr-20px line-1"
            >{{ route.query.bookName }}</span
          >
          <el-tree-select
            :teleported="false"
            @visible-change="handleVisibleChange"
            v-model="currentDirId"
            :data="catalogTreeData"
            :render-after-expand="false"
            check-strictly
            default-expand-all
            style="width: 280px"
            placeholder="请选择目录"
            :props="{
              label: 'bookCatalogName',
              value: 'bookCatalogId',
              children: 'children',
            }"
            :indent="8"
          >
            <template #default="{ data }">
              <div
                class="flex items-center !text-[12px] gap-10px w-[calc(100%)] overflow-hidden justify-between"
              >
                <g-mathjax :text="data.bookCatalogName"></g-mathjax>
              </div>
            </template>
          </el-tree-select>
        </div>

        <!-- 资源选择下拉菜单 - 只有多个资源时才显示 -->
        <el-dropdown @command="handleResourceSelect" trigger="click">
          <div class="flex items-center">
            <span
              class="el-dropdown-link cursor-pointer text-14px text-[#1890ff] hover:text-[#40a9ff]"
            >
              {{ currentResource?.fileName || "选择资源" }}
              <g-icon class="relative -top-2px" name="ri-arrow-down-s-line" />
            </span>
          </div>

          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="resource in bookFileList"
                :key="resource.quickQuestionId"
                :command="resource"
                :class="{
                  'bg-[#f0f9ff]':
                    currentResource?.quickQuestionId ===
                    resource.quickQuestionId,
                }"
              >
                {{ resource.fileName }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="flex">
        <div class="border h-[calc(100vh-50px)] w-80px flex flex-col">
          <div class="flex items-center justify-center h-30px relative top-4px">
            页码
          </div>
          <!-- 页码控制区域 -->
          <div
            class="page-controls flex-1 custom-scrollbar overflow-y-auto overflow-x-hidden p-2"
          >
            <!-- 页码列表 -->
            <div class="page-list space-y-2 flex flex-col items-center">
              <div
                v-for="(pageData, index) in pageDataList"
                :key="pageData.quickQuestionImageId || index"
                :class="[
                  'page-item w-40px h-40px border cursor-pointer flex items-center justify-center text-12px font-medium transition-all duration-200 relative',
                  currentPage?.quickQuestionImageId ===
                  pageData.quickQuestionImageId
                    ? 'bg-[#1890ff] text-white border-[#1890ff]'
                    : 'bg-white border-[#ddd] hover:bg-[#f5f5f5] hover:border-[#1890ff] text-[#333]',
                ]"
                @click="handlePageClick(pageData)"
              >
                <!-- 未完成数量角标 -->
                <div
                  v-if="getUnfinishedCount(pageData) > 0"
                  class="absolute -top-6px -right-6px bg-[#ff4d4f] text-white text-10px rounded-full w-16px h-16px flex items-center justify-center font-bold"
                >
                  {{ getUnfinishedCount(pageData) }}
                </div>
                <!-- 全部完成标识 -->
                <div
                  v-else-if="isPageFullyCompleted(pageData)"
                  class="absolute -top-6px -right-6px bg-[#00ce9b] text-white text-10px rounded-full w-16px h-16px flex items-center justify-center font-bold"
                >
                  <g-icon name="ri-check-line" size="12" color="" />
                </div>
                {{ pageData.page }}
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-center w-full relative">
          <ExamPaperNew
            ref="examPaperRef"
            :info="examPaperData"
            @active-question="handleActiveQuestion"
            @refresh-data="handleRefreshData"
          ></ExamPaperNew>
        </div>
      </div>
    </div>

    <div class="border w-1/2">
      <!-- 题号导航栏 -->
      <div
        class="border h-50px flex items-center bg-[#f8f8f8] px-3 overflow-hidden"
      >
        <span class="pr-10px">序号</span>
        <draggable
          v-model="examPaperData.list"
          item-key="id"
          class="flex-1 flex items-center space-x-2 overflow-x-auto whitespace-nowrap py-2 scrollbar-thin"
          direction="horizontal"
          :animation="200"
          ghost-class="ghost-question"
          chosen-class="chosen-question"
          @end="handleDragEnd"
        >
          <template #item="{ element: item, index }">
            <div
              :data-question-id="item"
              :class="[
                'question-item-sm px-3 h-30px border-2 flex items-center justify-center cursor-pointer rounded-sm transition-all duration-200 text-12px relative',
                getCurrentQuestionClass(item.quickQuestionImageBoxId),
              ]"
              @click="handleQuestionClick(item)"
            >
              {{ index + 1 }}
              <span
                class="inline-block w-6px h-6px rounded-full absolute -top-1px -right-1px"
                :class="item.isFinish == 2 ? 'bg-[#00ce9b]' : 'bg-[red]'"
              ></span>
            </div>
          </template>
        </draggable>
        <div
          class="w-30px h-30px -mt-6px ml-10px bg-[#00ce9b] text-center text-white rounded-sm cursor-pointer"
          @click="addSubQuestionArea"
        >
          <g-icon name="ri-add-line" size="16" />
        </div>
      </div>
      <!-- 录题页面内容区域 -->
      <div
        class="border h-[calc(100vh-50px)] p-3 overflow-y-auto custom-scrollbar"
        v-loading="examLoading"
      >
        <PaperEdit
          v-if="examPaperData.list?.length"
          ref="paperEdit"
          :imgBoxId="currentQuestion?.quickQuestionImageBoxId"
          :bookCatalogId="currentDirId"
          @changeCurrentQuestion="changeCurrentQuestion"
          @changeNext="saveEnterNextQuestion"
        />
        <g-empty
          v-if="!examPaperData.list?.length && !examLoading"
          class="h-full flex items-center justify-center"
        ></g-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getBookFileList,
  getBookCatalogTree,
  getResourcePageList,
  getQuestionImageDetails,
  addQuickQuestionArea,
  updateQuestionBoxSort,
} from "@/api/bookMgt"
import draggable from "vuedraggable"
import ExamPaperNew from "./components/ExamPaperNew/index.vue"
import { useQuickQuestionStore } from "@/stores/modules/quickQuestion"
import PaperEdit from "./paperEdit/index.vue"
import { useDebounceFn } from "@vueuse/core"

const quickQuestionStore = useQuickQuestionStore()
let { currentDirId, currentQuestion } = $(storeToRefs(quickQuestionStore))

// 资源文件类型定义
interface IBookFile {
  quickQuestionId: number
  bookAttachId: number
  splitImageState: number
  fileName: string
}

// 资源页面数据类型定义
interface IResourcePageItem {
  quickQuestionId?: number
  quickQuestionImageId?: number
  page?: number
  splitQuestionBoxState?: number
  isFinish?: number
  questionCount?: number
  finishQuestionCount?: number
}

/** 题目框信息接口 */
interface IQuestionBox {
  quickQuestionImageBoxId: number
  ocrState: number
  ocrResult: string
  xkwSearchState: number
  xkwSearchResult: string
  isFinish: number
  questionId: number | null
  bookCatalogQuestionId: number | null
  subBoxList: ISubBox[]
}

/** 子题目框接口 */
interface ISubBox {
  quickQuestionImageSubBoxId: number
  questionBox: IPoint[]
  questionBoxImageUrl: string
}

/** 坐标点接口 */
interface IPoint {
  x: number
  y: number
}

let paperEdit = $ref<any>()

// 当前选中页码数据
let currentPage = $ref<IResourcePageItem | null>(null)
const route = useRoute()
const router = useRouter()
// 页码数据列表
let pageDataList = $ref<IResourcePageItem[]>([])

let bookFileList = $ref<IBookFile[]>([])

// 当前选中的资源
let currentResource = $ref<IBookFile | null>(null)

// 题号列表 - 根据当前页面题目动态生成
let questionList = $ref<number[]>([])

let examLoading = $ref(true)
let examPaperData = $ref<any>({
  list: [],
})
// ExamPaperNew组件引用
let examPaperRef = $ref<any>(null)

/** 防抖包装的获取题目图片详情函数 */
const debouncedGetQuestionImageDetails = useDebounceFn(() => {
  getQuestionImageDetailsApi()
}, 300)

/** 处理页码点击 */
function handlePageClick(pageData: IResourcePageItem): void {
  currentPage = pageData
  // 立即设置loading状态为true
  if (examPaperRef) {
    examPaperRef.changeLoading(true)
  }
  // 切换页码时获取对应的题目图片详情，使用防抖函数防止快速点击导致数据不一致
  debouncedGetQuestionImageDetails()
}

/** 处理题目点击 */
function handleQuestionClick(item: any, needRefresh = true): void {
  const previousQuestion = currentQuestion
  currentQuestion = item

  if (needRefresh) {
    // 检查是否从有子图的题目切换到无子图的题目
    if (previousQuestion?.subBoxList?.length && !item.subBoxList?.length) {
      paperEdit?.reset()
      // 重置左侧图像的选择状态
      examPaperRef?.clearSelections?.()
    }
    paperEdit?.updateQuestionData()
  } else if (!item.questionId) {
    paperEdit?.reset()
  }

  // 自动滚动到选中的题号，让其居中显示
  nextTick(() => {
    const questionElement = document.querySelector(
      `[data-question-id="${item}"]`,
    )
    if (questionElement) {
      questionElement.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
        inline: "center",
      })
    }
  })
}

onMounted(async () => {
  getBookCatalogTreeApi()
  await getBookFileListApi()
  await getResourcePageListApi()
})

/* 获取资源列表 */
async function getBookFileListApi() {
  await getBookFileList({
    bookId: route.query.bookId,
  }).then((res) => {
    bookFileList = res || []
    // 默认选中第一个资源
    if (bookFileList.length > 0) {
      currentResource = bookFileList[0]
    }
  })
}

let catalogTreeData = $ref([])
/* 获取目录树 */
function getBookCatalogTreeApi() {
  getBookCatalogTree({
    bookId: route.query.bookId,
  }).then((res) => {
    catalogTreeData = res.catalogList || []
    nextTick(() => {
      $g.tool.renderMathjax("el-select__wrapper")
    })
    // 默认选中路由的dirId参数
    if (route.query.dirId) {
      currentDirId = Number(route.query.dirId)
    }
  })
}

/** 获取资源页面列表 */
async function getResourcePageListApi(onlyUpdateData = false) {
  if (!currentResource) return
  try {
    const res = await getResourcePageList({
      quickQuestionId: currentResource.quickQuestionId,
    })
    pageDataList = res || []

    // 只更新数据时不执行选中和获取详情操作
    if (!onlyUpdateData) {
      if (pageDataList.length > 0) {
        // 查找第一个未完成的页码
        const firstUnfinishedPage = pageDataList.find(
          (page: any) => !isPageFullyCompleted(page) && page?.questionCount > 0,
        )
        // 如果找到了未完成的页码，则选中它，否则选中第一个页码
        currentPage = firstUnfinishedPage || pageDataList[0]
      }
    }
    getQuestionImageDetailsApi()
  } catch (error) {
    console.error("获取页面数据失败：", error)
  }
}

/** 获取题目样式 */
function getCurrentQuestionClass(id: number): string {
  if (id === currentQuestion.quickQuestionImageBoxId) {
    return "question-active"
  }
  return "question-default"
}

/** 拖拽结束处理 */
function handleDragEnd(): void {
  updateQuestionBoxSort({
    quickQuestionImageId: examPaperData.quickQuestionImageId,
    quickQuestionImageBoxIdList: examPaperData.list?.map(
      (v) => v.quickQuestionImageBoxId,
    ),
  })
    .then((res) => {
      getResourcePageListApi(true)
    })
    .catch((err) => {})
}

/** 处理资源选择 */
async function handleResourceSelect(resource: IBookFile): Promise<void> {
  currentResource = resource
  console.log("选中资源：", resource)
  // 切换资源时重新获取页面数据
  await getResourcePageListApi()
}

/** 获取题目图片详情 */
function getQuestionImageDetailsApi() {
  if (!currentPage?.quickQuestionImageId) return

  getQuestionImageDetails({
    quickQuestionImageId: currentPage.quickQuestionImageId,
  })
    .then((res) => {
      examPaperData = res

      if (!res || !res.list?.length) {
        currentQuestion = null
      } else {
        // 检查当前选中的题目是否在新加载的列表中
        const currentIndex = res.list.findIndex(
          (v) =>
            v.quickQuestionImageBoxId ===
            currentQuestion?.quickQuestionImageBoxId,
        )

        if (currentIndex !== -1) {
          // 如果当前题目仍在列表中（例如：刷新数据），则保持选中
          currentQuestion = res.list[currentIndex]
        } else {
          // 如果是新页面或当前题目已不存在，则定位到第一个未完成的题目
          const firstUnfinished = res.list.find((q) => q.isFinish !== 2)
          // 如果都完成了，就定位到第一个
          currentQuestion = firstUnfinished || res.list[0]
        }
      }

      nextTick(() => {
        paperEdit?.updateQuestionData()
      })
      examLoading = false
    })
    .catch(() => {
      examLoading = false
    })
}

/**
 * 1、保存数据时(saveData)，更新完成状态和填入questionId
 * 2、删除右边的切图时(delImg)，手动更新subBoxList
 * 3、删除本题(refreshDetail)，更新当前quickQuestionImageId下的信息和页码
 */
function changeCurrentQuestion(type = "saveData", data?) {
  getResourcePageListApi(true)
  if (type == "saveData") {
    saveEnterNextQuestion()
  }
}

/**
 * 保存进入下一题
 */
async function saveEnterNextQuestion() {
  const index = examPaperData.list.findIndex(
    (v) => v.quickQuestionImageBoxId == currentQuestion.quickQuestionImageBoxId,
  )
  const allComplete = examPaperData.list.every((v) => v.isFinish == 2)
  if (allComplete) {
    $g.msg("当前页码已全部完成", "info")
    return
  }
  if (index != examPaperData.list?.length - 1) {
    const item = examPaperData.list[index + 1]
    await nextTick()
    handleQuestionClick(item, false)
    if (!item.questionId) {
      paperEdit.reset()
    }
  }
}

/** 处理来自试卷组件的激活题目事件 */
function handleActiveQuestion(
  questionBox: IQuestionBox,
  subBox: ISubBox,
): void {
  // 防止循环调用
  if (
    currentQuestion?.quickQuestionImageBoxId ===
    questionBox.quickQuestionImageBoxId
  ) {
    return
  }

  // 在题目列表中找到对应的题目并激活
  const targetQuestion = examPaperData.list.find(
    (q: any) =>
      q.quickQuestionImageBoxId === questionBox.quickQuestionImageBoxId,
  )

  if (targetQuestion) {
    currentQuestion = targetQuestion
    // 更新右侧录题页面
    paperEdit?.updateQuestionData()

    // 自动滚动到选中的题号，让其居中显示
    nextTick(() => {
      const questionElement = document.querySelector(
        `[data-question-id="${targetQuestion}"]`,
      )
      if (questionElement) {
        questionElement.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: "center",
        })
      }
    })
  }
}

/**
 * 添加子题目序号
 */
function addSubQuestionArea() {
  addQuickQuestionArea({
    quickQuestionImageId: currentPage?.quickQuestionImageId,
  }).then(() => {
    getResourcePageListApi(true)
  })
}

/** 获取未完成数量 */
function getUnfinishedCount(pageData: IResourcePageItem): number {
  const total = pageData.questionCount || 0
  const finished = pageData.finishQuestionCount || 0
  return total - finished
}

/** 判断页面是否完全完成 */
function isPageFullyCompleted(pageData: IResourcePageItem): boolean {
  return (
    !!pageData.questionCount &&
    pageData.questionCount === pageData.finishQuestionCount
  )
}

/** 处理刷新数据事件 */
function handleRefreshData(): void {
  // 仅更新数据，不改变当前选中状态
  getResourcePageListApi(true)
}

function handleVisibleChange(visible: boolean) {
  if (visible) {
    $g.tool.renderMathjax()
  } else {
    $g.tool.renderMathjax("el-select__wrapper")
  }
}
</script>

<style scoped lang="scss">
/* 题号默认状态 */
.question-default {
  background-color: white;
  border-color: #d9d9d9;
  color: #333333;

  &:hover {
    background-color: #f0f0f0;
    border-color: #999999;
  }
}

/* 题号选中状态 */
.question-active {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
  font-weight: bold;
  user-select: none;

  &:hover {
    background-color: #1890ff;
    border-color: #1890ff;
  }
}

/* 纤细滚动条样式 (仅 Webkit 浏览器) */
.scrollbar-thin::-webkit-scrollbar {
  height: 6px; /* 设置滚动条高度 */
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道背景色 */
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #c1c1c1; /* 滚动条滑块颜色 */
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1; /* 滚动条滑块悬浮颜色 */
}

/* 拖拽相关样式 */
.ghost-question {
  opacity: 0.5;
  background-color: #f0f0f0 !important;
  border-color: #ccc !important;
}

.chosen-question {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

:deep(.el-tooltip__trigger:focus-visible) {
  outline: unset;
}

:deep() {
  .el-tree-node__content {
    height: auto !important;
  }
  .el-tree-select__popper .el-select-dropdown__item {
    height: auto !important;
    .g-mathjax {
      white-space: normal !important;
    }
  }
}
</style>
