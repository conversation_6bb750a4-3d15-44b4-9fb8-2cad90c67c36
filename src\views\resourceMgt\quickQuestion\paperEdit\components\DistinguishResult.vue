<template>
  <div>
    <div class="flex items-center justify-between">
      <div>识别结果</div>
      <n-button type="primary" @click="manualAddQuestion">手动录题</n-button>
    </div>
    <List :data="data" :loading="loading">
      <template #action="{ row }">
        <n-space justify="center">
          <n-button @click="selectRow(row, 'edit')">改编此题</n-button>
          <n-button type="primary" @click="selectRow(row, 'import')"
            >导入此题</n-button
          >
        </n-space>
      </template>
    </List>
  </div>
</template>

<script setup lang="ts">
import List from "./List.vue"
import { bindQuestion } from "@/api/bookMgt"
import type { PropType } from "vue"
import { useQuickQuestionStore } from "@/stores/modules/quickQuestion"

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
  type: {
    type: Number,
    default: 0,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  imgBoxId: {
    type: [String, Number],
    default: "",
  },
})
let { currentDirId } = $(storeToRefs(useQuickQuestionStore()))

const route = useRoute()
const emit = defineEmits([
  "back",
  "close",
  "importQuestion",
  "manualAddQuestion",
])

/* 绑定试题 */
async function bindQuestionApi(val) {
  try {
    await bindQuestion({
      bookId: route.query.bookId,
      bookCatalogId: currentDirId,
      bindQuestionIdList: [val.questionId],
      quickQuestionImageBoxId: props.imgBoxId,
    })
  } catch (err) {
    console.log(err)
  }
}
/* 选中此题 */
async function selectRow(row, type) {
  if (type == "import") await bindQuestionApi(row)
  emit("importQuestion", row, type)
  emit("close")
}

function manualAddQuestion() {
  emit("manualAddQuestion")
  emit("close")
}
</script>

<style lang="scss" scoped></style>
