<template>
  <div v-loading="loading" class="h-[700px]">
    <OcrRecognitionResult
      v-if="!loading"
      :file-url="fileData.fileAbsoluteUrl"
      :mdList="fileData.list"
    />
  </div>
</template>

<script setup lang="ts">
import OcrRecognitionResult from "@/views/resourceMgt/components/OcrRecognitionResult/index.vue"
import { getQuestionAttachOcr } from "@/api/bookMgt"
const props = defineProps({
  bookAttachId: {
    type: [Number, String],
    required: true,
  },
})
let fileData = $ref<any>({})
let loading = $ref(true)
/* 获取文件数据 */
async function getDetail() {
  try {
    loading = true
    let res = await getQuestionAttachOcr({
      bookAttachId: props.bookAttachId,
    })
    fileData = res

    setTimeout(() => {
      loading = false
    }, 300)
  } catch (err) {
    loading = false
    console.log(err)
  }
}
onMounted(() => {
  getDetail()
})
</script>

<style lang="scss" scoped></style>
