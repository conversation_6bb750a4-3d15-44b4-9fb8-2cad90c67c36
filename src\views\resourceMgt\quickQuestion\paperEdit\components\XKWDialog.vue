<template>
  <n-drawer
    v-model:show="showXKWDialog"
    width="50%"
    class="px-20px"
    :on-after-enter="openDrawer"
    :on-after-leave="close"
    :trap-focus="false"
  >
    <n-drawer-content closable>
      <n-tabs type="line" animated v-model:value="activeTab">
        <n-tab-pane :name="0" tab="学科网搜索"> </n-tab-pane>
        <n-tab-pane :name="1" tab="本地搜索"> </n-tab-pane>
      </n-tabs>

      <div>
        <DistinguishResult
          :data="questionList"
          @back="enterDialog"
          @importQuestion="importQuestion"
          @close="close"
          @manualAddQuestion="manualAddQuestion"
          :chapterId="chapterId"
          :type="activeTab"
          :loading="loading"
          :imgBoxId="imgBoxId"
        />
        <g-page
          v-if="activeTab === 1"
          :pageOptions="pageOptions"
          @change="distinguish"
        ></g-page>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import DistinguishResult from "./DistinguishResult.vue"
import {
  getQuickXkwSearch,
  getQuickLocalSearchOcrResult,
  getEsQuestion,
} from "@/api/bookMgt"
import type { PropType } from "vue"

const emit = defineEmits(["update:show", "importQuestion", "manualAddQuestion"])
// 面板显示状态
const showXKWDialog = defineModel<boolean>({ required: true })

const route = useRoute()
let questionList = $ref<any>([])
let loading = $ref(true)
let activeTab = $ref(0) //0-学科网搜索 1-本地搜索
let xkwList = $ref<any>([])
let localList = $ref([])
let questionText = $ref<any>("") // 手动录题需要回填的题目

const pageOptions = $ref({
  page: 1,
  page_size: 10,
  total: 0,
})

const props = defineProps({
  chapterId: {
    type: [Number, String, null] as PropType<number | string | null>,
  },
  imgBoxId: {
    type: [String, Number],
    default: "",
  },
})

function close() {
  showXKWDialog.value = false
  loading = true
  activeTab = 0
  xkwList = []
  localList = []
  questionList = []
  enterDialog()
}

/* 导入试题 */
function importQuestion(val, type) {
  emit("importQuestion", val, type)
}

function openDrawer() {
  getOcrResult()
  distinguish()
}

/* 开始识别 */
async function distinguish() {
  try {
    questionList = []
    loading = true
    if (activeTab === 0) {
      const res = await getQuickXkwSearch({
        sysCourseId: route.query.sysCourseId,
        quickQuestionImageBoxId: props.imgBoxId,
      })
      questionList = res || []
      xkwList = res
    } else {
      getLocalSearchOcrResult()
    }
    loading = false
  } catch (err) {
    console.log("识别试题错误", err)
    loading = false
  }
}

/** 获取ocr识别结果 */
async function getOcrResult() {
  try {
    questionText = await getQuickLocalSearchOcrResult({
      quickQuestionImageBoxId: props.imgBoxId,
    })
  } catch (err) {
    console.log("获取ocr识别结果错误", err)
  }
}

/**
 * 本地搜索，先获取ocr结果，再获取试题列表
 */
async function getLocalSearchOcrResult() {
  if (questionText.length) {
    const res = await getEsQuestion({
      questionText,
      sysCourseId: route.query.sysCourseId,
      page: pageOptions.page,
      pageSize: pageOptions.page_size,
      outQuestionIdList: xkwList?.map((v) => v.questionId),
    })
    questionList = res.list || []
    pageOptions.total = res.total
    localList = res.list
  }
}

/**
 * 手动录题，回填questionTitle
 */
function manualAddQuestion() {
  emit("manualAddQuestion", questionText)
}

function enterDialog() {
  pageOptions.page = 1
  xkwList = []
  localList = []
}

watch(
  () => activeTab,
  async (newTab) => {
    const lists = {
      0: xkwList,
      1: localList,
    }

    const targetList = lists[newTab]

    if (targetList.length) {
      // 如果目标列表有数据，直接赋值给 questionList
      questionList = $g._.cloneDeep(targetList)
      nextTick(() => {
        $g.tool.renderMathjax()
      })
    } else {
      // 如果目标列表无数据，调用识别函数请求试题列表
      try {
        await distinguish()
      } catch (error) {
        console.error(error)
      }
    }
  },
)
</script>

<style lang="scss" scoped></style>
