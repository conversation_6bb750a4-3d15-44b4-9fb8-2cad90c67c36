<template>
  <div>
    <g-dialog
      title="关联书籍"
      v-bind="$attrs"
      width="600"
      :show-footer="false"
      @close-x="close"
    >
      <div class="max-h-[500px] overflow-y-auto">
        <div class="mb-[16px]" v-for="(item, index) in bookList" :key="index">
          {{ item?.bookName || "-" }}
        </div>
      </div>
    </g-dialog>
  </div>
</template>
<script lang="ts" setup>
let props = defineProps({
  bookList: {
    type: Array<any>,
    default: () => [],
  },
})
const emit = defineEmits(["update:show"])
function close() {
  emit("update:show", false)
}
</script>
<style scoped lang="scss"></style>
