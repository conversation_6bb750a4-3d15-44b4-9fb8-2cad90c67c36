<template>
  <div class="reroureTree-container-main">
    <g-form
      :formOptions="filterFormOptions"
      @search="getList"
      @reset="getList"
      :tableOptions="currentTableOptions"
    >
    </g-form>
    <g-table
      :tableOptions="tableOptions"
      @change-page="getList"
      :cell-style="scoreStyle"
    >
      <template #sysTextbookId="{ row }">
        <n-button text type="primary" class="mr-6px" @click="onIdClick(row)">{{
          row.sysTextbookId
        }}</n-button>
      </template>
      <template #bookList="{ row }">
        <n-button
          :disabled="!row?.bookList?.length"
          text
          type="primary"
          @click="onABookClick(row)"
          >{{ row.bookList?.length || 0 }}套</n-button
        >
      </template>
      <template
        #[slotName]="{ row, column }"
        v-for="slotName in slotList"
        :key="slotName + 'type'"
      >
        <div v-if="row?.statisticsMap?.[column.property]">
          {{ row?.statisticsMap?.[column.property].resourceCount || 0 }}
        </div>
        <div v-else>-</div>
      </template>
    </g-table>
    <AbookDialog v-model:show="showABook" :bookList="bookList"></AbookDialog>
  </div>
</template>

<script setup lang="ts">
import {
  getNewStageListApi,
  getResourceListApi,
  getNewSubjectListApi,
  getNewVersionListApi,
} from "@/api/resourceMgt"
import AbookDialog from "./components/AbookDialog.vue"
let slotList = $ref<any>([])
let data = $ref<any>([])
let showABook = $ref<any>(false)
const router = useRouter()
let bookList = $ref<any>([])
const filterFormOptions = reactive<any>({
  ref: null as any,
  filter: true,
  labelWidth: "60px",
  items: {
    sysStageId: {
      type: "select",
      label: "学段",
      labelField: "title",
      valueField: "id",
      options: [],
      width: "200px",
    },
    sysCourseId: {
      type: "select",
      label: "学科",
      labelField: "sysCourseName",
      valueField: "sysCourseId",
      options: [],
      width: "200px",
    },
    sysTextbookVersionId: {
      type: "select",
      label: "版本",
      width: "200px",
      labelField: "sysTextbookVersionName",
      valueField: "sysTextbookVersionId",
      options: [],
      tooltip: true,
    },
  },
  // 列表接口使用到的筛选值,及时搜索时使用
  data: {
    sysStageId: null,
    sysCourseId: null,
    sysTextbookVersionId: null,
  },
  // 列表接口使用到的筛选值,非及时搜索时使用
  filterData: {},
})
const initColumn = [
  {
    prop: "sysTextbookId",
    label: "ID",
    slot: true,
  },

  {
    prop: "sysStageName",
    label: "学段",
  },
  {
    prop: "sysSubjectName",
    label: "学科",
  },
  {
    prop: "sysTextbookVersionName",
    label: "版本",
  },
  {
    prop: "sysTextbookName",
    label: "教材",
  },
  {
    prop: "questionCount",
    label: "试题",
  },
  {
    prop: "bookList",
    label: "关联书籍",
    slot: true,
  },
]
const getList = async () => {
  tableOptions.loading = true
  let res = await getResourceListApi({
    ...filterFormOptions.filterData,
    page: tableOptions.pageOptions.page,
    pageSize: tableOptions.pageOptions.page_size,
  })
  tableOptions.pageOptions.total = res?.total || 0
  data = $g.tool.isTrue(res) ? res?.list : []
  tableOptions.data = data
  tableOptions.loading = false
}
let currentTableOptions = computed(() => {
  let columns = $g.tool.isTrue(data[0]?.statisticsMap)
    ? Object.values(data[0]?.statisticsMap || {}).map((item: any) => {
        return {
          prop: String(item?.resourceTypeName),
          label: item?.resourceTypeName,
          slot: true,
        }
      })
    : []
  tableOptions.column = [...initColumn, ...columns]
  slotList = ($g._ as any).map(columns, "prop")
  return tableOptions
})
function onABookClick(item) {
  bookList = item?.bookList || []
  if (!bookList.length) {
    return
  }
  showABook = true
}
async function getSubjectList() {
  const res = await getNewSubjectListApi({
    sysStageId: filterFormOptions.data.sysStageId,
  })
  filterFormOptions.items.sysCourseId.options = res || []
}

async function getNewVersionList() {
  const res = await getNewVersionListApi({
    sysCourseId: filterFormOptions.data.sysCourseId,
  })
  filterFormOptions.items.sysTextbookVersionId.options = res || []
}
watch(
  () => filterFormOptions.data.sysStageId,
  async (val) => {
    filterFormOptions.items.sysCourseId.options = []
    filterFormOptions.data.sysCourseId = null
    if (val) {
      getSubjectList()
    }
  },
)
watch(
  () => filterFormOptions.data.sysCourseId,
  async (val) => {
    filterFormOptions.items.sysTextbookVersionId.options = []
    filterFormOptions.data.sysTextbookVersionId = null
    if (val) {
      getNewVersionList()
    }
  },
)
const onIdClick = (item) => {
  router.push({
    name: "BookDetail",
    query: {
      sysTextbookId: item?.sysTextbookId,
      sysTextbooksName: item?.sysTextbookName,
      sysSubjectName: item?.sysSubjectName,
      sysStagesName: item?.sysStageName,
      sysTextbookVersionsName: item?.sysTextbookVersionName,
      sysCourseId: item?.sysCourseId,
      sysStagesId: item?.sysStageId,
    },
  })
}
const scoreStyle = ({ columnIndex, row, column }) => {
  if (row?.statisticsMap[column.property]?.resourceCount > 0) {
    return {
      background: "#6aa84f",
      color: "#333",
    }
  }
}
async function getStageList() {
  const res = await getNewStageListApi()
  filterFormOptions.items.sysStageId.options = res || []
}
onMounted(() => {
  getStageList()
  getList()
})
const tableOptions = reactive<any>({
  ref: null,
  loading: false,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
  column: [],
  data: [],
})
</script>

<style lang="scss" scoped></style>
