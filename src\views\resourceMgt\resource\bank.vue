<template>
  <div>
    <div>
      <div class="w-full flex justify-end mb-[10px]">
        <n-button type="primary" @click="onGoXKW">学科网导入</n-button>
      </div>
      <div class="flex items-center justify-between">
        <div class="flex">
          <div class="text-14px flex-shrink-0" style="width: 50px">来源：</div>
          <n-checkbox-group
            v-model:value="filterOptions.data.sourceIds"
            class="flex-grow"
          >
            <n-space item-style="display: flex;">
              <template
                v-for="item in filterOptions.items.sourceIdList"
                :key="item.value"
              >
                <n-checkbox :value="item.value" :label="item.label" />
              </template>
            </n-space>
          </n-checkbox-group>
        </div>
      </div>
      <div class="flex my-[10px]">
        <div class="text-14px flex-shrink-0" style="width: 50px">题型：</div>
        <div class="flex-grow">
          <n-checkbox-group
            v-model:value="filterOptions.data.sysQuestionTypeIds"
          >
            <n-space item-style="display: flex;">
              <template
                v-for="item in filterOptions.items.sysQuestionTypeList"
                :key="item.value"
              >
                <n-checkbox :value="item.value" :label="item.label" />
              </template>
            </n-space>
          </n-checkbox-group>
        </div>
      </div>
      <div class="flex items-center justify-between">
        <div class="flex">
          <div class="text-14px flex-shrink-0" style="width: 50px">难度：</div>
          <n-checkbox-group
            v-model:value="filterOptions.data.sysQuestionDifficultyIds"
            class="flex-grow"
          >
            <n-space item-style="display: flex;">
              <template
                v-for="item in filterOptions.items.sysQuestionDifficultyList"
                :key="item.value"
              >
                <n-checkbox :value="item.value" :label="item.label" />
              </template>
            </n-space>
          </n-checkbox-group>
        </div>
        <div class="flex">
          <div class="w-[250px] mr-[10px]">
            <n-input
              clearable
              v-model:value="textWord"
              type="text"
              placeholder="请输入书籍/试卷名称检索"
            />
          </div>
          <n-button type="primary" @click="search">查询</n-button>
        </div>
      </div>
    </div>
    <List
      :data="tableOptions.data"
      :loading="tableOptions.loading"
      :page-options="tableOptions.pageOptions"
      :show-audio-btn="true"
      @change-page="initData"
    >
      <template #left-header="{ row }">
        <div class="flex items-start">
          <n-tag
            :bordered="false"
            :color="
              sourceTagStyle[row.questionSource] || sourceTagStyle.default
            "
          >
            {{ getSource(row.questionSource) || "无数据" }}
          </n-tag>
          <el-popover
            :visible="row.expand"
            placement="bottom"
            :width="500"
            trigger="click"
          >
            <template #reference>
              <div class="ml-[10px] flex items-center bg-[#f2f2f2] px-[6px]">
                来源书籍：{{
                  row.bookList[0]?.bookName.length > 30
                    ? row.bookList[0]?.bookName.slice(0, 30) + "..."
                    : row.bookList[0]?.bookName
                }}
                <g-icon
                  v-if="row.bookList?.length > 1"
                  @click="onExpand(row)"
                  :name="
                    row?.expand
                      ? 'ri-arrow-up-double-line'
                      : 'ri-arrow-down-double-line'
                  "
                  size="16"
                ></g-icon>
              </div>
            </template>
            <div class="max-h-[270px] overflow-y-auto">
              <div
                class="mb-[16px]"
                v-for="(item, index) in row.bookList"
                :key="index"
              >
                {{ item?.bookName }}
              </div>
            </div>
          </el-popover>
        </div>
      </template>
      <template #action="{ row }">
        <n-space justify="space-evenly">
          <n-button text type="primary" @click="goEdit(row)">编辑</n-button>
          <n-button text type="error" @click="deleteItem(row)">删除</n-button>
        </n-space>
      </template>
    </List>
    <!-- <g-table :tableOptions="tableOptions" @changePage="initData">
      <template #subQuestions="{ row }">
        <div v-if="row.subQuestions">
          <TestItem :params="row" :key="row.questionId" />
        </div>
      </template>

      <template #knowledgePoints="{ row }">
        <div>
          <n-tag
            v-for="item in row.knowledgePoints"
            :key="item.sysKnowledgePointId"
            class="m-5px"
            type="primary"
          >
            {{ item.sysKnowledgePointName }}</n-tag
          >
        </div>
      </template>
      <template #cz="{ row }">
        <n-space justify="space-evenly">
          <n-button text type="primary" @click="goEdit(row)">编辑</n-button>
          <n-button text type="error" @click="deleteItem(row)">删除</n-button>
        </n-space>
      </template>
    </g-table> -->
    <VideoDialog v-model:show="showDialog" :params="fileInfo" />
    <!-- <import-dialog
      v-model:dialog-visible="dialogVisible"
      :xkw-data="xkwData"
      :edit-xkw-data="editXkwData"
      :action-type="actionType"
      :loading="loadingImport"
      @confirm="handleConfirm"
    ></import-dialog> -->
  </div>
</template>

<script setup lang="ts">
import VideoDialog from "./components/VideoDialog.vue"
import TestItem from "./components/TestItem.vue"
import List from "@/views/resourceMgt/bookManage/enterResoure/components/List.vue"
import {
  fetchExamXKWOAuth,
  fetchExamXKWBind,
  bindExamXKWApi,
  importPaperQuestion,
  getSourceIdsApi,
} from "@/api/resourceMgt"
import { getQuestionTypeList } from "@/api/bookMgt"
import {
  getQuestionDifficultySelect,
  getQuestionData,
  deleteQuestionApi,
  getPaperQuestion,
} from "@/api/resourceMgt"
import config from "@/config/index"
const { baseOrigin } = config
const diffInfo = {
  1: "简单",
  2: "较易",
  3: "一般",
  4: "较难",
  5: "困难",
}
let xkwOpenId = $ref<any>(null)
let textWord = $ref<any>("")
let xkwPaperId = $ref<any>(null)
let handler = $ref<any>(null)
let xkwData = $ref<any>(null)
let editXkwData = $ref<any>(null)
let dialogVisible = $ref<any>(null)
let loadingImport = $ref<any>(false)
let actionType = $ref<any>("add")
let list = $ref<any>([])
let expand = $ref<any>(false)
// 来源tag的样式
const sourceTagStyle = {
  default: {
    color: "#3398F71A",
    textColor: "#3398F7",
  },
  3: {
    color: "#00CE9B1A",
    textColor: "#00CE9B",
  },
  1: {
    color: "#6D88FA1A",
    textColor: "#6D88FA",
  },
  4: {
    color: "#F5319D1A",
    textColor: "#F5319D",
  },
  2: {
    color: "#4CACFC1A",
    textColor: "#4CACFC",
  },
}
const tableOptions = reactive<any>({
  loading: false,
  column: [
    {
      prop: "questionId",
      label: "试题编号",
      width: 100,
    },
    {
      prop: "sysQuestionTypeName",
      label: "试题类型",
      width: 100,
    },
    {
      prop: "subQuestions",
      label: "内容",
      slot: true,
      width: 700,
      tooltip: false,
    },
    {
      prop: "sysQuestionDifficultyName",
      label: "难度系数",
      width: 100,
    },
    {
      prop: "knowledgePoints",
      label: "知识点",
      slot: true,
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
      width: "200",
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})
const search = () => {
  tableOptions.pageOptions.page = 1
  initData()
}
const filterOptions = reactive<any>({
  items: {
    sysQuestionTypeList: [],
    sysQuestionDifficultyList: [],
    sourceIdList: [],
  },
  data: {
    sysQuestionTypeIds: [0],
    sysQuestionDifficultyIds: [0],
    sourceIds: [0],
  },
})
//章节题库试题难度列表
const getChapterQuestionBankLevelListApi = async () => {
  let res = await getQuestionDifficultySelect()
  filterOptions.items.sysQuestionDifficultyList = [
    { label: "全部", value: 0 },
  ].concat(
    res.map((v) => {
      return {
        label: v.sysQuestionDifficultyName,
        value: v.sysQuestionDifficultyId,
      }
    }),
  )
}
function getSource(Id) {
  return filterOptions.items.sourceIdList.find((item) => item?.value == Id)
    ?.label
}
//章节题库试题来源列表
const getSourceList = async () => {
  let res = await getSourceIdsApi()
  filterOptions.items.sourceIdList = [{ label: "全部", value: 0 }].concat(
    res.map((v) => {
      return {
        label: v.title,
        value: v.id,
      }
    }),
  )
}
let fileInfo = $ref<any>({})
let showDialog = $ref<any>(false)
//预览文件
const showVideoDialog = (item) => {
  showDialog = true
  fileInfo = item
}
let letterList = $ref<any>([])
let signList = ref<any>([])
const route = useRoute()
const initData = async () => {
  tableOptions.loading = true
  let res = await getQuestionData({
    page: tableOptions.pageOptions.page,
    pageSize: tableOptions.pageOptions.page_size,
    sysQuestionTypeIds: filterOptions.data.sysQuestionTypeIds.includes(0)
      ? null
      : filterOptions.data.sysQuestionTypeIds.join(","),
    sysQuestionDifficultyIds:
      filterOptions.data.sysQuestionDifficultyIds.includes(0)
        ? null
        : filterOptions.data.sysQuestionDifficultyIds.join(","),
    sysTextbookCatalogId: !route.query.sysKnowledgePointId
      ? route.query.sysTextbookCatalogId
      : null,
    questionSources: filterOptions.data.sourceIds.includes(0)
      ? null
      : filterOptions.data.sourceIds.join(","),
    keyword: textWord,
    sysKnowledgePointId:
      route.query.sysKnowledgePointId && route.query.sysKnowledgePointId,
    // sysKnowledgePointId: 18024,
    // sysQuestionTypeIds: "1, 50, 52",
  })
  tableOptions.pageOptions.total = res.total || 0
  tableOptions.loading = false
  tableOptions.data = res.list.map((item) => {
    return {
      ...item,
      expand: false,
    }
  })
  $g.tool.renderMathjax()
  if (res.list.length == 0) return
  let arr: any = []
  let arr1: any = []
  Object.keys(res.list[0].subQuestions[0]).forEach((val) => {
    if (val.includes("option") && !val.includes("Numbers")) {
      arr.push(val)
      arr1.push(val.replace("option", ""))
    }
  })
  signList = arr
  letterList = arr1
}
const changeFile = (file, row) => {
  console.log(file, row)
}
//获取题型
const getKnowledgeQuestionTypeListApi = async () => {
  let res = await getQuestionTypeList({
    sysCourseId: route.query.discipline,
  })
  let option = [
    {
      sysQuestionTypeId: 0,
      sysQuestionTypeName: "全部",
      label: "全部",
      value: 0,
    },
  ]
  filterOptions.items.sysQuestionTypeList = option.concat(
    res.map((val) => {
      return {
        ...val,
        value: val.sysQuestionTypeId,
        label: val.sysQuestionTypeName,
      }
    }),
  )
}
watch(
  () => filterOptions.data.sysQuestionDifficultyIds,
  (newVal, oldVal) => {
    if (newVal[newVal.length - 1] == 0 && newVal.length > 1) {
      filterOptions.data.sysQuestionDifficultyIds = [0]
      return
    }
    if (newVal.length == 0) {
      filterOptions.data.sysQuestionDifficultyIds = oldVal
      return
    }
    if (newVal.includes(0) && newVal.length > 1) {
      filterOptions.data.sysQuestionDifficultyIds.splice(
        filterOptions.data.sysQuestionDifficultyIds.indexOf(0),
        1,
      )
    }
  },
)
watch(
  () => filterOptions.data.sysQuestionTypeIds,
  (newVal, oldVal) => {
    if (newVal[newVal.length - 1] == 0 && newVal.length > 1) {
      filterOptions.data.sysQuestionTypeIds = [0]
      return
    }
    if (newVal.length == 0) {
      filterOptions.data.sysQuestionTypeIds = oldVal
      return
    }
    if (newVal.length > 1 && newVal.includes(0)) {
      filterOptions.data.sysQuestionTypeIds.splice(
        filterOptions.data.sysQuestionTypeIds.indexOf(0),
        1,
      )
    }
  },
)
watch(
  () => filterOptions.data.sourceIds,
  (newVal, oldVal) => {
    if (newVal[newVal.length - 1] == 0 && newVal.length > 1) {
      filterOptions.data.sourceIds = [0]
      return
    }
    if (newVal.length == 0) {
      filterOptions.data.sourceIds = oldVal
      return
    }
    if (newVal.length > 1 && newVal.includes(0)) {
      filterOptions.data.sourceIds.splice(
        filterOptions.data.sourceIds.indexOf(0),
        1,
      )
    }
  },
)
function goToXKW() {
  const params = new URLSearchParams({
    _openid: xkwOpenId,
    _m: `${location.protocol}//zujuan.qimingdaren.com/#/resourceMgt/resMain`,
  })
  const url = `${
    location.protocol
  }//zujuan.qimingdaren.com/#/iframe?${params.toString()}`
  handler = window.open(url)
}

async function goToXKWAuth() {
  const res = await fetchExamXKWOAuth({
    redirectUri: `https://zujuan.qimingdaren.com/#/resourceMgt/resMain?${
      location.href.split("?")[1]
    }&tab=bank`,
    xkwService: "COMPOSITION",
  })
  if (res) {
    location.replace(res)
  }
}
async function bindExamXKW() {
  const { code, state } = route.query
  if (code && state) {
    const data = await bindExamXKWApi({
      code,
      state,
    })

    xkwOpenId = data?.openId
    if (data?.openId) {
      console.log("data?.openId", data?.openId)
      $g.msg("绑定成功", "success")
      location.replace(
        `${baseOrigin}/#/resourceMgt/resMain?${location.href
          ?.split("?")[1]
          ?.split("&")
          ?.filter((item) => !["code", "state"].includes(item.split("=")[0]))
          ?.join("&")}`,
      )
    }
  }
}
function onExpand(row) {
  tableOptions.data.map((item) => {
    if (item?.ordinal != row?.ordinal) {
      item.expand = false
    }
  })
  row.expand = !row.expand
}
async function getXKWBind() {
  const data = await fetchExamXKWBind()
  xkwOpenId = data?.openId
  if (!xkwOpenId) {
    bindExamXKW()
  }
}
function postMessageToXKW() {
  if (handler) {
    handler.postMessage("close", `${location.protocol}//zujuan.qimingdaren.com`)
  }
}
async function importQuestion(data) {
  const res = await importPaperQuestion({
    sysTextbookCatalogId: !route.query.sysKnowledgePointId
      ? route.query.sysTextbookCatalogId
      : null,
    sysKnowledgePointId: route.query.sysKnowledgePointId || null,
    xkwPaperDataId: data.xkwPaperDataId,
  })
  if (res) {
    initData()
    emit("getCount")
  }
}
async function getQuestions() {
  const res = await getPaperQuestion({
    openId: xkwOpenId,
    paperId: xkwPaperId,
  })
  if (res) {
    importQuestion(res)
  }
  console.log("res", res)
}
async function loadXKWQuestion() {
  if (xkwOpenId && xkwPaperId) {
    // @todo 添加到列表

    // 重新请求列表
    getQuestions()
  }
}
function handleConfirm() {
  initData()
}
function onPostMessage(event) {
  console.log("event", event)
  const { data } = event
  if (data) {
    const { paperid, openid } = data
    if (paperid && openid && openid === xkwOpenId) {
      xkwPaperId = paperid
      postMessageToXKW()
      loadXKWQuestion()
    }
  }
}
function onGoXKW() {
  if (xkwOpenId) {
    goToXKW()
  } else {
    goToXKWAuth()
  }
}
onBeforeUnmount(() => {
  window.removeEventListener("message", onPostMessage)
})
onMounted(() => {
  getKnowledgeQuestionTypeListApi()
  getChapterQuestionBankLevelListApi()
  getSourceList()
  initData()
  getXKWBind()
  window.addEventListener("message", onPostMessage)
})
const router = useRouter()
function goEdit(row) {
  router.push({
    name: "PaperEdit",
    query: {
      bookCatalogId: route.query.sysTextbookCatalogId,
      questionId: row.questionId,
      sysCourseId: route.query.discipline,
    },
  })
}
const emit = defineEmits(["getCount"])
function deleteItem(row) {
  $g.confirm({ content: "是否确认删除" })
    .then(async () => {
      await deleteQuestionApi({
        questionId: row.questionId,
        sysTextbookCatalogId: !route.query.sysKnowledgePointId
          ? route.query.sysTextbookCatalogId
          : null,

        sysKnowledgePointId:
          route.query.sysKnowledgePointId && route.query.sysKnowledgePointId,
      })
      $g.msg("删除成功", "success")
      emit("getCount")
      initData()
    })
    .catch(() => {})
}
</script>

<style lang="scss" scoped></style>
