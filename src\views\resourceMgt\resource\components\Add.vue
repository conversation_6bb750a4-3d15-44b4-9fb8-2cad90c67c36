<template>
  <g-dialog
    width="700"
    title="上传资源"
    v-model:show="showDialog"
    :form-options="formOptions"
    v-bind="$attrs"
    @confirm="confirm"
    transform-origin="center"
  >
    <div class="text-16px text-[#333] ml-16px mb-20px">
      知识点/章节
      <span class="text-[#999] ml-10px">{{ route.query.name }}</span>
    </div>
    <g-form :formOptions="formOptions">
      <template #files>
        <g-upload
          :key="formOptions.data.type"
          ref="ossUpload"
          :file-size="4.88 * 1024 * 1024 * 1024"
          :accept="'.gif,.word,.ppt,.png,.jpg,.jpeg,.m3u8,.mp4,.mp3,.pdf,.doc,.docx,.xls,.xlsx,.pptx'"
          v-model:fileList="formOptions.data.files"
          type="drag"
          :max="1"
        >
        </g-upload>
      </template>
    </g-form>
  </g-dialog>
</template>

<script setup lang="ts">
import {
  getTypeListApi,
  getKnowTypeListApi,
  addSourceApi,
  addKnowSourceApi,
} from "@/api/resourceMgt"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  params: {
    type: Object,
    required: true,
  },
})
const emit = defineEmits<{
  (e: "update:show", val: boolean): void
  (e: "initData"): void
}>()
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,

  items: {
    type: {
      type: "radio",
      label: "类型",
      rule: true,
      span: 19,
      options: [],
    },
    files: {
      type: "upload",
      label: "文件",
      rule: true,
      span: 19,
      slot: true,
    },
    name: {
      type: "text",
      label: "名称",
      rule: true,
    },
  },
  data: {
    type: null,
    files: [],
    name: "",
  },
})

watch(
  () => formOptions.data.files.length,
  () => {
    formOptions.data.name = formOptions.data.files[0]?.resource_title ?? ""
  },
)
let ossUpload = $ref<any>(null)
const route = useRoute()

const confirm = () => {
  let temp = formOptions.data
  let params = {
    resourceTypeId: temp.type,
    fileAbsoluteUrl: temp.files[0].fullUrl,
    fileName: temp.name,
    fileSize: temp.files[0].size,
    fileExtension: temp.files[0].suffix,
    fileDuration: temp.files[0].time_length ?? "",
  }
  if (route.query.sysKnowledgePointId) {
    addKnowSourceApi({
      ...params,
      sysKnowledgePointId: route.query.sysKnowledgePointId,
    })
      .then((res) => {
        $g.msg("上传资源成功")
        emit("update:show", false)
        emit("initData")
      })
      .catch((err) => {
        console.log("上传资源报错", err)
      })
      .finally(() => {
        formOptions.loading = false
      })
  } else {
    addSourceApi({
      ...params,
      sysTextbookCatalogId: route.query.sysTextbookCatalogId,
    })
      .then((res) => {
        $g.msg("上传资源成功")
        emit("update:show", false)
        emit("initData")
      })
      .catch((err) => {
        console.log("上传资源报错", err)
      })
      .finally((fin) => {
        formOptions.loading = false
      })
  }
}

const getTypeList = async () => {
  let res = await getTypeListApi({
    sysTextbookCatalogId: route.query.sysTextbookCatalogId,
  })
  formOptions.items.type.options = res
    .filter((item) => item.resourceTypeName != "全部")
    .map((item) => {
      return {
        label: item.resourceTypeName,
        value: item.resourceTypeId,
      }
    })
  formOptions.data.type =
    props.params.typeA == 0
      ? formOptions.items.type.options[0].value
      : props.params.typeA
}
const getKnowTypeList = async () => {
  let res = await getKnowTypeListApi({
    sysKnowledgePointId: route.query.sysKnowledgePointId,
  })
  formOptions.items.type.options = res
    .filter((item) => item.resourceTypeName != "全部")
    .map((item) => {
      return {
        label: item.resourceTypeName,
        value: item.resourceTypeId,
      }
    })
  formOptions.data.type =
    props.params.typeA == 0
      ? formOptions.items.type.options[0].value
      : props.params.typeA
}
let showDialog = $computed({
  get() {
    return props.show
  },
  set(val) {
    emit("update:show", val)
  },
})
watch(
  () => formOptions.data.type,
  (val) => {
    if (ossUpload) {
      if (formOptions.data.files.length) formOptions.data.files = []
    }
  },
)
function getType() {
  if (route.query.sysKnowledgePointId) {
    getKnowTypeList()
  } else {
    getTypeList()
  }
}
watch(
  () => props.show,
  (val) => {
    formOptions.data = {
      type: null,
      level: null,
      origin: null,
      files: [],
      name: "",
    }
    if (val) {
      getType()
    }
  },
)
</script>

<style lang="scss" scoped></style>
