<template>
  <g-dialog title="预览" :show-footer="false" v-bind="$attrs" width="800">
    <iframe
      class="w-full h-[700px]"
      :src="$g.tool.getWeb365Url(fileInfo.fileAbsoluteUrl)"
      frameborder="0"
      v-if="
        ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf'].includes(
          fileInfo.fileType,
        )
      "
    >
    </iframe>
    <g-video
      :url="fileInfo.fileAbsoluteUrl"
      v-else-if="['video', 'mp4', 'm3u8'].includes(fileInfo.fileType)"
    ></g-video>
    <div
      v-else-if="['png', 'gif', 'jpg', 'jpeg'].includes(fileInfo.fileType)"
      class="flex justify-center"
    >
      <n-image
        preview-disabled
        width="500"
        :src="
          fileInfo.fileAbsoluteUrl +
          '?x-oss-process=image/quality,q_70/interlace,1/format,webp'
        "
        :fallback-src="$g.tool.getFileUrl('status/error.png')"
      />
    </div>
    <div v-else-if="fileInfo.fileType === 'audio'" class="flex justify-center">
      <audio
        :src="fileInfo.fileAbsoluteUrl"
        controls
        autoplay
        class="w-500px"
      ></audio>
    </div>
    <div v-else>文件地址或格式不正确，无法预览</div>
  </g-dialog>
</template>
<script setup lang="ts">
defineProps({
  fileInfo: {
    type: Object,
    required: true,
    default: () => {
      return {}
    },
  },
})
</script>
