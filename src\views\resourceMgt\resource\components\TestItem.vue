<template>
  <div>
    <g-mathjax :text="params.questionTitle" class="text-left"></g-mathjax>
    <div class="flex my-10px" v-if="params.questionTitle?.length > 1">
      <div
        v-for="item in params.subQuestions?.length"
        :key="item"
        class="border br-[5px] mr-10px mb-10px w-30px h-30px flex items-center justify-center cursor-pointer"
        :class="{ active: item == currentIndex }"
        @click="currentIndex = item"
      >
        {{ item }}
      </div>
    </div>
    <div class="pb-20px">
      <g-mathjax
        :text="curQuestion?.subQuestionTitle"
        class="text-left"
      ></g-mathjax>
      <div class="questions-answer">
        <div
          v-for="option in curQuestion?.optionArr"
          :key="option.name"
          class="flex pl-30px items-center"
        >
          <div class="w-20px mr-6px">{{ option.name }}.</div>
          <div>
            <g-mathjax :text="option.title"></g-mathjax>
          </div>
        </div>
      </div>
    </div>
    <div class="text-left mt-20px">
      <div class="flex items-baseline">
        <span class="text-success w-70px">【答案】</span>
        <g-mathjax
          class="text-left"
          :text="curQuestion.subQuestionAnswer"
        ></g-mathjax>
      </div>
      <div class="flex items-baseline">
        <span class="text-success w-70px">【详解】</span>
        <div v-if="curQuestion.subQuestionParseList.length">
          <div
            v-for="(value, index) in curQuestion?.subQuestionParseList"
            :key="value.subQuestionParseId"
          >
            <div
              class="text-primary"
              v-if="curQuestion.subQuestionParseList.length > 1"
            >
              ({{ index + 1 }})
            </div>
            <g-mathjax :text="value.content" />
          </div>
        </div>
        <div v-else>-</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  params: {
    type: Object,
    required: true,
  },
})
let currentIndex = $ref(1)
const subQuestion = computed(() => {
  return props.params.subQuestions.map((item) => {
    return {
      ...item,
      optionArr: Object.keys(item)
        .filter(
          (key) =>
            key.includes("option") && item[key] && key !== "optionNumbers",
        )
        .map((realKey) => {
          return {
            name: realKey.charAt(realKey.length - 1).toLocaleUpperCase(),
            title: item[realKey],
          }
        }),
    }
  })
})
watch(
  () => currentIndex,
  async () => {
    $g.tool.renderMathjax()
  },
)
const curQuestion = computed(() => {
  return subQuestion.value[currentIndex - 1]
})
</script>

<style lang="scss" scoped>
.active {
  background: var(--g-primary);
  color: white;
  border: none;
}
</style>
