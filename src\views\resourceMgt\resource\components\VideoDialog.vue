<template>
  <div>
    <g-dialog
      :title="title"
      v-model:show="showDialog"
      :show-footer="false"
      :width="800"
    >
      <g-video :url="url"></g-video>
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  params: {
    type: Object,
    required: true,
  },
})
let url = $ref<any>(null)
let title = $ref("")
const emit = defineEmits(["update:show"])
const showDialog = useVModel(props, "show", emit)
watch(
  () => props.show,
  (val) => {
    url = props.params.videoFile
    title = props.params.videoFileName
  },
)
</script>

<style lang="scss" scoped></style>
