<template>
  <div class="pt-10px">
    <div class="flex items-center mb-14px">
      <div class="text-14px">资源类型：</div>
      <n-radio-group v-model:value="typeA" @change="resurceChange">
        <n-radio-button
          v-for="tp in typeAList"
          :key="tp.value"
          :value="tp.value"
          :label="getLabel(tp)"
        />
      </n-radio-group>
    </div>
    <g-table
      :tableOptions="tableOptions"
      @changePage="getList"
      :max-height="800"
    >
      <template #header-left>
        <div class="leading-[32px]">共计资源{{ sourcesTotal }}个</div>
      </template>
      <template #header-right>
        <div class="flex items-center">
          <n-space>
            <n-input-group>
              <n-input
                class="w-140px"
                v-model:value="keywords"
                placeholder="输入资源名称搜索"
                clearable
              />
              <n-button type="primary" @click="search">搜索</n-button>
            </n-input-group>
            <n-button type="warning" @click="addResource">
              <template #icon>
                <g-icon name="add-line" size="14" />
              </template>
              上传新资源
            </n-button>
          </n-space>
        </div>
      </template>
      <template #cover="{ row }">
        <g-icon
          :name="$g.tool.getFileTypeIcon(row.fileExtension)"
          size="20"
        ></g-icon>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="primary" text @click="onPreView(row)">预览</n-button>
          <n-button type="primary" text @click="onDownLoad(row)">下载</n-button>
          <n-button type="error" text @click="onDeleteSource(row)"
            >删除</n-button
          >
        </n-space>
      </template>
    </g-table>
    <Add
      v-model:show="showAdd"
      @initData="refresh"
      :params="{
        typeA,
      }"
    />
    <PreviewDialog v-model:show="showPreviewDialog" :fileInfo="fileInfo" />
  </div>
</template>

<script setup lang="ts">
import {
  deleteSourceApi,
  deleteKnowSourceApi,
  getResourcesApi,
  getKnowResourcesApi,
  getSourceListApi,
  getKnowSourceListApi,
} from "@/api/resourceMgt"
import Add from "./components/Add.vue"
import PreviewDialog from "./components/PreviewDialog.vue"
let showAdd = $ref(false)
let showBind = $ref(false)
let sourcesTotal = $ref(0)

let keywords = $ref("")
let typeA = $ref<any>(null)
let typeAList = $ref<any>([])
let typeB = $ref(0)
let showPreviewDialog = $ref<Boolean>(false)
let fileInfo = $ref<any>({})

const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: false,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 10,
  },
  column: [
    { prop: "cover", label: "文件类型", slot: true, width: 150 },
    { prop: "fileName", label: "资源名称" },
    { prop: "userName", label: "上传人" },
    {
      prop: "createTime",
      label: "上传时间",
      formatter(row) {
        return row?.createTime
          ? $g.dayjs(row?.createTime).format("YYYY-MM-DD HH:mm")
          : "-"
      },
    },
    { prop: "cz", label: "操作", slot: true, width: 220 },
  ],
  data: [],
})
const emit = defineEmits(["getCount"])
const getLabel = (item) => {
  return item.count > 0 ? item.label + "（" + item.count + "）" : item.label
}

const onChange = () => {}
const search = () => {
  tableOptions.pageOptions.page = 1
  tableOptions.pageOptions.total = 0
  tableOptions.data = []
  if (typeA != null) {
    getList()
  }
}
const addResource = () => {
  showAdd = true
}
const route = useRoute()
const getSelectData = () => {
  if (route.query.sysKnowledgePointId) {
    getKnowResourcesApi({
      sysKnowledgePointId: route.query.sysKnowledgePointId,
    }).then((res) => {
      typeAList = $g.tool.isTrue(res)
        ? res.map((item) => {
            return {
              count: item.resourceCount,
              value: item.resourceTypeId,
              label: item.resourceTypeName,
            }
          })
        : []
      typeA = typeA != null ? typeA : typeAList?.[0]?.value
    })
  } else {
    getResourcesApi({
      sysTextbookCatalogId: route.query.sysTextbookCatalogId,
    }).then((res) => {
      typeAList = $g.tool.isTrue(res)
        ? res.map((item) => {
            return {
              count: item.resourceCount,
              value: item.resourceTypeId,
              label: item.resourceTypeName,
            }
          })
        : []
      typeA = typeA != null ? typeA : typeAList?.[0]?.value
    })
  }
}

async function getResourceListData() {
  if (!route.query.sysTextbookCatalogId) {
    return
  }
  const res = await getSourceListApi({
    sysTextbookCatalogId: route.query.sysTextbookCatalogId,
    keyword: keywords,
    resourceTypeId: typeA,
    page: tableOptions.pageOptions.page,
    pageSize: 10,
  })
  tableOptions.data = res ? res?.list : []
  tableOptions.pageOptions.total = res.total || 0

  sourcesTotal = res.total || 0
}

async function getKnowSourceList() {
  if (!route.query.sysKnowledgePointId) {
    return
  }
  const res = await getKnowSourceListApi({
    sysKnowledgePointId: route.query.sysKnowledgePointId,
    keyword: keywords,
    resourceTypeId: typeA,
    page: tableOptions.pageOptions.page,
    pageSize: 10,
  })
  tableOptions.data = res ? res?.list : []
  tableOptions.pageOptions.total = res.total || 0
  emit("getCount", res.total || 0)
  sourcesTotal = res.total || 0
}
function getList() {
  if (route.query.sysKnowledgePointId) {
    getKnowSourceList()
  } else {
    getResourceListData()
  }
}

function refresh() {
  initData()
  getList()
  emit("getCount")
}
watch(
  () => typeA,
  () => {
    tableOptions.pageOptions.page = 1
    tableOptions.pageOptions.total = 0
    tableOptions.data = []
    if (typeA != null) {
      getList()
    }
  },
)
const onDeleteSource = (row) => {
  $g.confirm({
    content: "确定删除该资源吗？",
  })
    .then(async () => {
      if (route.query?.sysKnowledgePointId) {
        deleteKnowSourceApi({
          resourceKnowledgeId: row.resourceKnowledgeId,
        }).then((res) => {
          $g.msg("删除成功")
          refresh()
        })
      } else {
        deleteSourceApi({ resourceCatalogId: row.resourceCatalogId }).then(
          (res) => {
            $g.msg("删除成功")
            refresh()
          },
        )
      }
    })
    .catch(() => {})
}
const onDownLoad = (row) => {
  $g.tool.downloadFile(row.fileAbsoluteUrl, row.fileName)
}
const resurceChange = () => {
  tableOptions.pageOptions.page = 1
}
const onPreView = (row) => {
  showPreviewDialog = true
  fileInfo = {
    fileAbsoluteUrl: row.fileAbsoluteUrl,
    fileType: row.fileExtension,
  }
}
function initData() {
  getSelectData()
}
onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped></style>
