<template>
  <div class="resource-detail-container-main">
    <div class="text-center text-18px font-bold pb-14px text-[#333]">
      {{ route.query.name }}
    </div>
    <div class="flex items-center w-full text-14px text-gray-dark mb-14px">
      <div class="w-[48%] min-w-[200px] whitespace-nowrap mr-[4%]">
        所属教材：{{ textBookInfo }}
      </div>
      <div class="w-[48%] min-w-[200px] whitespace-nowrap">
        所属章节：{{ chapterInfo }}
      </div>
    </div>
    <div>ID：{{ route.query.id }}</div>
    <n-tabs type="line" animated v-model:value="tabValue">
      <n-tab-pane
        name="file"
        :tab="
          resourceCount
            ? '文件类资源' + '(' + resourceCount + ')'
            : '文件类资源(0)'
        "
      >
        <File @getCount="getCount" />
      </n-tab-pane>
      <n-tab-pane name="bank" :tab="'试题资源' + '(' + bankQuestionCount + ')'">
        <Bank @getCount="getCount" />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<script setup lang="ts">
import {
  getKnowledgeChapterRelation,
  getTotalApi,
  getKnowTotalApi,
} from "@/api/resourceMgt"
import File from "./file.vue"
import Bank from "./bank.vue"
const route = useRoute()
let textBookInfo = $ref<any>(null)
let tabValue = $ref<any>("file")
let chapterInfo = $ref<any>("")
let bankQuestionCount = $ref<any>(null)
//获取信息
const getKnowledgeChapterRelationApi = async () => {
  let res = await getKnowledgeChapterRelation({
    sysTextbookCatalogId: route.query.sysTextbookCatalogId,
    sysKnowledgePointId: route.query.sysKnowledgePointId,
  })
  textBookInfo =
    res.sysStageName +
    "/" +
    res.sysSubjectName +
    "/" +
    res.sysTextbookVersionName +
    "/" +
    res.sysTextbookName
  chapterInfo = res?.belong

  if (!res.parentVO) return
  getData(res.parentVO)
}
let resourceCount = $ref<any>(0)
async function getCount() {
  let res: any = null
  if (route.query.sysKnowledgePointId) {
    res = await getKnowTotalApi({
      sysKnowledgePointId: route.query.sysKnowledgePointId,
    })
  } else {
    res = await getTotalApi({
      sysTextbookCatalogId: route.query.sysTextbookCatalogId,
    })
  }
  resourceCount = res?.fileCount || 0
  bankQuestionCount = res?.questionCount || 0
}
//获取章节信息
const getData = (data) => {
  chapterInfo = data.sysTextbooksCatalogName + "/" + chapterInfo
  if (data.parentVO) {
    getData(data.parentVO)
  }
}
onMounted(() => {
  getKnowledgeChapterRelationApi()
  getCount()
  if (route.query?.tab) {
    tabValue = String(route.query?.tab)
  }
})
</script>

<style lang="scss" scoped></style>
