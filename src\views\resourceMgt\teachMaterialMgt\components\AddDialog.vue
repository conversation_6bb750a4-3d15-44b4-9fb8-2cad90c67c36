<template>
  <g-dialog
    :title="props.data.sysTextbookVersionId ? '修改版本信息' : '新建版本信息'"
    :formOptions="formOptions"
    v-bind="$attrs"
  >
    <g-form :formOptions="formOptions"></g-form>
  </g-dialog>
</template>
<script lang="ts" setup>
import { getNewSubjectListApi } from "@/api/resourceMgt"
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
  filterFormOptions: {
    type: Object,
    default: () => {},
  },
})
const formOptions: any = reactive({
  ref: null as any,
  items: {
    sysTextbookVersionName: {
      type: "text",
      label: "版本名称",
      width: "100%",
      span: 24,
      rule: true,
      maxlength: 20,
    },
    sysStageId: {
      type: "select",
      label: "学段",
      disabled: false,
      options: [],
      labelField: "title",
      valueField: "id",
      width: "100%",
      span: 24,
      rule: true,
    },
    sysCourseId: {
      type: "select",
      label: "学科",
      labelField: "sysCourseName",
      valueField: "sysCourseId",
      options: [],
      width: "100%",
      disabled: false,
      span: 24,
      rule: true,
    },
  },
  data: {
    sysTextbookVersionName: null,
    sysStageId: null,
    sysCourseId: null,
  },
})

async function getNewSubjectList() {
  const res = await getNewSubjectListApi({
    sysStageId: formOptions.data.sysStageId,
  })
  formOptions.items.sysCourseId.options = res
  if (!formOptions.data.sysCourseId) {
    return
  }
  const find = formOptions.items.sysCourseId.options.some((item) => {
    return item.sysCourseId == formOptions.data.sysCourseId
  })
  if (!find) {
    formOptions.data.sysCourseId = null
  }
}

watch(
  () => formOptions.data.sysStageId,
  () => {
    if (formOptions.data.sysStageId) {
      getNewSubjectList()
      return
    }
    formOptions.items.sysCourseId.options = []
    formOptions.data.sysCourseId = null
  },
)

watch(
  () => props.data,
  () => {
    formOptions.data = { ...props.data }
    const disabled = Boolean(props.data.sysTextbookVersionId)
    formOptions.items.sysStageId.disabled = disabled
    formOptions.items.sysCourseId.disabled = disabled
    formOptions.items.sysStageId.options =
      props.filterFormOptions.items.sysStageId.options
  },
)
</script>
<style scoped></style>
