<template>
  <div class="teachMaterialMg-container-main">
    <g-form
      :formOptions="filterFormOptions"
      @search="getList"
      @reset="getList"
      :tableOptions="tableOptions"
    >
    </g-form>
    <g-table :tableOptions="tableOptions" @changePage="getList">
      <template #header-right>
        <n-button type="primary" @click="onAddClick()">
          <template #icon>
            <g-icon name="ri-add-fill" size="" color="" />
          </template>
          新建
        </n-button>
      </template>
      <template #isDelete="{ row }">
        {{ row.isDelete == 1 ? "启用" : "禁用" }}
      </template>
      <template #cz="{ row }">
        <n-space inline justify="center">
          <n-button text type="primary" class="mr-6px" @click="onAddClick(row)"
            >修改</n-button
          >
          <n-button
            text
            :type="row.isDelete == 1 ? 'error' : 'success'"
            :disabled="row.isMain == 2 && row.isDelete == 1"
            @click="open(row)"
            >{{ row.isDelete == 1 ? "禁用" : "启用" }}</n-button
          >
        </n-space>
      </template>
    </g-table>
    <AddDialog
      :data="data"
      v-model:show="showAddDialog"
      :filterFormOptions="filterFormOptions"
      @confirm="onConfirm($event)"
    >
    </AddDialog>
  </div>
</template>
<script lang="ts" setup>
import AddDialog from "./components/AddDialog.vue"
import {
  getNewStageListApi,
  getListApi,
  getNewSubjectListApi,
  stopVersionApi,
  addVersionApi,
  editVersionApi,
} from "@/api/resourceMgt"

let data: any = $ref({})
let showAddDialog = $ref(false)
const filterFormOptions = reactive<any>({
  ref: null as any,
  filter: true,
  labelWidth: "100px",
  items: {
    sysTextbookVersionId: {
      type: "number",
      label: "版本ID",
      width: "180px",
      showButton: false,
    },
    keyword: {
      type: "text",
      label: "版本名称",
      width: "180px",
    },
    sysStageId: {
      type: "select",
      label: "学段",
      labelField: "title",
      valueField: "id",
      options: [],
      width: "180px",
    },
    sysCourseId: {
      type: "select",
      label: "学科",
      width: "180px",
      labelField: "sysCourseName",
      valueField: "sysCourseId",
    },
  },
  // 列表接口使用到的筛选值,及时搜索时使用
  data: {
    sysTextbookVersionId: null,
    keyword: null,
    sysStageId: null,
    sysCourseId: null,
  },
  // 列表接口使用到的筛选值,非及时搜索时使用
  filterData: {},
})
const tableOptions = reactive({
  ref: null,
  loading: false,
  column: [
    {
      prop: "sysTextbookVersionId",
      label: "版本ID",
      slot: true,
    },

    {
      prop: "sysTextbookVersionName",
      label: "版本名称",
    },

    {
      prop: "sysStageName",
      label: "学段",
    },
    {
      prop: "sysCourseName",
      label: "学科",
    },
    {
      prop: "textbookNum",
      label: "教材数量",
    },
    {
      prop: "isDelete",
      label: "状态",
      slot: true,
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})
function onAddClick(row?) {
  data = row ? row : {}
  showAddDialog = !showAddDialog
}

async function addVersion(val) {
  await addVersionApi({
    sysCourseId: val.sysCourseId,
    sysTextbookVersionName: val.sysTextbookVersionName,
  })
  $g.msg("添加成功", "success")
  getList()
}
async function editVersion(val) {
  await editVersionApi({
    sysTextbookVersionId: val.sysTextbookVersionId,
    sysTextbookVersionName: val.sysTextbookVersionName,
  })
  $g.msg("修改成功", "success")
  getList()
}
function onConfirm(val) {
  if (data?.sysTextbookVersionId) {
    editVersion(val)
  } else {
    addVersion(val)
  }
  showAddDialog = false
}
async function getList() {
  try {
    tableOptions.loading = true
    const res = await getListApi({
      ...filterFormOptions.data,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.pageOptions.total = res.total
    tableOptions.data = res.list
    tableOptions.loading = false
  } catch (err) {
    tableOptions.loading = false
  }
}
async function getNewSubjectList() {
  const res = await getNewSubjectListApi({
    sysStageId: filterFormOptions.data.sysStageId,
  })
  filterFormOptions.items.sysCourseId.options = res
}
function open(row) {
  $g.confirm({
    content: `你确定要${row.isDelete == 1 ? "禁用" : "启用"}此版本？${
      row.isDelete == 1 ? "禁用" : "启用"
    }后用户端将${
      row.isDelete == 1 ? "不再展示" : "展示"
    }该版本及所包含的教材信息`,
  })
    .then(async () => {
      await stopVersionApi({
        sysTextbookVersionId: row.sysTextbookVersionId,
      })
      $g.msg(`${row.isDelete == 1 ? "禁用" : "启用"}成功`)
      initData()
    })
    .catch((err) => {})
}
watch(
  () => filterFormOptions.data.sysStageId,
  () => {
    filterFormOptions.data.sysCourseId = null
    if (filterFormOptions.data.sysStageId) {
      getNewSubjectList()
      return
    }
    filterFormOptions.items.sysCourseId.options = []
  },
)
async function getNewStageList() {
  const res = await getNewStageListApi()
  filterFormOptions.items.sysStageId.options = res || []
}
const initData = async () => {
  await getList()
  await getNewStageList()
}
onBeforeMount(() => {
  initData()
})
</script>
<style scoped></style>
