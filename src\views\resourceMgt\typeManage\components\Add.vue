<template>
  <g-dialog
    :title="title"
    :formOptions="formOptions"
    v-model:show="showDialog"
    @confirm="confirm"
  >
    <g-form :formOptions="formOptions">
      <template #parentBookTypeId>
        <n-tree-select
          v-model:value="formOptions.data.parentBookTypeId"
          :options="formOptions.items.parentBookTypeId.options"
          placeholder="请选择父级类型"
          :renderLabel="renderLabel"
        />
      </template>
    </g-form>
  </g-dialog>
</template>

<script setup lang="ts">
import { getTypeList, addType, editType } from "@/api/bookMgt"
import { getNewStageListApi } from "@/api/resourceMgt"
import GTooltip from "@/components/global/g-tooltip/index.vue"
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  info: {
    type: Object,
    default: () => {},
  },
})
const route = useRoute()
const emit = defineEmits(["update:show", "refresh"])
const showDialog = useVModel(props, "show", emit)
const formOptions = reactive<any>({
  ref: null as any,
  loading: false,
  items: {
    bookTypeName: {
      type: "text",
      label: "类型名称",
      rule: true,
      labelWidth: "150px",
    },
    parentBookTypeId: {
      type: "select",
      label: "父级类型",
      placeholder: "请选择父级类型",
      rule: true,
      options: [],
      slot: true,
      labelWidth: "150px",
    },
    sysStageId: {
      type: "select",
      label: "学段",
      options: [],
      clearable: false,
      show: false,
      rule: true,
      labelWidth: "150px",
    },
    category: {
      type: "radio",
      label: "归类",
      options: [
        { label: "书籍", value: 1 },
        { label: "试卷", value: 2 },
      ],
      clearable: false,
      rule: true,
      labelWidth: "150px",
    },
    ordinal: {
      type: "number",
      label: "排序值(越小越靠前)",
      labelWidth: "150px",
      min: 0,
    },
  },
  data: {
    bookTypeName: "",
    parentBookTypeId: "",
    sysStageId: null,
    ordinal: 0,
    category: 1,
  },
})
const title = $computed(() => {
  return props.isEdit ? "编辑" : "新增"
})
function renderLabel({ option }) {
  return h(GTooltip, { content: option.label, refName: String(option.key) }, {})
}
/* 获取父级类型列表 */
async function getTypeListApi() {
  let res = await getTypeList({ keyword: "" })

  formOptions.items.parentBookTypeId.options = [
    { label: "无", key: 0, children: null },
  ].concat(transformDataStructure(res))
}
/* 递归处理数据 */
function transformDataStructure(data) {
  if (Array.isArray(data)) {
    return data.map((item) => {
      const newItem = { ...item }
      newItem.label = newItem.bookTypeName
      newItem.key = newItem.bookTypeId
      newItem.children = newItem.list.length
        ? transformDataStructure(newItem.list)
        : null

      return newItem
    })
  } else if (typeof data === "object" && data !== null) {
    return Object.keys(data).reduce((obj, key) => {
      obj[key] = transformDataStructure(data[key])
      return obj
    }, {})
  }
  return data
}
/* 获取学段 */
async function getStage() {
  let res = await getNewStageListApi()
  formOptions.items.sysStageId.options = [{ label: "全部", value: 0 }].concat(
    res.map((v) => {
      return {
        value: v.id,
        label: v.title,
      }
    }),
  )
}
/* 新增 */
async function add() {
  await addType({
    bookTypeName: formOptions.data.bookTypeName,
    parentBookTypeId: formOptions.data.parentBookTypeId,
    sysStageId: formOptions.data.sysStageId,
    ordinal: formOptions.data.ordinal,
    category: formOptions.data.category,
  })
  $g.msg("新增成功")
}
/* 编辑 */
async function edit() {
  await editType({
    bookTypeName: formOptions.data.bookTypeName,
    parentBookTypeId: formOptions.data.parentBookTypeId,
    bookTypeId: props.info.bookTypeId,
    state: props.info.state,
    sysStageId: formOptions.data.sysStageId,
    ordinal: formOptions.data.ordinal,
    category: formOptions.data.category,
  })
  $g.msg("编辑成功")
}
async function confirm() {
  try {
    props.isEdit ? await edit() : await add()
    formOptions.loading = false
    await emit("refresh")
    await emit("update:show", false)
  } catch (err) {
    formOptions.loading = false
    console.log(err)
  }
}
/* 监听父级类型，如果父级类型为0，显示学段并且为必填 */
watch(
  () => formOptions.data.parentBookTypeId,
  (val) => {
    if (val == 0) {
      formOptions.items.sysStageId.show = true
    } else {
      formOptions.items.sysStageId.show = false
      formOptions.data.sysStageId = null
    }
  },
)
watch(
  () => props.show,
  async (val) => {
    if (val) {
      formOptions.items.sysStageId.show = false
      await getStage()
      await getTypeListApi()
      if (props.isEdit) {
        formOptions.data.bookTypeName = props.info.bookTypeName
        formOptions.data.parentBookTypeId = props.info.parentBookTypeId
        formOptions.data.sysStageId = props.info.sysStageId
        formOptions.data.ordinal = props.info.ordinal || 0
        formOptions.data.category = props.info.category
      }
    }
  },
)
</script>

<style lang="scss" scoped></style>
