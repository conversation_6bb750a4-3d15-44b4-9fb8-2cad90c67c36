<template>
  <div class="type-manage-container-main">
    <g-form :formOptions="formOptions" @reset="initData" @search="initData">
    </g-form>
    <el-radio-group v-model="radioValue" @change="initData">
      <el-radio :value="0">全部</el-radio>
      <el-radio :value="1">书籍</el-radio>
      <el-radio :value="2">试卷</el-radio>
    </el-radio-group>
    <g-table :tableOptions="tableOptions" row-key="bookTypeId">
      <template #header-right>
        <n-space justify="center">
          <n-button
            type="primary"
            @click="
              () => {
                showDialog = true
                isEdit = false
              }
            "
          >
            <g-icon name="add-line" size="" color="" />
            新增类型
          </n-button>
        </n-space>
      </template>
      <template #state="{ row }">
        <div v-if="row.state == 1" class="text-success">启用</div>
        <div v-if="row.state == 2" class="text-error">禁用</div>
      </template>
      <template #category="{ row }">
        <span>{{ row.category == 1 ? "书籍" : "试卷" }}</span>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <!-- <n-button type="primary" text @click="edit(row)">编辑</n-button> -->
          <n-button type="error" text v-if="row.state == 1" @click="enable(row)"
            >禁用</n-button
          >
          <n-button type="success" text v-else @click="enable(row)"
            >启用</n-button
          >
        </n-space>
      </template>
    </g-table>
    <Add
      v-model:show="showDialog"
      @refresh="initData"
      :info="info"
      :isEdit="isEdit"
    />
  </div>
</template>

<script setup lang="ts" name="TypeManage">
import { getTypeList, editType } from "@/api/bookMgt"
import Add from "./components/Add.vue"
let showDialog = $ref(false)
const formOptions = reactive<any>({
  loading: false,
  ref: null as any,
  filter: true,
  items: {
    keyword: {
      type: "text",
      label: "类型名称/id",
      showLabel: false,
    },
  },
  data: {
    keyword: "",
  },
})
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      prop: "bookTypeId",
      label: "ID",
    },
    {
      prop: "bookTypeName",
      label: "类型名称",
    },
    {
      prop: "tier",
      label: "层级",
    },
    {
      prop: "state",
      label: "状态",
      slot: true,
    },
    {
      prop: "num",
      label: "资源关联数量",
    },
    {
      prop: "category",
      label: "归类",
      slot: true,
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
})
let radioValue = $ref(1)
let info = $ref<any>({})
let isEdit = $ref(false)
/* 列表数据 */
async function initData() {
  try {
    tableOptions.loading = true
    let res = await getTypeList({
      keyword: formOptions.data.keyword,
      category: radioValue || null,
    })
    tableOptions.loading = false
    tableOptions.data = res
    renameListToChildren(tableOptions.data)
  } catch (err) {
    console.log(err)
  }
}
/* 递归处理数据 */
function renameListToChildren(data) {
  // 检查data是否是数组
  if (Array.isArray(data)) {
    // 遍历数组中的每个对象
    for (let i = 0; i < data.length; i++) {
      // 重命名list为children
      if (data[i].hasOwnProperty("list")) {
        data[i].children = data[i].list
      }
      // 递归调用以处理子对象
      renameListToChildren(data[i].children || [])
    }
  } else if (typeof data === "object") {
    // 如果data是对象，递归调用以处理其属性
    for (let key in data) {
      if (data.hasOwnProperty(key)) {
        renameListToChildren(data[key])
      }
    }
  }
}
/* 启用/禁用 */
function enable(row) {
  let state = row.state == 1 ? 2 : 1
  let title = row.state == 1 ? "禁用" : "启用"
  $g.confirm({
    content: `是否${title}该类型`,
  }).then(async () => {
    await editType({
      bookTypeId: row.bookTypeId,
      bookTypeName: row.bookTypeName,
      parentBookTypeId: row.parentBookTypeId,
      state: state,
    })
    initData()
    $g.msg(`${title}成功`)
  })
}
function edit(row) {
  info = row
  isEdit = true
  showDialog = true
}

const route = useRoute()
onMounted(() => {
  radioValue = Number(route.query.category) || 0
  initData()
})
</script>

<style lang="scss" scoped>
:deep() {
  .el-table__expand-icon {
    .el-icon {
      background: url("@/assets/img/arrow-right-s-line.png") no-repeat center
        center;
      svg {
        display: none;
      }
    }
  }
}
</style>
