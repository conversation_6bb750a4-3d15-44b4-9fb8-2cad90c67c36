<template>
  <div class="bg-white shadow-header px-40px min-h-[100vh] flex justify-center">
    <div>
      <div class="mt-[20px] font-500 w-full text-center">
        星空图预览-《{{ paperName }}》
      </div>
      <div class="flex items-center text-[14px] mb-[19px]">
        <div class="mr-[12px]">统计维度：</div>
        <el-radio-group v-model="radio">
          <el-radio
            v-for="item in [
              {
                value: 1,
                label: '教材章节聚合',
              },
              {
                value: 2,
                label: '知识点树聚合',
              },
            ]"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
      <g-loading v-if="loading" class="h-200px !w-[1400px]"></g-loading>
      <div v-else>
        <g-chart
          v-show="!isEmpty"
          ref="chartRef"
          class="w-[1400px] echart"
          :option="chartOption"
        />
        <g-empty v-if="isEmpty" class="!w-[1400px] echart"></g-empty>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getStartData, getKnowlegeData } from "@/api/star"
let chartRef = $ref<any>(null)
const route = useRoute()
let paperName = $computed(() => {
  return route.query.paperName
})
let isEmpty = $ref<any>(false)
let loading = $ref<any>(false)
let radio = $ref<any>(1)
let chartOption = $ref<any>(null)
watch(
  () => radio,
  () => {
    initData()
  },
)
function handleResize() {
  if (chartRef) {
    chartRef?.chart?.resize()
  }
}
onMounted(() => {
  window.addEventListener("resize", handleResize)
  initData()
})
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize)
})
function hslToRgba(h, s, l) {
  s /= 100
  l /= 100
  const k = (n) => (n + h / 30) % 12
  const a = s * Math.min(l, 1 - l)
  const f = (n) => l - a * Math.max(Math.min(k(n) - 3, 9 - k(n), 1), -1)
  const r = Math.round(f(0) * 255)
  const g = Math.round(f(8) * 255)
  const b = Math.round(f(4) * 255)
  return `rgba(${r}, ${g}, ${b}, 1)`
}

function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

function rgbaToHsl(r, g, b, a) {
  r /= 255
  g /= 255
  b /= 255
  const max = Math.max(r, g, b),
    min = Math.min(r, g, b)
  let h,
    s,
    l = (max + min) / 2

  if (max === min) {
    h = s = 0 // 消色差
  } else {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0)
        break
      case g:
        h = (b - r) / d + 2
        break
      case b:
        h = (r - g) / d + 4
        break
    }
    h /= 6
  }

  return [h * 360, s * 100, l * 100, a]
}

function generateSimilarColors(baseColor, n) {
  // 从基本颜色中提取RGBA值
  const rgba = baseColor.match(
    /rgba?\((\d+),\s*(\d+),\s*(\d+),?\s*([\d\.]+)?\)/,
  )
  const r = parseInt(rgba[1], 10)
  const g = parseInt(rgba[2], 10)
  const b = parseInt(rgba[3], 10)
  const a = parseFloat(rgba[4]) || 1

  // 将基色转换为HSL
  const [h, s, l] = rgbaToHsl(r, g, b, a)

  // 生成相似的颜色
  const colors: any = []
  for (let i = 0; i < n; i++) {
    const newH = (h + getRandomInt(-10, 10)) % 360
    const newS = Math.min(Math.max(s + getRandomInt(-10, 10), 0), 100)
    const newL = Math.min(Math.max(l + getRandomInt(-10, 10), 0), 100)
    colors.push(hslToRgba(newH, newS, newL))
  }

  return colors
}
function processItemColor(dataArr) {
  let mainColors = [
    "rgba(24, 159, 144, 1)",
    "rgba(103, 188, 107, 1)",
    "rgba(126, 87, 194, 1)",
    "rgba(238, 83, 79, 1)",
    "rgba(67, 164, 243, 1)",
  ]
  dataArr.forEach((level1, index1) => {
    let itemColors = generateSimilarColors(
      mainColors[index1 % mainColors.length],
      level1.children.length,
    )
    level1.children.forEach((level2, index2) => {
      level2.itemStyle = {
        color: itemColors[index2],
      }
    })
  })
  return dataArr
}
async function initData() {
  try {
    loading = true
    const res =
      radio == 1
        ? await getStartData({ bookId: route.query.bookId })
        : await getKnowlegeData({ bookId: route.query.bookId })
    loading = false
    if (res && res.length) {
      let data = processItemColor(res)
      isEmpty = false
      chartOption = {
        title: {
          left: "center",
        },
        tooltip: {
          formatter: function (info) {
            const rate = info.data.rate ? info.data.rate : 0
            const path = info.data.path || "无数据"
            return [
              '<div class="tooltip-title">' + path + "</div>",
              "<div>占比" + rate + "%</div>",
            ].join("")
          },
        },
        series: [
          {
            width: "100%",
            height: "100%",
            name: "星空图预览",
            type: "treemap",
            visibleMin: 300,
            labelLayout: function (params) {
              if (params.dataIndex) {
                return {
                  dy: -params.labelRect.height / 2,
                }
              }
            },
            upperLabel: {
              show: true,
              height: 30,
            },
            itemStyle: {
              borderColor: "#fff",
            },
            levels: [
              {
                itemStyle: {
                  borderWidth: 0,
                  gapWidth: 8,
                },
                upperLabel: {
                  show: false,
                },
              },
              {
                itemStyle: {
                  gapWidth: 1,
                },
              },
              {
                colorSaturation: [0.35, 0.5],
                itemStyle: {
                  gapWidth: 1,
                  borderColorSaturation: 0.6,
                },
              },
            ],
            data: data || [],
          },
        ],
      }
      await nextTick()
      chartRef.mergeOptions({
        title: {
          left: "center",
        },
        tooltip: {
          formatter: function (info) {
            const rate = info.data.rate ? info.data.rate : 0
            const path = info.data.path || "无数据"
            return [
              '<div class="tooltip-title">' + path + "</div>",
              "<div>占比" + rate + "%</div>",
            ].join("")
          },
        },
        series: [
          {
            width: "100%",
            height: "100%",
            name: "星空图预览",
            type: "treemap",
            visibleMin: 300,
            labelLayout: function (params) {
              if (params.dataIndex) {
                return {
                  dy: -params.labelRect.height / 2,
                }
              }
            },
            label: {
              show: true,
              position: ["50%", "50%"],
              align: "center",
              verticalAlign: "top",
              formatter: function (params) {
                const name = params.data.name || "无数据"
                const rate = params.data.rate || 0
                const chartView = chartRef?.chart?._chartsViews[0]
                const treeRoot = chartView.seriesModel._viewRoot
                const node = treeRoot.hostTree._nodes[params.dataIndex]
                const nodeLayout = node.getLayout()
                const data = params.data.children || []
                let arr: any = []
                // 不同尺寸展示不同字体大小
                if (nodeLayout.width >= 25 && nodeLayout.height >= 12) {
                  if (nodeLayout.width > 300 && nodeLayout.height > 65) {
                    arr = ["{myStyle1|" + name + "}", "{myRate1|" + rate + "%}"]
                  } else if (
                    nodeLayout.width >= 200 &&
                    nodeLayout.height > 53
                  ) {
                    arr = ["{myStyle2|" + name + "}", "{myRate2|" + rate + "%}"]
                  } else if (
                    nodeLayout.width >= 100 &&
                    nodeLayout.height > 38
                  ) {
                    arr = ["{myStyle3|" + name + "}", "{myRate3|" + rate + "%}"]
                  } else {
                    arr = ["{myStyle4|" + name + "}", "{myRate4|" + rate + "%}"]
                  }
                }
                return !data.length ? arr.join("\n") : name
              },
              rich: {
                myStyle1: {
                  fontSize: 32,
                },
                myRate1: {
                  fontSize: 24,
                },
                myStyle2: {
                  fontSize: 24,
                },
                myRate2: {
                  fontSize: 20,
                },
                myStyle3: {
                  fontSize: 20,
                },
                myRate3: {
                  fontSize: 16,
                },
                myStyle4: {
                  fontSize: 16,
                },
                myRate4: {
                  fontSize: 13,
                },
              },
            },
            upperLabel: {
              show: true,
              height: 30,
            },
            itemStyle: {
              borderColor: "#fff",
            },
            levels: [
              {
                itemStyle: {
                  borderWidth: 0,
                  gapWidth: 8,
                },
                upperLabel: {
                  show: false,
                },
              },
              {
                itemStyle: {
                  gapWidth: 1,
                },
              },
              {
                colorSaturation: [0.35, 0.5],
                itemStyle: {
                  gapWidth: 1,
                  borderColorSaturation: 0.6,
                },
              },
            ],
            data: data || [],
          },
        ],
      })
      chartRef?.chart?.resize()
    } else {
      loading = false
      isEmpty = true
    }
  } catch (err) {
    isEmpty = true
  }
}
</script>
<style scoped lang="scss">
.echart {
  height: calc(100vh - 100px);
}
</style>
