<template>
  <div>
    <g-dialog
      title="预览"
      v-model:show="showDialog"
      :show-footer="false"
      :width="1000"
    >
      <div class="relative py-20px">
        <div class="flex justify-between items-center">
          <div
            v-if="imgList.length > 1"
            class="button flex-cc flex-shrink-0 text-center leading-[160px]"
            @click="showPrev"
          >
            <g-icon name="arrow-left-s-line" size="40" color="#aaa" />
          </div>
          <div v-else class="w-60px"></div>

          <div class="mx-[16px] slider-container flex-1 flex py-5px">
            <div
              v-for="img in imgList"
              :key="img"
              class="slider"
              :style="{ transform: `translateX(${translateValue})` }"
            >
              <div class="imgItem">
                <img :src="img" alt="" class="w-full h-[460px]" />
              </div>
            </div>
          </div>
          <div
            v-if="imgList.length > 1"
            class="button flex-cc flex-shrink-0 text-center leading-[160px]"
            @click="showNext"
          >
            <g-icon name="arrow-right-s-line" size="40" color="#aaa" />
          </div>
          <div v-else class="w-60px"></div>
        </div>
        <div class="absolute bottom-[-10px] right-100px">
          {{ curIndex + 1 }} / {{ imgList.length }}
        </div>
      </div>
    </g-dialog>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  images: {
    type: Array,
    default: () => [],
  },
})
let curIndex = $ref(0)
let translateValue = ref(`-${curIndex * 100}%`)
let imgList = $computed<any>(() => props.images)
const emit = defineEmits(["update:show"])
const showDialog = useVModel(props, "show", emit)
const showPrev = () => {
  if (curIndex === 0) {
    $g.msg("已经是第一张了", "warning")
    return
  }
  curIndex = (curIndex - 1 + imgList.length) % imgList.length
  translateValue.value = `-${curIndex * 100}%`
}
const showNext = () => {
  if (curIndex === imgList.length - 1) {
    $g.msg("已经是最后一张了", "warning")
    return
  }
  curIndex = (curIndex + 1) % imgList.length
  translateValue.value = `-${curIndex * 100}%`
}
watch(
  () => props.show,
  (val) => {
    if (!val) {
      curIndex = 0
      imgList = []
      translateValue.value = "0"
    }
  },
)
</script>

<style lang="scss" scoped>
.button {
  width: 60px;
  height: 160px;
  background: rgba(51, 152, 247, 0.12);
  border-radius: 8px;
  cursor: pointer;
}
.imgItem {
  // box-shadow: 5px 5px 15px 5px rgba(222, 222, 222, 0.5);
  width: 800px;
  height: 460px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 0 0 8px 8px;
  position: relative;
}
.bgImg {
  height: 40px;
  width: 800px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  // box-shadow: 5px 5px 15px 5px rgba(222, 222, 222, 0.5);
  position: relative;
}
.slider-container {
  width: 800px;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
}

.slider {
  transition: transform 0.5s ease-in-out;
}
</style>
