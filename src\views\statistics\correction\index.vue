<template>
  <div class="correction-container-main">
    <g-form
      @search="search"
      @reset="search"
      :formOptions="filterFormOptions"
      :tableOptions="tableOptions"
    >
    </g-form>
    <g-table
      :tableOptions="tableOptions"
      :loading="tableOptions.loading"
      @changePage="initData"
    >
      <template #img="{ row }">
        <n-space justify="center">
          <n-button
            type="primary"
            v-if="row.imageList?.length"
            text
            @click="openPreview(row.imageList)"
            >点击预览</n-button
          >
          <div v-else>-</div>
        </n-space>
      </template>
      <template #questionStatus="{ row }">
        <div
          :class="{
            'text-success': row.state == 1,
          }"
        >
          {{ questionStatus[row.state] }}
        </div>
      </template>
      <template #cz="{ row }">
        <div>
          <n-space justify="center" v-if="![3, 4].includes(row.state)">
            <n-button
              text
              type="primary"
              @click="
                $router.push({
                  name: 'QuestionEdit',
                  query: {
                    questionAmendId: row.questionAmendId,
                  },
                })
              "
              >{{ row.state != 1 ? "前往修正" : "再次修正" }}</n-button
            >
          </n-space>
        </div>
      </template>
    </g-table>
    <PreviewDialog v-model:show="showDialog" :images="images" />
  </div>
</template>
<script setup lang="ts" name="StatisticsIndex">
import {
  getErrorTypeList,
  getSchoolSelectList,
  getQuestionCorrectionList,
  getQuestionStatus,
} from "@/api/statistics"
import PreviewDialog from "./components/PreviewDialog.vue"
let showDialog = $ref(false)
const filterFormOptions = reactive<any>({
  ref: null as any,
  filter: true,
  items: {
    errorType: {
      type: "select",
      label: "错误类型",
      showLabel: false,
      options: [],
    },
    schoolId: {
      type: "select",
      label: "学校",
      showLabel: false,
      options: [],
    },
    state: {
      type: "select",
      label: "题目状态",
      showLabel: false,
      options: [],
    },
    accountAdminName: {
      type: "text",
      label: "修正人",
      showLabel: false,
    },
    questionId: {
      type: "number",
      label: "题目ID",
      showButton: false,
      showLabel: false,
    },
    correctionTimeRange: {
      type: "daterange",
      label: "纠错时间",
    },
    fixTimeRange: {
      type: "daterange",
      label: "修正时间",
    },
  },
  // 列表接口使用到的筛选值,及时搜索时使用
  data: {
    errorType: null,
    schoolId: null,
    questionId: null,
    accountAdminName: null,
    correctionTimeRange: null,
    fixTimeRange: null,
    state: null,
  },
  // 列表接口使用到的筛选值,非及时搜索时使用
  filterData: {
    errorType: null,
    schoolId: null,
    questionId: null,
    accountAdminName: null,
    correctionTimeRange: null,
    fixTimeRange: null,
    state: null,
  },
})
const questionStatus = {
  0: "待修正",
  1: "已修正",
}
let images = $ref<any>([])
const tableOptions = reactive({
  ref: null as any,
  key: "",
  loading: false,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
  column: [
    {
      prop: "questionId",
      label: "题目ID",
      width: "100px",
    },
    {
      prop: "errorTypeString",
      label: "错误类型",
    },
    {
      prop: "sysCourseName",
      label: "科目",
    },
    { prop: "description", label: "错误描述", width: "400px" },
    { prop: "img", label: "图片", slot: true },
    { prop: "studentName", label: "纠错学生" },
    {
      prop: "schoolName",
      label: "所在学校-年级-班级",
      formatter: (row: any) => {
        return `${row.schoolName}-${row.sysGradeName}-${row.className || ""}`
      },
      width: "200px",
    },
    { prop: "updateTime", label: "纠错时间", width: "200px" },
    {
      prop: "questionStatus",
      label: "题目状态",
      slot: true,
      width: "100px",
    },
    { prop: "accountAdminName", label: "修正人" },
    { prop: "amendTime", label: "修正时间", width: "200px" },
    { prop: "cz", label: "操作", slot: true, width: "250px", fixed: "right" },
  ],

  data: [],
})
/* 打开预览 */
const openPreview = (list) => {
  images = list
  showDialog = true
}
/* 查询 */
function search() {
  tableOptions.pageOptions.page = 1
  initData()
}

/* 获取列表数据 */
const initData = async () => {
  tableOptions.loading = true

  const params = {
    ...filterFormOptions.filterData,
    amendStartTime: filterFormOptions.filterData.correctionTimeRange?.[0],
    amendEndTime: filterFormOptions.filterData.correctionTimeRange?.[1],
    correctingStartTime: filterFormOptions.filterData.fixTimeRange?.[0],
    correctingEndTime: filterFormOptions.filterData.fixTimeRange?.[1],
  }
  delete params.correctionTimeRange
  delete params.fixTimeRange

  let res = await getQuestionCorrectionList({
    ...params,
    page: tableOptions.pageOptions.page,
    pageSize: tableOptions.pageOptions.page_size,
  })
  tableOptions.loading = false
  tableOptions.data = res.list
  tableOptions.pageOptions.total = res.total
}

/* 错误类型 */
const getErrorTypeListApi = async () => {
  let res = await getErrorTypeList()
  filterFormOptions.items.errorType.options = res.map((v) => {
    return {
      label: v.title,
      value: v.id,
    }
  })
}
/* 学校 */
const getSchoolSelectListApi = async () => {
  let res = await getSchoolSelectList()
  filterFormOptions.items.schoolId.options = res.map((v) => {
    return {
      label: v.schoolName,
      value: v.schoolId,
    }
  })
}

/*  题目状态下拉框*/
const getQuestionStatusApi = async () => {
  let res = await getQuestionStatus()
  filterFormOptions.items.state.options = res.map((v) => {
    return {
      label: v.title,
      value: v.id,
    }
  })
  filterFormOptions.filterData.state = filterFormOptions.data.state =
    filterFormOptions.items.state.options?.find((v) => v.value == 0)?.value
}

onMounted(async () => {
  await getQuestionStatusApi()
  getErrorTypeListApi()
  getSchoolSelectListApi()
  initData()
})
</script>
