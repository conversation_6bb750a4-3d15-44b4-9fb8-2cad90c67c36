<template>
  <div class="flex">
    <div
      v-for="(item, index) in data"
      :key="index"
      class="rounded-sm px-8px text-[#606266] mr-10px mb-10px cursor-pointer text-14px h-28px leading-[28px]"
      :class="[{ active: value == item[props.value] }]"
      @click="changeValue(item[props.value])"
    >
      {{ item[props.label] }}
    </div>
  </div>
</template>

<script>
/* 单选按钮组 用来解决单选前置事件 */
export default {
  props: {
    value: {
      type: [String, Number],
      default: "",
    },
    data: {
      type: Array,
      default: () => {
        return []
      },
    },
    props: {
      type: Object,
      default: () => {
        return {
          label: "label",
          value: "value",
        }
      },
    },
    // 是否自动改变值
    auto: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {}
  },
  methods: {
    changeValue(id) {
      if (this.auto) {
        this.$emit("input", id)
        this.$emit("change", id)
      } else {
        this.$emit("beforeChange", () => {
          this.$emit("input", id)
          this.$emit("change", id)
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.active {
  color: #fff;
  background: var(--g-primary);
}
</style>
