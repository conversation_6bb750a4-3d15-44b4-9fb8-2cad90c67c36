<template>
  <div class="text-14px">
    <div class="flex justify-between items-center">
      <div class="flex">
        <n-radio-group v-model:value="current">
          <n-radio-button
            v-for="(item, index) in subQuestion"
            :key="item.subQuestionType || index + '小题'"
            :value="index"
            type="primary"
          >
            小题{{ index + 1 }}
          </n-radio-button>
        </n-radio-group>
      </div>
    </div>
    <div class="border border-gray-light px-16px py-12px mt-[10px]">
      <!-- 问题描述 -->
      <div>
        <div class="flex justify-between mt-18px mb-10px">
          <div class="text-16px">问题描述</div>
          <div></div>
        </div>
        <g-ueditor v-model="subQuestion[current].subQuestionTitle"></g-ueditor>
      </div>
      <!-- 答案描述 3种情况 选择题/其他题   判断题没有答案描述只有答案  -->
      <div>
        <div class="flex justify-between mt-18px">
          <div>答案描述</div>
          <div v-if="[1, 2].includes(subQuestion[current].subQuestionType)">
            <n-button
              type="success"
              size="small"
              @click="increaseOrDecreaseOptions('add')"
              class="mr-[10px]"
            >
              <template #icon>
                <g-icon name="ri-add-fill" size="" color="" />
              </template>
              添加选项</n-button
            >

            <n-button
              type="error"
              size="small"
              @click="increaseOrDecreaseOptions('edit')"
            >
              <template #icon>
                <g-icon name="ri-delete-bin-line" size="14" color="" />
              </template>
              删除选项
            </n-button>
          </div>
        </div>
        <!-- 选择题 -->
        <div v-if="[1, 2].includes(subQuestion[current].subQuestionType)">
          <MyCheckbox
            v-for="item in subQuestion[current].options"
            :key="item.label"
            v-model:model="subQuestion[current].optionsAnswer"
            :value="item.label"
            :max="subQuestion[current].subQuestionType == 1 ? 1 : 999"
            :allow-label-click="false"
          >
            <div class="flex items-center">
              <div class="mr-10px">{{ item.label }}</div>
              <g-ueditor
                v-model="item.value"
                :config="{ initialFrameHeight: 80 }"
                class="w-[1000px]"
              ></g-ueditor>
            </div>
          </MyCheckbox>
        </div>
        <!-- 判断题 -->

        <div
          class="flex items-center"
          v-else-if="subQuestion[current].subQuestionType == 3"
        >
          <MyCheckbox
            v-for="item in subQuestion[current].options"
            :key="item.value"
            v-model:model="subQuestion[current].optionsAnswer"
            :value="item.value"
            :max="1"
            :allow-label-click="false"
          >
            <div class="mr-10px">{{ item.key }}</div>
          </MyCheckbox>
        </div>
        <!-- 其他题 -->
        <div
          v-else-if="
            [4, 5, 6, 7].includes(subQuestion[current].subQuestionType)
          "
        >
          <g-ueditor
            v-model="subQuestion[current].subQuestionAnswer"
            class="ml-20px mt-10px"
            :config="{ initialFrameHeight: 120 }"
          ></g-ueditor>
        </div>
      </div>
      <!-- 详细解析 -->
      <div
        v-for="(item, index) in subQuestion[current].subQuestionParseList"
        :key="index"
      >
        <div class="mt-20px mb-10px text-16px flex justify-between">
          <div class="flex items-center gap-x-[15px]">
            <div>
              详细解析 <span v-if="index != 0">({{ index + 1 }})</span>
            </div>
          </div>
          <n-button
            type="primary"
            @click="addAnalysis"
            v-if="index == 0"
            size="small"
          >
            <g-icon name="add-line" size="" color="" />
            增加解析
          </n-button>
          <n-button
            type="error"
            @click="deleteAnalysis(index)"
            v-if="index != 0"
            size="small"
          >
            <g-icon name="delete-bin-line" size="" color="" />
            删除解析
          </n-button>
        </div>
        <g-ueditor
          v-model="item.content"
          :config="{ initialFrameHeight: 120 }"
          @click.prevent
        ></g-ueditor>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import RadioButton from "./RadioButton.vue"
import MyCheckbox from "./MyCheckbox.vue"
import { chooseOption } from "../options"
const props = defineProps({
  subQuestion: {
    type: Array<any>,
    default: () => {
      return []
    },
  },
  deleteId: {
    type: Array,
    default: () => {
      return []
    },
  },
})

let current = $ref(0)

const increaseOrDecreaseOptions = (type) => {
  let subQuestionLength = props.subQuestion[current].options.length
  switch (type) {
    case "add":
      if (subQuestionLength == 10) {
        $g.msg("小题选项最多添加10个", "warning")
      } else {
        let obj = JSON.parse(JSON.stringify(chooseOption[subQuestionLength]))
        props.subQuestion[current].options.push(obj)
      }
      break
    default:
      if (subQuestionLength == 2) {
        $g.msg("小题选项至少要有2个", "warning")
      } else {
        props.subQuestion[current].options.pop()
        $g.msg("删除小题选项成功", "success")
      }
      break
  }
}

/* 新增解析 */
function addAnalysis() {
  if (props.subQuestion[current].subQuestionParseList.length == 8)
    return $g.msg("解析不能超过8个", "warning")
  props.subQuestion[current].subQuestionParseList.push({
    content: "",
  })
}
/* 删除解析 */
function deleteAnalysis(index) {
  props.subQuestion[current].subQuestionParseList.splice(index, 1)
}

// const resetQuestionAnswer = () => {
//   let currentObj = props.subQuestion[current]
//   currentObj.questionAnswer = ""
//   currentObj.subQuestionAnswer = []
//   currentObj.options = JSON.parse(JSON.stringify(chooseOption.slice(0, 4)))
// }
// const beforeChangeSubType = (callback) => {
//   $g.confirm({ content: "您确定要切换当前题型吗？答案信息讲清空！" })
//     .then((res) => {
//       resetQuestionAnswer()
//       callback()
//     })
//     .catch(() => {})
// }
</script>
