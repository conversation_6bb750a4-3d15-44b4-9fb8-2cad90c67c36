<template>
  <div>
    <!-- 主体 -->
    <div class="bg-white rounded-default py-20px mt-20px">
      <div class="text-12px px-40px">
        <!-- 题型行 -->
        <!-- <div class="flex">
          <div class="text-14px leading-[30px] w-70px">题型：</div>
          <el-radio-group
            v-model="mainQuestion.jk_new_question_type_id"
            size="small"
          >
            <el-radio-button
              v-for="item in questionsTypeArr.mainQuestion_type"
              :key="item.jk_new_question_type_id"
              :label="item.jk_new_question_type_id"
            >
              {{ item.jk_new_question_type_name }}
            </el-radio-button>
          </el-radio-group>
        </div> -->
        <!-- 知识点行 -->
        <!-- <div class="flex">
          <div class="text-14px leading-[30px] w-90px" style="flex: 0 0 90px">
            前置知识点：
          </div>
          <div class="flex flex-wrap">
            <n-button
              type="primary"
              size="small"
              class="mr-8px mb-8px"
              @click="chooseKnowledgePoint"
            >
              <template #icon>
                <g-icon name="ri-add-fill" size="" color="" />
              </template>
              添加前置
            </n-button>
            <el-badge
              v-for="(item, index) in mainQuestion.knowledgeName"
              :key="item.knowledge_points_id"
              value="ri-close-line"
              type="danger"
              is-icon
              class="mr-8px mb-8px"
              @click="deleteKnowledge(item.knowledge_points_id, index)"
            >
              <el-button size="small">{{
                item.knowledge_points_name
              }}</el-button>
            </el-badge>
            <div class="ml-20px">
              <span class="text-14px">难度：</span>
              <n-radio-group v-model:value="radio" name="radiogroup">
                <n-radio
                  v-for="item in difficulty"
                  :key="item.id"
                  :value="item.id"
                  class="mr-[10px]"
                >
                  {{ item.title }}
                </n-radio>
              </n-radio-group>
            </div>
          </div>
        </div> -->
        <!-- 保存行 -->
        <div
          v-for="book in mainQuestion?.bookList"
          :key="book.bookId"
          class="flex flex-wrap text-14px mb-10px"
        >
          <div @click="toPageBook(book?.jumpUrl)" class="mr-20px">
            <span>所属书籍：</span>
            <span class="cursor-pointer underline text-[#1ea0f0]">{{
              book.bookName
            }}</span>
          </div>

          <div v-if="book.catalogNames?.length">
            <span>所属章节：</span>
            <span v-for="category in book.catalogNames" :key="category">{{
              category
            }}</span>
          </div>
        </div>
        <div class="text-14px font-bold">
          题目ID: {{ mainQuestion?.questionId }}
        </div>
        <div class="flex justify-end">
          <el-button type="success" size="small" @click="saveQuestion"
            >保存修改</el-button
          >
          <el-button type="danger" size="small" @click="saveQuestion(2)">
            保存后退出
          </el-button>
        </div>
        <!-- 题干 -->
        <div class="flex items-center mb-10px">
          <div
            class="pl-10px pb-2px"
            style="border-left: 4px solid rgb(0, 206, 155, 1)"
          >
            题干
          </div>
        </div>
        <!-- 富文本 -->
        <g-ueditor v-model="mainQuestion.questionTitle"></g-ueditor>
        <div class="flex justify-between mt-20px mb-10px">
          <div
            class="pl-10px pb-2px"
            style="border-left: 4px solid rgb(243, 154, 14, 1)"
          >
            小题
          </div>
        </div>
        <div class="pb-40px">
          <SubQuestions
            v-if="mainQuestion.subQuestions?.length"
            :sub-question="mainQuestion.subQuestions"
            :delete-id="deleteId"
          ></SubQuestions>
        </div>
      </div>
    </div>
    <!-- 选择知识点弹窗 -->
    <g-dialog
      width="600"
      title="添加前置知识点"
      v-model:show="showDialog"
      confirm-name="添加"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0 mr-[20px]">搜索</div>
        <n-input
          v-model:value="filterText"
          placeholder="请输入知识点关键字"
        ></n-input>
      </div>
      <n-checkbox-group v-model:value="mainQuestion.knowledgeName">
        <n-checkbox v-for="item in allKnowledge" :key="item.value"></n-checkbox>
      </n-checkbox-group>
    </g-dialog>
  </div>
</template>
<script lang="ts" setup>
import SubQuestions from "./components/SubQuestions.vue"
import { getQuestionDetail, saveQuestionCorrection } from "@/api/statistics"
let showDialog = $ref(false)
let allKnowledge = $ref<any>([])
let radio = $ref(1)

let mainQuestion = $ref<any>({ subQuestions: [] })
let filterText = $ref("")

let deleteId = $ref<any>([])
let questionsTypeArr = $ref<any>({})
let skipRouteGuard = $ref(false)
const router = useRouter()
const route = useRoute()

const saveQuestion = $g._.throttle(
  (num) => {
    savePost(num)
  },
  1500,
  {
    leading: true,
    trailing: false,
  },
)
const savePost = async (num?) => {
  mainQuestion.subQuestions?.forEach((h) => {
    if ([1, 2].includes(h.subQuestionType)) {
      let options = [
        "optionA",
        "optionB",
        "optionC",
        "optionD",
        "optionE",
        "optionF",
        "optionG",
        "optionH",
        "optionI",
        "optionJ",
      ]
      options.forEach((key) => (h[key] = null))
      h.options.forEach((v) => {
        h[`option${v.label}`] = v.value
      })
      h.optionNumbers = h.options.length
    }
    if ([1, 2, 3].includes(h.subQuestionType)) {
      h.subQuestionAnswer = h.optionsAnswer.join("")
    }
    h.subQuestionParseList = h.subQuestionParseList.filter(
      (vv) => vv.content.length,
    )
    h.subQuestionParse = h.subQuestionParseList[0].content
  })
  if (
    !questionVerify({ mainQuestion, sub_question: mainQuestion.subQuestions })
  )
    return

  try {
    await saveQuestionCorrection({
      ...mainQuestion,
    }).then(() => {
      if (num == 2) {
        skipRouteGuard = true
        router.back()
      }
      $g.msg("保存成功")
    })
  } catch (e) {
    console.log(e)
  }
}

/* 题目验证 */
function questionVerify({ mainQuestion, sub_question }) {
  let msg = ""
  $g._.forEach(sub_question, (e, i) => {
    let isTypeID12 = e.subQuestionType == 1 || e.subQuestionType == 2

    // 验证小题题号
    // if (!e.structure_number) {
    //   msg = `第${i + 1}小题：请输入小题题号！`
    //   return false
    // }

    // 如果是选择题 验证选项答案
    if (isTypeID12) {
      let flag = $g._.every(e.options, (e2) => {
        if (!e2.value) {
          msg = `第${i + 1}小题：请输入答案(${e2.label})！`
        }
        return e2.value
      })
      if (!flag) return false
    }

    // 验证详细解析
    if (!e.subQuestionParseList[0].content) {
      msg = `第${i + 1}小题：请输入详细解析！`
      return false
    }
    // 验证选择题 答案个数
    if ((isTypeID12 || e.subQuestionType == 3) && !e.optionsAnswer.length) {
      msg = `第${i + 1}小题：请选择正确答案！`
      return false
    }
  })
  if (!mainQuestion.questionTitle) {
    msg = "请输入题干！"
  }
  if (!msg) return true
  $g.msg(msg, "warning")
}

async function initData() {
  let { questionAmendId } = route.query
  let res = await getQuestionDetail({
    questionAmendId,
  })
  res.subQuestions?.forEach((h) => {
    h.options =
      h.subQuestionType == 3
        ? [
            {
              key: "√",
              value: "√",
            },
            {
              key: "×",
              value: "×",
            },
          ]
        : Object.keys(h)
            .filter((key) => key.includes("option") && key !== "optionNumbers")
            .map((realKey) => {
              return {
                label: realKey.replace("option", ""),
                value: h[realKey],
              }
            })
            .filter((v) => v.value)
    if ([1, 2].includes(h.subQuestionType))
      h.optionsAnswer = h.subQuestionAnswer?.split("") ?? []
    if (h.subQuestionType == 3) {
      if (/[\u4e00-\u9fa5]/.test(h.subQuestionAnswer)) {
        if (h.subQuestionAnswer.includes("错")) {
          h.optionsAnswer = ["×"]
        } else {
          h.optionsAnswer = ["√"]
        }
      } else {
        if (h.subQuestionAnswer.includes("√")) {
          h.optionsAnswer = ["√"]
        } else {
          h.optionsAnswer = ["×"]
        }
      }
    }
  })
  mainQuestion = res
}

/**
 * 跳转到书籍
 */
function toPageBook(url) {
  const pageUrl = `${location.origin}/#${url}`
  window.open(pageUrl, "_blank")
}

onBeforeMount(() => {
  initData()
  window.addEventListener("beforeunload", handleBeforeUnload)
})
onBeforeRouteLeave((to, from, next) => {
  if (skipRouteGuard) {
    next()
    return
  }

  const confirmationMessage = "你编辑的内容尚未保存是否离开?"
  if (window.confirm(confirmationMessage)) {
    next()
  } else {
    next(false)
  }
})
const handleBeforeUnload = (event: any) => {
  const confirmationMessage = "你编辑的内容尚未保存是否离开?"
  event.returnValue = confirmationMessage
  return confirmationMessage
}
onBeforeUnmount(() => {
  window.removeEventListener("beforeunload", handleBeforeUnload)
})
</script>
