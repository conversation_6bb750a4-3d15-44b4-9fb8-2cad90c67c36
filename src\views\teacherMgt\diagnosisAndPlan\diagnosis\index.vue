<template>
  <div class="diagnosis-container-main">
    <div class="flex justify-between items-center mb-[8px]">
      <div>
        <el-radio-group
          v-show="route.name == 'Diagnosis'"
          v-model="activeType"
          @change="getPromptListApi"
        >
          <el-radio-button
            :label="it.label"
            :value="it.value"
            v-for="it in diagnosisTypeList"
            :key="it.label"
          />
        </el-radio-group>
      </div>

      <n-button type="primary" @click="showDialog = true">
        <g-icon name="add-line" size="" color="" />
        新增提示词
      </n-button>
    </div>

    <g-loading class="h-200px" v-if="showLoading"></g-loading>
    <div v-else class="max-h-[calc(100vh-180px)] overflow-auto">
      <template v-if="cueWordList.length">
        <div
          v-for="it in cueWordList"
          :key="it.modelPromptId"
          class="mt-[15px]"
        >
          <div
            class="px-[20px] py-[10px] bg-[#eeeded] rounded-[6px] cursor-pointer"
          >
            <el-row :gutter="24" align="middle">
              <el-col :span="4">
                <div>ID:{{ it?.serialNumber || "-" }}</div>
              </el-col>
              <el-col :span="5">
                <div>名称:{{ it?.title || "-" }}</div>
              </el-col>
              <el-col :span="4">
                <div>模型:{{ it?.modelName || "-" }}</div>
              </el-col>
              <el-col :span="6">
                <div>更新时间:{{ it?.updateTime || "-" }}</div>
              </el-col>
              <el-col :span="2">
                <div style="border-right: 1px solid #999">
                  应用
                  <el-switch
                    v-model="it.state"
                    @change="changeStateApi(it)"
                    :active-value="2"
                    :inactive-value="1"
                  />
                </div>
              </el-col>
              <el-col :span="3">
                <div class="flex justify-end">
                  <el-button text type="primary" @click="editCueWord(it)"
                    >编辑内容</el-button
                  >
                  <el-button text type="primary" @click="deletePromptApi(it)"
                    >删除</el-button
                  >
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>
      <g-empty v-else></g-empty>
    </div>

    <!-- 新增编辑提示词 -->
    <n-drawer
      v-model:show="showDialog"
      width="800"
      placement="right"
      :trap-focus="false"
      destroy-on-close
      :on-after-leave="handleClose"
    >
      <n-drawer-content closable>
        <template #header>
          <div class="flex items-center justify-between w-[600px]">
            <div class="font-[500] text-[#333] text-[18px]">
              {{ modelPromptId ? "编辑" : "新增" }}提示词
            </div>
          </div>
        </template>
        <g-form :formOptions="formOptions">
          <template #prompt>
            <g-markdown
              class="w-full"
              height="450px"
              v-model="formOptions.data.prompt"
            ></g-markdown>
          </template>
        </g-form>
        <template #footer>
          <div class="flex gap-[15px] justify-end">
            <n-button @click="showDialog = false">取消</n-button>
            <n-button
              type="primary"
              @click="confirm"
              :loading="formOptions.loading"
              >确定</n-button
            >
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
import {
  getPromptList,
  addPrompt,
  getScenarioList,
  getModelList,
  deletePrompt,
  changeState,
  getPromptDetail,
  updatePromptDetail,
} from "@/api/teacherMgt"

enum ModuleType {
  Diagnosis = 1,
  StudyPlan = 2,
}
const route = useRoute()
let diagnosisTypeList = $ref<Array<{ label: string; value: number }>>([
  { label: "检测数据", value: 1 },
  { label: "报告模版", value: 2 },
])
let activeType = $ref<number>(1)
let showLoading = $ref<boolean>(true)
let showDialog = $ref<boolean>(false)
let modelPromptId = $ref<number | string>("")
let cacheInfo = $ref<any>({})
let formOptions = reactive({
  loading: false,
  ref: "baseInfo" as any,
  mode: "edit",
  labelWidth: "115px",
  items: {
    scenarioType: {
      type: "select",
      clearable: false,
      label: "分类",
      options: [],
      labelField: "title",
      valueField: "id",
      rule: true,
      disabled: false,
      placeholder: "请选择模型",
    },
    promptName: {
      type: "text",
      label: "prompt名称",
      maxlength: 10,
      showCount: true,
      rule: true,
    },

    promptType: {
      type: "select",
      clearable: false,
      label: "模型选择",
      options: [],
      labelField: "title",
      valueField: "modelId",
      rule: true,
    },
    remarks: {
      type: "textarea",
      label: "备注",
      maxlength: 30,
      showCount: true,
    },
    prompt: {
      type: "textarea",
      label: "prompt",
      rows: 6,
      rule: true,
      slot: true,
    },
  },
  data: {
    promptName: "",
    promptType: null,
    remarks: "",
    prompt: "",
    scenarioType: null,
  },
})
let cueWordList = $ref<any>([])

async function changeStateApi(obj) {
  // 如果是要打开（state=2），先关闭其他已打开项
  if (obj.state === 2) {
    // 找到所有已打开且不是当前项的项
    const openedArr = cueWordList.filter(
      (it) => it.state === 2 && it.modelPromptId !== obj.modelPromptId,
    )
    for (const item of openedArr) {
      await changeState({ modelPromptId: item.modelPromptId, state: 1 })
      item.state = 1
    }
  }
  // 切换当前项的状态
  await changeState({ modelPromptId: obj?.modelPromptId, state: obj?.state })
}
async function editCueWord(obj) {
  modelPromptId = obj?.modelPromptId
  formOptions.items.scenarioType.disabled = true
  showDialog = true
  let res = await getPromptDetail({ modelPromptId: obj.modelPromptId })
  formOptions.data.promptName = res?.title ?? ""
  formOptions.data.promptType = res?.modelId ?? ""
  formOptions.data.remarks = res?.remark ?? ""
  formOptions.data.prompt = res?.prompt ?? ""
  formOptions.data.scenarioType = res?.scenarioType ?? ""
  cacheInfo = $g._.cloneDeep(formOptions.data)
}
function deletePromptApi(obj) {
  $g.confirm({
    content: "是否删除该条Prompt",
  }).then(async () => {
    await deletePrompt({ modelPromptId: obj?.modelPromptId })
    $g.msg("操作成功")
    getPromptListApi()
  })
}
function handleClose() {
  formOptions.data.promptName = ""
  formOptions.data.promptType = null
  formOptions.data.remarks = ""
  formOptions.data.prompt = ""
  formOptions.data.scenarioType = null
  formOptions.items.scenarioType.disabled = false
  modelPromptId = ""
}
async function confirm() {
  try {
    formOptions.ref?.validate(async (errors) => {
      if (errors?.length) return
      if ($g.tool.deepEqual(cacheInfo, formOptions.data)) {
        showDialog = false
        formOptions.loading = false
        return
      }
      formOptions.loading = true
      const URL = modelPromptId ? updatePromptDetail : addPrompt
      let params = {
        moduleType: ModuleType[route?.name ?? "Diagnosis"],
        scenarioType: route.name == "Diagnosis" ? activeType : 3,
        title: formOptions.data.promptName,
        modelId: formOptions.data.promptType,
        remark: formOptions.data.remarks,
        prompt: formOptions.data.prompt,
      }
      if (modelPromptId) params["modelPromptId"] = modelPromptId
      await URL(params)
      showDialog = false
      setTimeout(() => {
        formOptions.loading = false
      }, 800)
      getPromptListApi()
    })
  } catch (error) {
    formOptions.loading = false
    console.log(error)
  }
}
//获取提示词列表
async function getPromptListApi() {
  try {
    showLoading = true
    let res = await getPromptList({
      moduleType: ModuleType[route?.name ?? "Diagnosis"],
      scenarioType: route.name == "Diagnosis" ? activeType : 3,
    })
    cueWordList = res ?? []
  } catch (error) {
    console.log(error)
  } finally {
    showLoading = false
  }
}
//大模型数据
async function getModelListApi() {
  let res = await getModelList()
  formOptions.items.promptType.options = res ?? []
}
//场景
async function getScenarioListApi() {
  let res = await getScenarioList({
    moduleType: ModuleType[route?.name ?? "Diagnosis"],
  })
  formOptions.items.scenarioType.options = res ?? []
}

onMounted(async () => {
  await getModelListApi()
  await getScenarioListApi()
  getPromptListApi()
})
</script>

<style scoped></style>
