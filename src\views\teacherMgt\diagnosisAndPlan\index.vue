<template>
  <div class="diagnosis-plan-container-main">
    <div class="text-[500] text-[16px] mb-[10px]">题库管理</div>
    <div class="flex flex-wrap gap-[20px]">
      <g-img
        v-for="it in routeCardList"
        :key="it.label"
        width="240"
        height="150"
        object-fit="contain"
        :src="it?.img"
        :previewDisabled="true"
        class="cursor-pointer"
        @click="goPage(it.route)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const router = useRouter()

let routeCardList = $ref<Array<{ label: string; route: string; img: string }>>([
  {
    label: "诊断与报告",
    route: "Diagnosis",
    img: $g.tool.getFileUrl("teacherMgt/diagnosis.png"),
  },
  {
    label: "学习计划",
    route: "StudyPlan",
    img: $g.tool.getFileUrl("teacherMgt/studyPlan.png"),
  },
])

function goPage(name) {
  if (!name) {
    $g.msg("该功能尚未开发，请等候！", "warning")
    return
  }
  router.push({ name })
}
</script>

<style lang="scss" scoped></style>
