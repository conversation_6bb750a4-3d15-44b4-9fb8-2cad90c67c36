<template>
  <div class="learn-task-mgt-container-main">
    <g-form
      :formOptions="filterFormOptions"
      @search="getList"
      @reset="getList"
      :tableOptions="tableOptions"
    >
    </g-form>
    <g-table :tableOptions="tableOptions" @changePage="getList">
      <template #integratedTaskCheck="{ row, item }">
        <n-switch
          :value="row.integratedTaskCheck == 2"
          @update:value="updateSwitch(row, item.prop)"
        />
      </template>
      <template #resourceWriteCheck="{ row, item }">
        <n-switch
          :value="row.resourceWriteCheck == 2"
          @update:value="updateSwitch(row, item.prop)"
        />
      </template>
      <template #resourceReadCheck="{ row, item }">
        <n-switch
          :value="row.resourceReadCheck == 2"
          @update:value="updateSwitch(row, item.prop)"
        />
      </template>
      <template #questionOneOneCheck="{ row, item }">
        <n-switch
          :value="row.questionOneOneCheck == 2"
          @update:value="updateSwitch(row, item.prop)"
        />
      </template>
      <template #cz="{ row }">
        <n-button type="primary" text @click="checkRecord(row)"
          >操作记录</n-button
        >
      </template>
    </g-table>
  </div>
</template>
<script setup lang="ts" name="LearnTaskMgt">
import {
  getLearnTaskMgtList,
  changeLearnTaskStatus,
  getTianliSchoolList,
} from "@/api/teacherMgt"
const router = useRouter()
const filterFormOptions = reactive<any>({
  ref: null as any,
  filter: true,
  labelWidth: "60px",
  items: {
    schoolId: {
      type: "select",
      label: "学校",
      options: [],
      labelField: "schoolName",
      valueField: "schoolId",
      width: "250px",
    },
  },
  // 列表接口使用到的筛选值,及时搜索时使用
  data: {
    schoolId: null,
  },
  // 列表接口使用到的筛选值,非及时搜索时使用
  filterData: {},
})
const tableOptions = reactive({
  ref: null,
  loading: false,
  column: [
    {
      prop: "schoolName",
      label: "学校名称",
    },
    {
      prop: "integratedTaskCheck",
      label: "综合任务",
      slot: true,
    },
    {
      prop: "resourceWriteCheck",
      label: "资源-默写模式",
      slot: true,
    },
    {
      prop: "resourceReadCheck",
      label: "资源-阅题模式",
      slot: true,
    },
    {
      prop: "questionOneOneCheck",
      label: "题目-举一反一模式",
      slot: true,
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [] as any,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})

const CHECK_TYPES = {
  integratedTaskCheck: 1,
  resourceWriteCheck: 2,
  resourceReadCheck: 3,
  questionOneOneCheck: 4,
}

onMounted(async () => {
  await fetchSchoolApi()
  await getList()
})

async function fetchSchoolApi() {
  let list = await getTianliSchoolList()
  filterFormOptions.items.schoolId.options = list
}

async function getList() {
  try {
    tableOptions.loading = true
    let { list = [], total } = await getLearnTaskMgtList({
      schoolId: filterFormOptions.data.schoolId,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.data = list
    tableOptions.pageOptions.total = total
    tableOptions.loading = false
  } catch (err) {
    tableOptions.loading = false
  }
}

function updateSwitch(row: any, type: string) {
  row[type] = row[type] == 2 ? 1 : 2
  changeLearnTaskStatus({
    schoolId: row.schoolId,
    checkType: CHECK_TYPES[type],
    isCheck: row[type],
  })
}

function checkRecord(row: any) {
  router.push({
    name: "LearnTaskMgtRecord",
    query: {
      schoolId: row.schoolId,
    },
  })
}
</script>
