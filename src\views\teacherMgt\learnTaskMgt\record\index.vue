<template>
  <div class="learn-task-record-container-main">
    <g-table :tableOptions="tableOptions" @changePage="getRecordListApi">
    </g-table>
  </div>
</template>
<script setup lang="ts">
import { learnTaskHistoryList } from "@/api/teacherMgt"
const router = useRouter()
const route = useRoute()
const tableOptions = reactive({
  ref: null,
  loading: false,
  column: [
    {
      prop: "accountName",
      label: "修改人",
    },
    {
      prop: "updateContent",
      label: "修改内容",
    },
    {
      prop: "updateTime",
      label: "修改时间",
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 0,
  },
})

onMounted(() => {
  getRecordListApi()
})

async function getRecordListApi() {
  try {
    tableOptions.loading = true

    const { list = [], total } = await learnTaskHistoryList({
      schoolId: route.query.schoolId,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.data = list
    tableOptions.pageOptions.total = total
    tableOptions.loading = false
  } catch (error) {
    tableOptions.loading = false
    console.log(error)
  }
}
</script>
