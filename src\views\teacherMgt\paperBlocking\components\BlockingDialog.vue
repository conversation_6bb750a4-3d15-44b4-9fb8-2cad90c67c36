<template>
  <g-dialog
    title="搜索结果"
    v-model:show="showDialog"
    width="1200"
    :on-after-enter="onAfterEnter"
    @handleClose="onAfterLeave"
    :show-footer="false"
  >
    <g-table :tableOptions="tableOptions">
      <template #header-left>
        <div class="text-error">
          屏蔽考试后该考试关联的所有学生将看不到该场考试信息
        </div>
      </template>
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button
            type="error"
            v-if="row.isDelete == 1"
            text
            @click="shieldExamApi(row, 2)"
            >取消屏蔽</n-button
          >
          <n-button type="primary" text @click="shieldExamApi(row, 1)" v-else
            >屏蔽考试</n-button
          >
        </n-space>
      </template>
    </g-table>
  </g-dialog>
</template>

<script setup lang="ts">
import { getExamSearch } from "@/api/teacherMgt"

let showDialog = defineModel<boolean>("showDialog")
const props = defineProps({
  keyword: {
    type: String,
    default: "",
  },
})
const tableOptions = reactive<any>({
  loading: true,
  ref: null as any,
  column: [
    {
      type: "index",
      label: "序号",
    },
    {
      prop: "examName",
      label: "试卷名称",
    },
    {
      prop: "examTypeName",
      label: "考试类型",
    },
    {
      prop: "relatedSchoolCount",
      label: "关联学校数量",
    },
    {
      prop: "relatedSchoolClassCount",
      label: "关联班级数量",
    },
    {
      prop: "isDelete",
      label: "展示状态",
      formatter: (row) => {
        return row.isDelete == 1 ? "已屏蔽" : "未屏蔽"
      },
    },
    {
      prop: "examDate",
      label: "考试时间",
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
})
const emit = defineEmits(["shieldExamApi", "refresh"])

async function onAfterEnter() {
  try {
    if (!props.keyword) return
    tableOptions.loading = true
    const res = await getExamSearch({
      keyword: props.keyword,
    })
    tableOptions.data = res
    tableOptions.loading = false
  } catch (err) {
    console.log(err)
    tableOptions.loading = false
    tableOptions.data = []
  }
}

async function shieldExamApi(item, isDelete) {
  try {
    await emit("shieldExamApi", [item.examId], isDelete)
    item.isDelete = isDelete
    $g.msg("操作成功")
  } catch (err) {
    console.log(err)
  }
}
function onAfterLeave() {
  emit("refresh")
}
</script>

<style lang="scss" scoped></style>
