<template>
  <div class="paper-blocking-container-main">
    <n-space>
      <n-input
        v-model:value="keyword"
        type="text"
        placeholder="请输入考试名称"
        class="!w-[300px]"
      ></n-input>
      <n-button type="primary" @click="search">搜索</n-button>
    </n-space>
    <g-table :tableOptions="tableOptions" @changePage="initData">
      <template #cz="{ row }">
        <n-space justify="center">
          <n-button type="error" text @click="cancelBlock(row)"
            >取消屏蔽</n-button
          >
        </n-space>
      </template>
    </g-table>
    <BlockingDialog
      v-model:showDialog="showDialog"
      @shieldExamApi="shieldExamApi"
      @refresh="initData"
      :keyword="keyword"
    />
  </div>
</template>

<script setup lang="ts">
import BlockingDialog from "./components/BlockingDialog.vue"
import { getShieldPaperList, shieldExam } from "@/api/teacherMgt"
let showDialog = $ref<boolean>(false)
const tableOptions = reactive<any>({
  loading: false,
  ref: null as any,
  column: [
    {
      prop: "index",
      label: "序号",
    },
    {
      prop: "examName",
      label: "试卷名称",
    },
    {
      prop: "examTypeName",
      label: "考试类型",
    },
    {
      prop: "relatedSchoolCount",
      label: "关联学校数量",
    },
    {
      prop: "relatedSchoolClassCount",
      label: "关联班级数量",
    },
    {
      prop: "isDelete",
      label: "展示状态",
      formatter: () => {
        return "已屏蔽"
      },
    },
    {
      prop: "updateTime",
      label: "操作时间",
    },
    {
      prop: "cz",
      label: "操作",
      slot: true,
    },
  ],
  data: [],
  pageOptions: {
    page_size: 10,
    page: 1,
    total: 0,
  },
})
let keyword = ref<any>("")
onBeforeMount(() => {
  initData()
})
function search() {
  if ($g.tool.isTrue(keyword.value)) {
    showDialog = true
  } else {
    $g.msg("请输入考试名称", "warning")
  }
}
async function initData() {
  try {
    tableOptions.loading = true
    const res = await getShieldPaperList({
      pageSize: tableOptions.pageOptions.page_size,
      page: tableOptions.pageOptions.page,
    })
    tableOptions.data = res.list.map((v, i) => {
      return {
        ...v,
        index:
          (tableOptions.pageOptions.page - 1) *
            tableOptions.pageOptions.page_size +
          i +
          1,
      }
    })
    tableOptions.pageOptions.total = res.total
    tableOptions.loading = false
  } catch (error) {
    tableOptions.loading = false
    tableOptions.data = []
    console.log(error)
  }
}
async function cancelBlock(row) {
  $g.confirm({
    title: "确定取消屏蔽？",
    content: "取消屏蔽后该考试关联的所有学生将看到该场考试信息",
  })
    .then(async () => {
      if (tableOptions.data.length == 1) {
        tableOptions.pageOptions.page != 1 &&
          (tableOptions.pageOptions.page -= 1)
      }
      await shieldExamApi([row.examId], 2)
      $g.msg("取消屏蔽成功")
      initData()
    })
    .catch(() => {})
}
/* 屏蔽 */
async function shieldExamApi(ids, isDelete) {
  try {
    await shieldExam({
      examIds: ids,
      isDelete,
    })
  } catch (error) {
    console.log(error)
  }
}
</script>

<style lang="scss" scoped></style>
