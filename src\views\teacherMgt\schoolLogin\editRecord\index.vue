<template>
  <div class="edit-record-container-main">
    <div class="font-[500] text-[16px]">密码修改记录</div>
    <g-table :tableOptions="tableOptions" @changePage="getRecordList">
    </g-table>
  </div>
</template>

<script setup lang="ts" name="EditRecord">
import { getSchoolLoginCheckCodeHistoryList } from "@/api/teacherMgt"

const route = useRoute()
const tableOptions = $ref<any>({
  loading: false,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 10,
  },
  column: [
    { prop: "accountName", label: "修改人" },
    { prop: "updateTime", label: "修改时间" },
  ],
  data: [],
})
async function getRecordList() {
  if (!route.query.schoolLoginCheckCodeId) return
  try {
    tableOptions.loading = true
    let res = await getSchoolLoginCheckCodeHistoryList({
      schoolLoginCheckCodeId: route.query.schoolLoginCheckCodeId,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
    })
    tableOptions.data = res?.list ?? []
    tableOptions.pageOptions.total = res?.total ?? 0
    tableOptions.loading = false
  } catch (error) {
    tableOptions.loading = false
  }
}
onMounted(() => {
  getRecordList()
})
</script>

<style scoped></style>
