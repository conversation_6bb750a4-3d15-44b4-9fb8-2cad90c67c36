<template>
  <div class="school-login-container-main">
    <g-form :formOptions="filterFormOptions" @search="search" @reset="reset">
    </g-form>

    <g-table :tableOptions="tableOptions" @changePage="getSchoolListLogin">
      <template #checkCode="{ row }">
        <div>
          {{ row?.checkCode || "-" }}
          <g-icon
            v-if="row?.checkCode"
            name="ri-file-copy-line"
            size="18"
            @click="copyLoginCode(row.checkCode)"
          />
        </div>
      </template>
      <template #isCheck="{ row }">
        <el-switch
          disabled
          v-model="row.isCheck"
          :active-value="2"
          :inactive-value="1"
        />
      </template>
      <template #cz="{ row }">
        <el-button
          type="primary"
          text
          :disabled="!row?.checkCode"
          @click="
            router.push({
              name: 'EditRecord',
              query: { schoolLoginCheckCodeId: row?.schoolLoginCheckCodeId },
            })
          "
        >
          密码修改记录
        </el-button>
      </template>
    </g-table>
  </div>
</template>

<script setup lang="ts" name="SchoolLogin">
import { getSchoolLoginCheckCodeList } from "@/api/teacherMgt"

const router = useRouter()
const { copy, copied } = useClipboard({
  legacy: true,
})
const filterFormOptions = reactive<any>({
  ref: null as any,
  filter: true,
  items: {
    schoolId: {
      type: "select",
      label: "学校搜索",
      options: [],
      labelWidth: 90,
      clearable: true,
    },
  },
  // 列表接口使用到的筛选值,及时搜索时使用
  data: {
    schoolId: null,
  },
  // 列表接口使用到的筛选值,非及时搜索时使用
  filterData: {
    schoolId: null,
  },
})
let loginPassword = $ref<string>("")
const tableOptions = $ref<any>({
  loading: true,
  pageOptions: {
    page: 1,
    page_size: 10,
    total: 10,
  },
  column: [
    { prop: "schoolName", label: "学校名字" },
    { prop: "checkCode", label: "登录验证码", slot: true },
    { prop: "isCheck", label: "验证码开关", slot: true },
    { prop: "cz", label: "操作", slot: true },
  ],
  data: [],
})
// 获取学校列表
async function getSchoolListLogin() {
  try {
    tableOptions.loading = true
    let res = await getSchoolLoginCheckCodeList({
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.page_size,
      schoolId: filterFormOptions.data.schoolId ?? "",
    })
    tableOptions.data = res?.list ?? []
    tableOptions.pageOptions.total = res?.total ?? 0
    tableOptions.loading = false
  } catch (error) {
    tableOptions.loading = false
  }
}
// 复制登录码
async function copyLoginCode(loginCode: string) {
  await copy(loginCode)
  console.log(copied.value)
  if (copied.value) {
    $g.msg("复制成功")
  } else {
    $g.msg("复制失败", "error")
  }
}
async function getSchoolList() {
  let res = await getSchoolLoginCheckCodeList({ page: 1, pageSize: 9999 })
  filterFormOptions.items.schoolId.options = res?.list?.map((v) => {
    return {
      label: v.schoolName,
      value: v.schoolId,
    }
  })
}

// 搜索
function search(data: any) {
  tableOptions.pageOptions.page = 1
  getSchoolListLogin()
}
// 重置
async function reset() {
  tableOptions.pageOptions.page = 1
  filterFormOptions.data.schoolId = null
  await getSchoolListLogin()
}

onMounted(async () => {
  getSchoolList()
  getSchoolListLogin()
})
</script>

<style scoped lang="scss">
.loginCode {
  padding: 2px 5px;
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
</style>
