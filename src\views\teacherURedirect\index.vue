<template>
  <div></div>
</template>
<script lang="ts" setup>
import {
  fetchExamXKWBind,
  fetchExamXKWOAuth,
  bindExamXKWApi,
} from "@/api/resourceMgt"
import config from "@/config/index"
const route = useRoute()
let xkwOpenId = $ref<any>(null)
const { baseOrigin } = config
async function bindExamXKW() {
  const { code, state } = route.query
  if (code && state) {
    const data = await bindExamXKWApi({
      code,
      state,
    })
    xkwOpenId = data?.openId
    if (data?.openId) {
      $g.msg("绑定成功", "success")
      location.replace(`${baseOrigin}/#/third/teacherURedirect`)
    }
  }
}
async function goToXKWAuth() {
  let agreement =
    import.meta.env.VITE_APP_ENV != "development" ? "https" : "http"
  const res = await fetchExamXKWOAuth({
    redirectUri: `${agreement}://zujuan.qimingdaren.com/#/third/teacherURedirect`,
    xkwService: "COMPOSITION",
  })
  if (res) {
    location.href = res
  }
}
function goToXKW() {
  const params = new URLSearchParams({
    _openid: xkwOpenId,
    _m: `${location.protocol}//zujuan.qimingdaren.com/#/third/teacherURedirect`,
  })
  const url = `${
    location.protocol
  }//zujuan.qimingdaren.com/#/teacherUserIframe?${params.toString()}`
  location.replace(url)
}
async function getXKWBind() {
  const data = await fetchExamXKWBind()
  xkwOpenId = data?.openId
  if (!xkwOpenId) {
    const { code, state } = route.query
    if (code && state) {
      bindExamXKW()
      return
    }
    goToXKWAuth()
  } else {
    goToXKW()
  }
}
onMounted(() => {
  getXKWBind()
})
</script>
