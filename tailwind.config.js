const plugin = require("tailwindcss/plugin")
const spacing = {}
for (let index = 1; index <= 500; index++) {
  spacing[index + "px"] = index + "px"
}

module.exports = {
  important: "body",
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    extend: { spacing },
    fontSize: {
      ...spacing,
    },
    maxHeight: {
      ...spacing,
    },
    minHeight: {
      ...spacing,
    },
    maxWidth: {
      ...spacing,
    },
    minWidth: {
      ...spacing,
    },
    colors: {
      primary: "var(--g-primary)",
      info: "var(--g-info)",
      success: "var(--g-success)",
      warning: "var(--g-warning)",
      danger: "var(--g-danger)",
      error: "var(--g-error)",
      borderColor: "var(--g-border-color)",
      white: "#fff",
      black: "#000",
      transparent: "transparent",
      gray: {
        lightest: "#f7f8fa",
        light: "#dcdee2",
        default: "#999",
        dark: "#666",
        darkest: "#333",
      },
    },
  },
  plugins: [
    plugin(function ({ addComponents, matchUtilities, theme }) {
      matchUtilities(
        {
          lh: (value) => ({
            "line-height": value,
          }),
        },
        { values: theme("lh") },
      )

      matchUtilities(
        {
          br: (value) => ({
            "border-radius": value,
          }),
        },
        { values: theme("br") },
      )
      addComponents({})
    }),
  ],
  corePlugins: {
    preflight: false,
  },
}
