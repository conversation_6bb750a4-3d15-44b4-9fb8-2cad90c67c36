{
  "extends": "@vue/tsconfig/tsconfig.web.json",
  "include": [
    "types/*.d.ts",
    "src/**/*",
    "src/**/*.vue",
    "src/**/*.tsx",
    "public/tinymec/langs/zh-Hans.js"
  ],
  "exclude": [
    "tailwind.config.js"
  ],
  "compilerOptions": {
    "module": "esnext",
    "lib": [
      "ESNext"
    ],
    "baseUrl": ".",
    "jsx": "preserve",
    "paths": {
      "@/*": [
        "./src/*"
      ]
    },
    "allowJs": true,
    "noImplicitAny": false,
    "isolatedModules": false,
    "types": [
      "vue/ref-macros",
      "node"
    ],
    "ignoreDeprecations": "5.0",
  },
  "references": [
    {
      "path": "./tsconfig.config.json"
    }
  ]
}