interface Tool {
  [x: string]: any
  /** 获取文件后缀 */
  getExt(filename: string): string
  /** 将url请求参数转为json格式 */
  paramObj(...params: any): object
  /** 获取随机id */
  uuid(length: number): string
  /** 判断是否为存在 */
  isTrue(a: any): boolean
  /** 获取静态资源地址 */
  getFileUrl(url: string): any
  /** 判断精准类型 */
  typeOf(
    obj: any,
  ):
    | "boolean"
    | "NaN"
    | "number"
    | "string"
    | "function"
    | "array"
    | "date"
    | "regExp"
    | "undefined"
    | "null"
    | "object"
  /** 点击复制 传入类名或者id名 加上要复制文字 */
  copyData(target: string, text: string): void
  /** 计算到现在时间 */
  timeAgo(time: any): string
  /** url参数拼接 */
  paramsToUrl(e: object, isNew?: boolean): void
  /** 动态加载js */
  loadJS(url: string): any
  /** 获取对应文件后缀缩略图 */
  getFileTypeIcon(suffix: string): any
  retry(fn: Function, times: number, delay: number): Promise<any>
  /** 下载 */
  downloadFile(...params: any): any
  /**通过文件后缀获取文件类型 */
  getFileType(suffix: string): any
  /**
   *
   * 根据链接地址获取对应的文档的外部预览地址，url: 链接地址，suffix:vip365额外参数
   */
  getDocFrameUrl(url: string, suffix?: string): any
  renderMathjax(className?: string): void
  /**打包多个文件为zip并下载 */
  compressZipFile(
    files: { fileUrl: string; fileName: string }[],
    exportName: string,
  ): Promise<any>
  mergeConfigs(...params: any): any
}

interface Lodash {
  /** 深拷贝 */
  cloneDeep(...params: any): any
  /** 调用 iteratee 遍历 collection(集合) 中的每个元素。 如果迭代函数（iteratee）显式的返回 false ，迭代会提前退出。 */
  forEach(...params: any): any
  /** 遍历 collection（集合）元素，返回 predicate（断言函数）返回真值 的所有元素的数组。 */
  filter(...params: any): any
  /** 移除数组中predicate（断言）返回为真值的所有元素，并返回移除元素组成的数组 */
  remove(...params: any): any
  /** 遍历 collection（集合）元素，返回 predicate（断言函数）第一个返回真值的第一个元素 */
  find(...params: any): any
  /** 该方法类似_.find，区别是该方法返回第一个通过 predicate 判断为真值的元素的索引值（index），而不是元素本身。 */
  findIndex(...params: any): any
  /** 将数组（array）拆分成多个 size 长度的区块，并将这些区块组成一个新数组 */
  chunk(...params: any): any
  /** 创建一个从 object 中选中的属性的对象。 */
  pick(...params: any): any
  /** 反向版_.pick */
  omit(...params: any): any
  /** 检查 value(值) 是否在 collection(集合) 中。 */
  includes(...params: any): any
  /** 通过 predicate（断言函数） 检查 collection（集合）中的 所有 元素是否都返回真值。 */
  every(...params: any): any
  /** 防抖:创建一个 debounced（防抖动）函数，该函数会从上一次被调用后，延迟 wait 毫秒后调用 func 方法。 */
  debounce(...params: any): any
  /** 节流:在 wait 秒内最多执行 func 一次的函数。 */
  throttle(...params: any): any
  /** 这个方法类似_.forEach，不同之处在于，_.forEachRight 是从右到左遍历集合中每一个元素的。 */
  forEachRight(...params: any): any
  random(...params: any): any
  findLastIndex(...params: any): any
  uniqBy(...params: any): any
  uniqueId(...params: any): any
  /** 判断对象是否相等 */
  isEqual(...params: any): any
  map(...params: any): any
  isArray(...params: any): any
  mergeWith(...params: any): any
}
interface MathNumber {
  /** 加  */
  add(num: number): MathNumber
  /** 减  */
  sub(num: number): MathNumber
  /** 乘  */
  multiply(num: number): MathNumber
  /** 除  */
  divide(num: number): MathNumber
  /** 四舍五入  */
  toFixed(precision?: number): MathNumber
  /** 获取结果  */
  value(): number
}
interface Global {
  /** 工具函数  */
  tool: Tool
  /** 事件总线  */
  bus: any
  dayjs: any
  _: Lodash
  msg(
    msg: string,
    type?: "success" | "error" | "info" | "warning" | "loading",
    duration?: number,
  ): void
  confirm(config: any): Promise<void>
  /** 数学方法*/
  math(num: number): MathNumber
  [propName: string]: any
}
interface Window {
  $g: any
  $_: any
  flutter_inappwebview: any
  MathJax: any
  VConsole: any
  leaveCurrentPageState: (...arg) => void
  refreshPage: (...arg) => void
}
declare const $g: Global
declare const _: any
declare const AliyunUpload: any
declare const __APP_VERSION__: string
declare const GGBApplet: any
declare const ElMessageBox: any
interface CanvasRenderingContext2D {
  webkitBackingStorePixelRatio?: number
  mozBackingStorePixelRatio?: number
  msBackingStorePixelRatio?: number
  oBackingStorePixelRatio?: number
  backingStorePixelRatio?: number
}
