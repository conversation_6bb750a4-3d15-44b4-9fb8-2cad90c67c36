import type { RouteRecordRaw } from "vue-router"

/**
 * 路由记录
 */
declare interface ChRouteRecord
  extends Omit<RouteRecordRaw, "name" | "meta" | "children"> {
  name: string
  meta: ChRouteMeta
  children?: ChRouteRecord[]
  childrenPathList?: string[]
  parentIcon?: string
  redirect?: string
}

declare interface ChRouteMeta {
  // 高亮指定菜单
  activeMenu?: string
  // 是否显示在菜单中显示隐藏路由(默认值：false)
  hidden?: boolean
  // 图标
  icon?: string
  // 当前路由是否不缓存(默认值：false)
  noKeepAlive?: boolean
  // 在新窗口中打开
  target?: "_blank" | false
  // 菜单、面包屑、多标签页显示的名称
  title?: string
}
