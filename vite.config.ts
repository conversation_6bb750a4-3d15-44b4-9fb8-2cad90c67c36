import { resolve } from "path"
import { setupVitePlugins } from "./vite/plugins/index"
import { createBuildConfig } from "./vite/build/index"
import { defineConfig, loadEnv } from "vite"

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const ENV = loadEnv(mode, process.cwd(), "")
  const { VITE_APP_ENV } = loadEnv(mode, process.cwd(), "")
  const version = new Date().getTime()
  return {
    base: "./",
    server: {
      // https: true,
      port: 80,
      //使用IP能访问
      host: "vite-template-dev.qimingdaren.com",
      // host: "zujuan.qimingdaren.com",
      //host: "rs-manage.qimingdaren.com",
      // host: true,
    },
    plugins: setupVitePlugins({ mode: VITE_APP_ENV, command, version, ENV }),
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
      },
    },
    define: {
      __APP_VERSION__: version,
    },
    build: createBuildConfig(ENV as any),
    optimizeDeps: {
      include: [
        "element-plus/es/components/scrollbar/style/index",
        "element-plus/es/components/tree/style/index",
        "element-tree-line",
        "@vueuse/core",
        "element-plus/es/components/loading/style/index",
        "seemly",
        "element-plus/es/components/empty/style/index",
        "xgplayer/dist/simple_player",
        "xgplayer/dist/controls/volume",
        "xgplayer/dist/controls/playbackRate",
        "xgplayer-flv.js",
        "xgplayer-hls.js",
        "xgplayer-hls",
        "@tinymce/tinymce-vue",
        "element-plus/es/components/image-viewer/style/index",
        "element-plus/es/components/button-group/style/index",
        "element-plus/es/components/button/style/index",
        "@ballcat/vue-cropper",
        "element-plus/es/components/input/style/index",
        "element-plus/es/components/table/style/index",
        "element-plus/es/components/table-column/style/index",
        "element-plus/es/components/tooltip/style/index",
        "element-plus/es/components/input-number/style/index",
        "element-plus/es/components/radio-group/style/index",
        "element-plus/es/components/radio-button/style/index",
        "element-plus/es/components/link/style/index",
        "lottie-web/build/player/lottie_light",
        "element-plus/es/components/text/style/index",
        "element-plus/es/components/card/style/index",
        "element-plus/es/components/icon/style/index",
        "element-plus/es/components/popconfirm/style/index",
        "element-plus/es/components/popover/style/index",
        "element-plus/es/components/space/style/index",
        "@l9m/v-md-editor/lib/plugins/tip/index",
        "@l9m/v-md-editor/lib/plugins/line-number/index",
        "@l9m/v-md-editor/lib/plugins/highlight-lines/index",
        "@l9m/v-md-editor/lib/plugins/copy-code/index",
        "@l9m/v-md-editor/lib/plugins/align",
        "element-plus/es/components/dropdown/style/index",
        "element-plus/es/components/dropdown-menu/style/index",
        "element-plus/es/components/dropdown-item/style/index",
        "element-plus/es/components/dialog/style/index",
        "element-plus/es/components/badge/style/index",
        "element-plus/es/components/tabs/style/index",
        "element-plus/es/components/tab-pane/style/index",
        "element-plus/es/components/radio/style/index",
        "element-plus/es/components/switch/style/index",
        "element-plus/es/components/upload/style/index",
        "element-plus/es/components/color-picker/style/index",
        "element-plus/es/components/checkbox/style/index",
      ],
    },
    css: {
      extract: true, // 是否使用css分离插件 ExtractTextPlugin
      sourceMap: false, // 开启 CSS source maps
      requireModuleExtension: true,
      preprocessorOptions: {
        scss: {
          quietDeps: true,
          javascriptEnabled: true,
          // additionalData: `@use "@/styles/variables/element.module.scss" as *;`,
        },
      },
    },
  }
})
