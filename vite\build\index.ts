// import externalGlobals from "rollup-plugin-external-globals"

export function createBuildConfig(ENV) {
  return {
    sourcemap: (ENV.VITE_APP_SOURCEMAP == "open" ? "hidden" : false) as any,
    commonjsOptions: {
      transformMixedEsModules: true,
    },
    rollupOptions: {
      output: {
        // manualChunks: (id) => {
        //   if (id.includes("node_modules")) {
        //     return "vendor";
        //   }
        // },
        manualChunks: {
          "vue-render": [
            "vue",
            "vue-router",
            "pinia",
            "pinia-plugin-persistedstate",
          ],
          "utils-vender": [
            "bignumber.js",
            "dayjs",
            "timeago.js",
            "eventemitter3",
            "axios",
            "lodash-es",
          ],
          // "element-vender": ["element-plus"],
          "element-tree-line-vender": ["element-tree-line"],
          "sentry-vender": ["@sentry/vue"],
          "naive-ui-vender": ["naive-ui", "chroma-js"],
        },
        entryFileNames: "js/[name].[hash].js",
        chunkFileNames: "js/[name].[hash].js",
        assetFileNames: "[ext]/[name].[hash].[ext]",
      },
      // external: ["echarts", "_"],
      plugins: [
        // externalGlobals({
        //   echarts: "echarts",
        //   // lodash: "_",
        // }),
      ],
    },
  }
}
