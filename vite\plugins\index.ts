import type { PluginOption } from "vite"
import { resolve } from "path"
import vue from "@vitejs/plugin-vue"
import vueSetupExtend from "vite-plugin-vue-setup-extend-plus"
import inject from "@rollup/plugin-inject"
import AutoImport from "unplugin-auto-import/vite"
import Components from "unplugin-vue-components/vite"
import vueJsx from "@vitejs/plugin-vue-jsx"
import {
  NaiveUiResolver,
  ElementPlusResolver,
} from "unplugin-vue-components/resolvers"
import { createSvgIconsPlugin } from "vite-plugin-svg-icons"
import viteCDNPlugin from "vite-plugin-cdn-import"
import { visualizer } from "rollup-plugin-visualizer"
import versionPlugin from "./version"
import { codeInspectorPlugin } from "code-inspector-plugin"
import progress from "cc-vite-progress"
import { sentryVitePlugin } from "@sentry/vite-plugin"

export function setupVitePlugins({
  mode,
  version,
  ENV,
  command,
}): PluginOption[] {
  let plugins = [
    vue({
      reactivityTransform: true,
      script: {
        defineModel: true,
      },
    }),
    vueJsx(),
    inject({
      include: ["src/**/**"],
      modules: {
        $g: resolve("src/utils/index"),
      },
    }),
    //vue SFC name support
    vueSetupExtend(),
    AutoImport({
      resolvers: [
        ElementPlusResolver({
          importStyle: "sass",
        }),
      ],
      eslintrc: {
        filepath: "./.eslintrc-auto-import.json",
        enabled: true,
      },
      imports: [
        "vue",
        "@vueuse/core",
        "vue-router",
        "pinia",
        {
          "naive-ui": [
            "useDialog",
            "useMessage",
            "useNotification",
            "useLoadingBar",
          ],
        },
      ],
      dts: "types/auto-imports.d.ts",
    }),
    Components({
      dirs: ["src/components/"],
      resolvers: [
        NaiveUiResolver(),
        ElementPlusResolver({
          importStyle: "sass",
        }),
      ],
      dts: "types/components.d.ts",
    }),
    createSvgIconsPlugin({
      iconDirs: [resolve(process.cwd(), "src/assets/svg-icon")],
      symbolId: "svg-[dir]-[name]",
    }),
    codeInspectorPlugin({
      bundler: "vite",
    }),
    visualizer(),
    progress({ projectName: "金字塔后台管理" }),
    // viteCDNPlugin({
    //   // 需要 CDN 加速的模块
    //   modules: [
    //     {
    //       name: "lodash",
    //       var: "_",
    //       path: `https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js`,
    //     },
    //   ],
    // }),
  ]

  if (!mode.includes("development")) {
    plugins = [
      ...plugins,
      versionPlugin({ version }),
      viteCDNPlugin({
        // 需要 CDN 加速的模块
        modules: [
          {
            name: "plotly.js/dist/plotly",
            var: "Plotly",
            path: "https://frontend-cdn.qimingdaren.com/three/js/plotly.js%402.26.0_dist_plotly.min.js",
          },
          {
            name: "echarts",
            var: "echarts",
            path: "https://frontend-store.oss-cn-chengdu.aliyuncs.com/cdn/echarts.min.js",
          },
        ],
      }),
    ]
  }
  if (ENV.VITE_APP_SOURCEMAP == "open") {
    plugins = [
      ...plugins,
      sentryVitePlugin({
        org: "qmdr",
        project: "ge-fu-admin",
        authToken:
          "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",
        debug: false, // 禁用调试信息打印
        silent: true,
      }),
    ]
  }
  return plugins
}
